ARG RABBITMQ_VERSION=4.1.0
ARG RABBITMQ_BASE_IMAGE=rabbitmq:${RABBITMQ_VERSION}-alpine
FROM $RABBITMQ_BASE_IMAGE

LABEL maintainer="XJP09_HK <<EMAIL>>"

# 安装常用插件
# RUN rabbitmq-plugins enable --offline \
#     rabbitmq_management \
#     rabbitmq_prometheus \
#     rabbitmq_federation \
#     rabbitmq_federation_management \
#     rabbitmq_shovel \
#     rabbitmq_shovel_management

RUN set -eux; \
    wget -P ${RABBITMQ_HOME}/plugins https://github.com/rabbitmq/rabbitmq-delayed-message-exchange/releases/download/v${RABBITMQ_VERSION}/rabbitmq_delayed_message_exchange-${RABBITMQ_VERSION}.ez; \
    rabbitmq-plugins enable \
        rabbitmq_delayed_message_exchange \
        ; \
    :

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=5 \
    CMD rabbitmq-diagnostics --quiet ping
