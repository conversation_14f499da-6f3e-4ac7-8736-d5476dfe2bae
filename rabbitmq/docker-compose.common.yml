services:
  rabbitmq-base:
    extends:
      file: ../docker-compose/common.yml
      service: common
    container_name: ${DOCKER_CONTAINER_PREFIX}${CI_SOURCE_NAME}-rabbitmq
    mem_limit: 300M
    extra_hosts:
      - "$DOCKER_HOSTNAME:127.0.0.1"
    healthcheck:
      # test: ["CMD", "rabbitmq-diagnostics", "--quiet", "ping"]
      interval: 30s
      timeout: 10s

  rabbitmq-build-base:
    extends:
      service: rabbitmq-base
    build:
      context: .
    image: ${DOCKER_IMAGE_PREFIX}${CI_SOURCE_NAME}/rabbitmq:${RABBITMQ_VERSION}-${CI_COMMIT_REF_NAME}

  rabbitmq:
    extends:
      service: rabbitmq-base
    image: ${DOCKER_BASE_IMAGE_PREFIX}rabbitmq:${RABBITMQ_VERSION}-${CI_COMMIT_REF_NAME}

  rabbitmq-build:
    extends:
      service: rabbitmq-build-base
    build:
      args:
        - RABBITMQ_SELF_BASE_IMAGE=${DOCKER_BASE_IMAGE_PREFIX}rabbitmq:${RABBITMQ_VERSION}-${CI_COMMIT_REF_NAME}

  rabbitmq-management:
    extends:
      service: rabbitmq-base
    image: ${DOCKER_BASE_IMAGE_PREFIX}rabbitmq:${RABBITMQ_VERSION}-management-${CI_COMMIT_REF_NAME}

  rabbitmq-management-build:
    extends:
      service: rabbitmq-build-base
    build:
      args:
        - RABBITMQ_SELF_BASE_IMAGE=${DOCKER_BASE_IMAGE_PREFIX}rabbitmq:${RABBITMQ_VERSION}-management-${CI_COMMIT_REF_NAME}
