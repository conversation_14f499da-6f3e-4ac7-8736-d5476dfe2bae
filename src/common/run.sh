#!/bin/sh
#
# Docker运行控制脚本 -- Run
#
# <AUTHOR> <EMAIL>
# Version: 1.0 (2025-04-01 19:04:20)

printf "${INFO1}当前目录: ${INFO2}$(pwd) ${NC}"

# 执行用户命令
case "$command" in
    help) printUsage;;
    update) update;;
    start) start "$@";;
    restart) restart "$@";;
    stop) stop "$@";;
    down) down;;
    logs) logs "$@";;
    status) status;;
    build) build;;
    exec) execComposer "$@";;
    config) config;;
    clean|clear) clean;;
    *) printf "${INFO1}$0:${RS} ${WARN1}'$command'${RS} ${INFO2}不是个标准命令, 请使用 ${INFO3}$0 help${RS} ${INFO2}查看帮助.${NC}";;
esac
