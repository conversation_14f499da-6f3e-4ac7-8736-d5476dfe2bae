#!/bin/sh
#
# Docker运行控制脚本 -- help
#
# <AUTHOR> <EMAIL>
# Version: 1.0 (2025-04-01 18:57:50)

printUsage() {
    echo
    printf "${INFO1}用法:${RS} ${INFO3}$0 [<环境>] <命令>${NC}"
    echo
    printf "${INFO1}目前可选的环境:${NC}"
    printf "  ${INFO2}local   (默认, 不写时选用)利于本地开发调试方便的, 平时开发选择这个即可${NC}"
    printf "  ${INFO5}dev     开发环境, 部署开发环境时请使用此选项${NC}"
    printf "  ${INFO5}test    测试环境, 部署测试环境时请使用此选项${NC}"
    printf "  ${INFO5}master  正式环境, 部署正式环境时请使用此选项${NC}"
    echo
    printf "${INFO1}一般的用户使用如下命令就好了:${NC}"
    printf "  ${INFO2}help       打印此帮助${NC}"
    printf "  ${INFO2}update     更新docker运行环境${NC}"
    printf "  ${INFO2}start      启动docker运行环境${NC}"
    printf "  ${WARN1}restart    重启docker运行环境, 本地初次或者线上部署发布请使用此命令${NC}"
    printf "  ${INFO2}stop       停止docker运行环境${NC}"
    printf "  ${INFO4}down       关闭所有docker运行环境${NC}"
    printf "  ${INFO2}logs       查看服务运行日志${NC}"
    echo
    printf "${INFO1}仅供docker熟悉的相关开发者使用:${NC}"
    printf "  ${INFO4}build      编译运行环境镜像${NC}"
    printf "  ${INFO2}exec       进入服务的运行环境${NC}"
    printf "  ${INFO4}status     查看运行状态${NC}"
    printf "  ${INFO4}config     查看compose文件配置${NC}"
    printf "  ${WARN1}clean      清理无用的容器及镜像${NC}"
    echo
}
