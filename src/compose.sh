#!/bin/sh
#
# Docker运行控制脚本 -- docker compose manager
#
# <AUTHOR> <EMAIL>
# Version: 1.0 (2025-01-17 15:47:44)

# 使用特定环境的dockerCompose文件
dockerComposeBaseBuildFile=cmd/docker-compose/base.build.$env.yml
dockerComposeFile=docker-compose.$env.yml
if [ ! -f "$dockerComposeBaseBuildFile" ]; then
    dockerComposeBaseBuildFile=cmd/docker-compose/base.build.yml
fi
if [ ! -f "$dockerComposeFile" ]; then
    dockerComposeFile=docker-compose.yml
fi

# 优先使用本地的dockerCompose文件
if [ -f "local.$dockerComposeFile" ]; then
    dockerComposeFile=local.$dockerComposeFile
fi

# 注意：原脚本使用了 docker compose (v2) 和 docker-compose (v1) 两种写法，这里统一为 docker compose
# 如果需要兼容 v1，请将下面的 docker compose 改回 docker-compose
dockerComposeBaseCmd="docker compose --env-file .env -p resc-base -f $dockerComposeBaseBuildFile"
dockerComposeCmd="docker compose -f $dockerComposeFile"
if [ -f "docker-compose.common.yml" ]; then
    dockerComposeCmd="$dockerComposeCmd -f docker-compose.common.yml"
fi

start() {
    down

    printf "${INFO1}启动环境${RS}: ${WARN1}${env}${RS}!${NC}";

    upOpt="-d"
    # 使用 POSIX 兼容的 ||
    if [ "$DEBUG_MODE" = "1" ] || [ "$DEBUG_MODE" = "true" ]; then
        printf "${WARN1}开启调试${NC}";
        upOpt=""
    fi

    $dockerComposeCmd up $upOpt
}

restart() {
    build
    start
}

status() {
    $dockerComposeCmd ps
}

stop() {
    shift 2
    args="$@"

    $dockerComposeCmd stop $args
}

down() {
    $dockerComposeCmd down --remove-orphans
}

logs() {
    shift 2
    args="$@"

    $dockerComposeCmd logs $args
}

buildBase() {
    # 使用 POSIX 兼容的 -n 判断字符串非空
    if [ -n "$DOCKER_BASE_IMAGE_LIST" ]; then
        printf "${INFO3}Building Base Image ...${NC}"

        $dockerComposeBaseCmd build --force-rm $DOCKER_BASE_IMAGE_LIST
    fi
}

build() {
    buildBase

    printf "${INFO3}Building Image ...${NC}"

    $dockerComposeCmd build --force-rm
}

execComposer() {
    shift 2
    args="$@"

    # 假设这里的 exec 是指 docker compose exec
    $dockerComposeCmd exec $args
}

config() {
    # 使用 POSIX 兼容的 -n 判断字符串非空
    if [ -n "$DOCKER_BASE_IMAGE_LIST" ]; then
        $dockerComposeBaseCmd config $DOCKER_BASE_IMAGE_LIST
    fi
    $dockerComposeCmd config
}

# 清理容器函数
cleanContainers() {
    printf "${WARN1}正在清理不再运行的容器...${NC}"
    # 确保命令在没有容器时也能正常运行 (使用 xargs -r)
    docker ps -a | grep -v Up | awk 'NR>1 {print $1}' | xargs -r docker rm
}

# 清理镜像函数
cleanImages() {
    printf "${WARN1}正在清理无用的镜像...${NC}"
    # 确保命令在没有镜像时也能正常运行 (使用 xargs -r)
    docker images | grep none | awk '{print $3}' | xargs -r docker rmi -f
}

# 主清理函数
clean() {
    down
    cleanContainers
    cleanImages
    printf "${WARN1}清理完成~${NC}"
}
