<?php
declare(strict_types=1);

namespace tests\model;

use app\common\model\Order;
use PHPUnit\Framework\TestCase;

final class OrderTest extends TestCase
{
    public function testGetOrderAllFields()
    {
        if (config('app.env') !== "development") {
            $this->assertTrue(true);
            return;
        };
        $expected = [
            'order_id' => '10000000000063022407261534030001',
            'order_data' => [
                'id' => '10000000000063022407261534030001',
                'user_id' => 1000000000012,
                'corp_id' => 100001,
                'station_id' => 1013,
                'piles_id' => 10000000000063,
                'sequence' => 2,
                'shot_id' => 1000000000006302,
                'money' => 0,
                'pay_money' => 0,
                'coupon_money' => 0,
                'charge_duration' => 0,
                'consumption_amount' => 0,
                'electricity_price' => 0,
                'ser_price' => 0,
                'amount_charged' => 0,
                'electricity_charged' => 0,
                'settled_amount' => 0,
                'create_time' => '2024-07-26 15:34:03',
                'update_time' => '2024-07-26 15:35:03',
                'trans_start_time' => NULL,
                'trans_end_time' => NULL,
                'status' => 6,
                'reason_for_stop' => NULL,
                'reason_for_stop_text' => '【运营平台远程控制启机】指令超时',
                'msg' => '【运营平台远程控制启机】指令超时',
                'sharp_electricity' => 0,
                'sharp_loss' => 0,
                'sharp_amount' => 0,
                'peak_electricity' => 0,
                'peak_loss' => 0,
                'peak_amount' => 0,
                'flat_electricity' => 0,
                'flat_loss' => 0,
                'flat_amount' => 0,
                'valley_electricity' => 0,
                'valley_loss' => 0,
                'valley_amount' => 0,
                'electricity_total' => 0,
                'electricity_loss' => 0,
                'sharp_price' => 0,
                'sharp_fee' => 130000,
                'sharp_ser_fee' => 20000,
                'peak_price' => 0,
                'peak_fee' => 130000,
                'peak_ser_fee' => 20000,
                'flat_price' => 0,
                'flat_fee' => 130000,
                'flat_ser_fee' => 20000,
                'valley_price' => 0,
                'valley_fee' => 130000,
                'valley_ser_fee' => 20000,
                'currency' => 1,
                'type' => 1,
                'pay_mode' => 3,
                'start_time' => NULL,
                'end_time' => NULL,
                'card_id' => NULL,
                'desc' => NULL,
                'build_type' => 1,
                'meter_start_value' => 0,
                'meter_end_value' => 0,
                'vin_code' => NULL,
                'ip' => '**************',
                'loss_rate' => 0,
                'period_codes' => '030303030303030303030303030302020202020200000000000000000000000000000000000101010101010101010101',
                'freeze_money' => 0,
                'freeze_status' => 1,
                'clearing_electricity_price' => 0,
                'clearing_ser_price' => 0,
                'clearing_status' => 1,
                'abnormal_alarm_node' => 0,
                'reservation_time' => NULL,
                'discount' => 100,
                'billing_mode_id' => 0,
            ]
        ];

        $orderModel = new Order();
        $orderData = $orderModel->getOrder($expected['order_id']);
        $this->assertSame($expected['order_data'], $orderData);
    }

    public function testGetOrderFieldUserIdAndShotId()
    {
        if (config('app.env') !== "development") {
            $this->assertTrue(true);
            return;
        };
        $expected = [
            'order_id' => '10000000000063022407261534030001',
            'order_data' => [
                'user_id' => 1000000000012,
                'shot_id' => 1000000000006302,
            ]
        ];

        $orderModel = new Order();
        $orderData = $orderModel->getOrder($expected['order_id'], ['user_id', 'shot_id']);
        $this->assertSame($expected['order_data'], $orderData);
    }
}