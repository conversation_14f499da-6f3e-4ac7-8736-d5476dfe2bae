<?php
declare(strict_types=1);

namespace tests\model;

use app\common\model\RestartOrderLog;
use PHPUnit\Framework\TestCase;

final class RestartOrderLogTest extends TestCase
{
    public function testAddLog()
    {
        if (config('app.env') !== "development") {
            $this->assertTrue(true);
            return;
        };
        $params = [
            'add_params' => [
                'order_id' => '',
                'applet_user_id' => 1000000000012,
                'op_user_id' => 1,
                'op_client_ip' => '127.0.0.1',
                'power_limit' => 7000
            ]
        ];

        $model = app(RestartOrderLog::class);

        $new_id = $model->addLog(
            $params['add_params']['order_id'],
            $params['add_params']['applet_user_id'],
            $params['add_params']['op_user_id'],
            $params['add_params']['op_client_ip'],
            $params['add_params']['power_limit'],
        );

        $this->assertSame(1, $model->getCount());

        $deleteRow = $model->deleteLog($new_id);
        $this->assertSame(1, $deleteRow);

        $this->assertSame(0, $model->getCount());
    }
}