<?php
declare(strict_types=1);

namespace tests\model;

use app\common\model\TariffGroup;
use PHPUnit\Framework\TestCase;
use think\db\exception\DbException;

final class TariffGroupTest extends TestCase
{
    public function testGetTariffGroupSuccess()
    {
        if (config('app.env') !== "development") {
            $this->assertTrue(true);
            return;
        };
        $data = [
            "id" => 1000,
            "name" => "默认",
            "sharp_fee" => 130000,
            "peak_fee" => 130000,
            "flat_fee" => 130000,
            "valley_fee" => 130000,
            "sharp_ser_fee" => 20000,
            "peak_ser_fee" => 20000,
            "flat_ser_fee" => 20000,
            "valley_ser_fee" => 20000,
            "loss_rate" => 0,
            "period_codes" => "030303030303030303030303030302020202020200000000000000000000000000000000000101010101010101010101",
            "opt_id" => 1,
            "create_time" => "2023-07-11 17:31:08",
            "period_json_sum" => '[{"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"00:00\uff5e07:00","sum_fee":150000,"end_time":"07:00","start_time":"00:00"},{"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"07:00\uff5e10:00","sum_fee":150000,"end_time":"10:00","start_time":"07:00"},{"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"10:00\uff5e18:30","sum_fee":150000,"end_time":"18:30","start_time":"10:00"},{"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"18:30\uff5e24:00","sum_fee":150000,"end_time":"24:00","start_time":"18:30"}]',
            "period_json" => '[{"id":1,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"00:00\uff5e00:30","sum_fee":150000,"end_time":"00:30","start_time":"00:00"},{"id":2,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"00:30\uff5e01:00","sum_fee":150000,"end_time":"01:00","start_time":"00:30"},{"id":3,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"01:00\uff5e01:30","sum_fee":150000,"end_time":"01:30","start_time":"01:00"},{"id":4,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"01:30\uff5e02:00","sum_fee":150000,"end_time":"02:00","start_time":"01:30"},{"id":5,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"02:00\uff5e02:30","sum_fee":150000,"end_time":"02:30","start_time":"02:00"},{"id":6,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"02:30\uff5e03:00","sum_fee":150000,"end_time":"03:00","start_time":"02:30"},{"id":7,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"03:00\uff5e03:30","sum_fee":150000,"end_time":"03:30","start_time":"03:00"},{"id":8,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"03:30\uff5e04:00","sum_fee":150000,"end_time":"04:00","start_time":"03:30"},{"id":9,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"04:00\uff5e04:30","sum_fee":150000,"end_time":"04:30","start_time":"04:00"},{"id":10,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"04:30\uff5e05:00","sum_fee":150000,"end_time":"05:00","start_time":"04:30"},{"id":11,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"05:00\uff5e05:30","sum_fee":150000,"end_time":"05:30","start_time":"05:00"},{"id":12,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"05:30\uff5e06:00","sum_fee":150000,"end_time":"06:00","start_time":"05:30"},{"id":13,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"06:00\uff5e06:30","sum_fee":150000,"end_time":"06:30","start_time":"06:00"},{"id":14,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"06:30\uff5e07:00","sum_fee":150000,"end_time":"07:00","start_time":"06:30"},{"id":15,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"07:00\uff5e07:30","sum_fee":150000,"end_time":"07:30","start_time":"07:00"},{"id":16,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"07:30\uff5e08:00","sum_fee":150000,"end_time":"08:00","start_time":"07:30"},{"id":17,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"08:00\uff5e08:30","sum_fee":150000,"end_time":"08:30","start_time":"08:00"},{"id":18,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"08:30\uff5e09:00","sum_fee":150000,"end_time":"09:00","start_time":"08:30"},{"id":19,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"09:00\uff5e09:30","sum_fee":150000,"end_time":"09:30","start_time":"09:00"},{"id":20,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"09:30\uff5e10:00","sum_fee":150000,"end_time":"10:00","start_time":"09:30"},{"id":21,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"10:00\uff5e10:30","sum_fee":150000,"end_time":"10:30","start_time":"10:00"},{"id":22,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"10:30\uff5e11:00","sum_fee":150000,"end_time":"11:00","start_time":"10:30"},{"id":23,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"11:00\uff5e11:30","sum_fee":150000,"end_time":"11:30","start_time":"11:00"},{"id":24,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"11:30\uff5e12:00","sum_fee":150000,"end_time":"12:00","start_time":"11:30"},{"id":25,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"12:00\uff5e12:30","sum_fee":150000,"end_time":"12:30","start_time":"12:00"},{"id":26,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"12:30\uff5e13:00","sum_fee":150000,"end_time":"13:00","start_time":"12:30"},{"id":27,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"13:00\uff5e13:30","sum_fee":150000,"end_time":"13:30","start_time":"13:00"},{"id":28,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"13:30\uff5e14:00","sum_fee":150000,"end_time":"14:00","start_time":"13:30"},{"id":29,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"14:00\uff5e14:30","sum_fee":150000,"end_time":"14:30","start_time":"14:00"},{"id":30,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"14:30\uff5e15:00","sum_fee":150000,"end_time":"15:00","start_time":"14:30"},{"id":31,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"15:00\uff5e15:30","sum_fee":150000,"end_time":"15:30","start_time":"15:00"},{"id":32,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"15:30\uff5e16:00","sum_fee":150000,"end_time":"16:00","start_time":"15:30"},{"id":33,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"16:00\uff5e16:30","sum_fee":150000,"end_time":"16:30","start_time":"16:00"},{"id":34,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"16:30\uff5e17:00","sum_fee":150000,"end_time":"17:00","start_time":"16:30"},{"id":35,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"17:00\uff5e17:30","sum_fee":150000,"end_time":"17:30","start_time":"17:00"},{"id":36,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"17:30\uff5e18:00","sum_fee":150000,"end_time":"18:00","start_time":"17:30"},{"id":37,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"18:00\uff5e18:30","sum_fee":150000,"end_time":"18:30","start_time":"18:00"},{"id":38,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"18:30\uff5e19:00","sum_fee":150000,"end_time":"19:00","start_time":"18:30"},{"id":39,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"19:00\uff5e19:30","sum_fee":150000,"end_time":"19:30","start_time":"19:00"},{"id":40,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"19:30\uff5e20:00","sum_fee":150000,"end_time":"20:00","start_time":"19:30"},{"id":41,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"20:00\uff5e20:30","sum_fee":150000,"end_time":"20:30","start_time":"20:00"},{"id":42,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"20:30\uff5e21:00","sum_fee":150000,"end_time":"21:00","start_time":"20:30"},{"id":43,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"21:00\uff5e21:30","sum_fee":150000,"end_time":"21:30","start_time":"21:00"},{"id":44,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"21:30\uff5e22:00","sum_fee":150000,"end_time":"22:00","start_time":"21:30"},{"id":45,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"22:00\uff5e22:30","sum_fee":150000,"end_time":"22:30","start_time":"22:00"},{"id":46,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"22:30\uff5e23:00","sum_fee":150000,"end_time":"23:00","start_time":"22:30"},{"id":47,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"23:00\uff5e23:30","sum_fee":150000,"end_time":"23:30","start_time":"23:00"},{"id":48,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"23:30\uff5e24:00","sum_fee":150000,"end_time":"24:00","start_time":"23:30"}]',
            "update_time" => "2024-05-06 11:09:27",
            "corp_id" => 100001
        ];
        $data['period_json_sum'] = json_decode($data['period_json_sum'], true);
        $data['period_json'] = json_decode($data['period_json'], true);

        $model = new TariffGroup();
        $mysql_data = $model->getTariffGroup($data['id']);
        $this->assertSame($data, $mysql_data);
    }

    public function testGetTariffGroupFailed()
    {
        if (config('app.env') !== "development") {
            $this->assertTrue(true);
            return;
        };
        $data = [
            "id" => 2000,
        ];
        $model = new TariffGroup();
        $mysql_data = $model->getTariffGroup($data['id']);
        $this->assertNull($mysql_data);
    }
}