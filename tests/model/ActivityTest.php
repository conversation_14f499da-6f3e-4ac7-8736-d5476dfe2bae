<?php
declare(strict_types=1);

namespace tests\model;

use app\common\model\Activity;
use PHPUnit\Framework\TestCase;

final class ActivityTest extends TestCase
{
    public function testGetStationDiscount()
    {
        if (config('app.env') !== "development") {
            $this->assertTrue(true);
            return;
        };
        if (time() > strtotime('2024-12-20 15:23:32')) {
            $expected_discount = 100;
        } else {
            $expected_discount = 80;
        }

        $discount = app(Activity::class)->getStationDiscount(1013, "2024-09-20 13:45:00");
        $this->assertSame($expected_discount, $discount);
    }
}