<?php
declare(strict_types=1);

namespace tests\context;

use app\common\model\Activity;
use PHPUnit\Framework\TestCase;
use Predis\Client as Predis;
use app\common\context\Order as OrderContext;

final class OrderTest extends TestCase
{
    public function testRecordOrderStatus()
    {
        $params = [
            'order_id' => '10000000000063012409211700070005',
            'status' => '创建订单'
        ];

        $context = new OrderContext();
        $result = $context->recordOrderStatus($params['order_id'], $params['status']);
        $this->assertSame(true, $result);

        $context_status = $context->getOrderStatus($params['order_id']);
        $this->assertSame($params['status'], $context_status);

        $delResult = $context->deleteOrderStatus($params['order_id']);
        $this->assertSame(true, $delResult);

        $context_status = $context->getOrderStatus($params['order_id']);
        $this->assertSame(null, $context_status);
    }
}