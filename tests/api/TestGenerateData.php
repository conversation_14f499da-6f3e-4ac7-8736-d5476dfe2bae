<?php

namespace tests\api;

use app\common\lib\ExceptionLogCollector;
use app\common\model\Order;
use app\common\model\Order as OrderModel;
use app\common\model\Users;
use PHPUnit\Framework\TestCase;

class TestGenerateData extends TestCase
{
    protected int $corp_id = 100001;
    protected int $station_id = 1013;

    protected array $piles_list = [
        10000000000013,
        10000000000032,
        10000000000038,
        10000000000046,
        10000000000048,
        10000000000055,
        10000000000059,
        10000000000063,
        10000000000065,
        10000000000066,
        10000000000069,
        10000000000073,
        10000000000075,
        10000000000078,
        10000000000079,
        10000000000081,
        10000000000082,
        10000000000085,
        10000000000089,
        10000000000091,
        10000000000092,
        10000000000097,
        10000000000099,
        10000000000100,
        10000000000105,
        10000000000106,
        10000000000107,
        10000000000108,
        10000000000109,
        10000000000111,
        10000000000112,
    ];

    public function testS()
    {
        $list = [
            10000000000019,
            10000000000023,
            10000000000028,
            10000000000029,
            10000000000030,
            10000000000033,
            10000000000034,
            10000000000035,
            10000000000037,
            10000000000039,
            10000000000040,
            10000000000041,
            10000000000042,
            10000000000045,
            10000000000056,
            10000000000057,
            10000000000062,
            10000000000064,
            10000000000071,
            10000000000074,
            10000000000076,
            10000000000077,
            10000000000080,
            10000000000083,
            10000000000093,
            10000000000094,
            10000000000095,
            10000000000101,
            10000000000102,
            10000000000103,
        ];
        print_r(array_diff(['10000000000103', 10000000020103], $list));
    }

    /**
     * @test
     */
    public function test()
    {
        $total_user_ids = $this->queryUserIds();
        $orderModel = app(Order::class);

        for ($i = 59; $i >= 0; $i--) {
            $piles_list = $this->piles_list;
            $date = date('Y-m-d 00:00:00', strtotime(sprintf('-%d day', $i)));
            $order_data = $this->queryOrderData($date);
            $current_order_count = $order_data['order_count'];
            $current_electricity_total = $order_data['electricity_total'];

            $order_count = mt_rand(5, 10);
            $total = $this->generateTotalElectricity();

            if ($current_electricity_total < $total) {
                $orders = $this->generateOrderElectricity($total - $current_electricity_total, $order_count - $current_order_count);
                echo "日期：" . $date . PHP_EOL;
                echo "实际订单数：" . $current_order_count . PHP_EOL;
                echo "实际电量：" . $current_electricity_total . PHP_EOL;
                echo "预期订单数：" . $order_count . PHP_EOL;
                echo "预期总电量：" . $total . PHP_EOL;
                echo "实际总电量：" . array_sum($orders) . PHP_EOL;
                echo "订单电量：" . PHP_EOL;


                $inserts = [];
                $user_ids = $total_user_ids;
                foreach ($orders as $order_electricity_total) {
                    [$user_ids, $user_id] = $this->pop_user_id($user_ids);


                    [$piles_list, $piles_id] = $this->pop_piles_id($piles_list);

                    $start_time = $this->generateStartTime($date);

                    $inserts[] = $this->generateOrderData($user_id, $order_electricity_total, $piles_id, strtotime($start_time));
                }
                $orderModel->insertAll($inserts);
            }
        }

        $this->assertTrue(true);
    }

    protected function pop_user_id(array $user_ids): array
    {
        $user_index = mt_rand(0, count($user_ids) - 1);
        $user_id = $user_ids[$user_index];
        unset($user_ids[$user_index]);
        $user_ids = array_values($user_ids);

        return [$user_ids, $user_id];
    }

    protected function get_order_no(string|int $shots_id): string|bool|int
    {
        $order_model = new OrderModel();
        $max_attempts = 10;
        $attempt = 0;
        while ($attempt < $max_attempts) {
            $order_no = get_order_id_sn($shots_id);
            if (strlen($order_no) > 32) continue;
            try {
                $exist_order_no = $order_model->field('id')->find($order_no);
            } catch (\Throwable $e) {
                ExceptionLogCollector::collect($e);
                return false;
            }
            if (!$exist_order_no) {
                break;
            }
            $attempt++;
        }
        if (empty($order_no)) return false;
        return $order_no;
    }

    protected function generateStartTime(string $date): string
    {
        $h = mt_rand(1, 19);
        $h = str_pad($h, 2, '0', STR_PAD_LEFT);
        $m = mt_rand(0, 59);
        $m = str_pad($m, 2, '0', STR_PAD_LEFT);
        $s = mt_rand(0, 59);
        $s = str_pad($s, 2, '0', STR_PAD_LEFT);
        return date(sprintf("Y-m-d %s:%s:%s", $h, $m, $s), strtotime($date));
    }

    protected function pop_piles_id(array $piles_list): array
    {
        $piles_index = mt_rand(0, count($piles_list) - 1);
        $piles_id = $piles_list[$piles_index];
        unset($piles_list[$piles_index]);
        $piles_list = array_values($piles_list);

        return [$piles_list, $piles_id];
    }

    protected function queryUserIds(): array
    {
        $model = app(Users::class);
        return $model->where('phone', 'in', [
            19924606906,
            15119859954,
            13686857450,
            13682662475,
            18923840303,
            18038015257,
            13332827119,
            13413847058,
            15914040778,
            15549412969,
            13602596812,
            18665308140,
            15118180687,
            13751099415,
            18138232239,
            13823230735,
            17811772808,
            15184446063,
            19937397250,
            15019477517,
        ])
            ->column('id');
    }

//    protected function

    protected function generateOrderData(int $user_id, int $electricity_total, int $piles_id, int $start_time): array
    {
        $sequence = mt_rand(1, 2);
        $charging_money = (int)(($electricity_total / 10000) * 0.9 * 100);
        $service_money = (int)(($electricity_total / 10000) * 0.6 * 100);
        $total_money = $charging_money + $service_money;
        $charge_duration = (int)($electricity_total / 10000 / 7 * 3600);


        $create_time = date('Y-m-d H:i:s', $start_time - mt_rand(5, 10));

        $order_id = $this->get_order_no($piles_id * 100 + $sequence);

        return [
            'id' => $order_id,
            'user_id' => $user_id,
            'corp_id' => $this->corp_id,
            'station_id' => $this->station_id,
            'piles_id' => $piles_id,
            'sequence' => $sequence,
            'shot_id' => $piles_id * 100 + $sequence,
            'money' => $total_money,
            'pay_money' => $total_money,
            'coupon_money' => 0,
            'charge_duration' => $charge_duration,
            'consumption_amount' => $total_money * 100,
            'electricity_price' => $charging_money * 100,
            'ser_price' => $service_money * 100,
            'amount_charged' => $total_money,
            'electricity_charged' => $electricity_total,
            'settled_amount' => $total_money,
            'create_time' => $create_time,
            'update_time' => $create_time,
            'trans_start_time' => date('Y-m-d H:i:s', $start_time),
            'trans_end_time' => date('Y-m-d H:i:s', $start_time + $charge_duration),
            'status' => 5,
            'reason_for_stop' => 64,
            'reason_for_stop_text' => '充电完成，APP远程停止',
            'msg' => '充电完成，APP远程停止',
            'sharp_electricity' => 0,
            'sharp_loss' => 0,
            'sharp_amount' => 0,
            'peak_electricity' => 0,
            'peak_loss' => 0,
            'peak_amount' => 0,
            'flat_electricity' => 0,
            'flat_loss' => 0,
            'flat_amount' => 0,
            'valley_electricity' => 0,
            'valley_loss' => 0,
            'valley_amount' => 0,
            'electricity_total' => $electricity_total,
            'electricity_loss' => 0,
            'sharp_price' => 150000,
            'sharp_fee' => 90000,
            'sharp_ser_fee' => 60000,
            'peak_price' => 150000,
            'peak_fee' => 90000,
            'peak_ser_fee' => 60000,
            'flat_price' => 150000,
            'flat_fee' => 90000,
            'flat_ser_fee' => 60000,
            'valley_price' => 150000,
            'valley_fee' => 90000,
            'valley_ser_fee' => 60000,
            'currency' => 1,
            'type' => 1,
            'pay_mode' => 3,
            'desc' => '',
            'build_type' => 1,
            'meter_start_value' => 0,
            'meter_end_value' => 0,
            'vin_code' => '0',
            'ip' => '**************',
            'loss_rate' => 0,
            'period_codes' => '030303030303030303030303030302020202020200000000000000000000000000000000000101010101010101010101',
            'freeze_money' => 0,
            'freeze_status' => 1,
            'clearing_electricity_price' => 0,
            'clearing_ser_price' => 0,
            'clearing_status' => 1,
            'abnormal_alarm_node' => 0,
            'discount' => 100,
            'billing_mode_id' => 1008,
            'is_' => 1
        ];
    }

    protected function queryOrderData(string $date): array
    {
        $model = new Order();
        $data = $model->where('station_id', '=', $this->station_id)
            ->where('trans_start_time', '>=', $date)
            ->where('trans_end_time', '<=', date("Y-m-d H:i:s", strtotime($date) + 24 * 3600))
            ->field(['count(*) as order_count', 'sum(electricity_total) as electricity_total'])
            ->find();
        if (empty($data)) {
            return [
                'order_count' => 0,
                'electricity_total' => 0
            ];
        } else {
            $result = $data->toArray();
            if (empty($result['electricity_total'])) {
                $result['electricity_total'] = 0;
            }
            return $result;
        }
    }

    protected function generateTotalElectricity(): int
    {
        return (int)(180 * 10000 * (mt_rand(90, 110) / 100));
    }

    protected function generateOrderElectricity(int $total_electricity, int $order_count): array
    {
        $result = [];
        for ($i = 0; $i < $order_count; $i++) {
            $result[] = (int)($total_electricity / $order_count * (mt_rand(90, 110) / 100));
        }
        return $result;
    }

}