<?php

namespace tests\api\admin;

use ReflectionClass;
use Respect\Validation\Exceptions\ValidationException;
use tests\api\BaseApi;
use app\common\logic\admin\DebugToolRestartOrder;

class DebugToolRestartOrderTest extends BaseApi
{
    protected array $params = [
        'transaction_serial_number' => '10000000000063012407261546220001',
        'power_limit' => 7000
    ];

    protected DebugToolRestartOrder $debugToolRestartOrder;
    protected ReflectionClass $debugToolRestartOrderReflection;

    public function __construct(?string $name = null, array $data = [], $dataName = '')
    {
        parent::__construct($name, $data, $dataName);
        // 实例化对象
        $this->debugToolRestartOrder = new DebugToolRestartOrder();

        // 通过反射API获取对象protocol方法的权限
        $this->debugToolRestartOrderReflection = new ReflectionClass($this->debugToolRestartOrder);
    }

    public function testVerifyParamsIntTypeForTransactionSerialNumber()
    {

        $method = $this->debugToolRestartOrderReflection->getMethod('verifyParams');
        $method->setAccessible(true);

        $this->expectException(ValidationException::class);
        $this->expectExceptionCode(0);
        $this->expectExceptionMessage("订单ID 必须是string类型");

        // 执行方法
        $this->params['transaction_serial_number'] = 1;
        $method->invokeArgs($this->debugToolRestartOrder, [$this->params]);
    }

    public function testVerifyParamsStringTypeForPowerLimit()
    {
        $method = $this->debugToolRestartOrderReflection->getMethod('verifyParams');
        $method->setAccessible(true);

        $this->expectException(ValidationException::class);
        $this->expectExceptionCode(0);
        $this->expectExceptionMessage("限制功率 必须是integer类型");

        // 执行方法
        $this->params['power_limit'] = "7000";
        $method->invokeArgs($this->debugToolRestartOrder, [$this->params]);
    }

    public function testVerifyParamsSuccess()
    {
        // 通过反射API获取对象protocol方法的权限
        $method = $this->debugToolRestartOrderReflection->getMethod('verifyParams');
        $method->setAccessible(true);

        // 执行方法
        $result = $method->invokeArgs($this->debugToolRestartOrder, [$this->params]);

        $this->assertSame($this->params['transaction_serial_number'], $result['transaction_serial_number']);
        $this->assertSame($this->params['power_limit'], $result['power_limit']);
    }


}