<?php

namespace tests\api\admin;

use app\admin\controller\Login;
use tests\api\BaseApi;

class RestartOrderTest extends BaseApi
{
    protected array $request = [
        'transaction_serial_number' => 10000000000063012407261546220001,
        'power_limit' => 7000
    ];

    public function test_login_test_success()
    {
        $params = [
            'id' => 1,
            'secret_key' => env('DEBUG_API.SECRET_KEY')
        ];

        $app = $this->init_post_request($params);

        $response = (new Login($app))->login_test();

        $body = $response->getData();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($body);
        $this->assertSame(200, $body['code']);
        $this->assertSame("登录成功", $body['msg']);
    }

    public function test_login_test_failed()
    {
        $params = [
            'id' => 1,
            'secret_key' => ""
        ];

        $app = $this->init_post_request($params);

        $response = (new Login($app))->login_test();

        $body = $response->getData();
        $this->assertSame(404, $response->getCode());
        $this->assertIsArray($body);
        $this->assertSame([], $body);
    }


}