<?php

namespace tests\applet;

use app\common\context\Order as OrderContext;
use app\common\lib\charging\applet\request\StartChargeRequest;
use app\common\lib\charging\applet\StartCharge;
use app\common\lib\exception\RuntimeException;
use app\common\lib\transfer_service\SerialNumber;
use app\common\model\Activity;
use app\common\model\Order as OrderModel;
use app\common\model\PilesStatus;
use app\common\model\Shots as ShotsModel;
use app\common\model\ShotsStatus;
use app\common\model\TariffGroup as TariffGroupModel;
use app\common\model\Users as UsersModel;
use app\common\queue\TransferServiceWriteQueue;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\BasePackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\StartOrderAndSetTariff;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use ReflectionException;

class StartChargingTest extends TestCase
{
    /**
     * @throws ReflectionException
     */
    public function testVerifyShotsHasRunOrderFailed()
    {
        // 实例化对象
        $StartChargeRequest = new StartChargeRequest([]);
        $add = new StartCharge($StartChargeRequest);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($add);
        $method = $reflectionClass->getMethod('verifyShotsHasRunOrder');
        $method->setAccessible(true);

        // 创建存根来替换真实的数据库查询
        $orderModel = $this->createStub(OrderModel::class);
        $orderModel->method('getChargingCount')->willReturn(1);

        // 将存根绑定Order模型
        bind(OrderModel::class, $orderModel);

        // 断言会抛出以下异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("该枪正在充电中");

        // 执行方法
        $method->invokeArgs($add, [1000000000004601]);
    }

    /**
     * @throws ReflectionException
     */
    public function testVerifyShotsHasRunOrderSuccess()
    {
        // 实例化对象
        $StartChargeRequest = new StartChargeRequest([]);
        $add = new StartCharge($StartChargeRequest);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($add);
        $method = $reflectionClass->getMethod('verifyShotsHasRunOrder');
        $method->setAccessible(true);

        // 创建存根来替换真实的数据库查询
        $orderModel = $this->createStub(OrderModel::class);
        $orderModel->method('getChargingCount')->willReturn(0);

        // 将存根绑定Order模型
        bind(OrderModel::class, $orderModel);

        // 执行方法
        $result = $method->invokeArgs($add, [1000000000004601]);

        // 断言返回值为null
        $this->assertNull($result);
    }

    /**
     * @throws ReflectionException
     */
    public function testVerifyUserBalanceFailed()
    {
        // 实例化对象
        $StartChargeRequest = new StartChargeRequest([]);
        $add = new StartCharge($StartChargeRequest);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($add);
        $method = $reflectionClass->getMethod('verifyUserBalance');
        $method->setAccessible(true);

        // 创建存根来替换真实的数据库查询
        $userModel = $this->createStub(UsersModel::class);
        $userModel->method('getUserAvailableBalance')->willReturn(0);

        // 将存根绑定Users模型
        bind(UsersModel::class, $userModel);

        // 断言会抛出以下异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("余额不足");

        // 执行方法
        $method->invokeArgs($add, [1000000001]);
    }


    /**
     * @throws ReflectionException
     */
    public function testVerifyUserBalanceSuccess()
    {
        $testData = [
            'user_id' => 1000000001,
            'user_balance' => 531
        ];

        // 实例化对象
        $StartChargeRequest = new StartChargeRequest([]);
        $add = new StartCharge($StartChargeRequest);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($add);
        $method = $reflectionClass->getMethod('verifyUserBalance');
        $method->setAccessible(true);


        // 创建存根来替换真实的数据库查询
        $userModel = $this->createStub(UsersModel::class);
        $userModel->method('getUserAvailableBalance')->willReturn($testData['user_balance']);

        // 将存根绑定Users模型
        bind(UsersModel::class, $userModel);


        // 执行方法
        $method->invokeArgs($add, [$testData['user_id']]);

        $userBalanceProperty = $reflectionClass->getProperty('user_balance');
        $this->assertSame($testData['user_balance'], $userBalanceProperty->getValue($add));
    }


    /**
     * @throws ReflectionException
     */
    public function testLoadShotsDataForGetShotsDataSuccess()
    {
        if (config('app.env') === 'development') {
            $testData = [
                'shots_id' => 1000000000001301,
                'shots_data' => [
                    "id" => 1000000000001301,
                    "name" => "A14桩1号枪",
                    "sequence" => 1,
                    "is_online" => 1,
                    "is_fault" => 1,
                    "work_status" => 1,
                    "station_id" => 1013,
                    "corp_id" => 100001,
                    "piles_id" => 10000000000013,
                    "tariff_group_id" => 1008,
                    "line" => null,
                    "centralized_controller_id" => null,
                    "link_status" => 1,
                    "corp_name" => "深圳新能智慧充电科技有限公司",
                    "station_name" => "前沿商务空间",
                    "piles_name" => "A14",
                    "period_json_sum" => "[{\"fee\": {\"fee\": 10000, \"ser_fee\": 70000}, \"rate\": \"03\", \"period\": \"00:00～07:00\", \"sum_fee\": 80000, \"end_time\": \"07:00\", \"start_time\": \"00:00\"}, {\"fee\": {\"fee\": 10000, \"ser_fee\": 70000}, \"rate\": \"02\", \"period\": \"07:00～10:00\", \"sum_fee\": 80000, \"end_time\": \"10:00\", \"start_time\": \"07:00\"}, {\"fee\": {\"fee\": 10000, \"ser_fee\": 70000}, \"rate\": \"00\", \"period\": \"10:00～18:30\", \"sum_fee\": 80000, \"end_time\": \"18:30\", \"start_time\": \"10:00\"}, {\"fee\": {\"fee\": 10000, \"ser_fee\": 70000}, \"rate\": \"01\", \"period\": \"18:30～24:00\", \"sum_fee\": 80000, \"end_time\": \"24:00\", \"start_time\": \"18:30\"}]",
                    "status" => 0,
                    "piles_is_online" => 2,
                    "is_support_reservation" => 1
                ]
            ];

            // 实例化对象
            $StartChargeRequest = new StartChargeRequest([]);
            $startCharging = new StartCharge($StartChargeRequest);

            // 通过反射API获取对象protocol方法的权限
            $reflectionClass = new ReflectionClass($startCharging);
            $method = $reflectionClass->getMethod('loadShotsData');
            $method->setAccessible(true);

            // 执行方法
            $method->invokeArgs($startCharging, [$testData['shots_id']]);

            $shotsDataProperty = $reflectionClass->getProperty('shotsData');
            $this->assertSame($testData['shots_data'], $shotsDataProperty->getValue($startCharging));
        } else {
            $this->assertTrue(true);
        }
    }

    /**
     * @throws ReflectionException
     */
    public function testLoadShotsDataSuccess()
    {
        $testData = [
            'shots_id' => 1000000000001301,
            'shots_data' => [
                "id" => 1000000000001301,
                "name" => "A14桩1号枪",
                "sequence" => 1,
                "is_online" => 1,
                "is_fault" => 1,
                "work_status" => 1,
                "station_id" => 1013,
                "corp_id" => 100001,
                "piles_id" => 10000000000013,
                "tariff_group_id" => 1008,
                "line" => null,
                "centralized_controller_id" => null,
                "link_status" => 1,
                "corp_name" => "深圳新能智慧充电科技有限公司",
                "station_name" => "前沿商务空间",
                "piles_name" => "A14",
                "period_json_sum" => "[{\"fee\": {\"fee\": 10000, \"ser_fee\": 70000}, \"rate\": \"03\", \"period\": \"00:00～07:00\", \"sum_fee\": 80000, \"end_time\": \"07:00\", \"start_time\": \"00:00\"}, {\"fee\": {\"fee\": 10000, \"ser_fee\": 70000}, \"rate\": \"02\", \"period\": \"07:00～10:00\", \"sum_fee\": 80000, \"end_time\": \"10:00\", \"start_time\": \"07:00\"}, {\"fee\": {\"fee\": 10000, \"ser_fee\": 70000}, \"rate\": \"00\", \"period\": \"10:00～18:30\", \"sum_fee\": 80000, \"end_time\": \"18:30\", \"start_time\": \"10:00\"}, {\"fee\": {\"fee\": 10000, \"ser_fee\": 70000}, \"rate\": \"01\", \"period\": \"18:30～24:00\", \"sum_fee\": 80000, \"end_time\": \"24:00\", \"start_time\": \"18:30\"}]",
                "status" => 0,
                "piles_is_online" => 2,
                "is_support_reservation" => 1
            ]
        ];

        // 实例化对象
        $StartChargeRequest = new StartChargeRequest([]);
        $StartCharge = new StartCharge($StartChargeRequest);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($StartCharge);
        $method = $reflectionClass->getMethod('loadShotsData');
        $method->setAccessible(true);


        // 创建存根来替换真实的数据库查询
        $shotsModel = $this->createStub(ShotsModel::class);
        $shotsModel->method('getShotsData')->willReturn($testData['shots_data']);

        // 将存根绑定Users模型
        bind(ShotsModel::class, $shotsModel);

        // 执行方法
        $method->invokeArgs($StartCharge, [$testData['shots_id']]);

        $shotsDataProperty = $reflectionClass->getProperty('shotsData');
        $this->assertSame($testData['shots_data'], $shotsDataProperty->getValue($StartCharge));
    }

    /**
     * @throws ReflectionException
     */
    public function testLoadShotsDataFailed()
    {
        $testData = [
            'shots_id' => 1000000000001301,
            'shots_data' => null
        ];

        // 实例化对象
        $StartChargeRequest = new StartChargeRequest([]);
        $StartCharge = new StartCharge($StartChargeRequest);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($StartCharge);
        $method = $reflectionClass->getMethod('loadShotsData');
        $method->setAccessible(true);


        // 创建存根来替换真实的数据库查询
        $shotsModel = $this->createStub(ShotsModel::class);
        $shotsModel->method('getShotsData')->willReturn($testData['shots_data']);

        // 将存根绑定Users模型
        bind(ShotsModel::class, $shotsModel);

        // 断言会抛出以下异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("找不到充电枪");

        // 执行方法
        $method->invokeArgs($StartCharge, [$testData['shots_id']]);
    }

    // 测试充电桩离线

    /**
     * @throws ReflectionException
     */
    public function testVerifyShotsOnlineStatusPilesOffline()
    {
        $testData = [
            'shots_id' => 1000000000001301,
            'shotsDataPilesOffline' => [
                'piles_is_online' => PilesStatus::IsOnlineNot,
                'is_fault' => ShotsStatus::IsFaultNot,
                'work_status' => ShotsStatus::WorkStatusIdle
            ],
            'shotsDataShotsFault' => [
                'piles_is_online' => PilesStatus::IsOnlineYes,
                'is_fault' => ShotsStatus::IsFaultYes,
                'work_status' => ShotsStatus::WorkStatusIdle
            ],
            'shotsDataCharging' => [
                'piles_is_online' => PilesStatus::IsOnlineYes,
                'is_fault' => ShotsStatus::IsFaultNot,
                'work_status' => ShotsStatus::WorkStatusCharging
            ]
        ];

        // 实例化对象
        $StartChargeRequest = new StartChargeRequest([]);
        $StartCharge = new StartCharge($StartChargeRequest);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($StartCharge);
        $method = $reflectionClass->getMethod('verifyShotsOnlineStatus');
        $method->setAccessible(true);

        // 设置 shotsData 属性
        $shotsDataProperty = $reflectionClass->getProperty('shotsData');
        $shotsDataProperty->setValue($StartCharge, $testData['shotsDataPilesOffline']);

        // 断言会抛出以下异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("充电桩不在线");

        // 执行方法
        $method->invokeArgs($StartCharge, [$testData['shots_id']]);

        // todo: 下边的方法会在上边这个方法抛出异常后，不在执行下去。


        // 设置 shotsData 属性
        $shotsDataProperty = $reflectionClass->getProperty('shotsData');
        $shotsDataProperty->setValue($StartCharge, $testData['shotsDataShotsFault']);


        // 断言会抛出以下异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("充电枪故障");

        // 执行方法
        $method->invokeArgs($StartCharge, [$testData['shots_id']]);


        // 设置 shotsData 属性
        $shotsDataProperty = $reflectionClass->getProperty('shotsData');
        $shotsDataProperty->setValue($StartCharge, $testData['shotsDataCharging']);

        // 断言会抛出以下异常
//        $this->expectException(RuntimeException::class);
//        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
//        $this->expectExceptionMessage("充电枪充电中");

        // 执行方法
        $method->invokeArgs($StartCharge, [$testData['shots_id']]);
    }


    /**
     * @throws ReflectionException
     */
    public function testLoadTariffGroupSuccess()
    {
        $params = [
            'tariff_group_id' => 1000,
            'tariff_group_data' => [
                "id" => 1000,
                "name" => "默认",
                "sharp_fee" => 130000,
                "peak_fee" => 130000,
                "flat_fee" => 130000,
                "valley_fee" => 130000,
                "sharp_ser_fee" => 20000,
                "peak_ser_fee" => 20000,
                "flat_ser_fee" => 20000,
                "valley_ser_fee" => 20000,
                "loss_rate" => 0,
                "period_codes" => "030303030303030303030303030302020202020200000000000000000000000000000000000101010101010101010101",
                "opt_id" => 1,
                "create_time" => "2023-07-11 17:31:08",
                "period_json_sum" => '[{"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"00:00\uff5e07:00","sum_fee":150000,"end_time":"07:00","start_time":"00:00"},{"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"07:00\uff5e10:00","sum_fee":150000,"end_time":"10:00","start_time":"07:00"},{"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"10:00\uff5e18:30","sum_fee":150000,"end_time":"18:30","start_time":"10:00"},{"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"18:30\uff5e24:00","sum_fee":150000,"end_time":"24:00","start_time":"18:30"}]',
                "period_json" => '[{"id":1,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"00:00\uff5e00:30","sum_fee":150000,"end_time":"00:30","start_time":"00:00"},{"id":2,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"00:30\uff5e01:00","sum_fee":150000,"end_time":"01:00","start_time":"00:30"},{"id":3,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"01:00\uff5e01:30","sum_fee":150000,"end_time":"01:30","start_time":"01:00"},{"id":4,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"01:30\uff5e02:00","sum_fee":150000,"end_time":"02:00","start_time":"01:30"},{"id":5,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"02:00\uff5e02:30","sum_fee":150000,"end_time":"02:30","start_time":"02:00"},{"id":6,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"02:30\uff5e03:00","sum_fee":150000,"end_time":"03:00","start_time":"02:30"},{"id":7,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"03:00\uff5e03:30","sum_fee":150000,"end_time":"03:30","start_time":"03:00"},{"id":8,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"03:30\uff5e04:00","sum_fee":150000,"end_time":"04:00","start_time":"03:30"},{"id":9,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"04:00\uff5e04:30","sum_fee":150000,"end_time":"04:30","start_time":"04:00"},{"id":10,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"04:30\uff5e05:00","sum_fee":150000,"end_time":"05:00","start_time":"04:30"},{"id":11,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"05:00\uff5e05:30","sum_fee":150000,"end_time":"05:30","start_time":"05:00"},{"id":12,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"05:30\uff5e06:00","sum_fee":150000,"end_time":"06:00","start_time":"05:30"},{"id":13,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"06:00\uff5e06:30","sum_fee":150000,"end_time":"06:30","start_time":"06:00"},{"id":14,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"06:30\uff5e07:00","sum_fee":150000,"end_time":"07:00","start_time":"06:30"},{"id":15,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"07:00\uff5e07:30","sum_fee":150000,"end_time":"07:30","start_time":"07:00"},{"id":16,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"07:30\uff5e08:00","sum_fee":150000,"end_time":"08:00","start_time":"07:30"},{"id":17,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"08:00\uff5e08:30","sum_fee":150000,"end_time":"08:30","start_time":"08:00"},{"id":18,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"08:30\uff5e09:00","sum_fee":150000,"end_time":"09:00","start_time":"08:30"},{"id":19,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"09:00\uff5e09:30","sum_fee":150000,"end_time":"09:30","start_time":"09:00"},{"id":20,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"09:30\uff5e10:00","sum_fee":150000,"end_time":"10:00","start_time":"09:30"},{"id":21,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"10:00\uff5e10:30","sum_fee":150000,"end_time":"10:30","start_time":"10:00"},{"id":22,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"10:30\uff5e11:00","sum_fee":150000,"end_time":"11:00","start_time":"10:30"},{"id":23,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"11:00\uff5e11:30","sum_fee":150000,"end_time":"11:30","start_time":"11:00"},{"id":24,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"11:30\uff5e12:00","sum_fee":150000,"end_time":"12:00","start_time":"11:30"},{"id":25,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"12:00\uff5e12:30","sum_fee":150000,"end_time":"12:30","start_time":"12:00"},{"id":26,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"12:30\uff5e13:00","sum_fee":150000,"end_time":"13:00","start_time":"12:30"},{"id":27,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"13:00\uff5e13:30","sum_fee":150000,"end_time":"13:30","start_time":"13:00"},{"id":28,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"13:30\uff5e14:00","sum_fee":150000,"end_time":"14:00","start_time":"13:30"},{"id":29,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"14:00\uff5e14:30","sum_fee":150000,"end_time":"14:30","start_time":"14:00"},{"id":30,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"14:30\uff5e15:00","sum_fee":150000,"end_time":"15:00","start_time":"14:30"},{"id":31,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"15:00\uff5e15:30","sum_fee":150000,"end_time":"15:30","start_time":"15:00"},{"id":32,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"15:30\uff5e16:00","sum_fee":150000,"end_time":"16:00","start_time":"15:30"},{"id":33,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"16:00\uff5e16:30","sum_fee":150000,"end_time":"16:30","start_time":"16:00"},{"id":34,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"16:30\uff5e17:00","sum_fee":150000,"end_time":"17:00","start_time":"16:30"},{"id":35,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"17:00\uff5e17:30","sum_fee":150000,"end_time":"17:30","start_time":"17:00"},{"id":36,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"17:30\uff5e18:00","sum_fee":150000,"end_time":"18:00","start_time":"17:30"},{"id":37,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"18:00\uff5e18:30","sum_fee":150000,"end_time":"18:30","start_time":"18:00"},{"id":38,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"18:30\uff5e19:00","sum_fee":150000,"end_time":"19:00","start_time":"18:30"},{"id":39,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"19:00\uff5e19:30","sum_fee":150000,"end_time":"19:30","start_time":"19:00"},{"id":40,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"19:30\uff5e20:00","sum_fee":150000,"end_time":"20:00","start_time":"19:30"},{"id":41,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"20:00\uff5e20:30","sum_fee":150000,"end_time":"20:30","start_time":"20:00"},{"id":42,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"20:30\uff5e21:00","sum_fee":150000,"end_time":"21:00","start_time":"20:30"},{"id":43,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"21:00\uff5e21:30","sum_fee":150000,"end_time":"21:30","start_time":"21:00"},{"id":44,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"21:30\uff5e22:00","sum_fee":150000,"end_time":"22:00","start_time":"21:30"},{"id":45,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"22:00\uff5e22:30","sum_fee":150000,"end_time":"22:30","start_time":"22:00"},{"id":46,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"22:30\uff5e23:00","sum_fee":150000,"end_time":"23:00","start_time":"22:30"},{"id":47,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"23:00\uff5e23:30","sum_fee":150000,"end_time":"23:30","start_time":"23:00"},{"id":48,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"23:30\uff5e24:00","sum_fee":150000,"end_time":"24:00","start_time":"23:30"}]',
                "update_time" => "2024-05-06 11:09:27",
                "corp_id" => 100001
            ]
        ];
        $params['tariff_group_data']['period_json_sum'] = json_decode($params['tariff_group_data']['period_json_sum'], true);
        $params['tariff_group_data']['period_json'] = json_decode($params['tariff_group_data']['period_json'], true);

        // 实例化对象
        $StartChargeRequest = new StartChargeRequest([]);
        $StartCharge = new StartCharge($StartChargeRequest);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($StartCharge);
        $method = $reflectionClass->getMethod('loadTariffGroup');
        $method->setAccessible(true);


        // 创建存根来替换真实的数据库查询
        $tariffGroupModel = $this->createStub(TariffGroupModel::class);
        $tariffGroupModel->method('getTariffGroup')->willReturn($params['tariff_group_data']);

        // 执行方法
        $method->invokeArgs($StartCharge, [$params['tariff_group_id']]);

        // 获取属性对象
        $property = $reflectionClass->getProperty('tariffGroupData');
        $tariffGroupData = $property->getValue($StartCharge);

        $this->assertSame($params['tariff_group_data'], $tariffGroupData);
    }


    /**
     * @throws ReflectionException
     */
    public function testLoadTariffGroupFailed()
    {
        $params = [
            'tariff_group_id' => 2000,
            'tariff_group_data' => null
        ];

        // 实例化对象
        $StartChargeRequest = new StartChargeRequest([]);
        $StartCharge = new StartCharge($StartChargeRequest);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($StartCharge);
        $method = $reflectionClass->getMethod('loadTariffGroup');
        $method->setAccessible(true);


        // 创建存根来替换真实的数据库查询
        $tariffGroupModel = $this->createStub(TariffGroupModel::class);
        $tariffGroupModel->method('getTariffGroup')->willReturn($params['tariff_group_data']);

        // 断言会抛出以下异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeServiceException);
        $this->expectExceptionMessage("找不到费率模型");


        // 执行方法
        $method->invokeArgs($StartCharge, [$params['tariff_group_id']]);
    }

    public function testGetPackage()
    {
        $params = [
            'request' => [
                'user_token' => "",
                "user_id" => 1000000000012,
                "shots_id" => 1000000000006301,
                "power_limit" => 6000
            ],
            'shots_data' => [
                "id" => 1000000000006301,
                "name" => "A14桩1号枪",
                "sequence" => 1,
                "is_online" => 1,
                "is_fault" => 1,
                "work_status" => 1,
                "station_id" => 1013,
                "corp_id" => 100001,
                "piles_id" => 10000000000063,
                "tariff_group_id" => 1008,
                "line" => null,
                "centralized_controller_id" => null,
                "link_status" => 1,
                "corp_name" => "深圳新能智慧充电科技有限公司",
                "station_name" => "前沿商务空间",
                "piles_name" => "A14",
                "period_json_sum" => "[{\"fee\": {\"fee\": 10000, \"ser_fee\": 70000}, \"rate\": \"03\", \"period\": \"00:00～07:00\", \"sum_fee\": 80000, \"end_time\": \"07:00\", \"start_time\": \"00:00\"}, {\"fee\": {\"fee\": 10000, \"ser_fee\": 70000}, \"rate\": \"02\", \"period\": \"07:00～10:00\", \"sum_fee\": 80000, \"end_time\": \"10:00\", \"start_time\": \"07:00\"}, {\"fee\": {\"fee\": 10000, \"ser_fee\": 70000}, \"rate\": \"00\", \"period\": \"10:00～18:30\", \"sum_fee\": 80000, \"end_time\": \"18:30\", \"start_time\": \"10:00\"}, {\"fee\": {\"fee\": 10000, \"ser_fee\": 70000}, \"rate\": \"01\", \"period\": \"18:30～24:00\", \"sum_fee\": 80000, \"end_time\": \"24:00\", \"start_time\": \"18:30\"}]",
                "status" => 0,
                "piles_is_online" => 1,
                "is_support_reservation" => 1
            ],
            'tariff_group_data' => [
                "id" => 1008,
                "name" => "默认",
                "sharp_fee" => 130000,
                "peak_fee" => 130000,
                "flat_fee" => 130000,
                "valley_fee" => 130000,
                "sharp_ser_fee" => 20000,
                "peak_ser_fee" => 20000,
                "flat_ser_fee" => 20000,
                "valley_ser_fee" => 20000,
                "loss_rate" => 0,
                "period_codes" => "030303030303030303030303030302020202020200000000000000000000000000000000000101010101010101010101",
                "opt_id" => 1,
                "create_time" => "2023-07-11 17:31:08",
                "period_json_sum" => '[{"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"00:00\uff5e07:00","sum_fee":150000,"end_time":"07:00","start_time":"00:00"},{"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"07:00\uff5e10:00","sum_fee":150000,"end_time":"10:00","start_time":"07:00"},{"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"10:00\uff5e18:30","sum_fee":150000,"end_time":"18:30","start_time":"10:00"},{"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"18:30\uff5e24:00","sum_fee":150000,"end_time":"24:00","start_time":"18:30"}]',
                "period_json" => '[{"id":1,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"00:00\uff5e00:30","sum_fee":150000,"end_time":"00:30","start_time":"00:00"},{"id":2,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"00:30\uff5e01:00","sum_fee":150000,"end_time":"01:00","start_time":"00:30"},{"id":3,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"01:00\uff5e01:30","sum_fee":150000,"end_time":"01:30","start_time":"01:00"},{"id":4,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"01:30\uff5e02:00","sum_fee":150000,"end_time":"02:00","start_time":"01:30"},{"id":5,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"02:00\uff5e02:30","sum_fee":150000,"end_time":"02:30","start_time":"02:00"},{"id":6,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"02:30\uff5e03:00","sum_fee":150000,"end_time":"03:00","start_time":"02:30"},{"id":7,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"03:00\uff5e03:30","sum_fee":150000,"end_time":"03:30","start_time":"03:00"},{"id":8,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"03:30\uff5e04:00","sum_fee":150000,"end_time":"04:00","start_time":"03:30"},{"id":9,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"04:00\uff5e04:30","sum_fee":150000,"end_time":"04:30","start_time":"04:00"},{"id":10,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"04:30\uff5e05:00","sum_fee":150000,"end_time":"05:00","start_time":"04:30"},{"id":11,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"05:00\uff5e05:30","sum_fee":150000,"end_time":"05:30","start_time":"05:00"},{"id":12,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"05:30\uff5e06:00","sum_fee":150000,"end_time":"06:00","start_time":"05:30"},{"id":13,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"06:00\uff5e06:30","sum_fee":150000,"end_time":"06:30","start_time":"06:00"},{"id":14,"fee":{"fee":130000,"ser_fee":20000},"rate":"03","period":"06:30\uff5e07:00","sum_fee":150000,"end_time":"07:00","start_time":"06:30"},{"id":15,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"07:00\uff5e07:30","sum_fee":150000,"end_time":"07:30","start_time":"07:00"},{"id":16,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"07:30\uff5e08:00","sum_fee":150000,"end_time":"08:00","start_time":"07:30"},{"id":17,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"08:00\uff5e08:30","sum_fee":150000,"end_time":"08:30","start_time":"08:00"},{"id":18,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"08:30\uff5e09:00","sum_fee":150000,"end_time":"09:00","start_time":"08:30"},{"id":19,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"09:00\uff5e09:30","sum_fee":150000,"end_time":"09:30","start_time":"09:00"},{"id":20,"fee":{"fee":130000,"ser_fee":20000},"rate":"02","period":"09:30\uff5e10:00","sum_fee":150000,"end_time":"10:00","start_time":"09:30"},{"id":21,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"10:00\uff5e10:30","sum_fee":150000,"end_time":"10:30","start_time":"10:00"},{"id":22,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"10:30\uff5e11:00","sum_fee":150000,"end_time":"11:00","start_time":"10:30"},{"id":23,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"11:00\uff5e11:30","sum_fee":150000,"end_time":"11:30","start_time":"11:00"},{"id":24,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"11:30\uff5e12:00","sum_fee":150000,"end_time":"12:00","start_time":"11:30"},{"id":25,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"12:00\uff5e12:30","sum_fee":150000,"end_time":"12:30","start_time":"12:00"},{"id":26,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"12:30\uff5e13:00","sum_fee":150000,"end_time":"13:00","start_time":"12:30"},{"id":27,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"13:00\uff5e13:30","sum_fee":150000,"end_time":"13:30","start_time":"13:00"},{"id":28,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"13:30\uff5e14:00","sum_fee":150000,"end_time":"14:00","start_time":"13:30"},{"id":29,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"14:00\uff5e14:30","sum_fee":150000,"end_time":"14:30","start_time":"14:00"},{"id":30,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"14:30\uff5e15:00","sum_fee":150000,"end_time":"15:00","start_time":"14:30"},{"id":31,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"15:00\uff5e15:30","sum_fee":150000,"end_time":"15:30","start_time":"15:00"},{"id":32,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"15:30\uff5e16:00","sum_fee":150000,"end_time":"16:00","start_time":"15:30"},{"id":33,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"16:00\uff5e16:30","sum_fee":150000,"end_time":"16:30","start_time":"16:00"},{"id":34,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"16:30\uff5e17:00","sum_fee":150000,"end_time":"17:00","start_time":"16:30"},{"id":35,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"17:00\uff5e17:30","sum_fee":150000,"end_time":"17:30","start_time":"17:00"},{"id":36,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"17:30\uff5e18:00","sum_fee":150000,"end_time":"18:00","start_time":"17:30"},{"id":37,"fee":{"fee":130000,"ser_fee":20000},"rate":"00","period":"18:00\uff5e18:30","sum_fee":150000,"end_time":"18:30","start_time":"18:00"},{"id":38,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"18:30\uff5e19:00","sum_fee":150000,"end_time":"19:00","start_time":"18:30"},{"id":39,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"19:00\uff5e19:30","sum_fee":150000,"end_time":"19:30","start_time":"19:00"},{"id":40,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"19:30\uff5e20:00","sum_fee":150000,"end_time":"20:00","start_time":"19:30"},{"id":41,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"20:00\uff5e20:30","sum_fee":150000,"end_time":"20:30","start_time":"20:00"},{"id":42,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"20:30\uff5e21:00","sum_fee":150000,"end_time":"21:00","start_time":"20:30"},{"id":43,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"21:00\uff5e21:30","sum_fee":150000,"end_time":"21:30","start_time":"21:00"},{"id":44,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"21:30\uff5e22:00","sum_fee":150000,"end_time":"22:00","start_time":"21:30"},{"id":45,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"22:00\uff5e22:30","sum_fee":150000,"end_time":"22:30","start_time":"22:00"},{"id":46,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"22:30\uff5e23:00","sum_fee":150000,"end_time":"23:00","start_time":"22:30"},{"id":47,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"23:00\uff5e23:30","sum_fee":150000,"end_time":"23:30","start_time":"23:00"},{"id":48,"fee":{"fee":130000,"ser_fee":20000},"rate":"01","period":"23:30\uff5e24:00","sum_fee":150000,"end_time":"24:00","start_time":"23:30"}]',
                "update_time" => "2024-05-06 11:09:27",
                "corp_id" => 100001
            ]
        ];
        $_SERVER['REMOTE_ADDR'] = '127.0.0.1';

        // 创建存根来替换真实的数据库查询
        $shotsModel = $this->createStub(ShotsModel::class);
        $shotsModel->method('getShotsData')->willReturn($params['shots_data']);
        bind(ShotsModel::class, $shotsModel);

        $orderModel = $this->createStub(OrderModel::class);
        $orderModel->method('createOrder')->willReturn(1);
        bind(OrderModel::class, $orderModel);

        // 创建存根来替换真实的数据库查询
        $tariffGroupModel = $this->createStub(TariffGroupModel::class);
        $tariffGroupModel->method('getTariffGroup')->willReturn($params['tariff_group_data']);
        bind(TariffGroupModel::class, $tariffGroupModel);

        $activityModel = $this->createStub(Activity::class);
        $activityModel->method('getStationDiscount')->willReturn(100);
        bind(Activity::class, $activityModel);

        $transferServiceWriteQueue = $this->createStub(TransferServiceWriteQueue::class);
        $transferServiceWriteQueue->method('push')->willReturn(1);
        bind(TransferServiceWriteQueue::class, $transferServiceWriteQueue);


        $orderContext = $this->createStub(OrderContext::class);
        $orderContext->method('recordOrderStatus')->willReturn(true);
        bind(OrderContext::class, $orderContext);

        // 创建存根来替换真实的数据库查询
        $userModel = $this->createStub(UsersModel::class);
        $userModel->method('getUserAvailableBalance')->willReturn(100);
        bind(UsersModel::class, $userModel);


        // 实例化对象
        $StartChargeRequest = new StartChargeRequest($params['request']);
        $StartCharge = new StartCharge($StartChargeRequest);

        $response = $StartCharge->run();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($StartCharge);
        $method = $reflectionClass->getMethod('getPackage');
        $method->setAccessible(true);

        // 执行方法
        $return = $method->invokeArgs($StartCharge, [1001]);


        // 断言返回值
        $this->assertInstanceOf(BasePackage::class, $return);
        $this->assertSame(1001, $return->getSerialNumber());
        $this->assertSame(BasePackage::TypeStartOrderAndSetTariff, $return->getIndex());

        $body = $return->getBody();
        $this->assertInstanceOf(StartOrderAndSetTariff::class, $body);
        $this->assertSame($params['shots_data']['piles_id'], $body->getPilesId());
        $this->assertSame($params['shots_data']['sequence'], $body->getShotsNumber());
        $this->assertSame($response['transaction_serial_number'], $body->getTransactionSerialNumber());
        $this->assertSame($params['request']['user_id'], (int)($body->getLogicalCardNumber()));
        $this->assertSame($params['request']['user_id'], (int)($body->getPhysicalCardNumber()));
        $this->assertSame(0, $body->getTimedTime());
        $this->assertSame($params['tariff_group_data']['id'], $body->getBillingModeID());
        $this->assertSame($params['request']['power_limit'], $body->getPowerLimit());
    }
}
