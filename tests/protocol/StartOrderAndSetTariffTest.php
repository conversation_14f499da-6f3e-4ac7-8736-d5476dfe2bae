<?php

declare(strict_types=1);

namespace tests\protocol;

use PHPUnit\Framework\TestCase;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\BasePackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\Kernel;

class StartOrderAndSetTariffTest extends TestCase
{
    public function testEncode()
    {
        $params = [
            "serial_number" => 1100,
            "index" => BasePackage::TypeStartOrderAndSetTariff,
            "body" => [
                "type" => 1,
                "piles_id" => **************,
                "shots_number" => 1,
                "transaction_serial_number" => "**************022407261545510004",
                "logical_card_number" => "****************",
                "physical_card_number" => "****************",
                "account_balance" => 100,
                "account_vip" => 1,
                "timed_time" => 0,
                "billing_mode_id" => 1000,
                "sharp_fee" => 130000,
                "sharp_ser_fee" => 20000,
                "peak_fee" => 130000,
                "peak_ser_fee" => 20000,
                "flat_fee" => 130000,
                "flat_ser_fee" => 20000,
                "valley_fee" => 130000,
                "valley_ser_fee" => 20000,
                "loss_rate" => 0,
                "period_codes" => "030303030303030303030303030302020202020200000000000000000000000000000000000101010101010101010101",
                "power_limit" => null
            ]
        ];

        $package = Kernel::create($params['serial_number'], $params['index'], $params['body']);

        $this->assertSame(json_encode($params), $package->encode());
    }

}