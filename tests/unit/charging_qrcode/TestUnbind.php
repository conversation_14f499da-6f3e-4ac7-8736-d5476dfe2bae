<?php /** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace tests\unit\charging_qrcode;

use app\common\logic\admin\charging_qrcode\Unbind;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use think\facade\Db;
use think\Request;

class TestUnbind extends TestCase
{
    protected function getLogic(): Unbind
    {
        $request = new Request();
        return new Unbind($request);
    }

    /**
     * @test
     * @testdox 验证参数
     */
    public function verifyParams()
    {
        $params = [
            'charging_qrcode_id' => 10001,
            'shots_id' => 1000000000003201,
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyParams');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [$params]);

        $this->assertSame($params, $result);
    }

    /**
     * @test
     * @testdox 验证是否存在
     */
    public function verifyIfItExists()
    {
        $charging_qrcode_id = 1;
        $shots_id = 2;

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIfItExists');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [$charging_qrcode_id, $shots_id]);
        $run_sql = Db::getLastSql();

        $expected_sql = sprintf("SELECT COUNT(*) AS think_count FROM `charging_qrcode_to_shots` WHERE  `id` = %d  AND `shots_id` = '%d'", $charging_qrcode_id, $shots_id);

        $this->assertSame($expected_sql, $run_sql);
        $this->assertSame(false, $result);

//        $this->assertSame($params, $result);


    }


    /**
     * @test
     * @testbox 建立绑定
     */
    public function unbind()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('unbind');
        $method->setAccessible(true);

        $charging_qrcode_id = 1;
        $shots_id = 1100000000000101;

        Db::startTrans();
        $result = $method->invokeArgs($logic, [$charging_qrcode_id, $shots_id]);
        $run_sql = Db::getLastSql();
        Db::rollback();

        $expected_sql = sprintf("DELETE FROM `charging_qrcode_to_shots` WHERE  `id` = %d  AND `shots_id` = '%d'", $charging_qrcode_id, $shots_id);

        $this->assertSame($expected_sql, $run_sql);
        $this->assertSame(false, $result);
    }
}