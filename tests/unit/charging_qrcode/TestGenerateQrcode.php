<?php /** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace tests\unit\charging_qrcode;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\charging_qrcode\GenerateQrcode;
use app\common\model\ChargingQrcode;
use PHPUnit\Framework\TestCase;
use think\exception\ErrorException;
use think\facade\Db;
use think\file\UploadedFile;
use think\Request;

class TestGenerateQrcode extends TestCase
{
    protected function getLogic(): GenerateQrcode
    {
        $loginUser = new AdminLoginUser([]);
        $request = new Request();
        return new GenerateQrcode($loginUser, $request);
    }

    /**
     * @test
     * @testdox 验证权限
     */
    public function verifyPermission()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyPermission');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 0])]);

        $this->assertTrue($result);

        $this->expectExceptionMessage("只允许超级管理员生成二维码");
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);

        $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 1, 'corp_id' => 0])]);
    }


    /**
     * @test
     * @testdox 验证参数
     */
    public function verifyParams()
    {
        $params = [
            'generate_count' => 1000,
            'url_prefix' => "https://admin.resmartcharge.com/applet"
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyParams');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [$params]);

        $this->assertSame($params, $result);
    }

    /**
     * @test
     * @testdox 设置运行超时时长
     */
    public function setTimeLimit()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('setTimeLimit');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [1]);

        $this->assertTrue($result);
    }

    /**
     * @test
     * @testdox 验证Logo图片
     */
    public function verifyLogo()
    {
        $logo_path = public_path() . "\logo.png";
        $params = [
            'logo' => new UploadedFile($logo_path, 'logo.png', 'image/png')
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyLogo');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [$params['logo']]);

        $this->assertSame($logo_path, $result);
    }

    /**
     * @test
     * @testdox 初始化二维码数据
     */
    public function initQrcodeData()
    {
        $date = date('Y-m-d H:i:s');
        $params = [
            'generate_count' => 1000,
            'url_prefix' => "https://admin.resmartcharge.com/applet"
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('initQrcodeData');
        $method->setAccessible(true);

        // 执行
        $result = $method->invokeArgs($logic, [$params['generate_count'], $params['url_prefix']]);


        // 断言
        $this->assertSame($params['generate_count'], count($result));
        foreach ($result as $value) {
            $this->assertArrayHasKey('id', $value);
            $this->assertArrayHasKey('url', $value);
            $this->assertArrayHasKey('path', $value);
            $this->assertArrayHasKey('create_time', $value);
            $this->assertIsInt($value['id']);
            $this->assertIsString($value['url']);
            $this->assertIsString($value['path']);
            $this->assertIsString($date, $value['create_time']);
        }
    }

    /**
     * @test
     * @testdox 为字符串中的每个字符之间增加一个空格的间隙
     */
    public function addSpaceGap()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addSpaceGap');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, ["12345678"]);

        $this->assertSame("1 2 3 4 5 6 7 8", $result);
    }

    /**
     * @test
     * @testdox 生成二维码
     */
    public function generateQrcode()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('generateQrcode');
        $method->setAccessible(true);

        $params = [
            'id' => 10000000,
            'content' => 'https://admin.resmartcharge.com/applet?id=1',
            'logo_tmp_path' => public_path() . '\\logo.png',
        ];


        // 执行
        $result = $method->invokeArgs($logic, [$params['content'], $params['id'], $params['logo_tmp_path']]);

        // 断言
        $this->assertSame([
            0 => sprintf('charging_qrcode\charging_qrcode_%d.png', $params['id']),
            1 => sprintf('%scharging_qrcode\charging_qrcode_%d.png', public_path(), $params['id'])
        ], $result);
    }

    /**
     * @test
     * @testdox 批量生成二维码
     */
    public function batchGenerateQrcode()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('batchGenerateQrcode');
        $method->setAccessible(true);

        $params = [
            'initQrcodeData' => [
                [
                    'id' => 8001,
                    'url' => 'https://admin.resmartcharge.com/applet?id=8001',
                    'path' => '',
                    'create_time' => '2024-10-16 17:29:40',
                ],
                [
                    'id' => 8002,
                    'url' => 'https://admin.resmartcharge.com/applet?id=8002',
                    'path' => '',
                    'create_time' => '2024-10-16 17:29:40',
                ],
                [
                    'id' => 8003,
                    'url' => 'https://admin.resmartcharge.com/applet?id=8003',
                    'path' => '',
                    'create_time' => '2024-10-16 17:29:40',
                ]
            ],
            'logo_tmp_path' => public_path() . 'logo.png'
        ];

        // 执行
        $result = $method->invokeArgs($logic, [$params['initQrcodeData'], $params['logo_tmp_path']]);

        var_export($result);

        foreach ($result as $value) {
            $this->assertNotEmpty($value['path']);
            $this->assertTrue(is_file(public_path() . $value['path']));
        }
    }

    /**
     * @test
     * @testdox 将大数组拆分成N个小数组
     */
    public function chunkArray()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('chunkArray');
        $method->setAccessible(true);

        $params = [
            'bigQrcodeData' => [
                [], [], [], [], [], [], [], [], [], [],
                [], [], [], [], [], [], [], [], [], [],
                []
            ]
        ];

        // 执行
        $result = $method->invokeArgs($logic, [$params['bigQrcodeData'], 10]);

        $this->assertSame(3, count($result));
        $this->assertSame(10, count($result[0]));
        $this->assertSame(10, count($result[1]));
        $this->assertSame(1, count($result[2]));
    }

    /**
     * @test
     * @testdox 批量保存二维码数据
     */
    public function batchSaveQrcodeData()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('batchSaveQrcodeData');
        $method->setAccessible(true);

        $params = [
            'chunkQrcodeDataArray' => [
                [
                    'id' => 8001,
                    'url' => 'https://admin.resmartcharge.com/applet?id=8001',
                    'path' => 'charging_qrcode\\charging_qrcode_8001.png',
                    'create_time' => '2024-10-16 17:29:40',
                ],
                [
                    'id' => 8002,
                    'url' => 'https://admin.resmartcharge.com/applet?id=8002',
                    'path' => 'charging_qrcode\\charging_qrcode_8002.png',
                    'create_time' => '2024-10-16 17:29:40',
                ],
                [
                    'id' => 8003,
                    'url' => 'https://admin.resmartcharge.com/applet?id=8003',
                    'path' => 'charging_qrcode\\charging_qrcode_8003.png',
                    'create_time' => '2024-10-16 17:29:40',
                ]
            ],
        ];

        // 执行
        Db::startTrans();
        $row = $method->invokeArgs($logic, [$params['chunkQrcodeDataArray']]);
        Db::rollback();

        $this->assertSame(count($params['chunkQrcodeDataArray']), $row);

        $sql = ChargingQrcode::getLastSql();

        $expected_sql = "INSERT INTO `charging_qrcode` (`id` , `url` , `path` , `create_time`) VALUES ";
        $inserts = [];
        foreach ($params['chunkQrcodeDataArray'] as $value) {
            $inserts[] = sprintf("( %d,'%s','%s','%s' )", $value['id'], $value['url'], addslashes($value['path']), $value['create_time']);
        }
        $expected_sql .= implode(' , ', $inserts);
        $this->assertSame($expected_sql, $sql);
    }
}