<?php /** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace tests\unit\charging_qrcode;

use app\common\logic\admin\charging_qrcode\Bind;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use think\facade\Db;
use think\Request;

class TestBind extends TestCase
{
    protected function getLogic(): Bind
    {
        $request = new Request();
        return new Bind($request);
    }

    /**
     * @test
     * @testdox 验证参数
     */
    public function verifyParams()
    {
        $params = [
            'charging_qrcode_id' => 10001,
            'shots_id' => 1000000000003201,
            'is_force_bind' => false
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyParams');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [$params]);

        $this->assertSame($params, $result);
    }

    /**
     * @test
     * @testdox 验证充电枪是否存在
     */
    public function verifyShotsIfExists()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyShotsIfExists');
        $method->setAccessible(true);

        $shots_id = 10001;

        $result = $method->invokeArgs($logic, [$shots_id]);
        $run_sql = Db::getLastSQL();

        $expected_sql = sprintf("SELECT COUNT(*) AS think_count FROM `shots` WHERE  `id` = '%d'", $shots_id);

        $this->assertSame($run_sql, $expected_sql);
        $this->assertIsBool($result);
    }

    /**
     * @test
     * @testdox 验证二维码是否存在
     */
    public function verifyChargingQrcodeIfExists()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyChargingQrcodeIfExists');
        $method->setAccessible(true);

        $charging_qrcode_id = 10001;

        $result = $method->invokeArgs($logic, [$charging_qrcode_id]);
        $run_sql = Db::getLastSQL();

        $expected_sql = sprintf("SELECT COUNT(*) AS think_count FROM `charging_qrcode` WHERE  `id` = %d", $charging_qrcode_id);

        $this->assertSame($run_sql, $expected_sql);
        $this->assertIsBool($result);
    }

    /**
     * @test
     * @testdox 验证二维码是否已经绑定
     */
    public function verifyQrcodeHasItBeenBound()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyQrcodeHasItBeenBound');
        $method->setAccessible(true);

        $charging_qrcode_id = 10001;

        $result = $method->invokeArgs($logic, [$charging_qrcode_id]);
        $sql = Db::getLastSQL();

        $expected_sql = sprintf("SELECT COUNT(*) AS think_count FROM `charging_qrcode_to_shots` WHERE  `id` = %d", $charging_qrcode_id);

        $this->assertSame(false, $result);
        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @test
     * @testdox 解绑充电二维码
     */
    public function unbindChargingQrcode()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('unbindChargingQrcode');
        $method->setAccessible(true);

        $charging_qrcode_id = 1;

        Db::startTrans();
        $result = $method->invokeArgs($logic, [$charging_qrcode_id]);
        $run_sql = Db::getLastSql();
        Db::rollback();

        $expected_sql = sprintf("DELETE FROM `charging_qrcode_to_shots` WHERE  `id` = %d", $charging_qrcode_id);

        $this->assertSame($expected_sql, $run_sql);
        $this->assertSame(false, $result);
    }

    /**
     * @test
     * @testdox 验证充电枪是否已绑定
     */
    public function verifyShotsHasItBeenBound()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyShotsHasItBeenBound');
        $method->setAccessible(true);

        $shots_id = 1100000000000101;

        $result = $method->invokeArgs($logic, [$shots_id]);
        $run_sql = Db::getLastSql();

        $expected_sql = sprintf("SELECT COUNT(*) AS think_count FROM `charging_qrcode_to_shots` WHERE  `shots_id` = '%d'", $shots_id);

        $this->assertSame($expected_sql, $run_sql);
        $this->assertSame(false, $result);
    }

    /**
     * @test
     * @testdox 解绑充电枪
     */
    public function unbindShots()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('unbindShots');
        $method->setAccessible(true);

        $shots_id = 1100000000000101;

        Db::startTrans();
        $result = $method->invokeArgs($logic, [$shots_id]);
        $run_sql = Db::getLastSql();
        Db::rollback();

        $expected_sql = sprintf("DELETE FROM `charging_qrcode_to_shots` WHERE  `shots_id` = '%d'", $shots_id);

        $this->assertSame($expected_sql, $run_sql);
        $this->assertSame(false, $result);
    }

    /**
     * @test
     * @testbox 建立绑定
     */
    public function bind()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('bind');
        $method->setAccessible(true);

        $charging_qrcode_id = 100001;
        $shots_id = 1100000000000101;

        Db::startTrans();
        $result = $method->invokeArgs($logic, [$charging_qrcode_id, $shots_id]);
        $run_sql = Db::getLastSql();
        Db::rollback();

        $expected_sql = sprintf("INSERT INTO `charging_qrcode_to_shots` SET `id` = %d , `shots_id` = '%d' , `create_time` = '%s'", $charging_qrcode_id, $shots_id, date('Y-m-d H:i:s'));

        $this->assertSame($expected_sql, $run_sql);
        $this->assertSame(true, $result);
    }
}