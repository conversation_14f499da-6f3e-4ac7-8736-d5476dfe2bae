<?php /** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace tests\unit\refund_order;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\refund_order\RefundSuccess;
use app\common\model\File;
use app\common\model\RefundOrder as RefundOrderModel;
use PHPUnit\Framework\TestCase;
use think\facade\Db;
use think\Request;

/**
 * @testdox 退款成功
 */
class TestRefundSuccess extends TestCase
{
    protected function getLogic(): RefundSuccess
    {
        $loginUser = new AdminLoginUser([]);
        $request = new Request();
        return new RefundSuccess($loginUser, $request);
    }

    /**
     * @test
     * @testdox 验证操作权限
     */
    public function verifyOperationPermissions()
    {
        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermissions');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 0])]);

        $this->assertNull($result);

        $this->expectExceptionMessage("没有权限");
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);

        $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 100001])]);
    }

    /**
     * @test
     * @testdox 验证参数
     */
    public function verifyParams()
    {
        $params = [
            'refund_id' => 'T20240613M1718260287781713S23830',
            'refund_screenshot_id' => 10001
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyParams');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [$params]);

        $this->assertSame($params, $result);
    }

    /**
     * @test
     * @testdox 查询退款截图是否存在
     */
    public function refundScreenshotIsExistence()
    {
        $params = [
            'refund_screenshot_id' => 10001,
            'user_id' => 20001
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('refundScreenshotIsExistence');
        $method->setAccessible(true);

        $method->invokeArgs($logic, [$params['refund_screenshot_id'], $params['user_id']]);
        $run_sql = Db::getLastSql();

        $expected_sql = sprintf(
            'SELECT COUNT(*) AS think_count FROM `file` WHERE  `id` = %d  AND `user_id` = \'%d\'',
            $params['refund_screenshot_id'], $params['user_id']
        );

        $this->assertSame($expected_sql, $run_sql);
    }

    /**
     * @test
     * @testdox 验证退款截图 - 退款截图存在
     */
    public function verifyRefundScreenshotPass()
    {
        try {
            $params = [
                'refund_screenshot_id' => 10001,
                'user_id' => 20001
            ];

            $logic = $this->getLogic();

            // 通过反射API获取对象protocol方法的权限
            $reflectionClass = new \ReflectionClass($logic);
            $method = $reflectionClass->getMethod('verifyRefundScreenshot');
            $method->setAccessible(true);

            // 创建存根来替换真实的数据库查询
            $model = $this->createStub(File::class);
            $model->method('queryUserIsUpload')->willReturn(true);
            bind(File::class, $model);

            $result = $method->invokeArgs($logic, [$params['refund_screenshot_id'], $params['user_id']]);

            $this->assertNull($result);
        } finally {
            // 对测试产生的垃圾进行清理
            app()->delete(File::class);
        }
    }

    /**
     * @test
     * @testdox 验证退款截图 - 退款截图不存在
     */
    public function verifyRefundScreenshotNotPass()
    {
        try {

            $params = [
                'refund_screenshot_id' => 10001,
                'user_id' => 20001
            ];

            $logic = $this->getLogic();

            // 通过反射API获取对象protocol方法的权限
            $reflectionClass = new \ReflectionClass($logic);
            $method = $reflectionClass->getMethod('verifyRefundScreenshot');
            $method->setAccessible(true);

            // 创建存根来替换真实的数据库查询
            $model = $this->createStub(File::class);
            $model->method('queryUserIsUpload')->willReturn(false);
            bind(File::class, $model);

            $this->expectExceptionMessage('无效退款截图ID');
            $this->expectExceptionCode(RuntimeException::CodeBusinessException);
            $this->expectException(RuntimeException::class);

            $method->invokeArgs($logic, [$params['refund_screenshot_id'], $params['user_id']]);

        } finally {
            // 对测试产生的垃圾进行清理
            app()->delete(File::class);
        }
    }

    /**
     * @test
     * @testdox 加载退款订单
     */
    public function loadRefundOrder()
    {
        $params = [
            'refund_id' => 'T20240613M1718260287781713S23830',
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('loadRefundOrder');
        $method->setAccessible(true);

        $method->invokeArgs($logic, [$params['refund_id']]);
        $run_sql = Db::getLastSql();

        $expected_sql = sprintf('SELECT `id`,`order_id`,`user_id`,`refund_price`,`order_price`,`currency`,`state` FROM `refund_order` WHERE  `id` = \'%s\' LIMIT 1', $params['refund_id']);

        $this->assertSame($expected_sql, $run_sql);
    }

    /**
     * @test
     * @testdox 验证退款订单 - 当状态处于申请状态
     */
    public function verifyRefundOrderSceneStateApply()
    {
        $params = [
            'state' => RefundOrderModel::StateApply,
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyRefundOrder');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [$params]);

        $this->assertNull($result);
    }

    /**
     * @test
     * @testdox 验证退款订单 - 当状态处于退款中
     */
    public function verifyRefundOrderSceneStateRefunding()
    {
        $params = [
            'state' => RefundOrderModel::StateRefunding,
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyRefundOrder');
        $method->setAccessible(true);

        $this->expectExceptionMessage('退款订单正在退款中');
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectException(RuntimeException::class);

        $method->invokeArgs($logic, [$params]);
    }

    /**
     * @test
     * @testdox 验证退款订单 - 当状态处于已拒绝退款
     */
    public function verifyRefundOrderSceneStateRefuseRefund()
    {
        $params = [
            'state' => RefundOrderModel::StateRefuseRefund,
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyRefundOrder');
        $method->setAccessible(true);

        $this->expectExceptionMessage('退款订单已被拒绝退款');
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectException(RuntimeException::class);

        $method->invokeArgs($logic, [$params]);
    }

    /**
     * @test
     * @testdox 验证退款订单 - 当状态处于已被取消
     */
    public function verifyRefundOrderSceneStateCancelRefund()
    {
        $params = [
            'state' => RefundOrderModel::StateCancelRefund,
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyRefundOrder');
        $method->setAccessible(true);

        $this->expectExceptionMessage('退款订单已被取消');
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectException(RuntimeException::class);

        $method->invokeArgs($logic, [$params]);
    }

    /**
     * @test
     * @testdox 验证退款订单 - 当状态处于已退款成功
     */
    public function verifyRefundOrderSceneStateRefundSuccess()
    {
        $params = [
            'state' => RefundOrderModel::StateRefundSuccess,
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyRefundOrder');
        $method->setAccessible(true);

        $this->expectExceptionMessage('退款订单已退款成功');
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectException(RuntimeException::class);

        $method->invokeArgs($logic, [$params]);
    }

    /**
     * @test
     * @testdox 验证退款订单 - 当状态处于已退款失败
     */
    public function verifyRefundOrderSceneStateRefundFail()
    {
        $params = [
            'state' => RefundOrderModel::StateRefundFail,
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyRefundOrder');
        $method->setAccessible(true);

        $this->expectExceptionMessage('退款订单已退款失败');
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectException(RuntimeException::class);

        $method->invokeArgs($logic, [$params]);
    }

    /**
     * @test
     * @testdox 验证退款订单 - 当状态处于其他
     */
    public function verifyRefundOrderSceneStateOther()
    {
        $params = [
            'state' => RefundOrderModel::StateOther,
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyRefundOrder');
        $method->setAccessible(true);

        $this->expectExceptionMessage('退款订单处于其他状态');
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectException(RuntimeException::class);

        $method->invokeArgs($logic, [$params]);
    }


    /**
     * @test
     * @testdox 更新退款订单状态
     */
    public function updateRefundOrderStatus()
    {
        $params = [
            'refund_id' => 'T20240613M1718260287781713S23830',
            'refund_screenshot_id' => 10001
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('updateRefundOrderStatus');
        $method->setAccessible(true);

        Db::startTrans();
        $method->invokeArgs($logic, [$params['refund_id'], $params['refund_screenshot_id']]);
        Db::rollback();
        $run_sql = Db::getLastSql();

        $expected_sql = sprintf(
            'UPDATE `refund_order`  SET `state` = 20 , `refund_screenshot_id` = %d , `msg` = \'退款成功\' , `success_time` = \'%s\'  WHERE  `id` = \'%s\'  LIMIT 1',
            $params['refund_screenshot_id'],
            date('Y-m-d H:i:s'),
            $params['refund_id']
        );

        $this->assertSame($expected_sql, $run_sql);
    }

    /**
     * @test
     * @testdox 更新退款订单状态
     */
    public function updatePayOrderRefundStatus()
    {
        $params = [
            'order_id' => '231129kem0g5rni96fgbkdicsciitfah',
        ];

        $logic = $this->getLogic();

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('updatePayOrderRefundStatus');
        $method->setAccessible(true);

        Db::startTrans();
        $method->invokeArgs($logic, [$params['order_id']]);
        Db::rollback();
        $run_sql = Db::getLastSql();

        $expected_sql = sprintf(
            'UPDATE `pay_order`  SET `refund_state` = 3  WHERE  `id` = \'%s\'  LIMIT 1',
            $params['order_id']
        );

        $this->assertSame($expected_sql, $run_sql);
    }

}