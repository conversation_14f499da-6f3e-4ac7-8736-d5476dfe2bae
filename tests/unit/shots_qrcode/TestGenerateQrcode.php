<?php

namespace tests\unit\shots_qrcode;

use Endroid\QrCode\Color\Color;
use Endroid\QrCode\Color\ColorInterface;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\Label\Font\Font;
use Endroid\QrCode\Label\Label;
use Endroid\QrCode\Label\Margin\Margin;
use Endroid\QrCode\Logo\Logo;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\RoundBlockSizeMode;
use Endroid\QrCode\Writer\PngWriter;
use PHPUnit\Framework\TestCase;

class TestGenerateQrcode extends TestCase
{

    public function testa()
    {
        $path = root_path('vendor\endroid\qr-code\assets') . 'noto_sans.otf';
        $a = file_get_contents($path);
        var_dump($a);
    }

    public function test_generate()
    {
        // 创建二维码实例

        $qrCode = QrCode::create('https://pdev.liehuo88.cn/resmartcharge')
            ->setEncoding(new Encoding('UTF-8'))
            ->setErrorCorrectionLevel(new ErrorCorrectionLevel\ErrorCorrectionLevelQuartile())
            ->setSize(560)
            ->setMargin(10)
            ->setRoundBlockSizeMode(new RoundBlockSizeMode\RoundBlockSizeModeMargin())
            ->setForegroundColor(new Color(0, 0, 0))
            ->setBackgroundColor(new Color(255, 255, 255));

        // 加载Logo
        $logo = new Logo(public_path() . "logo-new.png", 120, 120);

        // 使用PngWriter写出二维码
        $writer = new PngWriter();
        $fileName = 'qrcode.png';
        $font = new Font(__DIR__ . '/../../../vendor/endroid/qr-code/assets/open_sans.ttf', 36);
        $labelMargin = new Margin(0, 10, 8, 10);
        $label = new Label("NO. 1 2 3 4 5 6 7 8", $font, null, $labelMargin);

        $writer->write($qrCode, $logo, $label)->saveToFile($fileName);

// 输出二维码图片
//        header('Content-Type: image/png');
//        readfile($fileName);
    }

}