-- 删除场站原来的部分字段
ALTER TABLE `stations` DROP COLUMN `tariff_group_id`;
ALTER TABLE `stations` DROP COLUMN `status`;
ALTER TABLE `stations` DROP COLUMN `pictures`;
ALTER TABLE `stations` DROP COLUMN `work_time`;
ALTER TABLE `stations` DROP COLUMN `tag_pos`;
ALTER TABLE `stations` DROP COLUMN `tag_park`;
ALTER TABLE `stations` DROP COLUMN `place_rate`;
ALTER TABLE `stations` DROP COLUMN `charge`;
ALTER TABLE `stations` DROP COLUMN `charge_phone`;
ALTER TABLE `stations` DROP COLUMN `tag_toilet`;
ALTER TABLE `stations` DROP COLUMN `tag_canopy`;
ALTER TABLE `stations` DROP COLUMN `tag_rest`;
ALTER TABLE `stations` DROP COLUMN `tag_pnp`;
ALTER TABLE `stations` DROP COLUMN `tag_insure`;
ALTER TABLE `stations` DROP COLUMN `tag_protect`;
ALTER TABLE `stations` DROP COLUMN `tag_ultrafast`;
ALTER TABLE `stations` DROP COLUMN `tag_fast`;
ALTER TABLE `stations` DROP COLUMN `tag_slow`;
ALTER TABLE `stations` DROP COLUMN `opt_id`;


-- 新增一张场站额外信息表
CREATE TABLE `stations_extra_info`
(
    `id`              bigint(24) NOT NULL AUTO_INCREMENT COMMENT '场站id',
    `tariff_group_id` int(4) NOT NULL COMMENT '费率组id',
    `status`          tinyint(2) NOT NULL DEFAULT 3 COMMENT '运行状态：1-正常使用、2-维护中、3-未开放',
    `pictures`        json NULL COMMENT '场站图片url集',
    `work_time`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '运营时段描述',
    `tag_pos`         tinyint(2) NOT NULL DEFAULT 1 COMMENT '桩位置标示：1-地上,2-地库',
    `tag_park`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '停车费说明',
    `place_rate`      int(8) NOT NULL DEFAULT 0 COMMENT '占位费（X分/分钟）,X*100',
    `create_time`     timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `charge`          varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '负责人',
    `charge_phone`    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '负责人手机号',
    `tag_toilet`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '场站服务卫生间标识：1-无，2-有',
    `tag_canopy`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '场站服务雨棚标识：1-无，2-有',
    `tag_rest`        tinyint(2) NOT NULL DEFAULT 1 COMMENT '场站服务休息室标识：1-无，2-有',
    `tag_pnp`         tinyint(2) NOT NULL DEFAULT 1 COMMENT '场站特色即插即用标识：1-不支持，2-支持',
    `tag_insure`      tinyint(2) NOT NULL DEFAULT 1 COMMENT '场站特色充电保险标识：1-不支持，2-支持',
    `tag_protect`     tinyint(2) NOT NULL DEFAULT 1 COMMENT '场站特色充电电池防护标识：1-不支持，2-支持',
    `tag_ultrafast`   tinyint(2) NOT NULL DEFAULT 1 COMMENT '场站电桩超快充标识：1-不支持，2-支持',
    `tag_fast`        tinyint(2) NOT NULL DEFAULT 1 COMMENT '场站电桩快充标识：1-不支持，2-支持',
    `tag_slow`        tinyint(2) NOT NULL DEFAULT 1 COMMENT '场站电桩慢充标识：1-不支持，2-支持',
    `update_time`     timestamp NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;
