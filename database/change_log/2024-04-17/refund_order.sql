CREATE TABLE `refund_order`
(
    `id`                    varchar(64) NOT NULL,
    `user_id`               bigint(24) NOT NULL COMMENT '用户',
    `order_id`              varchar(64) NOT NULL COMMENT '支付订单id',
    `out_refund_id`         varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '外部退款号',
    `out_transaction_id`    varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '外部交易号',
    `refund_price`          int(11) NOT NULL DEFAULT '0' COMMENT '退款金额',
    `order_price`           int(11) NOT NULL DEFAULT '0' COMMENT '订单总金额',
    `currency`              tinyint(2) NOT NULL DEFAULT '1' COMMENT '币种：1:CNY,2:USD',
    `state`                 tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态：1已申请，5退款中，10已拒绝，11已取消，20已退款，30退款失败，40其它',
    `ip`                    varchar(255)                    DEFAULT NULL COMMENT '客户ip',
    `msg`                   longtext COMMENT '结果信息',
    `create_time`           timestamp   NOT NULL            DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`           timestamp   NOT NULL            DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `reason`                varchar(255)                    DEFAULT '用户主动申请退款' COMMENT '退款原因',
    `channel`               varchar(255)                    DEFAULT NULL COMMENT '退款渠道，\r\nORIGINAL: 原路退款，\r\nBALANCE: 退回到余额，\r\nOTHER_BALANCE: 原账户异常退到其他余额账户，\r\nOTHER_BANKCARD: 原银行卡异常退到其他银行卡，',
    `user_received_account` varchar(255)                    DEFAULT NULL COMMENT '【退款入账账户】 取当前退款单的退款入账方，有以下几种情况：\r\n1）退回银行卡：{银行名称}{卡类型}{卡尾号}\r\n2）退回支付用户零钱:支付用户零钱\r\n3）退还商户:商户基本账户商户结算银行账户\r\n4）退回支付用户零钱通:支付用户零钱通',
    `out_status`            varchar(20)                     DEFAULT NULL COMMENT '外部申请返回状态，【退款状态】 退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往商户平台（pay.weixin.qq.com）-交易中心，手动处理此笔退款。\r\n可选取值：\r\nSUCCESS: 退款成功\r\nCLOSED: 退款关闭\r\nPROCESSING: 退款处理中\r\nABNORMAL: 退款异常',
    `payer_refund`          int(11) NOT NULL DEFAULT '0' COMMENT '【用户退款金额】 退款给用户的金额，单位为分，不包含所有优惠券金额',
    `out_notice_status`     varchar(20)                     DEFAULT NULL COMMENT '外部通知状态，退款状态，枚举值：\r\nSUCCESS：退款成功\r\nCLOSED：退款关闭\r\nABNORMAL：退款异常，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往【商户平台—>交易中心】，手动处理此笔退款',
    `success_time`          timestamp NULL DEFAULT NULL COMMENT '退款成功时间',
    PRIMARY KEY (`id`),
    KEY                     `order_id` (`order_id`,`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;