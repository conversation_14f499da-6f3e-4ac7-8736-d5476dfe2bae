CREATE TABLE `refund_callback`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT,
    `refund_id`     varchar(255) NOT NULL COMMENT '本地退款id',
    `order_id`      varchar(255)                    DEFAULT NULL COMMENT '本地订单id',
    `trade_no`      varchar(255)                    DEFAULT NULL COMMENT '外部订单编号',
    `out_refund_id` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '外部退款号',
    `type`          varchar(255) NOT NULL COMMENT '类型',
    `log`           longtext COMMENT '返回什么就保存什么',
    `create_time`   datetime     NOT NULL           DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=201 DEFAULT CHARSET=utf8mb4;