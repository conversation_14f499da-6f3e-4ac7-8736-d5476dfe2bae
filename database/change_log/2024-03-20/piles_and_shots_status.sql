-- 创建充电桩状态表
CREATE TABLE `piles_status`
(
    `id`                    BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '充电桩ID',
    `is_online`             TINYINT UNSIGNED NOT NULL COMMENT '是否在线 1:在线 2:离线',
    `is_online_update_time` TIMESTAMP NOT NULL COMMENT '是否在线状态更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT="充电桩状态表";

-- 填充数据
INSERT INTO `piles_status` (`id`, `is_online`, `is_online_update_time`)
SELECT `id`, `status`, CURRENT_TIMESTAMP() as `is_online_update_time`
FROM `piles`;

-- 创建充电枪状态表
CREATE TABLE `shots_status`
(
    `id`                      BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '充电枪ID',
    `is_online`               TINYINT UNSIGNED NOT NULL COMMENT '在线在线 1:离线 2:在线',
    `is_online_update_time`   TIMESTAMP NOT NULL COMMENT '在线状态更新时间',
    `work_status`             TINYINT UNSIGNED NOT NULL COMMENT '工作状态 1:空闲 2:充电',
    `work_status_update_time` TIMESTAMP NOT NULL COMMENT '工作状态更新时间',
    `is_fault`                TINYINT UNSIGNED NOT NULL COMMENT '故障状态 1:正常 2:故障',
    `is_fault_update_time`    TIMESTAMP NOT NULL COMMENT '故障状态更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT="充电枪状态表";

-- 填充数据
INSERT INTO `shots_status` (`id`, `is_online`, `is_online_update_time`, `work_status`, `work_status_update_time`,
                            `is_fault`, `is_fault_update_time`)
SELECT `id`,
       `is_online`,
       `status_update_time` as `is_online_update_time`,
       `work_status`,
       `status_update_time` as `work_status_update_time`,
       `is_fault`,
       `status_update_time` as `is_fault_update_time`
FROM `shots`;
