-- ----------------------------
-- Table structure for admin_auth_group
-- ----------------------------
CREATE TABLE `admin_auth_group`  (
                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                     `group_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                     `state` tinyint(3) NOT NULL DEFAULT 1 COMMENT '状态，1正常，2禁用',
                                     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
                                     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     UNIQUE INDEX `group_name`(`group_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of admin_auth_group
-- ----------------------------
INSERT INTO `admin_auth_group` VALUES (1, '超级管理员', 1, '2023-07-17 14:39:28', '2023-07-17 14:39:28');

-- ----------------------------
-- Table structure for admin_auth_group_rules
-- ----------------------------
CREATE TABLE `admin_auth_group_rules`  (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                           `group_id` int(11) NOT NULL COMMENT '本地group表id',
                                           `rule_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限值',
                                           `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
                                           PRIMARY KEY (`id`) USING BTREE,
                                           UNIQUE INDEX `group_rule`(`group_id`, `rule_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 173 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of admin_auth_group_rules
-- ----------------------------
INSERT INTO `admin_auth_group_rules` VALUES (1, 1, 'monitoring_center', '2023-10-09 19:19:39');
INSERT INTO `admin_auth_group_rules` VALUES (2, 1, 'monitoring_center_operation_overview', '2023-10-09 19:20:18');
INSERT INTO `admin_auth_group_rules` VALUES (3, 1, 'system_management', '2023-10-10 09:45:53');
INSERT INTO `admin_auth_group_rules` VALUES (4, 1, 'system_management_permission_list', '2023-10-10 09:46:25');
INSERT INTO `admin_auth_group_rules` VALUES (5, 1, 'system_management_permission_group', '2023-10-10 10:01:25');
INSERT INTO `admin_auth_group_rules` VALUES (6, 1, 'system_management_admin_list', '2023-10-10 10:01:33');
INSERT INTO `admin_auth_group_rules` VALUES (7, 1, 'system_management_admin_list1', '2023-10-10 10:07:44');
INSERT INTO `admin_auth_group_rules` VALUES (8, 1, 'system_management_admin_list2', '2023-10-10 10:07:52');
INSERT INTO `admin_auth_group_rules` VALUES (9, 1, 'system_management_admin_list3', '2023-10-10 10:08:01');
INSERT INTO `admin_auth_group_rules` VALUES (10, 1, 'system_management_admin_list4', '2023-10-10 10:08:09');
INSERT INTO `admin_auth_group_rules` VALUES (11, 1, 'system_management_admin_list5', '2023-10-10 14:24:16');
INSERT INTO `admin_auth_group_rules` VALUES (12, 1, 'system_management_admin_list6', '2023-10-10 14:24:21');
INSERT INTO `admin_auth_group_rules` VALUES (13, 1, 'system_management_admin_list7', '2023-10-10 14:24:27');
INSERT INTO `admin_auth_group_rules` VALUES (14, 1, 'system_management_admin_list8', '2023-10-10 14:24:35');
INSERT INTO `admin_auth_group_rules` VALUES (18, 1, 'resource_center', '2023-10-12 14:08:07');
INSERT INTO `admin_auth_group_rules` VALUES (19, 1, 'resource_center_charging_station_management', '2023-10-12 14:08:17');
INSERT INTO `admin_auth_group_rules` VALUES (20, 1, 'resource_center_charging_pile_management', '2023-10-12 14:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (21, 1, 'resource_center_charging_gun_management', '2023-10-12 14:08:34');
INSERT INTO `admin_auth_group_rules` VALUES (22, 1, 'resource_center_rate_management', '2023-10-12 15:31:49');
INSERT INTO `admin_auth_group_rules` VALUES (23, 1, 'test', '2023-10-12 15:44:09');
INSERT INTO `admin_auth_group_rules` VALUES (24, 1, 'data_center', '2023-10-17 19:35:36');
INSERT INTO `admin_auth_group_rules` VALUES (25, 1, 'data_center_business_center', '2023-10-17 19:41:58');
INSERT INTO `admin_auth_group_rules` VALUES (26, 1, 'system_management_permission_group_add', '2023-10-18 14:51:57');
INSERT INTO `admin_auth_group_rules` VALUES (27, 1, 'system_management_permission_group_list', '2023-10-18 15:17:35');
INSERT INTO `admin_auth_group_rules` VALUES (29, 3, 'monitoring_center_operation_overview', '2023-10-18 15:46:02');
INSERT INTO `admin_auth_group_rules` VALUES (32, 3, 'operations_center_station_monitoring', '2023-10-18 15:48:47');
INSERT INTO `admin_auth_group_rules` VALUES (36, 3, 'monitoring_center', '2023-10-18 16:03:02');
INSERT INTO `admin_auth_group_rules` VALUES (37, 3, 'operations_center', '2023-10-18 16:03:02');
INSERT INTO `admin_auth_group_rules` VALUES (42, 1, 'system_management_admin_list_add', '2023-10-19 17:59:34');
INSERT INTO `admin_auth_group_rules` VALUES (52, 3, 'monitoring_center_operation_overview_api', '2023-10-20 16:50:40');
INSERT INTO `admin_auth_group_rules` VALUES (53, 3, 'admin_menu_user_menu_list', '2023-10-20 17:10:17');
INSERT INTO `admin_auth_group_rules` VALUES (54, 3, 'admin_Menu_user_route_list', '2023-10-20 17:21:51');
INSERT INTO `admin_auth_group_rules` VALUES (55, 3, 'admin_login_web_socket', '2023-10-20 17:27:24');
INSERT INTO `admin_auth_group_rules` VALUES (63, 1, 'operations_center', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (64, 1, 'operations_center_charging_monitoring', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (65, 1, 'operations_center_station_monitoring', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (66, 1, 'operations_center_device_control', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (67, 1, 'operations_center_alarm_monitoring', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (68, 1, 'resource_center_whitelist_management', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (69, 1, 'central_controller_management', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (70, 1, 'finance_center', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (71, 1, 'finance_center_revenue_overview', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (72, 1, 'finance_center_order_management', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (73, 1, 'finance_center_settlement_management', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (74, 1, 'finance_center_group_billing', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (75, 1, 'finance_center_interconnection_order', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (76, 1, 'finance_center_invoice_management', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (77, 1, 'finance_center_reconciliation_management', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (78, 1, 'member_center', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (79, 1, 'member_center_operator_management', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (80, 1, 'member_center_group_management', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (81, 1, 'member_center_member_management', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (82, 1, 'member_center_wallet_management', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (83, 1, 'member_center_card_number_management', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (84, 1, 'marketing_center', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (85, 1, 'marketing_center_coupon_management', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (86, 1, 'interaction_center', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (87, 1, 'interaction_center_CEPEF_standard_docking', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (88, 1, 'operations_maintenance_center', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (89, 1, 'operations_maintenance_center_remote_control', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (90, 1, 'operations_maintenance_center_order_evaluation', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (91, 1, 'operations_maintenance_center_fault_repair', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (92, 1, 'operations_maintenance_center_after-sales_service', '2023-10-24 09:42:15');
INSERT INTO `admin_auth_group_rules` VALUES (93, 3, 'resource_center', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (94, 3, 'resource_center_charging_station_management', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (95, 3, 'resource_center_charging_pile_management', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (96, 3, 'resource_center_charging_gun_management', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (97, 3, 'resource_center_rate_management', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (98, 3, 'resource_center_whitelist_management', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (99, 3, 'central_controller_management', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (100, 3, 'finance_center', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (101, 3, 'finance_center_invoice_management', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (102, 3, 'admin/Corp/get_corp_list', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (103, 3, 'admin/monitoringCenter/monthlyStatistics', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (104, 3, 'admin/monitoringCenter/dataOverview', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (105, 3, 'admin/AlarmRecord/get_list_data', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (106, 3, 'admin/monitoringCenter/stationsMonthRankingListTop10', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (107, 3, 'admin/Login/auto_login', '2023-11-16 11:12:34');
INSERT INTO `admin_auth_group_rules` VALUES (108, 3, 'admin/stationsMonitor/statisticsData', '2023-11-16 11:25:37');
INSERT INTO `admin_auth_group_rules` VALUES (109, 3, 'admin/stationsMonitor/electricityUsageStatistics', '2023-11-16 11:25:37');
INSERT INTO `admin_auth_group_rules` VALUES (110, 3, 'admin/monitoringCenter/dailyStatistics', '2023-11-16 11:25:37');
INSERT INTO `admin_auth_group_rules` VALUES (112, 3, 'admin/Shots/sort_list_info', '2023-11-16 11:30:37');
INSERT INTO `admin_auth_group_rules` VALUES (113, 3, 'admin/TariffGroup/get_tariff_group_list', '2023-11-16 11:30:37');
INSERT INTO `admin_auth_group_rules` VALUES (114, 3, 'admin/Stations/sort_list_info', '2023-11-16 11:33:07');
INSERT INTO `admin_auth_group_rules` VALUES (115, 3, 'admin/Stations/get_stations_list', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (116, 3, 'admin/Stations/get_info', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (117, 3, 'admin/Stations/add', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (118, 3, 'admin/Stations/edit', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (119, 3, 'admin/Stations/delete', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (120, 3, 'admin/Stations/stations_list', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (121, 3, 'admin/electronic_invoice/list', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (122, 3, 'admin/electronic_invoice/statistics', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (123, 3, 'admin/Shots/shots_list', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (124, 3, 'admin/Shots/get_info', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (125, 3, 'admin/Shots/add', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (126, 3, 'admin/Shots/edit', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (127, 3, '/admin/Shots/delete', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (128, 3, 'admin/Shots/out_shots_list_excel', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (129, 3, 'admin/Shots/batch_create_qr', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (130, 3, 'admin/Piles/piles_list', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (131, 3, 'admin/Piles/get_piles_list', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (132, 3, 'admin/Piles/sort_list_info', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (133, 3, 'admin/Piles/get_info', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (134, 3, 'admin/Piles/add', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (135, 3, 'admin/Piles/edit', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (136, 3, 'admin/Piles/delete', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (137, 3, 'admin/TariffGroup/tariff_group_list', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (138, 3, 'admin/TariffGroup/sort_list_info', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (139, 3, 'admin/TariffGroup/get_info', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (140, 3, 'admin/TariffGroup/add', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (141, 3, 'admin/TariffGroup/edit', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (142, 3, 'admin/TariffGroup/delete', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (143, 3, 'admin/TariffGroup/get_period_rate_list', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (144, 3, 'admin/TariffGroup/get_period_rate_type', '2023-11-16 12:08:26');
INSERT INTO `admin_auth_group_rules` VALUES (145, 3, 'admin/Common/get_host', '2023-11-16 12:42:12');
INSERT INTO `admin_auth_group_rules` VALUES (146, 3, 'admin/Common/upload_img', '2023-11-16 12:42:12');
INSERT INTO `admin_auth_group_rules` VALUES (147, 3, 'admin/stationsMonitor/shotsStatusStatistics', '2023-11-16 12:44:45');
INSERT INTO `admin_auth_group_rules` VALUES (153, 3, 'finance_center_order_management', '2023-11-16 12:47:19');
INSERT INTO `admin_auth_group_rules` VALUES (154, 3, 'member_center', '2023-11-16 12:47:19');
INSERT INTO `admin_auth_group_rules` VALUES (155, 3, 'member_center_operator_management', '2023-11-16 12:47:19');
INSERT INTO `admin_auth_group_rules` VALUES (156, 3, 'admin/AlarmRecord/delete_data', '2023-11-16 12:49:43');
INSERT INTO `admin_auth_group_rules` VALUES (157, 3, 'admin/Order/get_order_list', '2023-11-16 12:49:43');
INSERT INTO `admin_auth_group_rules` VALUES (158, 3, 'admin/Order/get_order_info', '2023-11-16 12:49:43');
INSERT INTO `admin_auth_group_rules` VALUES (159, 3, 'admin/Order/order_repair', '2023-11-16 12:49:43');
INSERT INTO `admin_auth_group_rules` VALUES (160, 3, 'admin/Corp/corp_list', '2023-11-16 12:55:00');
INSERT INTO `admin_auth_group_rules` VALUES (161, 3, 'admin/Corp/sort_list_info', '2023-11-16 12:55:00');
INSERT INTO `admin_auth_group_rules` VALUES (162, 3, 'admin/Corp/get_info', '2023-11-16 12:55:00');
INSERT INTO `admin_auth_group_rules` VALUES (163, 3, 'admin/Corp/add', '2023-11-16 12:55:00');
INSERT INTO `admin_auth_group_rules` VALUES (164, 3, 'admin/Corp/edit', '2023-11-16 12:55:00');
INSERT INTO `admin_auth_group_rules` VALUES (165, 3, 'admin/Corp/delete', '2023-11-16 12:55:00');
INSERT INTO `admin_auth_group_rules` VALUES (166, 3, 'admin/Corp/update_audit_status', '2023-11-16 12:55:00');
INSERT INTO `admin_auth_group_rules` VALUES (167, 3, 'admin/Corp/update_status', '2023-11-16 12:55:00');
INSERT INTO `admin_auth_group_rules` VALUES (168, 3, 'operations_center_device_control', '2023-11-17 13:47:33');
INSERT INTO `admin_auth_group_rules` VALUES (169, 3, 'operations_center_alarm_monitoring', '2023-11-17 13:47:33');
INSERT INTO `admin_auth_group_rules` VALUES (170, 3, 'admin/PilesControl/index', '2023-11-17 13:58:17');
INSERT INTO `admin_auth_group_rules` VALUES (171, 3, 'admin/PilesControl/sort_list_info', '2023-11-17 13:58:17');
INSERT INTO `admin_auth_group_rules` VALUES (172, 3, 'admin/PilesControl/issue_tariff_group', '2023-11-17 13:58:17');


-- ----------------------------
-- Table structure for admin_users
-- ----------------------------
DROP TABLE IF EXISTS `admin_users`;
CREATE TABLE `admin_users`  (
                                `id` bigint(24) NOT NULL AUTO_INCREMENT COMMENT '管理员id',
                                `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '管理员登录用户名',
                                `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '管理员登录密码',
                                `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '管理员头像url',
                                `phone` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '管理员手机号',
                                `email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '管理员邮箱',
                                `state` tinyint(3) NOT NULL DEFAULT 1 COMMENT '状态，1正常，2禁用',
                                `perm_group_id` int(11) NOT NULL COMMENT '管理员权限组id',
                                `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '用户创建时间',
                                `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`) USING BTREE,
                                UNIQUE INDEX `name`(`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of admin_users
-- ----------------------------
INSERT INTO `admin_users` VALUES (1, 'admin', '$2y$10$0gWFIfJxG8.gwxNS3ixVAOChXGRWS4lasSjjHc.ffxldI4zSmEoWu', 'static/shots/qrcode2/2023010500036902.png', '13700000000', '<EMAIL>', 1, 1, '2023-10-17 10:45:51', '2024-01-26 11:42:01');


-- ----------------------------
-- Table structure for agreement
-- ----------------------------
DROP TABLE IF EXISTS `agreement`;
CREATE TABLE `agreement`  (
                              `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '协议标题，分类',
                              `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '协议内容',
                              `status` tinyint(3) NOT NULL DEFAULT 1 COMMENT '状态： 1正常 2停用',
                              `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              PRIMARY KEY (`name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of agreement
-- ----------------------------
INSERT INTO `agreement` VALUES ('新能慧充小程序个人信息处理规则', '<p style=\"text-align: center;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">新能慧充小程序个人信息处理规则</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">2023年9月18日版本</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">前言</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">新能慧充非常重视用户个人信息的保护。本个人信息处理规则适用于新能慧充小程序和运营平台产品或服务。【新能慧充】由新能智慧充电科技有限公司（注册地址：深圳新能智慧充电科技有限公司）提供充电信息技术平台服务。以下称“新能慧充”或“我们”。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">根据《常见类型移动互联网应用程序必要个人信息范围规定》，新能慧充属于“网络购物类”的消费侧App，基本功能服务为“购买商品”，必要个人信息包括：1.注册用户移动电话号码；2.支付时间、支付金额、支付渠道等支付信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">本个人信息处理规则重点包括：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">1.具体功能可能收集、使用个人信息的范围、目的、方式，以及拒绝提供个人信息可能的影响；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">2.小程序可能需要调取的设备权限、调用目的及其对应业务功能，以及用户关闭相应权限的方式；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">3.我们如何共享、保护您的个人信息；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">4.您作为个人信息主体所享有的权利等。</span></p><p style=\"text-align: justify;\"><strong>您同意个人信息处理规则，仅代表您已了解本应用提供的功能，以及功能运行所需的必要个人信息，并不代表您已同意我们收集非必要个人信息。对于非必要的个人信息处理，会在您使用服务的过程中单独征求您的同意。如果您拒绝我们收集非必要个人信息，并不会影响您使用基本业务功能。</strong></p><p style=\"text-align: justify;\"><strong>您同意个人信息处理规则，个人信息相关权限并不会默认开启，我们会在您使用到相应业务功能时，另行弹窗再次征得您的同意后开启。权限开启后，您还可以随时通过设备设置关闭权限。您不同意开启权限，不会影响其他非相关业务功能的正常使用。</strong></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">目录：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">1.个人信息的定义与范围；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">2.个人信息的收集；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">3.需要您授权同意调用系统权限的情形；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">4.我们如何使用Cookie及同类技术；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.个人信息的保存及保护；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">6.个人信息的使用；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">7.个人信息的共享、转让、公开披露以及所接入的第三方；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">8.用户权利；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">9.变更；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">10.如何联系我们。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">1.&nbsp;个人信息的定义与范围</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">1.1&nbsp;个人信息，是以电子或者其他方式记录的与已识别或者可识别的自然人有关的各种信息，不包括匿名化处理后的信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">1.2&nbsp;敏感个人信息，是一旦泄露或者非法使用，容易导致自然人的人格尊严受到侵害或者人身、财产安全受到危害的个人信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">1.3&nbsp;匿名化，是指个人信息经过处理无法识别特定自然人且不能复原的过程。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">&nbsp;2.&nbsp;个人信息的收集</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">根据《常见类型移动互联网应用程序必要个人信息范围规定》，新能慧充小程序属于“网络购物类”的消费侧平台，基本功能服务为“购买商品”，必要个人信息包括：1.注册用户移动电话号码；2.支付时间、支付金额、支付渠道等支付信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">2.1&nbsp;用户账号注册、登录</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">（1）必要信息、非必要信息范围：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\"><strong>当您使用快速登录功能时，您需要至少向我们提供您本人的手机号码用于快速登录，我们将通过向该手机号发送短信验证码的方式来验证您的身份是否有效；此类信息属于该功能的必要信息，若您不提供，您将无法完成快速登录。</strong></span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">2.2&nbsp;购买商品，即充电服务</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">（1）必要信息、非必要信息范围</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">您在注册成为新能慧充用户后，可以通过新能慧充购买商品，即为您的爱车充电，此功能为基础功能，我们需要收集您的以下信息：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">1</span><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\"><strong>）订单及支付信息（支付金额、支付时间、支付渠道）</strong></span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\"><strong>基于《电子商务法》要求以及保护您的财产安全、依照平台规则处理用户纠纷之需要，我们将收集您的订单及支付信息（支付金额、支付时间、支付渠道）。上述信息为本功能必要信息，如果您拒绝我们收集上述信息，会导致您无法使用本功能</strong></span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">2</span><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\"><strong>）位置信息（含经纬度）</strong></span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\"><strong>为了向您推荐附近的充电场站，我们会收集您的位置信息（含经纬度）。在您首次使用平台前，我们会提示您选择是否允许我们通过您使用的平台获取以及何种情况下可以获取您的位置信息。</strong></span><span style=\"color: rgb(73, 73, 73); font-family: 宋体;\"><strong>在您使用新能慧充的过程中，您可以随时通过平台应用程序设置功能，选择是否允许我们获取以及何种情况下可以获取您的位置信息。获得您的允许后，我们会收集和处理您的动态位置信息。</strong></span><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">上述信息为充电服务的非必要信息，如果您拒绝我们收集，会导致您无法获得推荐场站，但不会影响您使用充电基本业务功能，您还可通过手动搜索查找充电场站。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">（2）调用的权限</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\"><strong>当我们使用您的位置信息推荐充电站时，我们会通过您使用的平台应用程序调用位置权限；当您使用扫一扫进入线下充电场景时，我们将调用摄像头权限。</strong></span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">2.3&nbsp;车辆管理服务</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">（1）必要信息、非必要信息范围</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">您可开通并享受新能慧充的车辆管理服务，此功能为附加功能，我们需要收集您的以下必要信息：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">1</span><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\"><strong>）车牌号</strong></span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\"><strong>当您结束充电离场时，您可以选择向我们提供您的车牌号用于场站免费放行核验。如果您拒绝提供此类信息，您可能需要按照场地要求支付相关费用，但不会影响您使用充电基本业务功能。上述信息为本功能必要信息，如果您拒绝我们收集上述信息，会导致您无法使用本功能，但不会影响您使用充电基本业务功能。</strong></span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">2.4&nbsp;发票功能</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">（1）必要信息、非必要信息范围</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\"><strong>根据《中华人民共和国电子商务法》第三十九条的规定，新能慧充为您提供了开具发票的途径。您在新能慧充平台选择开具发票时，您需要向我们提供您充电的订单信息及您接收发票的邮箱信息；为了保证您获得发票，上述信息为本功能必要信息，如果您拒绝我们收集上述信息，会导致您无法使用本功能，但不会影响您使用充电基本业务功能。</strong></span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">2.5&nbsp;客服</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">（1）必要信息、非必要信息范围</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\"><strong>当您与新能慧充客服团队联系时，我们会以通话录音及人工录入的方式记录您的通话信息、您提出的意见或建议、您提供的姓名、联系方式，以便我们处理纠纷、解决问题或向您反馈结果。此类信息为实现功能所需的必要信息，如果您拒绝我们收集此类信息，会导致您无法拨打我们的客服电话或客服团队无法将处理结果反馈到您，但不会影响您使用充电基本业务功能。</strong></span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">2.6&nbsp;小程序升级</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">（1）必要信息、非必要信息范围</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">当您使用小程序内的升级功能时，我们需要收集使用网络信息。此类信息为相关功能的必要信息。小程序有可升级的版本会提示您，您可进行选择安装或拒绝。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">2.7&nbsp;小程序适配及安全性</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">（1）必要信息、非必要信息范围</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(73, 73, 73); font-family: 宋体;\"><strong>为了保证不同设备用户的使用体验及兼容性，我们会收集您的设备信息，包括设备型号、操作系统版本、其他设备信息（屏设备像素比、屏幕尺寸）。为了判断用户点击行为是否符合正常产品流程，我们会收集您的点击查看记录。为了判断当前设备是否为真实设备，用于账户安全/反作弊/反黑灰产，我们会收集您的设备信息（设备型号、操作系统版本），日志信息。上述信息为小程序适配及保障安全性所需的必要信息。若您不提供这类信息，将可能导致小程序无法正常运行。</strong></span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">2.8&nbsp;请您理解，我们向您提供的功能和服务是不断更新和发展的，如果某一功能或服务未在前述说明中且需要收集您的信息，我们会通过页面提示、交互流程、网站公告、另行签署协议的方式另行向您说明信息收集的内容、范围和目的，以征得您的同意。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">2.9&nbsp;根据相关法律法规，以下情形中，我们可能会收集您的相关个人信息而无需征求您的授权同意：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">a)&nbsp;履行法律法规规定的义务所必需；&nbsp;b)&nbsp;维护国家安全、国防安全所必需；&nbsp;c)&nbsp;维护公共安全、公共卫生、重大公共利益所必需；&nbsp;d)&nbsp;刑事侦查、起诉、审判和判决执行所必需；&nbsp;e)&nbsp;出于维护个人信息主体或其他个人的生命、财产等重大合法权益但又很难得到本人授权同意的；&nbsp;f)&nbsp;所涉及的个人信息是您自行向社会公众公开的；&nbsp;g)&nbsp;根据您的要求签订和履行合同所必需的；&nbsp;h)&nbsp;从合法公开披露的信息中收集个人信息的，如合法的新闻报道、政府信息公开等渠道。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">3.需要您授权同意调取系统权限的情形</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">在为您提供前述基本功能、附加功能、改进产品/服务的功能时，部分场景的实现依赖于您同意我们调取您所使用的平台应用程序相应权限。只有在您登录且首次进入特定使用场景时，小程序才会通过弹窗申请来获取您的同意。您可以在小程序右上方【…】-【设置】中逐项查看各种权限的开启状态，并可以随时开启或关闭这些权限（视终端设备及操作系统不同，可能略有差异）；请您注意，您开启这些权限即代表您在使用我们服务期间持续授权我们可以收集和使用这些个人信息来实现上述的功能，您关闭权限即代表您取消了这些授权，则我们将不再继续收集和使用您的这些个人信息，也无法为您提供上述与这些授权所对应的功能。您关闭权限的决定不会影响此前基于您的授权所进行的个人信息的处理。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">4&nbsp;我们如何使用Cookie及同类技术</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">4.1&nbsp;Cookie是支持服务器端（或者脚本）在客户端上存储和检索信息的一种机制。当您使用新能慧充产品或服务时，为使您获得更轻松安全的访问体验，我们可能会使用Cookie或同类技术来收集和存储信息，在此过程中可能会向您的设备发送一个或多个Cookie或匿名标识符。这么做是为了收取您的信息，用于了解您的偏好，进行咨询或数据分析，改善产品服务及用户体验，或及时发现并防范安全风险，为您提供更好的服务。我们不会将Cookie用于本规则所述目的之外的任何用途，您可根据自己的偏好留存或删除Cookie。您可清除软件内保存的所有Cookie，当您手动清除后您的相关信息即已删除。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">4.2&nbsp;我们平台上还可能包含一些电子图像（以下简称“单像素GIF文件”或“网络Beacon”），使用网络Beacon可以计算浏览网页的用户或访问某些Cookie，我们会通过网络Beacon收集您浏览网页活动的信息，包括Internet协议(IP)地址，浏览器类型，Internet服务提供商（ISP），参考/退出页面，操作系统，日期/时间戳，和点击数据流。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5&nbsp;个人信息的保存及保护</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.1&nbsp;信息保存期限</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.1.1&nbsp;在用户使用新能慧充平台服务期间，我们会持续保存用户的个人信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.1.2&nbsp;当您自主删除个人信息、注销账号或者撤回个人信息处理规则授权，我们将按以下规则保存您的个人信息：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">a.依据相关法律法规规定，我们将您的个人信息继续保存三年。在此保存期限内，我们将对您的个人信息停止除存储和采取必要的安全保护措施之外的处理。当您的个人信息超出上述保存期限，我们将会对其进行删除或者匿名化处理。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">b.如果您使用新能慧充平台服务的过程中，存在严重违反法律法规、平台协议、平台规则的情形，您的违法、违约记录及相应的平台信用记录，将被永久保存。当您再次注册账号，我们会将以上信息恢复。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">c．为配合人民检察院、公安机关、国家安全机关侦查用户使用新能慧充平台服务过程中产生的犯罪行为、更好保护用户生命财产安全，如果您在使用新能慧充平台服务过程中涉及刑事案件，我们将按照刑法规定的追诉时效保存您的个人信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.1.3&nbsp;新能慧充与您另行签署的具体的个人信息相关协议就某类信息的保存期限做特殊约定的，从其约定。&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.2&nbsp;信息存放地域</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.2.1&nbsp;我们收集的您的个人信息，存储在中华人民共和国境内。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.2.2&nbsp;如果涉及向境外传输个人信息，我们将确保具备法律、行政法规或者国家网信部门规定的条件，明确向您告知个人信息出境情况并取得您的单独同意。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.3&nbsp;安全保护措施</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.3.1&nbsp;本公司将采用严格的安全制度以及行业通行的安全技术和程序来确保您的个人信息不丢失、泄露、毁损或被未经授权的访问、使用。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.3.2&nbsp;新能慧充采取以下安全技术措施保护您的个人信息：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">a.敏感个人信息被加密储存在服务器中，并通过数据隔离技术进行存储；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">b.数据传输过程中使用加密传输协议；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">c.严格控制数据访问权限，设立完善的敏感数据访问权限申请、审批制度；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">d.建立数据安全监控和审计制度，进行全面数据安全控制。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.3.3&nbsp;新能慧充同时采取其他安全措施保护您的个人信息：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">a.建立公司个人信息保护委员会，统一协调管理个人信息处理及保护工作（联系方式：</span><span style=\"color: rgb(229, 76, 94); font-family: 宋体;\"><EMAIL></span><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">）；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">b.公司内部颁布实施个人信息管理规范，明确对用户个人信息的保护标准和要求；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">c.新项目、新系统上线前对个人信息处理活动进行项目风险评估；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">d.与全体员工及服务外包人员签署保密协议，并严格按照工作职责分配数据访问权限；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">e.定期开展面向公司全体员工及外包服务人员的个人信息合规教育及培训。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.4&nbsp;安全事件处置</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.4.1&nbsp;为应对可能出现的安全风险，包括个人信息泄露、毁损、丢失，已经在公司范围内颁布多项制度，明确了安全事件、安全漏洞的分类分级标准，以及针对上述安全事件和漏洞，内部处理流程（包括应急响应流程）和补救措施。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.4.2&nbsp;一旦发生安全事件，我们将依法及时向您告知事件基本情况和风险、我们已经采取或将要采取的措施、您如何自行降低风险的建议。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.4.3&nbsp;我们将及时以推送通知、信函或电话的方式将安全事件情况通知受影响的用户。当难以逐一告知用户时，我们将通过发布平台公告的方式发布警示信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.5&nbsp;停止运营</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.5.1&nbsp;如果新能慧充平台停止运营，我们将至少提前30日在新能慧充平台发布公告，并及时停止收集个人信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">5.5.2&nbsp;停止运营后，我们将停止对个人信息的商业化使用，并在满足法律法规规定的最短保存期后，对收集的个人信息进行删除或匿名化处理。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">6&nbsp;个人信息的使用</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">6.1&nbsp;您的个人信息会被用于“个人信息的收集”条款明确列明的使用场景。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">6.2&nbsp;您的个人信息可能被用于以下与“个人信息的收集”条款所声称的目的具有直接或合理关联的场景：&nbsp;a.为向您提供更加个性化、便捷的服务，我们会使用您的个人信息进行特征分析，向您推送您可能感兴趣的商业广告及营销内容。&nbsp;如果您想管理个性化推荐内容，您可以在“我的-设置-个人信息-个性化管理”进行设置。当个性化推荐功能关闭后，我们不会基于个性化推送的目的处理您的个人信息，而仅会向您推送与您的偏好无关的具有普遍性的内容。&nbsp;b.我们可能会以去除身份识别信息的个人信息进行统计分析形成的数据为基础，设计、开发、推广全新的产品及服务。我们会根据用户的订单起终点类型，区分订单的出行场景，以此来统计分析不同出行场景下的订单数据变化情况，辅助公司运营决策。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">c.我们会对我们的服务使用情况进行统计，并可能会与公众或第三方分享这些统计信息，但这些统计信息不包含您的任何身份识别信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">d.我们会</span><span style=\"color: rgb(73, 73, 73); font-family: 宋体;\">通过短信或电话渠道</span><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">向您发送信息和通知，包括为保证服务完成所必需的验证码、使用产品或服务时所必要的推送通知、关于新能慧充平台服务的新闻以及市场活动及优惠促销信息、新能慧充或其合作第三方的推广信息、订单支付提醒通知，或您可能感兴趣的内容。如果您不希望继续接收上述信息，可以根据短信中提供的退订方式予以退订，也可以直接与我们联系进行退订。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">6.3&nbsp;凡是超出与“个人信息的收集”条款声称目的具有直接或合理关联的范围使用您的个人信息，我们会再次向您告知并征得您的明示同意。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">7&nbsp;个人信息的共享、转让、公开披露以及所接入的第三方</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">7.1&nbsp;共享是指新能慧充向其他个人信息处理者提供个人信息，且双方分别对个人信息拥有独立控制权的过程。新能慧充不会对外共享您的个人信息，但以下情况除外：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">7.1.1&nbsp;在获取明确同意情况下的共享：获得您的明确同意后，新能慧充会向您指定的第三方共享您授权范围内的信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">7.1.2&nbsp;在法定情形下的共享：新能慧充可能会根据法律法规规定、诉讼争议解决需要，或按行政、司法机关依法提出的要求，对外共享您的个人信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">7.1.3为维护用户或者其他相关方合法权益，在协助处理与您有关的交易纠纷或争议时，我们可能向您的交易相对方或存在利害关系的第三方提供解决交易纠纷或争议所必需的信息。在下述情形下，如您未配合支付相应费用、处理相应事故或解决相应争议，我们将向对应的充电服务提供方提供您的手机号、车辆信息（如有且需要）：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">a）您拒绝向服务提供方支付费用；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">b）您违反服务提供方的规则对其造成损失；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">c）您怠于处理您使用充电服务期间发生的事故（如剐蹭、损毁场地财产）；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">d）您与服务提供方因使用充电服务发生纠纷或争议的。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">7.1.4&nbsp;根据相关法律法规，以下情形中，我们可能会共享、转让、公开披露您的相关个人信息而无需征求您的授权同意：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">a)&nbsp;履行法律法规规定的义务所必需；&nbsp;b)&nbsp;维护国家安全、国防安全所必需；&nbsp;c)&nbsp;维护公共安全、公共卫生、重大公共利益所必需；&nbsp;d)&nbsp;刑事侦查、起诉、审判和判决执行所必需；&nbsp;e)&nbsp;出于维护您或其他个人的生命、财产等重大合法权益但又很难得到本人授权同意的；&nbsp;f)&nbsp;您自行向社会公众公开的个人信息；&nbsp;g)&nbsp;从合法公开披露的信息中收集个人信息的，如合法的新闻报道、政府信息公开等渠道。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">7.2&nbsp;如果发生收购、兼并、重组变更，我们会要求变更后的主体依然遵守本个人信息处理规则约定，履行原有责任及义务。如果变更后的主体需变更个人信息使用目的，我们会要求其事先获得您的明示同意。对内而言，在之前的收购、兼并活动中，对于被收购、被兼并对象管理的数据（包括用户个人信息）均采用与新能慧充自有数据相同的标准和要求进行处理和保护。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">7.3&nbsp;我们将遵守相关法律法规，对您的个人信息予以保密。除非事先获得您的明确同意或授权，或依照法律规定所必须，或为了保护其他相关方重大合法权益且对个人信息进行去标识化处理的，我们不会公开披露您的个人信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">8.用户权利</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">8.1&nbsp;您可以通过以下方式在新能慧充平台查阅、复制、更正、删除您的个人信息：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">a.您可以通过点击“我的”-“设置”-“个人信息”-“个人信息查询”，查询您的个人信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">b.当您需要更新您的个人信息，或发现新能慧充平台收集、存储的个人信息有错误时，您可以通过点击“我的”-“设置”-“个人信息”-“个人信息更正”，自行更正或修改个人信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">c.您可以通过点击“我的”-“设置”-“个人信息”-“个人信息删除”，删除您的个人信息。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">8.2&nbsp;我们将通过以下方式保障用户撤回同意的权利。当您撤回同意或授权后，可能导致我们无法为您继续提供撤回授权部分对应的服务。您撤回同意或授权，不影响撤回前基于您的同意开展的个人信息处理。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">a.您可以通过点击“我的”-“设置”-“个人信息”-“撤回授权管理”，撤回您的授权，您还可以通过删除信息的方式撤回部分授权。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">b.您可以通过点击“我的”-“设置”-“个人信息”-“权限管理”或者通过设备的设置功能，关闭相应设备权限，撤回对新能慧充获取您个人信息的授权。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">8.3&nbsp;注销账号。您可以通过新能慧充平台“我的”-“设置”-“账号管理”-“注销账号”注销在新能慧充平台的账号。除法律法规另有规定外，注销账号之后我们将停止为您提供服务，并根据本协议约定期限保存您的个人信息，保存期限届满后我们将对您的个人信息进行删除或者匿名化处理。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">8.4&nbsp;如果您无法通过上述方式查阅、复制、更正、删除个人信息，撤回同意或注销账号，您可以通过手机客户端的“客服”功能在线或者拨打新能慧充的客服团队电话，或者通过本规则披露的联系方式与我们取得联系。新能慧充客服团队可能需要验证您的身份，并在验证您的身份后15日内作出答复或合理解释。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">8.5&nbsp;根据相关法律法规，以下情形中，我们可不响应本章所列个人信息权利请求，包括：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">a)&nbsp;履行法律法规规定的义务所必需；&nbsp;b)&nbsp;维护国家安全、国防安全所必需；&nbsp;c)&nbsp;维护公共安全、公共卫生、重大公共利益所必需；&nbsp;d)&nbsp;刑事侦查、起诉、审判和执行判决所必需；&nbsp;e)&nbsp;我们有充分证据表明您存在主观恶意或滥用权利的；&nbsp;f)&nbsp;出于维护您或其他个人的生命、财产等重大合法权益但又很难得到本人授权同意的；&nbsp;g)&nbsp;响应您的请求将导致您或其他个人、组织的合法权益受到严重损害的；&nbsp;h)&nbsp;涉及商业秘密的。&nbsp;如果我们判断无法响应您的请求，我们会向您告知该决定的理由，并向您提供投诉的途径。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">9.变更</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">9.1&nbsp;如果新能慧充平台发生以下变化，我们将及时对本个人信息处理规则进行相应的修订：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">a.用户个人信息的处理目的、处理方式和处理的个人信息种类发生变更；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">b.用户个人信息保存地域发生变更；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">c.我们的联络方式及投诉渠道发生变更；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">d.发生其他可能影响用户个人信息安全或影响用户个人信息权利的变更。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">9.2&nbsp;个人信息处理规则修订后，我们会在新能慧充平台发布最新版本，并重新征得您的同意。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">9.3&nbsp;未经您的明确同意，我们不会削减您按照本个人信息处理规则所享有的权利。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">10.如何联系我们</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">10.1&nbsp;您可以通过手机客户端的“客服”功能在线或者电话与新能慧充的客服团队取得联系，反馈您的意见或投诉。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">10.2&nbsp;我们还设立了个人信息反馈专用邮箱，您可以将您的问题发送至以下邮箱地址：</span><span style=\"color: rgb(229, 76, 94); font-family: 宋体;\"><EMAIL></span><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">，我们将在15天内核实并处理您反馈的问题。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(51, 51, 51); font-family: 宋体;\">10.3&nbsp;如果您对我们的回复不满意，特别是您认为我们的个人信息处理行为损害了您的合法权益，您还可以通过向被告住所地有管辖权的法院提起诉讼来寻求解决方案。</span></p><p>&nbsp;</p>', 1, '2023-09-18 19:18:55');
INSERT INTO `agreement` VALUES ('新能慧充平台服务协议', '<p style=\"text-align: center;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">新能慧充平台服务协议</span></p><p style=\"text-align: center;\"><span style=\"color: rgb(0, 0, 0); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">更新：2023年9月18日</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">生效：2023年9月22日</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">1.特别提示</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">1.1本《新能慧充平台服务协议》（以下简称“本协议”）是您（以下简称“您”或“用户”）与深圳新能智慧充电科技有限公司（以下简称“我们”或“本公司”）之间关于您使用“新能慧充”信息平台（以下简称“平台”）以及您通过平台获得相关信息服务（以下简称“本服务”或“平台服务”）所订立的协议，深圳新能智慧充电科技有限公司为您提供技术开发服务。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">1.2</span><span style=\"color: rgb(0, 0, 0);\"><strong>您在使用平台各项服务（以下简称“服务”）之前，请您认真阅读本协议，特别是涉及免除或者限制平台责任的条款、限制您权利的条款、争议解决方式、司法管辖、法律适用的条款。免除或者限制责任的条款将以粗体标识，请您重点阅读。</strong></span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0);\"><strong>1.3平台如根据法律法规的变化及/或维护交易秩序、用户权益保障需要，对本协议内容进行修改，将以提前公示、推送通知或弹窗等方式通知到您。您可以随时在平台查阅修改后的最新版本协议。如您对已生效的变更事项不同意的，您应当于变更事项确定的生效之日起停止使用本平台服务，变更事项对您不产生效力。本协议变更事项生效后，如果您继续使用本平台服务，我们可以理解您已接受修改后的协议和已生效的变更事项</strong></span><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">2.用户资格</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">2.1您作为平台用户应当具备与您行为相适应的民事行为能力。若您是未满18周岁的未成年人，应在监护人陪同下阅读本协议并在征得监护人同意后在平台注册。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">2.2如您不符合用户资格要求，还请停止注册或使用平台服务。如您注册账号后因情况变化，不再符合有关用户资格的要求，还请您停止使用平台服务，并注销账号。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">2.3如您不符合平台用户资格要求，仍使用平台服务，且非平台原因给他人造成损失，您或您的监护人需承担相应法律责任。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">3.账号登录</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">3.1使用平台服务前，您需要在平台进行注册，如注册成功，您将获得平台账号。平台通过手机号码快速登录来识别用户的指令。用户确认，使用手机号码快速登录后在平台的一切行为均代表用户本人。用户账号操作所产生的电子信息记录均为用户行为的有效凭据，并由用户本人承担由此产生的法律责任。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">3.3用户如发现账号存在安全漏洞或其他异常情况，应立即以有效方式通知平台，要求平台暂停相关服务，并根据平台的要求，协助平台向公安机关报案。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">3.4</span><span style=\"color: rgb(0, 0, 0);\"><strong>用户向平台发出的通知至少应当包括情况描述、相关的初步证据、要求平台采取的处理措施、通知人联系方式等，缺乏前述信息的通知视为无效通知。用户理解平台对用户的通知请求识别、采取行动需要合理时间，平台对非因平台过错产生的后果不承担除法律法规明确规定之外的责任</strong></span><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">3.5用户注册账号后如果长期不登录该账号，平台可以收回、注销该账号或设定账号功能限制，以免造成资源浪费，由此带来的损失由用户自行承担。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">4.服务使用</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">4.1平台根据您的需求向您提供充电设施运营服务商（以下简称“服务提供方”）的相关信息（包括但不限于服务提供方的充电站/桩位置信息、电费、服务费价格信息等）供您自主选择，以便促成您与服务提供方之间达成充电服务交易（以下简称“充电服务”）。您可以在平台查询订单信息，以及使用平台的其他信息技术服务。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0);\"><strong>【特澄清如下】平台是协助您与服务提供方之间达成充电服务交易的网络信息技术平台，并非提供充电服务，而是为您和服务提供方之间提供信息及技术服务。除法律法规有明确规定或平台存在过错外，平台不对充电服务过程中您与服务提供商之间产生的纠纷或争议承担责任，您和服务提供方应自行解决相关事宜，平台将在平台公示的服务范围内提供必要协助。</strong></span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">4.2您可以免费下载和使用平台的应用程序和服务。我们保留日后就服务向您收费的权利。如果我们决定就服务收取费用，我们将提前向您发送通知，确保您享有充分选择的权利。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">4.3</span><span style=\"color: rgb(0, 0, 0);\"><strong>用户使用服务提供方提供的充电服务时，应根据实际使用的充电服务支付费用，且将由服务提供方向您提供相应的发票；您如逾期未支付费用，平台可通过短信、客户端内消息、语音客服等方式对用户进行提醒或警示，经催告后仍不履行支付义务的，平台有权暂停向您提供平台服务，同时您知悉并同意视情节轻重，平台可选择以下一种或多种方式追究您的责任：中止或终止信息服务、暂时冻结您的资金账户、从您的资金账户扣除您应支付的充电费用</strong></span><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">4.4用户使用充电服务时应确保您的车辆处于安全、可充电的状态，在车辆存在安全隐患时不得使用充电服务。用户应在使用充电服务时，按照服务提供方场所内的使用指示及安全提示等规范操作充电设施。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">4.5平台可能会与第三方合作并由第三方向您提供相关的服务或权益（包括但不限于支付服务、寻址定位服务等）。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.保证与承诺</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">您使用本服务的过程中，作出如下保证和承诺：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.1您向平台提供的全部信息真实、准确、完整、合法、有效。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.2您在使用平台服务的过程中实施的所有行为均遵守国家法律法规等的规定，不违背社会公共利益或公共道德，不侵犯或试图侵犯他人的合法权益。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.3您将严格遵守本协议、平台规则以及与服务提供方的要求。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.4您在平台上发送或传播的内容（包括但不限于网页、文字、图片、音频、视频、图表）不得包含以下信息。如您违反本条约定，平台有权暂停或终止您使用本平台服务，并删除相关内容。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.4.1违反国家法律法规禁止性规定的；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.4.2政治宣传、封建迷信、淫秽、色情、赌博、暴力、恐怖或者教唆犯罪的；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.4.3欺诈、虚假、不准确或存在误导性的；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.4.4侵犯他人知识产权或涉及第三方商业秘密及其他专有权利的；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.4.5侮辱、诽谤、恐吓、涉及他人隐私等侵害他人合法权益的；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.4.6存在可能破坏、篡改、删除、影响平台系统正常运行或未经授权秘密获取平台及其他用户的数据、个人资料的病毒、木马、爬虫等恶意软件、程序代码的；</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.4.7其他违背社会公共利益或公共道德或依据相关平台协议、规则的规定不适合在平台上发布的。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.5不以任何方式干扰或试图干扰平台的正常运作或任何形式侵害平台合法权益，包括但不限于使用任何装置、病毒、木马等干预平台的正常运作，采取任何将导致不合理的庞大数据负载加诸平台网络设备的行动等。在使用平台服务时，您的手机不得开启root权限。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.6不进行危害计算机网络安全的行为，包括但不限于：使用未经许可的数据或进入未经许可的服务器账号；未经允许不进入公众计算机网络或者他人计算机系统并删除、修改、增加存储信息；未经许可，不企图探查、扫描、测试平台系统或网络的弱点或其它实施破坏网络安全的行为；不企图干涉、破坏系统等的正常运行。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">5.7尊重平台合法权利，不实施侮辱、诽谤、侵犯平台名誉权或其他侵犯平台合法权利的行为。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">6.责任限制</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">6.1平台可能为不同的终端设备开发了不同的应用程序版本，您应当根据实际情况选择下载合适的版本进行安装。平台不保证所有的版本与相关设备的适配性和可用性，亦不保证所有的版本提供的服务的一致性。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">6.2为了完善服务内容，平台将不断努力开发新的服务，并为您不时提供应用程序的更新（这些更新可能会采取应用程序替换、修改、功能强化、版本升级等形式）。应用程序新版本发布后，旧版本的应用程序可能无法使用，平台不保证旧版本应用程序可继续使用及相应服务，因此请您随时核对并下载最新版本的应用程序。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">6.3平台作为信息技术平台，无法控制其他用户履约能力，无法对平台服务所涉及的技术及信息的有效性、准确性、正确性、可靠性、稳定性、完整性和及时性作出任何承诺和保证，除因平台存在过错或法律法规明确规定外，您自行承担使用平台服务产生的依据法律规定应当由您承担的风险。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">6.4平台将会尽其商业上的合理努力保障您在平台及服务中的数据存储安全，您可根据自身需求自行备份平台及服务中的相关数据。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">6.5在使用本服务的过程中，可能会遇到不可抗力、技术风险等因素，使本服务发生中断，对于下述原因导致的合同履行障碍、履行瑕疵、履行延后或履行内容变更、数据丢失等情形，平台在法律允许的范围内免责：</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">6.5.1因自然灾害、罢工、战争、政府行为、司法行政命令等不可抗力因素。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">6.5.2因电力供应故障、通讯网络故障（包括但不限于电子通讯传达失败或延时、用于电子通讯的计算机程序对电子通讯的拦截或操纵）等公共服务因素或您自身因素（包括但不限于您操作不当、通过非平台授权的方式使用本服务）或第三人因素（包括但不限于受到计算机病毒、木马或其他恶意程序、黑客攻击的破坏、顾客的错误下单等错误操作）。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">6.5.3在平台已尽善意管理的情况下，因紧急的设备与系统故障、缺陷、技术⻛险等因素引起且凭借现有技术水平无法预见、无法避免的情形。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">6.5.4其他平台无法控制或合理预见的情形。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">6.6如果平台发现了因系统故障或其他原因导致的处理错误，平台都应该在根据本协议约定通知用户后纠正该错误、回转/回档相关交易或数据。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">6.7平台发生合并、分立、收购、资产转让时，平台可向第三方转让本服务下相关资产。平台在单方通知您并确保您的权利不受损的情况下，将本协议下部分或全部服务转交由第三方运营或履行。具体受让主体以平台通知的为准。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">6.8平台对因平台原因给您造成的损失依法承担相应的赔偿责任。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">7.知识产权</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">7.1本公司及/或本公司关联公司是本平台的知识产权权利人。本平台的一切著作权、商标权、专利权、商业秘密等知识产权，以及与本平台相关的所有信息内容（包括但不限于文字、图片、音频、视频、图表、界面设计、版面框架、有关数据或电子文档）均受中华人民共和国法律法规和相应的国际条约保护，本公司及/或本公司关联公司享有上述知识产权，但相关权利人依照法律规定应享有的权利除外。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">7.2未经本公司或相关权利人书面同意，您不得自行或许可任何第三方实施、利用、转让上述知识产权</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">8.违约责任</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">8.1如用户违反本协议或平台规则的约定，平台视您行为严重程度采取警告、暂停服务、终止服务、注销账号等措施。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">9.通知与送达</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">9.1您提供给平台的联系方式（包括您的电子邮件地址、联系电话、联系地址等）应保持有效和可联系畅通，如联系方式发生变更的，您有义务及时更新有关信息，并保持可被联系的状态，否则平台存储的您最后一次提供的联系方式即视为您的有效联系方式；您的平台账号如可正常接受平台推送信息、系统消息的也视为有效联系方式；因您未及时更新联系方式而导致无法及时获取查看相关通知的，由您自行承担相应后果。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">9.2平台将向您的上述有效联系方式的其中之一或其中若干向您送达各类通知，而此类通知的内容可能对您的权利义务产生重大的有利或不利影响，请您务必及时关注。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">9.3通知如果是通过专人递送、邮资预付挂号信或商业快递服务发出的，则应视为在快递、信件送至您提供的联系地址于签收日或拒收之日有效送达。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">9.4通知如果是通过电子邮件发出的，则应自电子邮件进入您提供的电子邮箱的时间或您提供的电子邮箱地址失效时平台授权电子邮箱收到传送失败的系统提示时即视为有效送达。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">9.5通知如果是以除电子邮件以外的其他电子形式发出的，包括但不限于在平台公告、公示，向您提供的联系电话发送手机短信，向您的账号发送弹窗信息、系统消息等，在平台使用的发送设备上显示发送成功后即视为送达。</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: Arial, sans-serif;\">&nbsp;</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">10.法律适用与管辖</span></p><p style=\"text-align: justify;\"><span style=\"color: rgb(0, 0, 0); font-family: 宋体;\">10.1本协议适用中华人民共和国法律。关于本协议的履行、违约终止、解释或有效性，或者您因使用平台服务所产生及与平台服务有关的争议，由平台与您协商解决，协商不成时任何一方均可向被告住所地/所在地：例如【深圳市南山区人民法院】；或提交合同签订地（深圳市南山区）有管辖权的法院处理。</span></p><p><span style=\"color: rgb(0, 0, 0);\">&nbsp;</span></p>', 1, '2023-09-18 19:18:33');

-- ----------------------------
-- Table structure for alarm_record
-- ----------------------------
DROP TABLE IF EXISTS `alarm_record`;
CREATE TABLE `alarm_record`  (
                                 `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增id',
                                 `corp_id` bigint(20) UNSIGNED NOT NULL COMMENT '运营商编号',
                                 `station_id` bigint(20) UNSIGNED NOT NULL COMMENT '场站id',
                                 `device_id` bigint(20) UNSIGNED NOT NULL COMMENT '告警设备id，与类型对应：充电枪编号、充电桩编号',
                                 `device_type` tinyint(3) UNSIGNED NOT NULL COMMENT '设备类型 1:充电枪 2:充电桩',
                                 `type` tinyint(3) UNSIGNED NOT NULL COMMENT '告警代码类型 1:开启充电 2:远程启动充电命令应答 3:上传实时监控数据 4:远程停止充电命令应答 5:交易记录',
                                 `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '告警代码',
                                 `level` tinyint(3) UNSIGNED NOT NULL COMMENT '告警等级 1:一级 2:二级',
                                 `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '告警状态 1:创建 2:处理中 3:处理完成',
                                 `info` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '告警信息',
                                 `detail` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '告警详情',
                                 `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
                                 `start_time` int(10) UNSIGNED NOT NULL COMMENT '告警开始时间',
                                 `end_time` int(10) UNSIGNED NOT NULL COMMENT '告警结束时间，处理完成时间',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 113 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '告警记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for centralized_controller
-- ----------------------------
DROP TABLE IF EXISTS `centralized_controller`;
CREATE TABLE `centralized_controller`  (
                                           `id` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '集中器id，集中器mac地址（全大写）',
                                           `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
                                           `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `station_id` int(10) UNSIGNED NOT NULL COMMENT '场站ID',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for city
-- ----------------------------
DROP TABLE IF EXISTS `city`;
CREATE TABLE `city`  (
                         `id` int(11) NOT NULL AUTO_INCREMENT,
                         `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                         `pid` int(11) NULL DEFAULT NULL,
                         `py` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                         `s` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                         `type` tinyint(3) NULL DEFAULT 1 COMMENT '类型，1省，2市，3区',
                         `hot` tinyint(4) NULL DEFAULT 1 COMMENT '热门，1普通，2热门',
                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 440108 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

INSERT INTO `city` VALUES (331504, '开江县', 331500, 'KJX', 'K', 3, 1);
INSERT INTO `city` VALUES (331505, '大竹县', 331500, 'DZX', 'D', 3, 1);
INSERT INTO `city` VALUES (331506, '渠县', 331500, 'QX', 'Q', 3, 1);
INSERT INTO `city` VALUES (331507, '万源市', 331500, 'W,MYS', 'W', 3, 1);
INSERT INTO `city` VALUES (331600, '雅安市', 330000, 'YAS', 'Y', 2, 1);
INSERT INTO `city` VALUES (331601, '雨城区', 331600, 'YCQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (331602, '名山区', 331600, 'MSQ,O', 'M', 3, 1);
INSERT INTO `city` VALUES (331603, '荥经县', 331600, 'Y,XJX', 'Y', 3, 1);
INSERT INTO `city` VALUES (331604, '汉源县', 331600, 'HYX', 'H', 3, 1);
INSERT INTO `city` VALUES (331605, '石棉县', 331600, 'S,DMX', 'S', 3, 1);
INSERT INTO `city` VALUES (331606, '天全县', 331600, 'TQX', 'T', 3, 1);
INSERT INTO `city` VALUES (331607, '芦山县', 331600, 'LSX', 'L', 3, 1);
INSERT INTO `city` VALUES (331608, '宝兴县', 331600, 'BXX', 'B', 3, 1);
INSERT INTO `city` VALUES (331700, '巴中市', 330000, 'BZS', 'B', 2, 1);
INSERT INTO `city` VALUES (331701, '巴州区', 331700, 'BZQ,O', 'B', 3, 1);
INSERT INTO `city` VALUES (331702, '恩阳区', 331700, 'EYQ,O', 'E', 3, 1);
INSERT INTO `city` VALUES (331703, '通江县', 331700, 'TJX', 'T', 3, 1);
INSERT INTO `city` VALUES (331704, '南江县', 331700, 'NJX', 'N', 3, 1);
INSERT INTO `city` VALUES (331705, '平昌县', 331700, 'PCX', 'P', 3, 1);
INSERT INTO `city` VALUES (331800, '资阳市', 330000, 'ZYS', 'Z', 2, 1);
INSERT INTO `city` VALUES (331801, '雁江区', 331800, 'YJQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (331802, '安岳县', 331800, 'AYX', 'A', 3, 1);
INSERT INTO `city` VALUES (331803, '乐至县', 331800, 'Y,LZX', 'Y', 3, 1);
INSERT INTO `city` VALUES (331900, '阿坝藏族羌族自治州', 330000, 'A,EBZ,CZQZZZZ', 'A', 2, 1);
INSERT INTO `city` VALUES (331901, '马尔康市', 331900, 'MEKS', 'M', 3, 1);
INSERT INTO `city` VALUES (331902, '汶川县', 331900, 'WCX', 'W', 3, 1);
INSERT INTO `city` VALUES (331903, '理县', 331900, 'LX', 'L', 3, 1);
INSERT INTO `city` VALUES (331904, '茂县', 331900, 'MX', 'M', 3, 1);
INSERT INTO `city` VALUES (331905, '松潘县', 331900, 'SPX', 'S', 3, 1);
INSERT INTO `city` VALUES (331906, '九寨沟县', 331900, 'JZGX', 'J', 3, 1);
INSERT INTO `city` VALUES (331907, '金川县', 331900, 'JCX', 'J', 3, 1);
INSERT INTO `city` VALUES (331908, '小金县', 331900, 'XJX', 'X', 3, 1);
INSERT INTO `city` VALUES (331909, '黑水县', 331900, 'HSX', 'H', 3, 1);
INSERT INTO `city` VALUES (331910, '壤塘县', 331900, 'RTX', 'R', 3, 1);
INSERT INTO `city` VALUES (331911, '阿坝县', 331900, 'A,EBX', 'A', 3, 1);
INSERT INTO `city` VALUES (331912, '若尔盖县', 331900, 'REGX', 'R', 3, 1);
INSERT INTO `city` VALUES (331913, '红原县', 331900, 'H,GYX', 'H', 3, 1);
INSERT INTO `city` VALUES (332000, '甘孜藏族自治州', 330000, 'GZZ,CZZZZ', 'G', 2, 1);
INSERT INTO `city` VALUES (332001, '康定市', 332000, 'KDS', 'K', 3, 1);
INSERT INTO `city` VALUES (332002, '泸定县', 332000, 'LDX', 'L', 3, 1);
INSERT INTO `city` VALUES (332003, '丹巴县', 332000, 'DBX', 'D', 3, 1);
INSERT INTO `city` VALUES (332004, '九龙县', 332000, 'JLX', 'J', 3, 1);
INSERT INTO `city` VALUES (332005, '雅江县', 332000, 'YJX', 'Y', 3, 1);
INSERT INTO `city` VALUES (332006, '道孚县', 332000, 'DFX', 'D', 3, 1);
INSERT INTO `city` VALUES (332007, '炉霍县', 332000, 'LHX', 'L', 3, 1);
INSERT INTO `city` VALUES (332008, '甘孜县', 332000, 'GZX', 'G', 3, 1);
INSERT INTO `city` VALUES (332009, '新龙县', 332000, 'XLX', 'X', 3, 1);
INSERT INTO `city` VALUES (332010, '德格县', 332000, 'DGX', 'D', 3, 1);
INSERT INTO `city` VALUES (332011, '白玉县', 332000, 'BYX', 'B', 3, 1);
INSERT INTO `city` VALUES (332012, '石渠县', 332000, 'S,DQX', 'S', 3, 1);
INSERT INTO `city` VALUES (332013, '色达县', 332000, 'SDX', 'S', 3, 1);
INSERT INTO `city` VALUES (332014, '理塘县', 332000, 'LTX', 'L', 3, 1);
INSERT INTO `city` VALUES (332015, '巴塘县', 332000, 'BTX', 'B', 3, 1);
INSERT INTO `city` VALUES (332016, '乡城县', 332000, 'XCX', 'X', 3, 1);
INSERT INTO `city` VALUES (332017, '稻城县', 332000, 'DCX', 'D', 3, 1);
INSERT INTO `city` VALUES (332018, '得荣县', 332000, 'DRX', 'D', 3, 1);
INSERT INTO `city` VALUES (332100, '凉山彝族自治州', 330000, 'LSYZZZZ', 'L', 2, 1);
INSERT INTO `city` VALUES (332101, '西昌市', 332100, 'XCS', 'X', 3, 1);
INSERT INTO `city` VALUES (332102, '会理市', 332100, 'K,HLS', 'K', 3, 1);
INSERT INTO `city` VALUES (332103, '木里藏族自治县', 332100, 'MLZ,CZZZX', 'M', 3, 1);
INSERT INTO `city` VALUES (332104, '盐源县', 332100, 'YYX', 'Y', 3, 1);
INSERT INTO `city` VALUES (332105, '德昌县', 332100, 'DCX', 'D', 3, 1);
INSERT INTO `city` VALUES (332106, '会东县', 332100, 'K,HDX', 'K', 3, 1);
INSERT INTO `city` VALUES (332107, '宁南县', 332100, 'NNX', 'N', 3, 1);
INSERT INTO `city` VALUES (332108, '普格县', 332100, 'PGX', 'P', 3, 1);
INSERT INTO `city` VALUES (332109, '布拖县', 332100, 'BTX', 'B', 3, 1);
INSERT INTO `city` VALUES (332110, '金阳县', 332100, 'JYX', 'J', 3, 1);
INSERT INTO `city` VALUES (332111, '昭觉县', 332100, 'ZJX', 'Z', 3, 1);
INSERT INTO `city` VALUES (332112, '喜德县', 332100, 'XDX', 'X', 3, 1);
INSERT INTO `city` VALUES (332113, '冕宁县', 332100, 'MNX', 'M', 3, 1);
INSERT INTO `city` VALUES (332114, '越西县', 332100, 'YXX', 'Y', 3, 1);
INSERT INTO `city` VALUES (332115, '甘洛县', 332100, 'GLX', 'G', 3, 1);
INSERT INTO `city` VALUES (332116, '美姑县', 332100, 'MGX', 'M', 3, 1);
INSERT INTO `city` VALUES (332117, '雷波县', 332100, 'LBX', 'L', 3, 1);
INSERT INTO `city` VALUES (340000, '贵州省', 0, 'GZS,X', 'G', 1, 1);
INSERT INTO `city` VALUES (340100, '贵阳市', 340000, 'GYS', 'G', 2, 1);
INSERT INTO `city` VALUES (340101, '南明区', 340100, 'NMQ,O', 'N', 3, 1);
INSERT INTO `city` VALUES (340102, '云岩区', 340100, 'YYQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (340103, '花溪区', 340100, 'HXQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (340104, '乌当区', 340100, 'WDQ,O', 'W', 3, 1);
INSERT INTO `city` VALUES (340105, '白云区', 340100, 'BYQ,O', 'B', 3, 1);
INSERT INTO `city` VALUES (340106, '观山湖区', 340100, 'GSHQ,O', 'G', 3, 1);
INSERT INTO `city` VALUES (340107, '开阳县', 340100, 'KYX', 'K', 3, 1);
INSERT INTO `city` VALUES (340108, '息烽县', 340100, 'XFX', 'X', 3, 1);
INSERT INTO `city` VALUES (340109, '修文县', 340100, 'XWX', 'X', 3, 1);
INSERT INTO `city` VALUES (340110, '清镇市', 340100, 'QZS', 'Q', 3, 1);
INSERT INTO `city` VALUES (340200, '六盘水市', 340000, 'LPSS', 'L', 2, 1);
INSERT INTO `city` VALUES (340201, '钟山区', 340200, 'ZSQ,O', 'Z', 3, 1);
INSERT INTO `city` VALUES (340202, '六枝特区', 340200, 'LZTQ,O', 'L', 3, 1);
INSERT INTO `city` VALUES (340203, '水城区', 340200, 'SCQ,O', 'S', 3, 1);
INSERT INTO `city` VALUES (340204, '盘州市', 340200, 'PZS', 'P', 3, 1);
INSERT INTO `city` VALUES (340300, '遵义市', 340000, 'ZYS', 'Z', 2, 1);
INSERT INTO `city` VALUES (340301, '红花岗区', 340300, 'H,GHGQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (340302, '汇川区', 340300, 'HCQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (340303, '播州区', 340300, 'BZQ,O', 'B', 3, 1);
INSERT INTO `city` VALUES (340304, '桐梓县', 340300, 'TZX', 'T', 3, 1);
INSERT INTO `city` VALUES (340305, '绥阳县', 340300, 'SYX', 'S', 3, 1);
INSERT INTO `city` VALUES (340306, '正安县', 340300, 'ZAX', 'Z', 3, 1);
INSERT INTO `city` VALUES (340307, '道真仡佬族苗族自治县', 340300, 'DZY,GLZMZZZX', 'D', 3, 1);
INSERT INTO `city` VALUES (340308, '务川仡佬族苗族自治县', 340300, 'WCY,GLZMZZZX', 'W', 3, 1);
INSERT INTO `city` VALUES (340309, '凤冈县', 340300, 'FGX', 'F', 3, 1);
INSERT INTO `city` VALUES (340310, '湄潭县', 340300, 'MTX', 'M', 3, 1);
INSERT INTO `city` VALUES (340311, '余庆县', 340300, 'YQX', 'Y', 3, 1);
INSERT INTO `city` VALUES (340312, '习水县', 340300, 'XSX', 'X', 3, 1);
INSERT INTO `city` VALUES (340313, '赤水市', 340300, 'CSS', 'C', 3, 1);
INSERT INTO `city` VALUES (340314, '仁怀市', 340300, 'RHS', 'R', 3, 1);
INSERT INTO `city` VALUES (340400, '安顺市', 340000, 'ASS', 'A', 2, 1);
INSERT INTO `city` VALUES (340401, '西秀区', 340400, 'XXQ,O', 'X', 3, 1);
INSERT INTO `city` VALUES (340402, '平坝区', 340400, 'PBQ,O', 'P', 3, 1);
INSERT INTO `city` VALUES (340403, '普定县', 340400, 'PDX', 'P', 3, 1);
INSERT INTO `city` VALUES (340404, '镇宁布依族苗族自治县', 340400, 'ZNBYZMZZZX', 'Z', 3, 1);
INSERT INTO `city` VALUES (340405, '关岭布依族苗族自治县', 340400, 'GLBYZMZZZX', 'G', 3, 1);
INSERT INTO `city` VALUES (340406, '紫云苗族布依族自治县', 340400, 'ZYMZBYZZZX', 'Z', 3, 1);
INSERT INTO `city` VALUES (340500, '毕节市', 340000, 'BJS', 'B', 2, 1);
INSERT INTO `city` VALUES (340501, '七星关区', 340500, 'QXGQ,O', 'Q', 3, 1);
INSERT INTO `city` VALUES (340502, '大方县', 340500, 'DFX', 'D', 3, 1);
INSERT INTO `city` VALUES (340503, '金沙县', 340500, 'JSX', 'J', 3, 1);
INSERT INTO `city` VALUES (340504, '织金县', 340500, 'ZJX', 'Z', 3, 1);
INSERT INTO `city` VALUES (340505, '纳雍县', 340500, 'NYX', 'N', 3, 1);
INSERT INTO `city` VALUES (340506, '威宁彝族回族苗族自治县', 340500, 'WNYZHZMZZZX', 'W', 3, 1);
INSERT INTO `city` VALUES (340507, '赫章县', 340500, 'HZX', 'H', 3, 1);
INSERT INTO `city` VALUES (340508, '黔西市', 340500, 'QXS', 'Q', 3, 1);
INSERT INTO `city` VALUES (340600, '铜仁市', 340000, 'TRS', 'T', 2, 1);
INSERT INTO `city` VALUES (340601, '碧江区', 340600, 'BJQ,O', 'B', 3, 1);
INSERT INTO `city` VALUES (340602, '万山区', 340600, 'W,MSQ,O', 'W', 3, 1);
INSERT INTO `city` VALUES (340603, '江口县', 340600, 'JKX', 'J', 3, 1);
INSERT INTO `city` VALUES (340604, '玉屏侗族自治县', 340600, 'YP,BT,DZZZX', 'Y', 3, 1);
INSERT INTO `city` VALUES (340605, '石阡县', 340600, 'S,DQX', 'S', 3, 1);
INSERT INTO `city` VALUES (340606, '思南县', 340600, 'SNX', 'S', 3, 1);
INSERT INTO `city` VALUES (340607, '印江土家族苗族自治县', 340600, 'YJTJZMZZZX', 'Y', 3, 1);
INSERT INTO `city` VALUES (340608, '德江县', 340600, 'DJX', 'D', 3, 1);
INSERT INTO `city` VALUES (340609, '沿河土家族自治县', 340600, 'YHTJZZZX', 'Y', 3, 1);
INSERT INTO `city` VALUES (340610, '松桃苗族自治县', 340600, 'STMZZZX', 'S', 3, 1);
INSERT INTO `city` VALUES (340700, '黔西南布依族苗族自治州', 340000, 'QXNBYZMZZZZ', 'Q', 2, 1);
INSERT INTO `city` VALUES (340701, '兴义市', 340700, 'XYS', 'X', 3, 1);
INSERT INTO `city` VALUES (340702, '兴仁市', 340700, 'XRS', 'X', 3, 1);
INSERT INTO `city` VALUES (340703, '普安县', 340700, 'PAX', 'P', 3, 1);
INSERT INTO `city` VALUES (340704, '晴隆县', 340700, 'QLX', 'Q', 3, 1);
INSERT INTO `city` VALUES (340705, '贞丰县', 340700, 'ZFX', 'Z', 3, 1);
INSERT INTO `city` VALUES (340706, '望谟县', 340700, 'WMX', 'W', 3, 1);
INSERT INTO `city` VALUES (340707, '册亨县', 340700, 'CHX', 'C', 3, 1);
INSERT INTO `city` VALUES (340708, '安龙县', 340700, 'ALX', 'A', 3, 1);
INSERT INTO `city` VALUES (340800, '黔东南苗族侗族自治州', 340000, 'QDNMZT,DZZZZ', 'Q', 2, 1);
INSERT INTO `city` VALUES (340801, '凯里市', 340800, 'KLS', 'K', 3, 1);
INSERT INTO `city` VALUES (340802, '黄平县', 340800, 'HPX', 'H', 3, 1);
INSERT INTO `city` VALUES (340803, '施秉县', 340800, 'SBX', 'S', 3, 1);
INSERT INTO `city` VALUES (340804, '三穗县', 340800, 'SSX', 'S', 3, 1);
INSERT INTO `city` VALUES (340805, '镇远县', 340800, 'ZYX', 'Z', 3, 1);
INSERT INTO `city` VALUES (340806, '岑巩县', 340800, 'CGX', 'C', 3, 1);
INSERT INTO `city` VALUES (340807, '天柱县', 340800, 'TZX', 'T', 3, 1);
INSERT INTO `city` VALUES (340808, '锦屏县', 340800, 'JP,BX', 'J', 3, 1);
INSERT INTO `city` VALUES (340809, '剑河县', 340800, 'JHX', 'J', 3, 1);
INSERT INTO `city` VALUES (340810, '台江县', 340800, 'TJX', 'T', 3, 1);
INSERT INTO `city` VALUES (340811, '黎平县', 340800, 'LPX', 'L', 3, 1);
INSERT INTO `city` VALUES (340812, '榕江县', 340800, 'RJX', 'R', 3, 1);
INSERT INTO `city` VALUES (340813, '从江县', 340800, 'CJX', 'C', 3, 1);
INSERT INTO `city` VALUES (340814, '雷山县', 340800, 'LSX', 'L', 3, 1);
INSERT INTO `city` VALUES (340815, '麻江县', 340800, 'MJX', 'M', 3, 1);
INSERT INTO `city` VALUES (340816, '丹寨县', 340800, 'DZX', 'D', 3, 1);
INSERT INTO `city` VALUES (340900, '黔南布依族苗族自治州', 340000, 'QNBYZMZZZZ', 'Q', 2, 1);
INSERT INTO `city` VALUES (340901, '都匀市', 340900, 'DYS', 'D', 3, 1);
INSERT INTO `city` VALUES (340902, '福泉市', 340900, 'FQS', 'F', 3, 1);
INSERT INTO `city` VALUES (340903, '荔波县', 340900, 'LBX', 'L', 3, 1);
INSERT INTO `city` VALUES (340904, '贵定县', 340900, 'GDX', 'G', 3, 1);
INSERT INTO `city` VALUES (340905, '瓮安县', 340900, 'WAX', 'W', 3, 1);
INSERT INTO `city` VALUES (340906, '独山县', 340900, 'DSX', 'D', 3, 1);
INSERT INTO `city` VALUES (340907, '平塘县', 340900, 'PTX', 'P', 3, 1);
INSERT INTO `city` VALUES (340908, '罗甸县', 340900, 'LDX', 'L', 3, 1);
INSERT INTO `city` VALUES (340909, '长顺县', 340900, 'Z,CSX', 'Z', 3, 1);
INSERT INTO `city` VALUES (340910, '龙里县', 340900, 'LLX', 'L', 3, 1);
INSERT INTO `city` VALUES (340911, '惠水县', 340900, 'HSX', 'H', 3, 1);
INSERT INTO `city` VALUES (340912, '三都水族自治县', 340900, 'SDSZZZX', 'S', 3, 1);
INSERT INTO `city` VALUES (350000, '云南省', 0, 'YNS,X', 'Y', 1, 1);
INSERT INTO `city` VALUES (350100, '昆明市', 350000, 'KMS', 'K', 2, 1);
INSERT INTO `city` VALUES (350101, '五华区', 350100, 'WHQ,O', 'W', 3, 1);
INSERT INTO `city` VALUES (350102, '盘龙区', 350100, 'PLQ,O', 'P', 3, 1);
INSERT INTO `city` VALUES (350103, '官渡区', 350100, 'GDQ,O', 'G', 3, 1);
INSERT INTO `city` VALUES (350104, '西山区', 350100, 'XSQ,O', 'X', 3, 1);
INSERT INTO `city` VALUES (350105, '东川区', 350100, 'DCQ,O', 'D', 3, 1);
INSERT INTO `city` VALUES (350106, '呈贡区', 350100, 'CGQ,O', 'C', 3, 1);
INSERT INTO `city` VALUES (350107, '晋宁区', 350100, 'JNQ,O', 'J', 3, 1);
INSERT INTO `city` VALUES (350108, '富民县', 350100, 'FMX', 'F', 3, 1);
INSERT INTO `city` VALUES (350109, '宜良县', 350100, 'YLX', 'Y', 3, 1);
INSERT INTO `city` VALUES (350110, '石林彝族自治县', 350100, 'S,DLYZZZX', 'S', 3, 1);
INSERT INTO `city` VALUES (350111, '嵩明县', 350100, 'SMX', 'S', 3, 1);
INSERT INTO `city` VALUES (350112, '禄劝彝族苗族自治县', 350100, 'LQYZMZZZX', 'L', 3, 1);
INSERT INTO `city` VALUES (350113, '寻甸回族彝族自治县', 350100, 'XDHZYZZZX', 'X', 3, 1);
INSERT INTO `city` VALUES (350114, '安宁市', 350100, 'ANS', 'A', 3, 1);
INSERT INTO `city` VALUES (350200, '曲靖市', 350000, 'QJS', 'Q', 2, 1);
INSERT INTO `city` VALUES (350201, '麒麟区', 350200, 'QLQ,O', 'Q', 3, 1);
INSERT INTO `city` VALUES (350202, '沾益区', 350200, 'ZYQ,O', 'Z', 3, 1);
INSERT INTO `city` VALUES (350203, '马龙区', 350200, 'MLQ,O', 'M', 3, 1);
INSERT INTO `city` VALUES (350204, '陆良县', 350200, 'LLX', 'L', 3, 1);
INSERT INTO `city` VALUES (350205, '师宗县', 350200, 'SZX', 'S', 3, 1);
INSERT INTO `city` VALUES (350206, '罗平县', 350200, 'LPX', 'L', 3, 1);
INSERT INTO `city` VALUES (350207, '富源县', 350200, 'FYX', 'F', 3, 1);
INSERT INTO `city` VALUES (350208, '会泽县', 350200, 'K,HZX', 'K', 3, 1);
INSERT INTO `city` VALUES (350209, '宣威市', 350200, 'XWS', 'X', 3, 1);
INSERT INTO `city` VALUES (350300, '玉溪市', 350000, 'YXS', 'Y', 2, 1);
INSERT INTO `city` VALUES (350301, '红塔区', 350300, 'H,GT,DQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (350302, '江川区', 350300, 'JCQ,O', 'J', 3, 1);
INSERT INTO `city` VALUES (350303, '通海县', 350300, 'THX', 'T', 3, 1);
INSERT INTO `city` VALUES (350304, '华宁县', 350300, 'HNX', 'H', 3, 1);
INSERT INTO `city` VALUES (350305, '易门县', 350300, 'YMX', 'Y', 3, 1);
INSERT INTO `city` VALUES (350306, '峨山彝族自治县', 350300, 'ESYZZZX', 'E', 3, 1);
INSERT INTO `city` VALUES (350307, '新平彝族傣族自治县', 350300, 'XPYZDZZZX', 'X', 3, 1);
INSERT INTO `city` VALUES (350308, '元江哈尼族彝族傣族自治县', 350300, 'YJHNZYZDZZZX', 'Y', 3, 1);
INSERT INTO `city` VALUES (350309, '澄江市', 350300, 'D,CJS', 'D', 3, 1);
INSERT INTO `city` VALUES (350400, '保山市', 350000, 'BSS', 'B', 2, 1);
INSERT INTO `city` VALUES (350401, '隆阳区', 350400, 'LYQ,O', 'L', 3, 1);
INSERT INTO `city` VALUES (350402, '施甸县', 350400, 'SDX', 'S', 3, 1);
INSERT INTO `city` VALUES (350403, '龙陵县', 350400, 'LLX', 'L', 3, 1);
INSERT INTO `city` VALUES (350404, '昌宁县', 350400, 'CNX', 'C', 3, 1);
INSERT INTO `city` VALUES (350405, '腾冲市', 350400, 'TCS', 'T', 3, 1);
INSERT INTO `city` VALUES (350500, '昭通市', 350000, 'ZTS', 'Z', 2, 1);
INSERT INTO `city` VALUES (350501, '昭阳区', 350500, 'ZYQ,O', 'Z', 3, 1);
INSERT INTO `city` VALUES (350502, '鲁甸县', 350500, 'LDX', 'L', 3, 1);
INSERT INTO `city` VALUES (350503, '巧家县', 350500, 'QJX', 'Q', 3, 1);
INSERT INTO `city` VALUES (350504, '盐津县', 350500, 'YJX', 'Y', 3, 1);
INSERT INTO `city` VALUES (350505, '大关县', 350500, 'DGX', 'D', 3, 1);
INSERT INTO `city` VALUES (350506, '永善县', 350500, 'YSX', 'Y', 3, 1);
INSERT INTO `city` VALUES (350507, '绥江县', 350500, 'SJX', 'S', 3, 1);
INSERT INTO `city` VALUES (350508, '镇雄县', 350500, 'ZXX', 'Z', 3, 1);
INSERT INTO `city` VALUES (350509, '彝良县', 350500, 'YLX', 'Y', 3, 1);
INSERT INTO `city` VALUES (350510, '威信县', 350500, 'WXX', 'W', 3, 1);
INSERT INTO `city` VALUES (350511, '水富市', 350500, 'SFS', 'S', 3, 1);
INSERT INTO `city` VALUES (350600, '丽江市', 350000, 'LJS', 'L', 2, 1);
INSERT INTO `city` VALUES (350601, '古城区', 350600, 'GCQ,O', 'G', 3, 1);
INSERT INTO `city` VALUES (350602, '玉龙纳西族自治县', 350600, 'YLNXZZZX', 'Y', 3, 1);
INSERT INTO `city` VALUES (350603, '永胜县', 350600, 'YSX', 'Y', 3, 1);
INSERT INTO `city` VALUES (350604, '华坪县', 350600, 'HPX', 'H', 3, 1);
INSERT INTO `city` VALUES (350605, '宁蒗彝族自治县', 350600, 'NLYZZZX', 'N', 3, 1);
INSERT INTO `city` VALUES (350700, '普洱市', 350000, 'PES', 'P', 2, 1);
INSERT INTO `city` VALUES (350701, '思茅区', 350700, 'SMQ,O', 'S', 3, 1);
INSERT INTO `city` VALUES (350702, '宁洱哈尼族彝族自治县', 350700, 'NEHNZYZZZX', 'N', 3, 1);
INSERT INTO `city` VALUES (350703, '墨江哈尼族自治县', 350700, 'MJHNZZZX', 'M', 3, 1);
INSERT INTO `city` VALUES (350704, '景东彝族自治县', 350700, 'JDYZZZX', 'J', 3, 1);
INSERT INTO `city` VALUES (350705, '景谷傣族彝族自治县', 350700, 'JY,GDZYZZZX', 'J', 3, 1);
INSERT INTO `city` VALUES (350706, '镇沅彝族哈尼族拉祜族自治县', 350700, 'ZYYZHNZLHZZZX', 'Z', 3, 1);
INSERT INTO `city` VALUES (350707, '江城哈尼族彝族自治县', 350700, 'JCHNZYZZZX', 'J', 3, 1);
INSERT INTO `city` VALUES (350708, '孟连傣族拉祜族佤族自治县', 350700, 'MLDZLHZWZZZX', 'M', 3, 1);
INSERT INTO `city` VALUES (350709, '澜沧拉祜族自治县', 350700, 'LCLHZZZX', 'L', 3, 1);
INSERT INTO `city` VALUES (350710, '西盟佤族自治县', 350700, 'XMWZZZX', 'X', 3, 1);
INSERT INTO `city` VALUES (350800, '临沧市', 350000, 'LCS', 'L', 2, 1);
INSERT INTO `city` VALUES (350801, '临翔区', 350800, 'LXQ,O', 'L', 3, 1);
INSERT INTO `city` VALUES (350802, '凤庆县', 350800, 'FQX', 'F', 3, 1);
INSERT INTO `city` VALUES (350803, '云县', 350800, 'YX', 'Y', 3, 1);
INSERT INTO `city` VALUES (350804, '永德县', 350800, 'YDX', 'Y', 3, 1);
INSERT INTO `city` VALUES (350805, '镇康县', 350800, 'ZKX', 'Z', 3, 1);
INSERT INTO `city` VALUES (350806, '双江拉祜族佤族布朗族傣族自治县', 350800, 'SJLHZWZBLZDZZZX', 'S', 3, 1);
INSERT INTO `city` VALUES (350807, '耿马傣族佤族自治县', 350800, 'GMDZWZZZX', 'G', 3, 1);
INSERT INTO `city` VALUES (350808, '沧源佤族自治县', 350800, 'CYWZZZX', 'C', 3, 1);
INSERT INTO `city` VALUES (350900, '楚雄彝族自治州', 350000, 'CXYZZZZ', 'C', 2, 1);
INSERT INTO `city` VALUES (350901, '楚雄市', 350900, 'CXS', 'C', 3, 1);
INSERT INTO `city` VALUES (350902, '禄丰市', 350900, 'LFS', 'L', 3, 1);
INSERT INTO `city` VALUES (350903, '双柏县', 350900, 'SBX', 'S', 3, 1);
INSERT INTO `city` VALUES (350904, '牟定县', 350900, 'MDX', 'M', 3, 1);
INSERT INTO `city` VALUES (350905, '南华县', 350900, 'NHX', 'N', 3, 1);
INSERT INTO `city` VALUES (350906, '姚安县', 350900, 'YAX', 'Y', 3, 1);
INSERT INTO `city` VALUES (350907, '大姚县', 350900, 'DYX', 'D', 3, 1);
INSERT INTO `city` VALUES (350908, '永仁县', 350900, 'YRX', 'Y', 3, 1);
INSERT INTO `city` VALUES (350909, '元谋县', 350900, 'YMX', 'Y', 3, 1);
INSERT INTO `city` VALUES (350910, '武定县', 350900, 'WDX', 'W', 3, 1);
INSERT INTO `city` VALUES (351000, '红河哈尼族彝族自治州', 350000, 'H,GHHNZYZZZZ', 'H', 2, 1);
INSERT INTO `city` VALUES (351001, '个旧市', 351000, 'GJS', 'G', 3, 1);
INSERT INTO `city` VALUES (351002, '开远市', 351000, 'KYS', 'K', 3, 1);
INSERT INTO `city` VALUES (351003, '蒙自市', 351000, 'MZS', 'M', 3, 1);
INSERT INTO `city` VALUES (351004, '弥勒市', 351000, 'MLS', 'M', 3, 1);
INSERT INTO `city` VALUES (351005, '屏边苗族自治县', 351000, 'P,BBMZZZX', 'P', 3, 1);
INSERT INTO `city` VALUES (351006, '建水县', 351000, 'JSX', 'J', 3, 1);
INSERT INTO `city` VALUES (351007, '石屏县', 351000, 'S,DP,BX', 'S', 3, 1);
INSERT INTO `city` VALUES (351008, '泸西县', 351000, 'LXX', 'L', 3, 1);
INSERT INTO `city` VALUES (351009, '元阳县', 351000, 'YYX', 'Y', 3, 1);
INSERT INTO `city` VALUES (351010, '红河县', 351000, 'H,GHX', 'H', 3, 1);
INSERT INTO `city` VALUES (351011, '金平苗族瑶族傣族自治县', 351000, 'JPMZYZDZZZX', 'J', 3, 1);
INSERT INTO `city` VALUES (351012, '绿春县', 351000, 'LCX', 'L', 3, 1);
INSERT INTO `city` VALUES (351013, '河口瑶族自治县', 351000, 'HKYZZZX', 'H', 3, 1);
INSERT INTO `city` VALUES (351100, '文山壮族苗族自治州', 350000, 'WSZZMZZZZ', 'W', 2, 1);
INSERT INTO `city` VALUES (351101, '文山市', 351100, 'WSS', 'W', 3, 1);
INSERT INTO `city` VALUES (351102, '砚山县', 351100, 'YSX', 'Y', 3, 1);
INSERT INTO `city` VALUES (351103, '西畴县', 351100, 'XCX', 'X', 3, 1);
INSERT INTO `city` VALUES (351104, '麻栗坡县', 351100, 'MLPX', 'M', 3, 1);
INSERT INTO `city` VALUES (351105, '马关县', 351100, 'MGX', 'M', 3, 1);
INSERT INTO `city` VALUES (351106, '丘北县', 351100, 'QBX', 'Q', 3, 1);
INSERT INTO `city` VALUES (351107, '广南县', 351100, 'G,ANX', 'G', 3, 1);
INSERT INTO `city` VALUES (351108, '富宁县', 351100, 'FNX', 'F', 3, 1);
INSERT INTO `city` VALUES (351200, '西双版纳傣族自治州', 350000, 'XSBNDZZZZ', 'X', 2, 1);
INSERT INTO `city` VALUES (351201, '景洪市', 351200, 'JHS', 'J', 3, 1);
INSERT INTO `city` VALUES (351202, '勐海县', 351200, 'MHX', 'M', 3, 1);
INSERT INTO `city` VALUES (351203, '勐腊县', 351200, 'MX,LX', 'M', 3, 1);
INSERT INTO `city` VALUES (351300, '大理白族自治州', 350000, 'DLBZZZZ', 'D', 2, 1);
INSERT INTO `city` VALUES (351301, '大理市', 351300, 'DLS', 'D', 3, 1);
INSERT INTO `city` VALUES (351302, '漾濞彝族自治县', 351300, 'YBYZZZX', 'Y', 3, 1);
INSERT INTO `city` VALUES (351303, '祥云县', 351300, 'XYX', 'X', 3, 1);
INSERT INTO `city` VALUES (351304, '宾川县', 351300, 'BCX', 'B', 3, 1);
INSERT INTO `city` VALUES (351305, '弥渡县', 351300, 'MDX', 'M', 3, 1);
INSERT INTO `city` VALUES (351306, '南涧彝族自治县', 351300, 'NJYZZZX', 'N', 3, 1);
INSERT INTO `city` VALUES (351307, '巍山彝族回族自治县', 351300, 'WSYZHZZZX', 'W', 3, 1);
INSERT INTO `city` VALUES (351308, '永平县', 351300, 'YPX', 'Y', 3, 1);
INSERT INTO `city` VALUES (351309, '云龙县', 351300, 'YLX', 'Y', 3, 1);
INSERT INTO `city` VALUES (351310, '洱源县', 351300, 'EYX', 'E', 3, 1);
INSERT INTO `city` VALUES (351311, '剑川县', 351300, 'JCX', 'J', 3, 1);
INSERT INTO `city` VALUES (351312, '鹤庆县', 351300, 'HQX', 'H', 3, 1);
INSERT INTO `city` VALUES (351400, '德宏傣族景颇族自治州', 350000, 'DHDZJPZZZZ', 'D', 2, 1);
INSERT INTO `city` VALUES (351401, '瑞丽市', 351400, 'RLS', 'R', 3, 1);
INSERT INTO `city` VALUES (351402, '芒市', 351400, 'W,MS', 'W', 3, 1);
INSERT INTO `city` VALUES (351403, '梁河县', 351400, 'LHX', 'L', 3, 1);
INSERT INTO `city` VALUES (351404, '盈江县', 351400, 'YJX', 'Y', 3, 1);
INSERT INTO `city` VALUES (351405, '陇川县', 351400, 'LCX', 'L', 3, 1);
INSERT INTO `city` VALUES (351500, '怒江傈僳族自治州', 350000, 'NJLSZZZZ', 'N', 2, 1);
INSERT INTO `city` VALUES (351501, '泸水市', 351500, 'LSS', 'L', 3, 1);
INSERT INTO `city` VALUES (351502, '福贡县', 351500, 'FGX', 'F', 3, 1);
INSERT INTO `city` VALUES (351503, '贡山独龙族怒族自治县', 351500, 'GSDLZNZZZX', 'G', 3, 1);
INSERT INTO `city` VALUES (351504, '兰坪白族普米族自治县', 351500, 'LPBZPMZZZX', 'L', 3, 1);
INSERT INTO `city` VALUES (351600, '迪庆藏族自治州', 350000, 'DQZ,CZZZZ', 'D', 2, 1);
INSERT INTO `city` VALUES (351601, '香格里拉市', 351600, 'XGLLS', 'X', 3, 1);
INSERT INTO `city` VALUES (351602, '德钦县', 351600, 'DQX', 'D', 3, 1);
INSERT INTO `city` VALUES (351603, '维西傈僳族自治县', 351600, 'WXLSZZZX', 'W', 3, 1);
INSERT INTO `city` VALUES (360000, '西藏自治区', 0, 'XZ,CZZQ,O', 'X', 1, 1);
INSERT INTO `city` VALUES (360100, '拉萨市', 360000, 'LSS', 'L', 2, 1);
INSERT INTO `city` VALUES (360101, '城关区', 360100, 'CGQ,O', 'C', 3, 1);
INSERT INTO `city` VALUES (360102, '堆龙德庆区', 360100, 'Z,DLDQQ,O', 'Z', 3, 1);
INSERT INTO `city` VALUES (360103, '达孜区', 360100, 'DZQ,O', 'D', 3, 1);
INSERT INTO `city` VALUES (360104, '林周县', 360100, 'LZX', 'L', 3, 1);
INSERT INTO `city` VALUES (360105, '当雄县', 360100, 'DXX', 'D', 3, 1);
INSERT INTO `city` VALUES (360106, '尼木县', 360100, 'NMX', 'N', 3, 1);
INSERT INTO `city` VALUES (360107, '曲水县', 360100, 'QSX', 'Q', 3, 1);
INSERT INTO `city` VALUES (360108, '墨竹工卡县', 360100, 'MZGQ,KX', 'M', 3, 1);
INSERT INTO `city` VALUES (360200, '日喀则市', 360000, 'RKZS', 'R', 2, 1);
INSERT INTO `city` VALUES (360201, '桑珠孜区', 360200, 'SZZQ,O', 'S', 3, 1);
INSERT INTO `city` VALUES (360202, '南木林县', 360200, 'NMLX', 'N', 3, 1);
INSERT INTO `city` VALUES (360203, '江孜县', 360200, 'JZX', 'J', 3, 1);
INSERT INTO `city` VALUES (360204, '定日县', 360200, 'DRX', 'D', 3, 1);
INSERT INTO `city` VALUES (360205, '萨迦县', 360200, 'SJX', 'S', 3, 1);
INSERT INTO `city` VALUES (360206, '拉孜县', 360200, 'LZX', 'L', 3, 1);
INSERT INTO `city` VALUES (360207, '昂仁县', 360200, 'ARX', 'A', 3, 1);
INSERT INTO `city` VALUES (360208, '谢通门县', 360200, 'XTMX', 'X', 3, 1);
INSERT INTO `city` VALUES (360209, '白朗县', 360200, 'BLX', 'B', 3, 1);
INSERT INTO `city` VALUES (360210, '仁布县', 360200, 'RBX', 'R', 3, 1);
INSERT INTO `city` VALUES (360211, '康马县', 360200, 'KMX', 'K', 3, 1);
INSERT INTO `city` VALUES (360212, '定结县', 360200, 'DJX', 'D', 3, 1);
INSERT INTO `city` VALUES (360213, '仲巴县', 360200, 'ZBX', 'Z', 3, 1);
INSERT INTO `city` VALUES (360214, '亚东县', 360200, 'YDX', 'Y', 3, 1);
INSERT INTO `city` VALUES (360215, '吉隆县', 360200, 'JLX', 'J', 3, 1);
INSERT INTO `city` VALUES (360216, '聂拉木县', 360200, 'NLMX', 'N', 3, 1);
INSERT INTO `city` VALUES (360217, '萨嘎县', 360200, 'SGX', 'S', 3, 1);
INSERT INTO `city` VALUES (360218, '岗巴县', 360200, 'GBX', 'G', 3, 1);
INSERT INTO `city` VALUES (360300, '昌都市', 360000, 'CDS', 'C', 2, 1);
INSERT INTO `city` VALUES (360301, '卡若区', 360300, 'Q,KRQ,O', 'Q', 3, 1);
INSERT INTO `city` VALUES (360302, '江达县', 360300, 'JDX', 'J', 3, 1);
INSERT INTO `city` VALUES (360303, '贡觉县', 360300, 'GJX', 'G', 3, 1);
INSERT INTO `city` VALUES (360304, '类乌齐县', 360300, 'LWQ,JX', 'L', 3, 1);
INSERT INTO `city` VALUES (360305, '丁青县', 360300, 'D,ZQX', 'D', 3, 1);
INSERT INTO `city` VALUES (360306, '察雅县', 360300, 'CYX', 'C', 3, 1);
INSERT INTO `city` VALUES (360307, '八宿县', 360300, 'BS,XX', 'B', 3, 1);
INSERT INTO `city` VALUES (360308, '左贡县', 360300, 'ZGX', 'Z', 3, 1);
INSERT INTO `city` VALUES (360309, '芒康县', 360300, 'W,MKX', 'W', 3, 1);
INSERT INTO `city` VALUES (360310, '洛隆县', 360300, 'LLX', 'L', 3, 1);
INSERT INTO `city` VALUES (360311, '边坝县', 360300, 'BBX', 'B', 3, 1);
INSERT INTO `city` VALUES (360400, '林芝市', 360000, 'LZS', 'L', 2, 1);
INSERT INTO `city` VALUES (360401, '巴宜区', 360400, 'BYQ,O', 'B', 3, 1);
INSERT INTO `city` VALUES (360402, '工布江达县', 360400, 'GBJDX', 'G', 3, 1);
INSERT INTO `city` VALUES (360403, '米林县', 360400, 'MLX', 'M', 3, 1);
INSERT INTO `city` VALUES (360404, '墨脱县', 360400, 'MTX', 'M', 3, 1);
INSERT INTO `city` VALUES (360405, '波密县', 360400, 'BMX', 'B', 3, 1);
INSERT INTO `city` VALUES (360406, '察隅县', 360400, 'CYX', 'C', 3, 1);
INSERT INTO `city` VALUES (360407, '朗县', 360400, 'LX', 'L', 3, 1);
INSERT INTO `city` VALUES (360500, '山南市', 360000, 'SNS', 'S', 2, 1);
INSERT INTO `city` VALUES (360501, '乃东区', 360500, 'NDQ,O', 'N', 3, 1);
INSERT INTO `city` VALUES (360502, '扎囊县', 360500, 'ZNX', 'Z', 3, 1);
INSERT INTO `city` VALUES (360503, '贡嘎县', 360500, 'GGX', 'G', 3, 1);
INSERT INTO `city` VALUES (360504, '桑日县', 360500, 'SRX', 'S', 3, 1);
INSERT INTO `city` VALUES (360505, '琼结县', 360500, 'QJX', 'Q', 3, 1);
INSERT INTO `city` VALUES (360506, '曲松县', 360500, 'QSX', 'Q', 3, 1);
INSERT INTO `city` VALUES (360507, '措美县', 360500, 'CMX', 'C', 3, 1);
INSERT INTO `city` VALUES (360508, '洛扎县', 360500, 'LZX', 'L', 3, 1);
INSERT INTO `city` VALUES (360509, '加查县', 360500, 'JC,ZX', 'J', 3, 1);
INSERT INTO `city` VALUES (360510, '隆子县', 360500, 'LZX', 'L', 3, 1);
INSERT INTO `city` VALUES (360511, '错那县', 360500, 'CNX', 'C', 3, 1);
INSERT INTO `city` VALUES (360512, '浪卡子县', 360500, 'LQ,KZX', 'L', 3, 1);
INSERT INTO `city` VALUES (360600, '那曲市', 360000, 'NQS', 'N', 2, 1);
INSERT INTO `city` VALUES (360601, '色尼区', 360600, 'SNQ,O', 'S', 3, 1);
INSERT INTO `city` VALUES (360602, '嘉黎县', 360600, 'JLX', 'J', 3, 1);
INSERT INTO `city` VALUES (360603, '比如县', 360600, 'BRX', 'B', 3, 1);
INSERT INTO `city` VALUES (360604, '聂荣县', 360600, 'NRX', 'N', 3, 1);
INSERT INTO `city` VALUES (360605, '安多县', 360600, 'ADX', 'A', 3, 1);
INSERT INTO `city` VALUES (360606, '申扎县', 360600, 'SZX', 'S', 3, 1);
INSERT INTO `city` VALUES (360607, '索县', 360600, 'SX', 'S', 3, 1);
INSERT INTO `city` VALUES (360608, '班戈县', 360600, 'BGX', 'B', 3, 1);
INSERT INTO `city` VALUES (360609, '巴青县', 360600, 'BQX', 'B', 3, 1);
INSERT INTO `city` VALUES (360610, '尼玛县', 360600, 'NMX', 'N', 3, 1);
INSERT INTO `city` VALUES (360611, '双湖县', 360600, 'SHX', 'S', 3, 1);
INSERT INTO `city` VALUES (360700, '阿里地区', 360000, 'A,ELDQ,O', 'A', 2, 1);
INSERT INTO `city` VALUES (360701, '普兰县', 360700, 'PLX', 'P', 3, 1);
INSERT INTO `city` VALUES (360702, '札达县', 360700, 'ZDX', 'Z', 3, 1);
INSERT INTO `city` VALUES (360703, '噶尔县', 360700, 'GEX', 'G', 3, 1);
INSERT INTO `city` VALUES (360704, '日土县', 360700, 'RTX', 'R', 3, 1);
INSERT INTO `city` VALUES (360705, '革吉县', 360700, 'G,JJX', 'G', 3, 1);
INSERT INTO `city` VALUES (360706, '改则县', 360700, 'GZX', 'G', 3, 1);
INSERT INTO `city` VALUES (360707, '措勤县', 360700, 'CQX', 'C', 3, 1);
INSERT INTO `city` VALUES (370000, '陕西省', 0, 'SXS,X', 'S', 1, 1);
INSERT INTO `city` VALUES (370100, '西安市', 370000, 'XAS', 'X', 2, 1);
INSERT INTO `city` VALUES (370101, '新城区', 370100, 'XCQ,O', 'X', 3, 1);
INSERT INTO `city` VALUES (370102, '碑林区', 370100, 'BLQ,O', 'B', 3, 1);
INSERT INTO `city` VALUES (370103, '莲湖区', 370100, 'LHQ,O', 'L', 3, 1);
INSERT INTO `city` VALUES (370104, '灞桥区', 370100, 'BQQ,O', 'B', 3, 1);
INSERT INTO `city` VALUES (370105, '未央区', 370100, 'WYQ,O', 'W', 3, 1);
INSERT INTO `city` VALUES (370106, '雁塔区', 370100, 'YT,DQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (370107, '阎良区', 370100, 'YLQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (370108, '临潼区', 370100, 'LTQ,O', 'L', 3, 1);
INSERT INTO `city` VALUES (370109, '长安区', 370100, 'Z,CAQ,O', 'Z', 3, 1);
INSERT INTO `city` VALUES (370110, '高陵区', 370100, 'GLQ,O', 'G', 3, 1);
INSERT INTO `city` VALUES (370111, '鄠邑区', 370100, 'HYQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (370112, '蓝田县', 370100, 'LTX', 'L', 3, 1);
INSERT INTO `city` VALUES (370113, '周至县', 370100, 'ZZX', 'Z', 3, 1);
INSERT INTO `city` VALUES (370200, '铜川市', 370000, 'TCS', 'T', 2, 1);
INSERT INTO `city` VALUES (370201, '王益区', 370200, 'WYQ,O', 'W', 3, 1);
INSERT INTO `city` VALUES (370202, '印台区', 370200, 'YTQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (370203, '耀州区', 370200, 'YZQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (370204, '宜君县', 370200, 'YJX', 'Y', 3, 1);
INSERT INTO `city` VALUES (370300, '宝鸡市', 370000, 'BJS', 'B', 2, 1);
INSERT INTO `city` VALUES (370301, '渭滨区', 370300, 'WBQ,O', 'W', 3, 1);
INSERT INTO `city` VALUES (370302, '金台区', 370300, 'JTQ,O', 'J', 3, 1);
INSERT INTO `city` VALUES (370303, '陈仓区', 370300, 'CCQ,O', 'C', 3, 1);
INSERT INTO `city` VALUES (370304, '凤翔区', 370300, 'FXQ,O', 'F', 3, 1);
INSERT INTO `city` VALUES (370305, '岐山县', 370300, 'QSX', 'Q', 3, 1);
INSERT INTO `city` VALUES (370306, '扶风县', 370300, 'FFX', 'F', 3, 1);
INSERT INTO `city` VALUES (370307, '眉县', 370300, 'MX', 'M', 3, 1);
INSERT INTO `city` VALUES (370308, '陇县', 370300, 'LX', 'L', 3, 1);
INSERT INTO `city` VALUES (370309, '千阳县', 370300, 'QYX', 'Q', 3, 1);
INSERT INTO `city` VALUES (370310, '麟游县', 370300, 'LYX', 'L', 3, 1);
INSERT INTO `city` VALUES (370311, '凤县', 370300, 'FX', 'F', 3, 1);
INSERT INTO `city` VALUES (370312, '太白县', 370300, 'TBX', 'T', 3, 1);
INSERT INTO `city` VALUES (370400, '咸阳市', 370000, 'XYS', 'X', 2, 1);
INSERT INTO `city` VALUES (370401, '秦都区', 370400, 'QDQ,O', 'Q', 3, 1);
INSERT INTO `city` VALUES (370402, '杨陵区', 370400, 'YLQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (370403, '渭城区', 370400, 'WCQ,O', 'W', 3, 1);
INSERT INTO `city` VALUES (370404, '三原县', 370400, 'SYX', 'S', 3, 1);
INSERT INTO `city` VALUES (370405, '泾阳县', 370400, 'JYX', 'J', 3, 1);
INSERT INTO `city` VALUES (370406, '乾县', 370400, 'Q,GX', 'Q', 3, 1);
INSERT INTO `city` VALUES (370407, '礼泉县', 370400, 'LQX', 'L', 3, 1);
INSERT INTO `city` VALUES (370408, '永寿县', 370400, 'YSX', 'Y', 3, 1);
INSERT INTO `city` VALUES (370409, '长武县', 370400, 'Z,CWX', 'Z', 3, 1);
INSERT INTO `city` VALUES (370410, '旬邑县', 370400, 'XYX', 'X', 3, 1);
INSERT INTO `city` VALUES (370411, '淳化县', 370400, 'CHX', 'C', 3, 1);
INSERT INTO `city` VALUES (370412, '武功县', 370400, 'WGX', 'W', 3, 1);
INSERT INTO `city` VALUES (370413, '兴平市', 370400, 'XPS', 'X', 3, 1);
INSERT INTO `city` VALUES (370414, '彬州市', 370400, 'BZS', 'B', 3, 1);
INSERT INTO `city` VALUES (370500, '渭南市', 370000, 'WNS', 'W', 2, 1);
INSERT INTO `city` VALUES (370501, '临渭区', 370500, 'LWQ,O', 'L', 3, 1);
INSERT INTO `city` VALUES (370502, '华州区', 370500, 'HZQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (370503, '潼关县', 370500, 'TGX', 'T', 3, 1);
INSERT INTO `city` VALUES (370504, '大荔县', 370500, 'DLX', 'D', 3, 1);
INSERT INTO `city` VALUES (370505, '合阳县', 370500, 'H,GYX', 'H', 3, 1);
INSERT INTO `city` VALUES (370506, '澄城县', 370500, 'D,CCX', 'D', 3, 1);
INSERT INTO `city` VALUES (370507, '蒲城县', 370500, 'PCX', 'P', 3, 1);
INSERT INTO `city` VALUES (370508, '白水县', 370500, 'BSX', 'B', 3, 1);
INSERT INTO `city` VALUES (370509, '富平县', 370500, 'FPX', 'F', 3, 1);
INSERT INTO `city` VALUES (370510, '韩城市', 370500, 'HCS', 'H', 3, 1);
INSERT INTO `city` VALUES (370511, '华阴市', 370500, 'HYS', 'H', 3, 1);
INSERT INTO `city` VALUES (370600, '延安市', 370000, 'YAS', 'Y', 2, 1);
INSERT INTO `city` VALUES (370601, '宝塔区', 370600, 'BT,DQ,O', 'B', 3, 1);
INSERT INTO `city` VALUES (370602, '安塞区', 370600, 'ASQ,O', 'A', 3, 1);
INSERT INTO `city` VALUES (370603, '延长县', 370600, 'YZ,CX', 'Y', 3, 1);
INSERT INTO `city` VALUES (370604, '延川县', 370600, 'YCX', 'Y', 3, 1);
INSERT INTO `city` VALUES (370605, '志丹县', 370600, 'ZDX', 'Z', 3, 1);
INSERT INTO `city` VALUES (370606, '吴起县', 370600, 'WQX', 'W', 3, 1);
INSERT INTO `city` VALUES (370607, '甘泉县', 370600, 'GQX', 'G', 3, 1);
INSERT INTO `city` VALUES (370608, '富县', 370600, 'FX', 'F', 3, 1);
INSERT INTO `city` VALUES (370609, '洛川县', 370600, 'LCX', 'L', 3, 1);
INSERT INTO `city` VALUES (370610, '宜川县', 370600, 'YCX', 'Y', 3, 1);
INSERT INTO `city` VALUES (370611, '黄龙县', 370600, 'HLX', 'H', 3, 1);
INSERT INTO `city` VALUES (370612, '黄陵县', 370600, 'HLX', 'H', 3, 1);
INSERT INTO `city` VALUES (370613, '子长市', 370600, 'ZZ,CS', 'Z', 3, 1);
INSERT INTO `city` VALUES (370700, '汉中市', 370000, 'HZS', 'H', 2, 1);
INSERT INTO `city` VALUES (370701, '汉台区', 370700, 'HTQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (370702, '南郑区', 370700, 'NZQ,O', 'N', 3, 1);
INSERT INTO `city` VALUES (370703, '城固县', 370700, 'CGX', 'C', 3, 1);
INSERT INTO `city` VALUES (370704, '洋县', 370700, 'YX', 'Y', 3, 1);
INSERT INTO `city` VALUES (370705, '西乡县', 370700, 'XXX', 'X', 3, 1);
INSERT INTO `city` VALUES (370706, '勉县', 370700, 'MX', 'M', 3, 1);
INSERT INTO `city` VALUES (370707, '宁强县', 370700, 'NQ,JX', 'N', 3, 1);
INSERT INTO `city` VALUES (370708, '略阳县', 370700, 'LYX', 'L', 3, 1);
INSERT INTO `city` VALUES (370709, '镇巴县', 370700, 'ZBX', 'Z', 3, 1);
INSERT INTO `city` VALUES (370710, '留坝县', 370700, 'LBX', 'L', 3, 1);
INSERT INTO `city` VALUES (370711, '佛坪县', 370700, 'BPX', 'B', 3, 1);
INSERT INTO `city` VALUES (370800, '榆林市', 370000, 'YLS', 'Y', 2, 1);
INSERT INTO `city` VALUES (370801, '榆阳区', 370800, 'YYQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (370802, '横山区', 370800, 'HSQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (370803, '府谷县', 370800, 'FY,GX', 'F', 3, 1);
INSERT INTO `city` VALUES (370804, '靖边县', 370800, 'JBX', 'J', 3, 1);
INSERT INTO `city` VALUES (370805, '定边县', 370800, 'DBX', 'D', 3, 1);
INSERT INTO `city` VALUES (370806, '绥德县', 370800, 'SDX', 'S', 3, 1);
INSERT INTO `city` VALUES (370807, '米脂县', 370800, 'MZX', 'M', 3, 1);
INSERT INTO `city` VALUES (370808, '佳县', 370800, 'JX', 'J', 3, 1);
INSERT INTO `city` VALUES (370809, '吴堡县', 370800, 'WP,BX', 'W', 3, 1);
INSERT INTO `city` VALUES (370810, '清涧县', 370800, 'QJX', 'Q', 3, 1);
INSERT INTO `city` VALUES (370811, '子洲县', 370800, 'ZZX', 'Z', 3, 1);
INSERT INTO `city` VALUES (370812, '神木市', 370800, 'SMS', 'S', 3, 1);
INSERT INTO `city` VALUES (370900, '安康市', 370000, 'AKS', 'A', 2, 1);
INSERT INTO `city` VALUES (370901, '汉滨区', 370900, 'HBQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (370902, '汉阴县', 370900, 'HYX', 'H', 3, 1);
INSERT INTO `city` VALUES (370903, '石泉县', 370900, 'S,DQX', 'S', 3, 1);
INSERT INTO `city` VALUES (370904, '宁陕县', 370900, 'NSX', 'N', 3, 1);
INSERT INTO `city` VALUES (370905, '紫阳县', 370900, 'ZYX', 'Z', 3, 1);
INSERT INTO `city` VALUES (370906, '岚皋县', 370900, 'LGX', 'L', 3, 1);
INSERT INTO `city` VALUES (370907, '平利县', 370900, 'PLX', 'P', 3, 1);
INSERT INTO `city` VALUES (370908, '镇坪县', 370900, 'ZPX', 'Z', 3, 1);
INSERT INTO `city` VALUES (370909, '白河县', 370900, 'BHX', 'B', 3, 1);
INSERT INTO `city` VALUES (370910, '旬阳市', 370900, 'XYS', 'X', 3, 1);
INSERT INTO `city` VALUES (371000, '商洛市', 370000, 'SLS', 'S', 2, 1);
INSERT INTO `city` VALUES (371001, '商州区', 371000, 'SZQ,O', 'S', 3, 1);
INSERT INTO `city` VALUES (371002, '洛南县', 371000, 'LNX', 'L', 3, 1);
INSERT INTO `city` VALUES (371003, '丹凤县', 371000, 'DFX', 'D', 3, 1);
INSERT INTO `city` VALUES (371004, '商南县', 371000, 'SNX', 'S', 3, 1);
INSERT INTO `city` VALUES (371005, '山阳县', 371000, 'SYX', 'S', 3, 1);
INSERT INTO `city` VALUES (371006, '镇安县', 371000, 'ZAX', 'Z', 3, 1);
INSERT INTO `city` VALUES (371007, '柞水县', 371000, 'ZSX', 'Z', 3, 1);
INSERT INTO `city` VALUES (380000, '甘肃省', 0, 'GSS,X', 'G', 1, 1);
INSERT INTO `city` VALUES (380100, '兰州市', 380000, 'LZS', 'L', 2, 1);
INSERT INTO `city` VALUES (380101, '城关区', 380100, 'CGQ,O', 'C', 3, 1);
INSERT INTO `city` VALUES (380102, '七里河区', 380100, 'QLHQ,O', 'Q', 3, 1);
INSERT INTO `city` VALUES (380103, '西固区', 380100, 'XGQ,O', 'X', 3, 1);
INSERT INTO `city` VALUES (380104, '安宁区', 380100, 'ANQ,O', 'A', 3, 1);
INSERT INTO `city` VALUES (380105, '红古区', 380100, 'H,GGQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (380106, '永登县', 380100, 'YDX', 'Y', 3, 1);
INSERT INTO `city` VALUES (380107, '皋兰县', 380100, 'GLX', 'G', 3, 1);
INSERT INTO `city` VALUES (380108, '榆中县', 380100, 'YZX', 'Y', 3, 1);
INSERT INTO `city` VALUES (380200, '金昌市', 380000, 'JCS', 'J', 2, 1);
INSERT INTO `city` VALUES (380201, '金川区', 380200, 'JCQ,O', 'J', 3, 1);
INSERT INTO `city` VALUES (380202, '永昌县', 380200, 'YCX', 'Y', 3, 1);
INSERT INTO `city` VALUES (380300, '白银市', 380000, 'BYS', 'B', 2, 1);
INSERT INTO `city` VALUES (380301, '白银区', 380300, 'BYQ,O', 'B', 3, 1);
INSERT INTO `city` VALUES (380302, '平川区', 380300, 'PCQ,O', 'P', 3, 1);
INSERT INTO `city` VALUES (380303, '靖远县', 380300, 'JYX', 'J', 3, 1);
INSERT INTO `city` VALUES (380304, '会宁县', 380300, 'K,HNX', 'K', 3, 1);
INSERT INTO `city` VALUES (380305, '景泰县', 380300, 'JTX', 'J', 3, 1);
INSERT INTO `city` VALUES (380400, '天水市', 380000, 'TSS', 'T', 2, 1);
INSERT INTO `city` VALUES (380401, '秦州区', 380400, 'QZQ,O', 'Q', 3, 1);
INSERT INTO `city` VALUES (380402, '麦积区', 380400, 'MJQ,O', 'M', 3, 1);
INSERT INTO `city` VALUES (380403, '清水县', 380400, 'QSX', 'Q', 3, 1);
INSERT INTO `city` VALUES (380404, '秦安县', 380400, 'QAX', 'Q', 3, 1);
INSERT INTO `city` VALUES (380405, '甘谷县', 380400, 'GY,GX', 'G', 3, 1);
INSERT INTO `city` VALUES (380406, '武山县', 380400, 'WSX', 'W', 3, 1);
INSERT INTO `city` VALUES (380407, '张家川回族自治县', 380400, 'ZJCHZZZX', 'Z', 3, 1);
INSERT INTO `city` VALUES (380500, '武威市', 380000, 'WWS', 'W', 2, 1);
INSERT INTO `city` VALUES (380501, '凉州区', 380500, 'LZQ,O', 'L', 3, 1);
INSERT INTO `city` VALUES (380502, '民勤县', 380500, 'MQX', 'M', 3, 1);
INSERT INTO `city` VALUES (380503, '古浪县', 380500, 'GLX', 'G', 3, 1);
INSERT INTO `city` VALUES (380504, '天祝藏族自治县', 380500, 'TZZ,CZZZX', 'T', 3, 1);
INSERT INTO `city` VALUES (380600, '张掖市', 380000, 'ZYS', 'Z', 2, 1);
INSERT INTO `city` VALUES (380601, '甘州区', 380600, 'GZQ,O', 'G', 3, 1);
INSERT INTO `city` VALUES (380602, '肃南裕固族自治县', 380600, 'SNYGZZZX', 'S', 3, 1);
INSERT INTO `city` VALUES (380603, '民乐县', 380600, 'MY,LX', 'M', 3, 1);
INSERT INTO `city` VALUES (380604, '临泽县', 380600, 'LZX', 'L', 3, 1);
INSERT INTO `city` VALUES (380605, '高台县', 380600, 'GTX', 'G', 3, 1);
INSERT INTO `city` VALUES (380606, '山丹县', 380600, 'SDX', 'S', 3, 1);
INSERT INTO `city` VALUES (380700, '平凉市', 380000, 'PLS', 'P', 2, 1);
INSERT INTO `city` VALUES (380701, '崆峒区', 380700, 'KT,DQ,O', 'K', 3, 1);
INSERT INTO `city` VALUES (380702, '泾川县', 380700, 'JCX', 'J', 3, 1);
INSERT INTO `city` VALUES (380703, '灵台县', 380700, 'LTX', 'L', 3, 1);
INSERT INTO `city` VALUES (380704, '崇信县', 380700, 'CXX', 'C', 3, 1);
INSERT INTO `city` VALUES (380705, '庄浪县', 380700, 'ZLX', 'Z', 3, 1);
INSERT INTO `city` VALUES (380706, '静宁县', 380700, 'JNX', 'J', 3, 1);
INSERT INTO `city` VALUES (380707, '华亭市', 380700, 'HTS', 'H', 3, 1);
INSERT INTO `city` VALUES (380800, '酒泉市', 380000, 'JQS', 'J', 2, 1);
INSERT INTO `city` VALUES (380801, '肃州区', 380800, 'SZQ,O', 'S', 3, 1);
INSERT INTO `city` VALUES (380802, '金塔县', 380800, 'JT,DX', 'J', 3, 1);
INSERT INTO `city` VALUES (380803, '瓜州县', 380800, 'GZX', 'G', 3, 1);
INSERT INTO `city` VALUES (380804, '肃北蒙古族自治县', 380800, 'SBMGZZZX', 'S', 3, 1);
INSERT INTO `city` VALUES (380805, '阿克塞哈萨克族自治县', 380800, 'A,EKSHSKZZZX', 'A', 3, 1);
INSERT INTO `city` VALUES (380806, '玉门市', 380800, 'YMS', 'Y', 3, 1);
INSERT INTO `city` VALUES (380807, '敦煌市', 380800, 'DHS', 'D', 3, 1);
INSERT INTO `city` VALUES (380900, '庆阳市', 380000, 'QYS', 'Q', 2, 1);
INSERT INTO `city` VALUES (380901, '西峰区', 380900, 'XFQ,O', 'X', 3, 1);
INSERT INTO `city` VALUES (380902, '庆城县', 380900, 'QCX', 'Q', 3, 1);
INSERT INTO `city` VALUES (380903, '环县', 380900, 'HX', 'H', 3, 1);
INSERT INTO `city` VALUES (380904, '华池县', 380900, 'HCX', 'H', 3, 1);
INSERT INTO `city` VALUES (380905, '合水县', 380900, 'H,GSX', 'H', 3, 1);
INSERT INTO `city` VALUES (380906, '正宁县', 380900, 'ZNX', 'Z', 3, 1);
INSERT INTO `city` VALUES (380907, '宁县', 380900, 'NX', 'N', 3, 1);
INSERT INTO `city` VALUES (380908, '镇原县', 380900, 'ZYX', 'Z', 3, 1);
INSERT INTO `city` VALUES (381000, '定西市', 380000, 'DXS', 'D', 2, 1);
INSERT INTO `city` VALUES (381001, '安定区', 381000, 'ADQ,O', 'A', 3, 1);
INSERT INTO `city` VALUES (381002, '通渭县', 381000, 'TWX', 'T', 3, 1);
INSERT INTO `city` VALUES (381003, '陇西县', 381000, 'LXX', 'L', 3, 1);
INSERT INTO `city` VALUES (381004, '渭源县', 381000, 'WYX', 'W', 3, 1);
INSERT INTO `city` VALUES (381005, '临洮县', 381000, 'LTX', 'L', 3, 1);
INSERT INTO `city` VALUES (381006, '漳县', 381000, 'ZX', 'Z', 3, 1);
INSERT INTO `city` VALUES (381007, '岷县', 381000, 'MX', 'M', 3, 1);
INSERT INTO `city` VALUES (381100, '陇南市', 380000, 'LNS', 'L', 2, 1);
INSERT INTO `city` VALUES (381101, '武都区', 381100, 'WDQ,O', 'W', 3, 1);
INSERT INTO `city` VALUES (381102, '成县', 381100, 'CX', 'C', 3, 1);
INSERT INTO `city` VALUES (381103, '文县', 381100, 'WX', 'W', 3, 1);
INSERT INTO `city` VALUES (381104, '宕昌县', 381100, 'DCX', 'D', 3, 1);
INSERT INTO `city` VALUES (381105, '康县', 381100, 'KX', 'K', 3, 1);
INSERT INTO `city` VALUES (381106, '西和县', 381100, 'XHX', 'X', 3, 1);
INSERT INTO `city` VALUES (381107, '礼县', 381100, 'LX', 'L', 3, 1);
INSERT INTO `city` VALUES (381108, '徽县', 381100, 'HX', 'H', 3, 1);
INSERT INTO `city` VALUES (381109, '两当县', 381100, 'LDX', 'L', 3, 1);
INSERT INTO `city` VALUES (381200, '临夏回族自治州', 380000, 'LXHZZZZ', 'L', 2, 1);
INSERT INTO `city` VALUES (381201, '临夏市', 381200, 'LXS', 'L', 3, 1);
INSERT INTO `city` VALUES (381202, '临夏县', 381200, 'LXX', 'L', 3, 1);
INSERT INTO `city` VALUES (381203, '康乐县', 381200, 'KY,LX', 'K', 3, 1);
INSERT INTO `city` VALUES (381204, '永靖县', 381200, 'YJX', 'Y', 3, 1);
INSERT INTO `city` VALUES (381205, '广河县', 381200, 'G,AHX', 'G', 3, 1);
INSERT INTO `city` VALUES (381206, '和政县', 381200, 'HZX', 'H', 3, 1);
INSERT INTO `city` VALUES (381207, '东乡族自治县', 381200, 'DXZZZX', 'D', 3, 1);
INSERT INTO `city` VALUES (381208, '积石山保安族东乡族撒拉族自治县', 381200, 'JS,DSBAZDXZSLZZZX', 'J', 3, 1);
INSERT INTO `city` VALUES (381300, '甘南藏族自治州', 380000, 'GNZ,CZZZZ', 'G', 2, 1);
INSERT INTO `city` VALUES (381301, '合作市', 381300, 'H,GZS', 'H', 3, 1);
INSERT INTO `city` VALUES (381302, '临潭县', 381300, 'LTX', 'L', 3, 1);
INSERT INTO `city` VALUES (381303, '卓尼县', 381300, 'ZNX', 'Z', 3, 1);
INSERT INTO `city` VALUES (381304, '舟曲县', 381300, 'ZQX', 'Z', 3, 1);
INSERT INTO `city` VALUES (381305, '迭部县', 381300, 'DBX', 'D', 3, 1);
INSERT INTO `city` VALUES (381306, '玛曲县', 381300, 'MQX', 'M', 3, 1);
INSERT INTO `city` VALUES (381307, '碌曲县', 381300, 'LQX', 'L', 3, 1);
INSERT INTO `city` VALUES (381308, '夏河县', 381300, 'XHX', 'X', 3, 1);
INSERT INTO `city` VALUES (390000, '青海省', 0, 'QHS,X', 'Q', 1, 1);
INSERT INTO `city` VALUES (390100, '西宁市', 390000, 'XNS', 'X', 2, 1);
INSERT INTO `city` VALUES (390101, '城东区', 390100, 'CDQ,O', 'C', 3, 1);
INSERT INTO `city` VALUES (390102, '城中区', 390100, 'CZQ,O', 'C', 3, 1);
INSERT INTO `city` VALUES (390103, '城西区', 390100, 'CXQ,O', 'C', 3, 1);
INSERT INTO `city` VALUES (390104, '城北区', 390100, 'CBQ,O', 'C', 3, 1);
INSERT INTO `city` VALUES (390105, '湟中区', 390100, 'HZQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (390106, '大通回族土族自治县', 390100, 'DTHZTZZZX', 'D', 3, 1);
INSERT INTO `city` VALUES (390107, '湟源县', 390100, 'HYX', 'H', 3, 1);
INSERT INTO `city` VALUES (390200, '海东市', 390000, 'HDS', 'H', 2, 1);
INSERT INTO `city` VALUES (390201, '乐都区', 390200, 'Y,LDQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (390202, '平安区', 390200, 'PAQ,O', 'P', 3, 1);
INSERT INTO `city` VALUES (390203, '民和回族土族自治县', 390200, 'MHHZTZZZX', 'M', 3, 1);
INSERT INTO `city` VALUES (390204, '互助土族自治县', 390200, 'HZTZZZX', 'H', 3, 1);
INSERT INTO `city` VALUES (390205, '化隆回族自治县', 390200, 'HLHZZZX', 'H', 3, 1);
INSERT INTO `city` VALUES (390206, '循化撒拉族自治县', 390200, 'XHSLZZZX', 'X', 3, 1);
INSERT INTO `city` VALUES (390300, '海北藏族自治州', 390000, 'HBZ,CZZZZ', 'H', 2, 1);
INSERT INTO `city` VALUES (390301, '门源回族自治县', 390300, 'MYHZZZX', 'M', 3, 1);
INSERT INTO `city` VALUES (390302, '祁连县', 390300, 'QLX', 'Q', 3, 1);
INSERT INTO `city` VALUES (390303, '海晏县', 390300, 'HYX', 'H', 3, 1);
INSERT INTO `city` VALUES (390304, '刚察县', 390300, 'GCX', 'G', 3, 1);
INSERT INTO `city` VALUES (390400, '黄南藏族自治州', 390000, 'HNZ,CZZZZ', 'H', 2, 1);
INSERT INTO `city` VALUES (390401, '同仁市', 390400, 'TRS', 'T', 3, 1);
INSERT INTO `city` VALUES (390402, '尖扎县', 390400, 'JZX', 'J', 3, 1);
INSERT INTO `city` VALUES (390403, '泽库县', 390400, 'ZKX', 'Z', 3, 1);
INSERT INTO `city` VALUES (390404, '河南蒙古族自治县', 390400, 'HNMGZZZX', 'H', 3, 1);
INSERT INTO `city` VALUES (390500, '海南藏族自治州', 390000, 'HNZ,CZZZZ', 'H', 2, 1);
INSERT INTO `city` VALUES (390501, '共和县', 390500, 'GHX', 'G', 3, 1);
INSERT INTO `city` VALUES (390502, '同德县', 390500, 'TDX', 'T', 3, 1);
INSERT INTO `city` VALUES (390503, '贵德县', 390500, 'GDX', 'G', 3, 1);
INSERT INTO `city` VALUES (390504, '兴海县', 390500, 'XHX', 'X', 3, 1);
INSERT INTO `city` VALUES (390505, '贵南县', 390500, 'GNX', 'G', 3, 1);
INSERT INTO `city` VALUES (390600, '果洛藏族自治州', 390000, 'GLZ,CZZZZ', 'G', 2, 1);
INSERT INTO `city` VALUES (390601, '玛沁县', 390600, 'MQX', 'M', 3, 1);
INSERT INTO `city` VALUES (390602, '班玛县', 390600, 'BMX', 'B', 3, 1);
INSERT INTO `city` VALUES (390603, '甘德县', 390600, 'GDX', 'G', 3, 1);
INSERT INTO `city` VALUES (390604, '达日县', 390600, 'DRX', 'D', 3, 1);
INSERT INTO `city` VALUES (390605, '久治县', 390600, 'JZX', 'J', 3, 1);
INSERT INTO `city` VALUES (390606, '玛多县', 390600, 'MDX', 'M', 3, 1);
INSERT INTO `city` VALUES (390700, '玉树藏族自治州', 390000, 'YSZ,CZZZZ', 'Y', 2, 1);
INSERT INTO `city` VALUES (390701, '玉树市', 390700, 'YSS', 'Y', 3, 1);
INSERT INTO `city` VALUES (390702, '杂多县', 390700, 'ZDX', 'Z', 3, 1);
INSERT INTO `city` VALUES (390703, '称多县', 390700, 'CDX', 'C', 3, 1);
INSERT INTO `city` VALUES (390704, '治多县', 390700, 'ZDX', 'Z', 3, 1);
INSERT INTO `city` VALUES (390705, '囊谦县', 390700, 'NQX', 'N', 3, 1);
INSERT INTO `city` VALUES (390706, '曲麻莱县', 390700, 'QMLX', 'Q', 3, 1);
INSERT INTO `city` VALUES (390800, '海西蒙古族藏族自治州', 390000, 'HXMGZZ,CZZZZ', 'H', 2, 1);
INSERT INTO `city` VALUES (390801, '格尔木市', 390800, 'GEMS', 'G', 3, 1);
INSERT INTO `city` VALUES (390802, '德令哈市', 390800, 'DLHS', 'D', 3, 1);
INSERT INTO `city` VALUES (390803, '茫崖市', 390800, 'MYS', 'M', 3, 1);
INSERT INTO `city` VALUES (390804, '乌兰县', 390800, 'WLX', 'W', 3, 1);
INSERT INTO `city` VALUES (390805, '都兰县', 390800, 'DLX', 'D', 3, 1);
INSERT INTO `city` VALUES (390806, '天峻县', 390800, 'TJX', 'T', 3, 1);
INSERT INTO `city` VALUES (400000, '宁夏回族自治区', 0, 'NXHZZZQ,O', 'N', 1, 1);
INSERT INTO `city` VALUES (400100, '银川市', 400000, 'YCS', 'Y', 2, 1);
INSERT INTO `city` VALUES (400101, '兴庆区', 400100, 'XQQ,O', 'X', 3, 1);
INSERT INTO `city` VALUES (400102, '西夏区', 400100, 'XXQ,O', 'X', 3, 1);
INSERT INTO `city` VALUES (400103, '金凤区', 400100, 'JFQ,O', 'J', 3, 1);
INSERT INTO `city` VALUES (400104, '永宁县', 400100, 'YNX', 'Y', 3, 1);
INSERT INTO `city` VALUES (400105, '贺兰县', 400100, 'HLX', 'H', 3, 1);
INSERT INTO `city` VALUES (400106, '灵武市', 400100, 'LWS', 'L', 3, 1);
INSERT INTO `city` VALUES (400200, '石嘴山市', 400000, 'S,DZSS', 'S', 2, 1);
INSERT INTO `city` VALUES (400201, '大武口区', 400200, 'DWKQ,O', 'D', 3, 1);
INSERT INTO `city` VALUES (400202, '惠农区', 400200, 'HNQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (400203, '平罗县', 400200, 'PLX', 'P', 3, 1);
INSERT INTO `city` VALUES (400300, '吴忠市', 400000, 'WZS', 'W', 2, 1);
INSERT INTO `city` VALUES (400301, '利通区', 400300, 'LTQ,O', 'L', 3, 1);
INSERT INTO `city` VALUES (400302, '红寺堡区', 400300, 'H,GSP,BQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (400303, '盐池县', 400300, 'YCX', 'Y', 3, 1);
INSERT INTO `city` VALUES (400304, '同心县', 400300, 'TXX', 'T', 3, 1);
INSERT INTO `city` VALUES (400305, '青铜峡市', 400300, 'QTXS', 'Q', 3, 1);
INSERT INTO `city` VALUES (400400, '固原市', 400000, 'GYS', 'G', 2, 1);
INSERT INTO `city` VALUES (400401, '原州区', 400400, 'YZQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (400402, '西吉县', 400400, 'XJX', 'X', 3, 1);
INSERT INTO `city` VALUES (400403, '隆德县', 400400, 'LDX', 'L', 3, 1);
INSERT INTO `city` VALUES (400404, '泾源县', 400400, 'JYX', 'J', 3, 1);
INSERT INTO `city` VALUES (400405, '彭阳县', 400400, 'PYX', 'P', 3, 1);
INSERT INTO `city` VALUES (400500, '中卫市', 400000, 'ZWS', 'Z', 2, 1);
INSERT INTO `city` VALUES (400501, '沙坡头区', 400500, 'SPTQ,O', 'S', 3, 1);
INSERT INTO `city` VALUES (400502, '中宁县', 400500, 'ZNX', 'Z', 3, 1);
INSERT INTO `city` VALUES (400503, '海原县', 400500, 'HYX', 'H', 3, 1);
INSERT INTO `city` VALUES (410000, '新疆维吾尔自治区', 0, 'XJWWEZZQ,O', 'X', 1, 1);
INSERT INTO `city` VALUES (410100, '乌鲁木齐市', 410000, 'WLMQ,JS', 'W', 2, 1);
INSERT INTO `city` VALUES (410101, '天山区', 410100, 'TSQ,O', 'T', 3, 1);
INSERT INTO `city` VALUES (410102, '沙依巴克区', 410100, 'SYBKQ,O', 'S', 3, 1);
INSERT INTO `city` VALUES (410103, '新市区', 410100, 'XSQ,O', 'X', 3, 1);
INSERT INTO `city` VALUES (410104, '水磨沟区', 410100, 'SMGQ,O', 'S', 3, 1);
INSERT INTO `city` VALUES (410105, '头屯河区', 410100, 'TZ,THQ,O', 'T', 3, 1);
INSERT INTO `city` VALUES (410106, '达坂城区', 410100, 'DBCQ,O', 'D', 3, 1);
INSERT INTO `city` VALUES (410107, '米东区', 410100, 'MDQ,O', 'M', 3, 1);
INSERT INTO `city` VALUES (410108, '乌鲁木齐县', 410100, 'WLMQ,JX', 'W', 3, 1);
INSERT INTO `city` VALUES (410200, '克拉玛依市', 410000, 'KLMYS', 'K', 2, 1);
INSERT INTO `city` VALUES (410201, '独山子区', 410200, 'DSZQ,O', 'D', 3, 1);
INSERT INTO `city` VALUES (410202, '克拉玛依区', 410200, 'KLMYQ,O', 'K', 3, 1);
INSERT INTO `city` VALUES (410203, '白碱滩区', 410200, 'BJTQ,O', 'B', 3, 1);
INSERT INTO `city` VALUES (410204, '乌尔禾区', 410200, 'WEHQ,O', 'W', 3, 1);
INSERT INTO `city` VALUES (410300, '吐鲁番市', 410000, 'TLP,FS', 'T', 2, 1);
INSERT INTO `city` VALUES (410301, '高昌区', 410300, 'GCQ,O', 'G', 3, 1);
INSERT INTO `city` VALUES (410302, '鄯善县', 410300, 'SSX', 'S', 3, 1);
INSERT INTO `city` VALUES (410303, '托克逊县', 410300, 'TKXX', 'T', 3, 1);
INSERT INTO `city` VALUES (410400, '哈密市', 410000, 'HMS', 'H', 2, 1);
INSERT INTO `city` VALUES (410401, '伊州区', 410400, 'YZQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (410402, '巴里坤哈萨克自治县', 410400, 'BLKHSKZZX', 'B', 3, 1);
INSERT INTO `city` VALUES (410403, '伊吾县', 410400, 'YWX', 'Y', 3, 1);
INSERT INTO `city` VALUES (410500, '昌吉回族自治州', 410000, 'CJHZZZZ', 'C', 2, 1);
INSERT INTO `city` VALUES (410501, '昌吉市', 410500, 'CJS', 'C', 3, 1);
INSERT INTO `city` VALUES (410502, '阜康市', 410500, 'FKS', 'F', 3, 1);
INSERT INTO `city` VALUES (410503, '呼图壁县', 410500, 'HTBX', 'H', 3, 1);
INSERT INTO `city` VALUES (410504, '玛纳斯县', 410500, 'MNSX', 'M', 3, 1);
INSERT INTO `city` VALUES (410505, '奇台县', 410500, 'Q,JTX', 'Q', 3, 1);
INSERT INTO `city` VALUES (410506, '吉木萨尔县', 410500, 'JMSEX', 'J', 3, 1);
INSERT INTO `city` VALUES (410507, '木垒哈萨克自治县', 410500, 'MLHSKZZX', 'M', 3, 1);
INSERT INTO `city` VALUES (410600, '博尔塔拉蒙古自治州', 410000, 'BET,DLMGZZZ', 'B', 2, 1);
INSERT INTO `city` VALUES (410601, '博乐市', 410600, 'BY,LS', 'B', 3, 1);
INSERT INTO `city` VALUES (410602, '阿拉山口市', 410600, 'A,ELSKS', 'A', 3, 1);
INSERT INTO `city` VALUES (410603, '精河县', 410600, 'JHX', 'J', 3, 1);
INSERT INTO `city` VALUES (410604, '温泉县', 410600, 'WQX', 'W', 3, 1);
INSERT INTO `city` VALUES (410700, '巴音郭楞蒙古自治州', 410000, 'BYGLMGZZZ', 'B', 2, 1);
INSERT INTO `city` VALUES (410701, '库尔勒市', 410700, 'KELS', 'K', 3, 1);
INSERT INTO `city` VALUES (410702, '轮台县', 410700, 'LTX', 'L', 3, 1);
INSERT INTO `city` VALUES (410703, '尉犁县', 410700, 'Y,WLX', 'Y', 3, 1);
INSERT INTO `city` VALUES (410704, '若羌县', 410700, 'RQX', 'R', 3, 1);
INSERT INTO `city` VALUES (410705, '且末县', 410700, 'Q,JMX', 'Q', 3, 1);
INSERT INTO `city` VALUES (410706, '焉耆回族自治县', 410700, 'YQHZZZX', 'Y', 3, 1);
INSERT INTO `city` VALUES (410707, '和静县', 410700, 'HJX', 'H', 3, 1);
INSERT INTO `city` VALUES (410708, '和硕县', 410700, 'HSX', 'H', 3, 1);
INSERT INTO `city` VALUES (410709, '博湖县', 410700, 'BHX', 'B', 3, 1);
INSERT INTO `city` VALUES (410800, '阿克苏地区', 410000, 'A,EKSDQ,O', 'A', 2, 1);
INSERT INTO `city` VALUES (410801, '阿克苏市', 410800, 'A,EKSS', 'A', 3, 1);
INSERT INTO `city` VALUES (410802, '库车市', 410800, 'KJ,CS', 'K', 3, 1);
INSERT INTO `city` VALUES (410803, '温宿县', 410800, 'WS,XX', 'W', 3, 1);
INSERT INTO `city` VALUES (410804, '沙雅县', 410800, 'SYX', 'S', 3, 1);
INSERT INTO `city` VALUES (410805, '新和县', 410800, 'XHX', 'X', 3, 1);
INSERT INTO `city` VALUES (410806, '拜城县', 410800, 'BCX', 'B', 3, 1);
INSERT INTO `city` VALUES (410807, '乌什县', 410800, 'WSX', 'W', 3, 1);
INSERT INTO `city` VALUES (410808, '阿瓦提县', 410800, 'A,EWT,DX', 'A', 3, 1);
INSERT INTO `city` VALUES (410809, '柯坪县', 410800, 'KPX', 'K', 3, 1);
INSERT INTO `city` VALUES (410900, '克孜勒苏柯尔克孜自治州', 410000, 'KZLSKEKZZZZ', 'K', 2, 1);
INSERT INTO `city` VALUES (410901, '阿图什市', 410900, 'A,ETSS', 'A', 3, 1);
INSERT INTO `city` VALUES (410902, '阿克陶县', 410900, 'A,EKY,TX', 'A', 3, 1);
INSERT INTO `city` VALUES (410903, '阿合奇县', 410900, 'A,EH,GQ,JX', 'A', 3, 1);
INSERT INTO `city` VALUES (410904, '乌恰县', 410900, 'WQX', 'W', 3, 1);
INSERT INTO `city` VALUES (411000, '喀什地区', 410000, 'KSDQ,O', 'K', 2, 1);
INSERT INTO `city` VALUES (411001, '喀什市', 411000, 'KSS', 'K', 3, 1);
INSERT INTO `city` VALUES (411002, '疏附县', 411000, 'SFX', 'S', 3, 1);
INSERT INTO `city` VALUES (411003, '疏勒县', 411000, 'SLX', 'S', 3, 1);
INSERT INTO `city` VALUES (411004, '英吉沙县', 411000, 'YJSX', 'Y', 3, 1);
INSERT INTO `city` VALUES (411005, '泽普县', 411000, 'ZPX', 'Z', 3, 1);
INSERT INTO `city` VALUES (411006, '莎车县', 411000, 'SJ,CX', 'S', 3, 1);
INSERT INTO `city` VALUES (411007, '叶城县', 411000, 'Y,XCX', 'Y', 3, 1);
INSERT INTO `city` VALUES (411008, '麦盖提县', 411000, 'MGT,DX', 'M', 3, 1);
INSERT INTO `city` VALUES (411009, '岳普湖县', 411000, 'YPHX', 'Y', 3, 1);
INSERT INTO `city` VALUES (411010, '伽师县', 411000, 'Q,J,GSX', 'Q', 3, 1);
INSERT INTO `city` VALUES (411011, '巴楚县', 411000, 'BCX', 'B', 3, 1);
INSERT INTO `city` VALUES (411012, '塔什库尔干塔吉克自治县', 411000, 'T,DSKEGT,DJKZZX', 'T', 3, 1);
INSERT INTO `city` VALUES (411100, '和田地区', 410000, 'HTDQ,O', 'H', 2, 1);
INSERT INTO `city` VALUES (411101, '和田市', 411100, 'HTS', 'H', 3, 1);
INSERT INTO `city` VALUES (411102, '和田县', 411100, 'HTX', 'H', 3, 1);
INSERT INTO `city` VALUES (411103, '墨玉县', 411100, 'MYX', 'M', 3, 1);
INSERT INTO `city` VALUES (411104, '皮山县', 411100, 'PSX', 'P', 3, 1);
INSERT INTO `city` VALUES (411105, '洛浦县', 411100, 'LPX', 'L', 3, 1);
INSERT INTO `city` VALUES (411106, '策勒县', 411100, 'CLX', 'C', 3, 1);
INSERT INTO `city` VALUES (411107, '于田县', 411100, 'YTX', 'Y', 3, 1);
INSERT INTO `city` VALUES (411108, '民丰县', 411100, 'MFX', 'M', 3, 1);
INSERT INTO `city` VALUES (411200, '伊犁哈萨克自治州', 410000, 'YLHSKZZZ', 'Y', 2, 1);
INSERT INTO `city` VALUES (411201, '伊宁市', 411200, 'YNS', 'Y', 3, 1);
INSERT INTO `city` VALUES (411202, '奎屯市', 411200, 'KZ,TS', 'K', 3, 1);
INSERT INTO `city` VALUES (411203, '霍尔果斯市', 411200, 'HEGSS', 'H', 3, 1);
INSERT INTO `city` VALUES (411204, '伊宁县', 411200, 'YNX', 'Y', 3, 1);
INSERT INTO `city` VALUES (411205, '察布查尔锡伯自治县', 411200, 'CBC,ZEXBZZX', 'C', 3, 1);
INSERT INTO `city` VALUES (411206, '霍城县', 411200, 'HCX', 'H', 3, 1);
INSERT INTO `city` VALUES (411207, '巩留县', 411200, 'GLX', 'G', 3, 1);
INSERT INTO `city` VALUES (411208, '新源县', 411200, 'XYX', 'X', 3, 1);
INSERT INTO `city` VALUES (411209, '昭苏县', 411200, 'ZSX', 'Z', 3, 1);
INSERT INTO `city` VALUES (411210, '特克斯县', 411200, 'TKSX', 'T', 3, 1);
INSERT INTO `city` VALUES (411211, '尼勒克县', 411200, 'NLKX', 'N', 3, 1);
INSERT INTO `city` VALUES (411300, '塔城地区', 410000, 'T,DCDQ,O', 'T', 2, 1);
INSERT INTO `city` VALUES (411301, '塔城市', 411300, 'T,DCS', 'T', 3, 1);
INSERT INTO `city` VALUES (411302, '乌苏市', 411300, 'WSS', 'W', 3, 1);
INSERT INTO `city` VALUES (411303, '沙湾市', 411300, 'SWS', 'S', 3, 1);
INSERT INTO `city` VALUES (411304, '额敏县', 411300, 'EMX', 'E', 3, 1);
INSERT INTO `city` VALUES (411305, '托里县', 411300, 'TLX', 'T', 3, 1);
INSERT INTO `city` VALUES (411306, '裕民县', 411300, 'YMX', 'Y', 3, 1);
INSERT INTO `city` VALUES (411307, '和布克赛尔蒙古自治县', 411300, 'HBKSEMGZZX', 'H', 3, 1);
INSERT INTO `city` VALUES (411400, '阿勒泰地区', 410000, 'A,ELTDQ,O', 'A', 2, 1);
INSERT INTO `city` VALUES (411401, '阿勒泰市', 411400, 'A,ELTS', 'A', 3, 1);
INSERT INTO `city` VALUES (411402, '布尔津县', 411400, 'BEJX', 'B', 3, 1);
INSERT INTO `city` VALUES (411403, '富蕴县', 411400, 'FYX', 'F', 3, 1);
INSERT INTO `city` VALUES (411404, '福海县', 411400, 'FHX', 'F', 3, 1);
INSERT INTO `city` VALUES (411405, '哈巴河县', 411400, 'HBHX', 'H', 3, 1);
INSERT INTO `city` VALUES (411406, '青河县', 411400, 'QHX', 'Q', 3, 1);
INSERT INTO `city` VALUES (411407, '吉木乃县', 411400, 'JMNX', 'J', 3, 1);
INSERT INTO `city` VALUES (411408, '石河子市', 411400, 'S,DHZS', 'S', 3, 1);
INSERT INTO `city` VALUES (411409, '阿拉尔市', 411400, 'A,ELES', 'A', 3, 1);
INSERT INTO `city` VALUES (411410, '图木舒克市', 411400, 'TMSKS', 'T', 3, 1);
INSERT INTO `city` VALUES (411411, '五家渠市', 411400, 'WJQS', 'W', 3, 1);
INSERT INTO `city` VALUES (411412, '北屯市', 411400, 'BZ,TS', 'B', 3, 1);
INSERT INTO `city` VALUES (411413, '铁门关市', 411400, 'TMGS', 'T', 3, 1);
INSERT INTO `city` VALUES (411414, '双河市', 411400, 'SHS', 'S', 3, 1);
INSERT INTO `city` VALUES (411415, '可克达拉市', 411400, 'KKDLS', 'K', 3, 1);
INSERT INTO `city` VALUES (411416, '昆玉市', 411400, 'KYS', 'K', 3, 1);
INSERT INTO `city` VALUES (411417, '胡杨河市', 411400, 'HYHS', 'H', 3, 1);
INSERT INTO `city` VALUES (411418, '新星市', 411400, 'XXS', 'X', 3, 1);
INSERT INTO `city` VALUES (420000, '台湾省', 0, 'TWS,X', 'T', 1, 1);
INSERT INTO `city` VALUES (420100, '台北市', 420000, 'TBS', 'T', 2, 1);
INSERT INTO `city` VALUES (420101, '台北市', 420100, 'TBS', 'T', 3, 1);
INSERT INTO `city` VALUES (420200, '新北市', 420000, 'XBS', 'X', 2, 1);
INSERT INTO `city` VALUES (420201, '新北市', 420200, 'XBS', 'X', 3, 1);
INSERT INTO `city` VALUES (420300, '桃园市', 420000, 'TYS', 'T', 2, 1);
INSERT INTO `city` VALUES (420301, '桃园市', 420300, 'TYS', 'T', 3, 1);
INSERT INTO `city` VALUES (420400, '台中市', 420000, 'TZS', 'T', 2, 1);
INSERT INTO `city` VALUES (420401, '台中市', 420400, 'TZS', 'T', 3, 1);
INSERT INTO `city` VALUES (420500, '台南市', 420000, 'TNS', 'T', 2, 1);
INSERT INTO `city` VALUES (420501, '台南市', 420500, 'TNS', 'T', 3, 1);
INSERT INTO `city` VALUES (420600, '高雄市', 420000, 'GXS', 'G', 2, 1);
INSERT INTO `city` VALUES (420601, '高雄市', 420600, 'GXS', 'G', 3, 1);
INSERT INTO `city` VALUES (420700, '基隆市', 420000, 'JLS', 'J', 2, 1);
INSERT INTO `city` VALUES (420701, '基隆市', 420700, 'JLS', 'J', 3, 1);
INSERT INTO `city` VALUES (420800, '新竹市', 420000, 'XZS', 'X', 2, 1);
INSERT INTO `city` VALUES (420801, '新竹市', 420800, 'XZS', 'X', 3, 1);
INSERT INTO `city` VALUES (420900, '嘉义市', 420000, 'JYS', 'J', 2, 1);
INSERT INTO `city` VALUES (420901, '嘉义市', 420900, 'JYS', 'J', 3, 1);
INSERT INTO `city` VALUES (421000, '新竹县', 420000, 'XZX', 'X', 2, 1);
INSERT INTO `city` VALUES (421001, '新竹县', 421000, 'XZX', 'X', 3, 1);
INSERT INTO `city` VALUES (421100, '苗栗县', 420000, 'MLX', 'M', 2, 1);
INSERT INTO `city` VALUES (421101, '苗栗县', 421100, 'MLX', 'M', 3, 1);
INSERT INTO `city` VALUES (421200, '彰化县', 420000, 'ZHX', 'Z', 2, 1);
INSERT INTO `city` VALUES (421201, '彰化县', 421200, 'ZHX', 'Z', 3, 1);
INSERT INTO `city` VALUES (421300, '南投县', 420000, 'NTX', 'N', 2, 1);
INSERT INTO `city` VALUES (421301, '南投县', 421300, 'NTX', 'N', 3, 1);
INSERT INTO `city` VALUES (421400, '云林县', 420000, 'YLX', 'Y', 2, 1);
INSERT INTO `city` VALUES (421401, '云林县', 421400, 'YLX', 'Y', 3, 1);
INSERT INTO `city` VALUES (421500, '嘉义县', 420000, 'JYX', 'J', 2, 1);
INSERT INTO `city` VALUES (421501, '嘉义县', 421500, 'JYX', 'J', 3, 1);
INSERT INTO `city` VALUES (421600, '屏东县', 420000, 'P,BDX', 'P', 2, 1);
INSERT INTO `city` VALUES (421601, '屏东县', 421600, 'P,BDX', 'P', 3, 1);
INSERT INTO `city` VALUES (421700, '宜兰县', 420000, 'YLX', 'Y', 2, 1);
INSERT INTO `city` VALUES (421701, '宜兰县', 421700, 'YLX', 'Y', 3, 1);
INSERT INTO `city` VALUES (421800, '花莲县', 420000, 'HLX', 'H', 2, 1);
INSERT INTO `city` VALUES (421801, '花莲县', 421800, 'HLX', 'H', 3, 1);
INSERT INTO `city` VALUES (421900, '台东县', 420000, 'TDX', 'T', 2, 1);
INSERT INTO `city` VALUES (421901, '台东县', 421900, 'TDX', 'T', 3, 1);
INSERT INTO `city` VALUES (422000, '澎湖县', 420000, 'PHX', 'P', 2, 1);
INSERT INTO `city` VALUES (422001, '澎湖县', 422000, 'PHX', 'P', 3, 1);
INSERT INTO `city` VALUES (422100, '连江县', 420000, 'LJX', 'L', 2, 1);
INSERT INTO `city` VALUES (422101, '连江县', 422100, 'LJX', 'L', 3, 1);
INSERT INTO `city` VALUES (430000, '香港特别行政区', 0, 'XGTBX,HZQ,O', 'X', 1, 1);
INSERT INTO `city` VALUES (430100, '香港特别行政区', 430000, 'XGTBX,HZQ,O', 'X', 2, 1);
INSERT INTO `city` VALUES (430101, '中西区', 430100, 'ZXQ,O', 'Z', 3, 1);
INSERT INTO `city` VALUES (430102, '东区', 430100, 'DQ,O', 'D', 3, 1);
INSERT INTO `city` VALUES (430103, '南区', 430100, 'NQ,O', 'N', 3, 1);
INSERT INTO `city` VALUES (430104, '湾仔区', 430100, 'WZQ,O', 'W', 3, 1);
INSERT INTO `city` VALUES (430105, '九龙城区', 430100, 'JLCQ,O', 'J', 3, 1);
INSERT INTO `city` VALUES (430106, '深水埗区', 430100, 'SSBQ,O', 'S', 3, 1);
INSERT INTO `city` VALUES (430107, '黄大仙区', 430100, 'HDXQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (430108, '油尖旺区', 430100, 'YJWQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (430109, '离岛区', 430100, 'LDQ,O', 'L', 3, 1);
INSERT INTO `city` VALUES (430110, '葵青区', 430100, 'KQQ,O', 'K', 3, 1);
INSERT INTO `city` VALUES (430111, '北区', 430100, 'BQ,O', 'B', 3, 1);
INSERT INTO `city` VALUES (430112, '西贡区', 430100, 'XGQ,O', 'X', 3, 1);
INSERT INTO `city` VALUES (430113, '沙田区', 430100, 'STQ,O', 'S', 3, 1);
INSERT INTO `city` VALUES (430114, '大埔区', 430100, 'DP,BQ,O', 'D', 3, 1);
INSERT INTO `city` VALUES (430115, '荃湾区', 430100, 'QWQ,O', 'Q', 3, 1);
INSERT INTO `city` VALUES (430116, '屯门区', 430100, 'Z,TMQ,O', 'Z', 3, 1);
INSERT INTO `city` VALUES (430117, '元朗区', 430100, 'YLQ,O', 'Y', 3, 1);
INSERT INTO `city` VALUES (440000, '澳门特别行政区', 0, 'AMTBX,HZQ,O', 'A', 1, 1);
INSERT INTO `city` VALUES (440100, '澳门特别行政区', 440000, 'AMTBX,HZQ,O', 'A', 2, 1);
INSERT INTO `city` VALUES (440101, '花地玛堂区', 440100, 'HDMTQ,O', 'H', 3, 1);
INSERT INTO `city` VALUES (440102, '圣安多尼堂区', 440100, 'SADNTQ,O', 'S', 3, 1);
INSERT INTO `city` VALUES (440103, '大堂区', 440100, 'DTQ,O', 'D', 3, 1);
INSERT INTO `city` VALUES (440104, '望德堂区', 440100, 'WDTQ,O', 'W', 3, 1);
INSERT INTO `city` VALUES (440105, '风顺堂区', 440100, 'FSTQ,O', 'F', 3, 1);
INSERT INTO `city` VALUES (440106, '氹仔', 440100, 'DZ', 'D', 3, 1);
INSERT INTO `city` VALUES (440107, '路环岛', 440100, 'LHD', 'L', 3, 1);

CREATE TABLE `corp`
(
    `id` bigint(24) NOT NULL AUTO_INCREMENT COMMENT '运营商编号',
    `name` varchar(128) NOT NULL COMMENT '运营商名称',
    `icon_url` varchar(255) DEFAULT NULL COMMENT '运营商图标url',
    `province` varchar(128) NOT NULL COMMENT '省',
    `city` varchar(128) NOT NULL COMMENT '市',
    `district` varchar(128) NOT NULL COMMENT '区',
    `parent_id` bigint(24) NOT NULL DEFAULT '0' COMMENT '上级运营商编号',
    `audit_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '审核状态，1-未审核，2-审核通过',
    `contact` varchar(64) NOT NULL COMMENT '联系人',
    `phone` varchar(32) NOT NULL COMMENT '联系电话',
    `opt_id` bigint(24) NOT NULL COMMENT '创建人id',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '运营商状态，1-关闭，2-开启',
    `token` varchar(255) DEFAULT NULL COMMENT 'token',
    `city_id` json NOT NULL COMMENT '省市区id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=100059 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;


CREATE TABLE `stations`
(
    `id`              bigint(24) NOT NULL AUTO_INCREMENT COMMENT '场站id',
    `corp_id`         bigint(24) NOT NULL COMMENT '运营商编号',
    `name`            varchar(128) NOT NULL COMMENT '场站名称',
    `all_address`     varchar(500) NOT NULL COMMENT '完整场站地址，需带省市区',
    `address`         varchar(500) NOT NULL COMMENT '场站地址，不必带省市区',
    `province`        varchar(128) NOT NULL COMMENT '省\r\n',
    `city`            varchar(128) NOT NULL COMMENT '市',
    `district`        varchar(128) NOT NULL COMMENT '区',
    `pile_num`        int(8) NOT NULL DEFAULT '0' COMMENT '桩数',
    `tariff_group_id` int(4) NOT NULL COMMENT '费率组id',
    `type`            tinyint(2) NOT NULL DEFAULT '1' COMMENT '场站类型：1-公共、2-自营',
    `status`          tinyint(2) NOT NULL DEFAULT '3' COMMENT '运行状态：1-正常使用、2-维护中、3-未开放',
    `pictures`        json                  DEFAULT NULL COMMENT '场站图片url集',
    `work_time`       text COMMENT '运营时段描述',
    `tag_pos`         tinyint(2) NOT NULL DEFAULT '1' COMMENT '桩位置标示：1-地上,2-地库',
    `tag_park`        text COMMENT '停车费说明',
    `place_rate`      int(8) NOT NULL DEFAULT '0' COMMENT '占位费（X分/分钟）,X*100',
    `opt_id`          bigint(24) NOT NULL COMMENT '创建人',
    `create_time`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `charge`          varchar(20)  NOT NULL COMMENT '负责人',
    `charge_phone`    varchar(20)  NOT NULL COMMENT '负责人手机号',
    `tag_toilet`      tinyint(2) NOT NULL DEFAULT '1' COMMENT '场站服务卫生间标识：1-无，2-有',
    `tag_canopy`      tinyint(2) NOT NULL DEFAULT '1' COMMENT '场站服务雨棚标识：1-无，2-有',
    `tag_rest`        tinyint(2) NOT NULL DEFAULT '1' COMMENT '场站服务休息室标识：1-无，2-有',
    `tag_pnp`         tinyint(2) NOT NULL DEFAULT '1' COMMENT '场站特色即插即用标识：1-不支持，2-支持',
    `tag_insure`      tinyint(2) NOT NULL DEFAULT '1' COMMENT '场站特色充电保险标识：1-不支持，2-支持',
    `tag_protect`     tinyint(2) NOT NULL DEFAULT '1' COMMENT '场站特色充电电池防护标识：1-不支持，2-支持',
    `tag_ultrafast`   tinyint(2) NOT NULL DEFAULT '1' COMMENT '场站电桩超快充标识：1-不支持，2-支持',
    `tag_fast`        tinyint(2) NOT NULL DEFAULT '1' COMMENT '场站电桩快充标识：1-不支持，2-支持',
    `tag_slow`        tinyint(2) NOT NULL DEFAULT '1' COMMENT '场站电桩慢充标识：1-不支持，2-支持',
    `lonlat`          varchar(100) NOT NULL COMMENT '经纬度，前面纬度，后面经度',
    `city_id`         json                  DEFAULT NULL COMMENT '省市区id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20000066 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `alarm_info`
(
      `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
      `corp_id` bigint(20) unsigned NOT NULL COMMENT '运营商编号',
      `station_id` bigint(20) unsigned NOT NULL COMMENT '场站id',
      `device_id` bigint(20) unsigned NOT NULL COMMENT '告警设备id，与类型对应：充电枪编号、充电桩编号',
      `type` tinyint(3) unsigned NOT NULL COMMENT '告警类型 1:充电枪 2:充电桩',
      `code` varchar(16) NOT NULL COMMENT '告警代码 1:充电停止原因代码表 2:启机命令返回0x03设备故障 3:实时监测数据0x13，硬件故障1~15位。',
      `level` tinyint(3) unsigned NOT NULL COMMENT '告警等级 1:一级 2:二级',
      `status` tinyint(3) unsigned NOT NULL COMMENT '告警状态 1:创建 2:处理中 3:处理完成',
      `info` varchar(50) DEFAULT NULL COMMENT '告警信息',
      `detail` varchar(50) DEFAULT NULL COMMENT '告警详情',
      `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
      `start_time` int(10) unsigned NOT NULL COMMENT '告警开始时间',
      `end_time` int(10) unsigned NOT NULL COMMENT '告警结束时间，处理完成时间',
      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='告警记录';


CREATE TABLE `electricity_meter_power_record`
(
      `id` int(10) unsigned NOT NULL COMMENT '主键',
      `centralized_controller_id` varchar(24) NOT NULL COMMENT '能源路由器ID',
      `corp_id` int(10) unsigned NOT NULL COMMENT '运营商ID',
      `station_id` int(10) unsigned NOT NULL COMMENT '充电站ID',
      `a_active_power` int(10) unsigned NOT NULL COMMENT 'A相有功功率(单位:kW)',
      `b_active_power` int(10) unsigned NOT NULL COMMENT 'B相有功功率(单位:kW)',
      `c_active_power` int(10) unsigned NOT NULL COMMENT 'C相有功功率(单位:kW)',
      `total_active_power` int(10) unsigned NOT NULL COMMENT '总的有功功率(单位:kW)',
      `record_time` timestamp NOT NULL COMMENT '记录时间',
      PRIMARY KEY (`id`) USING BTREE,
      KEY `corp_and_station_id` (`corp_id`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='电表功率记录';

CREATE TABLE `electronic_invoice_apply_detail`
(
       `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
       `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
       `apply_record_id` char(32) NOT NULL COMMENT '电子发票申请记录ID',
       `order_id` varchar(64) NOT NULL COMMENT '充电订单ID',
       `status` tinyint(3) unsigned NOT NULL COMMENT '状态 0:未生效 1:已生效',
       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='电子发票申请明细';

CREATE TABLE `electronic_invoice_apply_record`
(
       `id` char(32) NOT NULL COMMENT '自增ID',
       `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
       `service_type` tinyint(3) unsigned NOT NULL COMMENT '第三方服务类型 1:微信支付服务',
       `total_amount` int(10) unsigned NOT NULL COMMENT '发票总金额(单位:分)',
       `title_type` tinyint(3) unsigned NOT NULL COMMENT '抬头类型 0:个人 1:单位',
       `title_name` varchar(256) NOT NULL COMMENT '抬头购买方名称',
       `taxpayer_id` varchar(32) NOT NULL COMMENT '购买方纳税人识别号(抬头类型为单位时不为空)',
       `stage` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '阶段 0:用户发起申请 1:用户填写抬头完成 2:开具发票完成',
       `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
       `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='电子发票申请记录';

CREATE TABLE `electronic_invoice_config`
(
         `type` varchar(20) NOT NULL COMMENT '类型',
         `key` varchar(20) NOT NULL COMMENT '键',
         `value` varchar(255) NOT NULL COMMENT '值',
         `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '生效状态 1:未生效 2:已生效',
         PRIMARY KEY (`type`,`key`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='电子发票配置';

CREATE TABLE `electronic_invoice_record`
(
         `id` char(32) NOT NULL COMMENT '微信商户发票单号',
         `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
         `apply_record_id` char(32) NOT NULL COMMENT '电子发票申请记录ID',
         `total_amount` int(10) unsigned NOT NULL COMMENT '发票总金额(单位:分)',
         `status` tinyint(3) unsigned NOT NULL COMMENT '发票状态 0:初始状态 1:开票请求已受理 2:发票已开具',
         `card_status` tinyint(3) unsigned NOT NULL COMMENT '卡包状态 0:初始状态 1:插卡已受理 2:已插入用户卡包 3:作废申请已受理 4:发票卡券已作废',
         `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
         `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='电子发票记录';

CREATE TABLE `file`
(
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL COMMENT '文件名称',
        `url` varchar(255) NOT NULL COMMENT '文件网址',
        `size` varchar(255) NOT NULL COMMENT '文件大小',
        `format` varchar(255) NOT NULL COMMENT '文件格式',
        `md5` varchar(255) NOT NULL COMMENT '文件MD5',
        `sha1` varchar(255) NOT NULL COMMENT '文件SHA1',
        `state` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态，1 正常，2 无效',
        `type` varchar(255) NOT NULL COMMENT '文件类型',
        `user_id` varchar(16) CHARACTER SET utf8 NOT NULL COMMENT '用户id',
        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `order`
(
         `id` varchar(64) NOT NULL COMMENT '订单ID',
         `user_id` bigint(24) NOT NULL COMMENT '用户ID',
         `corp_id` bigint(24) NOT NULL COMMENT '运营商ID',
         `station_id` bigint(24) NOT NULL COMMENT '场站ID',
         `piles_id` bigint(24) NOT NULL COMMENT '桩ID',
         `sequence` int(2) NOT NULL COMMENT '枪号',
         `shot_id` bigint(24) NOT NULL COMMENT '枪ID',
         `money` int(8) NOT NULL DEFAULT '0' COMMENT '订单应付金额 - 单位分(小数点后2位)',
         `pay_money` int(8) NOT NULL DEFAULT '0' COMMENT '订单实付金额 - 单位分(小数点后2位)',
         `coupon_money` int(8) NOT NULL DEFAULT '0' COMMENT '订单优惠金额 - 单位分(小数点后2位)',
         `charge_duration` mediumint(9) DEFAULT '0' COMMENT '充电耗时(单位:秒)',
         `consumption_amount` int(8) NOT NULL DEFAULT '0' COMMENT '消费金额',
         `electricity_price` int(11) NOT NULL DEFAULT '0' COMMENT '本次充电费用 - 保留4为小数',
         `ser_price` int(11) NOT NULL DEFAULT '0' COMMENT '本次服务费用 - 保留4为小数',
         `amount_charged` int(8) NOT NULL DEFAULT '0' COMMENT '已充金额(精确到小数点后四位、包含电费、服务费)',
         `electricity_charged` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '已充电量(精确小数点后4位)',
         `settled_amount` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '已结算金额(单位:分)',
         `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
         `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
         `trans_start_time` timestamp NULL DEFAULT NULL COMMENT '充电桩交易开始时间',
         `trans_end_time` timestamp NULL DEFAULT NULL COMMENT '充电桩交易结束时间',
         `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '订单状态 1:下单 2:已冻结 3:充电中 4:充电完成 5:完成 6:异常 7:强制结算 20:其它',
         `reason_for_stop` int(2) DEFAULT NULL COMMENT '停止原因，见异常码',
         `reason_for_stop_text` varchar(255) DEFAULT NULL COMMENT '停止原因文本',
         `msg` varchar(255) DEFAULT NULL COMMENT '信息',
         `sharp_electricity` int(8) NOT NULL DEFAULT '0' COMMENT '尖电量 - 精确小数点后4位',
         `sharp_loss` int(11) NOT NULL DEFAULT '0' COMMENT '计损尖电量 - 精确小数点后4位',
         `sharp_amount` int(8) NOT NULL DEFAULT '0' COMMENT '尖金额(包括平电费+平服务费) - 精确小数点后4位',
         `peak_electricity` int(8) NOT NULL DEFAULT '0' COMMENT '峰电量 - 精确小数点后4位',
         `peak_loss` int(8) NOT NULL DEFAULT '0' COMMENT '计损峰电量 - 精确小数点后4位',
         `peak_amount` int(8) NOT NULL DEFAULT '0' COMMENT '峰金额(包括平电费+平服务费) - 精确小数点后4位',
         `flat_electricity` int(8) NOT NULL DEFAULT '0' COMMENT '平电量 - 精确小数点后4位',
         `flat_loss` int(8) NOT NULL DEFAULT '0' COMMENT '计损平电量 - 精确小数点后4位',
         `flat_amount` int(8) NOT NULL DEFAULT '0' COMMENT '平金额(包括平电费+平服务费) - 精确小数点后4位',
         `valley_electricity` int(8) NOT NULL DEFAULT '0' COMMENT '谷电量 - 精确小数点后4位',
         `valley_loss` int(8) NOT NULL DEFAULT '0' COMMENT '计损谷电量 - 精确小数点后4位',
         `valley_amount` int(8) NOT NULL DEFAULT '0' COMMENT '谷金额(包括平电费+平服务费) - 精确小数点后4位',
         `electricity_total` int(8) NOT NULL DEFAULT '0' COMMENT '总电量 - 精确小数点后4位',
         `electricity_loss` int(8) NOT NULL DEFAULT '0' COMMENT '计损总电量 - 精确小数点后4位',
         `sharp_price` int(8) NOT NULL DEFAULT '0' COMMENT '尖电价(尖电价+尖服务价) - 精确小数点后5位',
         `sharp_fee` int(8) NOT NULL DEFAULT '0' COMMENT '尖时电价格 - 精确到小数点后5位',
         `sharp_ser_fee` int(8) NOT NULL DEFAULT '0' COMMENT '尖时服务价格 - 精确到小数点后5位',
         `peak_price` int(8) NOT NULL DEFAULT '0' COMMENT '峰电价(峰电价+峰服务价) - 精确小数点后5位',
         `peak_fee` int(8) NOT NULL DEFAULT '0' COMMENT '峰时电价格 - 精确到小数点后5位',
         `peak_ser_fee` int(8) NOT NULL DEFAULT '0' COMMENT '峰时服务价格 - 精确到小数点后5位',
         `flat_price` int(8) NOT NULL DEFAULT '0' COMMENT '平电价(平电价+平服务价) - 精确小数点后5位',
         `flat_fee` int(8) NOT NULL DEFAULT '0' COMMENT '平时电价格 - 精确到小数点后5位',
         `flat_ser_fee` int(8) NOT NULL DEFAULT '0' COMMENT '平时服务价格 - 精确到小数点后5位',
         `valley_price` int(8) NOT NULL DEFAULT '0' COMMENT '谷电价(谷电价+谷服务价) - 精确小数点后5位',
         `valley_fee` int(8) NOT NULL DEFAULT '0' COMMENT '谷时电价格 - 精确到小数点后5位',
         `valley_ser_fee` int(8) NOT NULL DEFAULT '0' COMMENT '谷时服务价格 - 精确到小数点后5位',
         `currency` tinyint(2) NOT NULL DEFAULT '1' COMMENT '币种：1:CNY,2:USD',
         `type` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '订单类型：1立即支付，2先享后付',
         `pay_mode` tinyint(2) unsigned NOT NULL DEFAULT '3' COMMENT '支付方式：1微信、2支付宝、3零钱、4会员卡',
         `start_time` timestamp NULL DEFAULT NULL COMMENT '预约开始时间',
         `end_time` timestamp NULL DEFAULT NULL COMMENT '预约结束时间',
         `card_id` bigint(24) DEFAULT NULL COMMENT '会员卡id',
         `desc` text COMMENT '订单描述',
         `build_type` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '生成类型，1小程序，2后台模拟',
         `meter_start_value` int(8) NOT NULL DEFAULT '0' COMMENT '电表总起值 - 精确小数点后4位',
         `meter_end_value` int(8) NOT NULL DEFAULT '0' COMMENT '电表总止值 - 精确小数点后4位',
         `vin_code` varchar(255) DEFAULT NULL COMMENT '电动汽车唯一标识',
         `ip` varchar(255) DEFAULT NULL COMMENT '客户ip',
         `loss_rate` int(2) NOT NULL DEFAULT '0' COMMENT '计损比（%）',
         `period_codes` varchar(255) NOT NULL COMMENT '24小时分为48个时段，将每个时段标识尖峰平谷。尖时段:00,峰时段:01,平时段：02，谷时段：03',
         `freeze_money` int(8) NOT NULL DEFAULT '0' COMMENT '订单冻结金额（分）',
         `freeze_status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '冻结金额状态，1冻结，2解冻',
         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `pay_callback`
(
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `order_id` varchar(255) DEFAULT NULL COMMENT '本地订单id',
        `trade_no` varchar(255) DEFAULT NULL COMMENT '外部订单编号',
        `type` varchar(255) NOT NULL COMMENT '类型',
        `log` longtext COMMENT '返回什么就保存什么',
        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=122 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `pay_order`
(
     `id` varchar(64) NOT NULL,
     `user_id` bigint(24) NOT NULL COMMENT '用户',
     `trade_no` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '外部订单编号',
     `list_id` int(11) DEFAULT NULL COMMENT '支付项id',
     `name` varchar(500) NOT NULL COMMENT '名称',
     `price` int(11) NOT NULL DEFAULT '0' COMMENT '支付金额',
     `currency` tinyint(2) NOT NULL DEFAULT '1' COMMENT '币种：1:CNY,2:USD',
     `state` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1生成订单，2支付中，3完成支付，4失败或其他问题，5关闭支付',
     `ip` varchar(255) DEFAULT NULL COMMENT '客户ip',
     `msg` longtext COMMENT '结果信息',
     `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     `add_balance` tinyint(1) NOT NULL DEFAULT '3' COMMENT '加余额结果，1成功，2失败，3没有加币（默认3）',
     `type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '支付类型，1充值余额，2充卡',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `period_rate`
(
       `id` int(4) NOT NULL AUTO_INCREMENT,
       `period` varchar(100) NOT NULL COMMENT '时段',
       `rate` varchar(4) NOT NULL COMMENT '费率',
       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `period_rate_type`
(
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `type` varchar(4) NOT NULL COMMENT '费率类型',
        `name` varchar(255) DEFAULT NULL,
        `remarks` varchar(50) DEFAULT NULL COMMENT '备注',
        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;


CREATE TABLE `piles`
(
         `id` bigint(24) NOT NULL COMMENT '桩id(生产时写入）长度7Bytes',
         `name` varchar(128) NOT NULL COMMENT '桩名称',
         `corp_id` bigint(24) NOT NULL COMMENT '运营商id',
         `station_id` bigint(24) NOT NULL COMMENT '场站id',
         `ac_dc` int(2) NOT NULL DEFAULT '1' COMMENT '交直流：0表示直流桩，1表示交流桩',
         `type` int(2) NOT NULL DEFAULT '1' COMMENT '桩类型：1-慢充，2-快充，3超快充',
         `model` varchar(32) NOT NULL COMMENT '桩型号',
         `power` int(8) NOT NULL DEFAULT '0' COMMENT '桩功率（w）',
         `comm_id` varchar(16) NOT NULL COMMENT '认证id:4G模块IMEI号,mac地址，根据通讯类型区分',
         `shot_num` int(2) DEFAULT NULL COMMENT '枪数量',
         `comm_ver` int(2) DEFAULT NULL COMMENT '通讯协议版本，版本号乘10，v1.0 表示 0x0A',
         `version` varchar(32) DEFAULT NULL COMMENT '程序版本,如：23070101',
         `comm_type` int(2) DEFAULT NULL COMMENT '通讯类型：0-SIM卡，1-LAN，2-WAN，3-其他',
         `simid` varchar(32) DEFAULT NULL COMMENT 'SIM卡号',
         `comm_corp` int(2) DEFAULT NULL COMMENT '通讯运营商：0-移动，2-电信，3-联通，4-其他',
         `opt_id` bigint(24) NOT NULL COMMENT '创建人id',
         `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
         `line` varchar(1) DEFAULT NULL COMMENT 'ABC线',
         `centralized_controller_id` varchar(24) DEFAULT NULL COMMENT '集中器id，集中器mac地址（全大写）',
         `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '在线状态 1:在线 2:离线',
         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `recharge_list`
(
         `id` int(11) NOT NULL AUTO_INCREMENT,
         `title` varchar(255) NOT NULL COMMENT '标题',
         `original_price` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '原价',
         `price` int(11) NOT NULL DEFAULT '0' COMMENT '支付金额',
         `describe` varchar(255) DEFAULT NULL COMMENT '描述',
         `currency` tinyint(2) NOT NULL DEFAULT '1' COMMENT '币种：1:CNY,2:USD',
         `state` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态，1 正常，2 无效',
         `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
         `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
         `sort` int(255) NOT NULL DEFAULT '50' COMMENT '排序',
         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1006 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;


CREATE TABLE `shots`  (
      `id` bigint(24) NOT NULL COMMENT '枪id，桩id+枪序号',
      `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '枪名称',
      `corp_id` bigint(24) NOT NULL COMMENT '运营商id',
      `station_id` bigint(24) NOT NULL COMMENT '场站id',
      `piles_id` bigint(24) NOT NULL COMMENT '桩id',
      `sequence` int(2) NOT NULL COMMENT '枪序号',
      `qrcode` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '枪二维码url',
      `port_num` int(4) NOT NULL COMMENT '虚拟枪号，用于485访问的地址',
      `is_online` int(2) NOT NULL DEFAULT 1 COMMENT '枪在线状态：1-离线，2-在线',
      `work_status` int(2) NOT NULL DEFAULT 1 COMMENT '枪工作状态：1-空闲，2-充电中',
      `link_status` int(2) NOT NULL DEFAULT 1 COMMENT '枪连接状态：1-无连接，2-连接中',
      `opt_id` bigint(24) NOT NULL COMMENT '创建人id',
      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `status` tinyint(4) UNSIGNED NOT NULL DEFAULT 2 COMMENT '状态 0:离线 1:故障 2:空闲 3:充电',
      `is_put_back` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '充电枪是否放回原处(是否插枪) 0:否 1:是  2:未知',
      `status_update_time` datetime NOT NULL COMMENT '状态更新时间',
      `is_detection` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否检测 1:检测 0:不检测',
      `is_fault` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否故障 1:未故障 2:已故障',
      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;


CREATE TABLE `stations_month_statistics`
(
         `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
         `corp_id` bigint(24) unsigned NOT NULL COMMENT '运营商编号',
         `station_id` bigint(24) unsigned NOT NULL COMMENT '场站id',
         `year` mediumint(8) unsigned NOT NULL COMMENT '年份',
         `statistic_time` int(10) unsigned NOT NULL COMMENT '统计时间',
         `sharp` bigint(16) unsigned NOT NULL COMMENT '尖时电量（Kwh），精确到小数点后5位',
         `peak` bigint(16) unsigned NOT NULL COMMENT '峰时电量（Kwh），精确到小数点后5位',
         `flat` bigint(16) unsigned NOT NULL COMMENT '平时电量（Kwh），精确到小数点后5位',
         `valley` bigint(16) unsigned NOT NULL COMMENT '谷时电量（Kwh），精确到小数点后5位',
         `electricity` bigint(16) unsigned NOT NULL COMMENT '使用电量（Kwh），精确到小数点后5位',
         `amount` bigint(16) unsigned NOT NULL COMMENT '总金额（元），精确到小数点后5位',
         `elec_amount` bigint(16) unsigned NOT NULL COMMENT '电费金额（元），精确到小数点后5位',
         `serv_amount` bigint(16) unsigned NOT NULL COMMENT '服务费金额（元），精确到小数点后5位',
         `order_count` bigint(16) unsigned NOT NULL COMMENT '完成的订单数量（已完成的订单）',
         `charge_tick` bigint(16) unsigned NOT NULL COMMENT '充电时长单位（秒）',
         `utility_rate` decimal(5,2) unsigned NOT NULL COMMENT '场站利用率精确到小数点后2位',
         PRIMARY KEY (`id`) USING BTREE,
         KEY `crop_statin` (`corp_id`,`station_id`,`statistic_time`) USING BTREE,
         KEY `statistic_time` (`statistic_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='充电站月统计表';


CREATE TABLE `stations_stats`
(
      `id` bigint(24) NOT NULL AUTO_INCREMENT COMMENT '自增id',
      `corp_id` bigint(24) DEFAULT NULL COMMENT '运营商编号',
      `station_id` bigint(24) DEFAULT NULL COMMENT '场站id',
      `day` timestamp NULL DEFAULT NULL COMMENT '按天统计的时间戳',
      `month` timestamp NULL DEFAULT NULL COMMENT '用于按月分组',
      `year` timestamp NULL DEFAULT NULL COMMENT '用于按年分组',
      `sharp` bigint(16) DEFAULT NULL COMMENT '尖时电量（Kwh），精确到小数点后5位',
      `peak` bigint(16) DEFAULT NULL COMMENT '峰时电量（Kwh），精确到小数点后5位',
      `flat` bigint(16) DEFAULT NULL COMMENT '平时电量（Kwh），精确到小数点后5位',
      `valley` bigint(16) DEFAULT NULL COMMENT '谷时电量（Kwh），精确到小数点后5位',
      `electricity` bigint(16) DEFAULT NULL COMMENT '每天使用电量（Kwh），精确到小数点后5位',
      `amount` bigint(16) DEFAULT NULL COMMENT '每天总金额（元），精确到小数点后5位',
      `elec_amount` bigint(16) DEFAULT NULL COMMENT '每天电费金额（元），精确到小数点后5位',
      `serv_amount` bigint(16) DEFAULT NULL COMMENT '每天服务费金额（元），精确到小数点后5位',
      `order_count` bigint(16) DEFAULT NULL COMMENT '每天完成的订单数量（已完成的订单）',
      `charge_tick` bigint(16) DEFAULT NULL COMMENT '每天充电时长单位（秒）',
      `utility_rate` decimal(5,2) DEFAULT NULL COMMENT '每天场站利用率精确到小数点后2位',
      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=923 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `stations_year_statistics`
(
    `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `corp_id` bigint(24) unsigned DEFAULT NULL COMMENT '运营商编号',
    `station_id` bigint(24) unsigned DEFAULT NULL COMMENT '场站id',
    `statistic_time` int(10) unsigned NOT NULL COMMENT '统计时间',
    `sharp` bigint(16) unsigned NOT NULL COMMENT '尖时电量（Kwh），精确到小数点后5位',
    `peak` bigint(16) unsigned NOT NULL COMMENT '峰时电量（Kwh），精确到小数点后5位',
    `flat` bigint(16) unsigned NOT NULL COMMENT '平时电量（Kwh），精确到小数点后5位',
    `valley` bigint(16) unsigned NOT NULL COMMENT '谷时电量（Kwh），精确到小数点后5位',
    `electricity` bigint(16) unsigned NOT NULL COMMENT '使用电量（Kwh），精确到小数点后5位',
    `amount` bigint(16) unsigned NOT NULL COMMENT '总金额（元），精确到小数点后5位',
    `elec_amount` bigint(16) unsigned NOT NULL COMMENT '电费金额（元），精确到小数点后5位',
    `serv_amount` bigint(16) unsigned NOT NULL COMMENT '服务费金额（元），精确到小数点后5位',
    `order_count` bigint(16) unsigned NOT NULL COMMENT '完成的订单数量（已完成的订单）',
    `charge_tick` bigint(16) unsigned NOT NULL COMMENT '充电时长单位（秒）',
    `utility_rate` decimal(5,2) unsigned NOT NULL COMMENT '场站利用率精确到小数点后2位',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `crop_statin` (`corp_id`,`station_id`,`statistic_time`) USING BTREE,
    KEY `statistic_time` (`statistic_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='充电站年统计表';

CREATE TABLE `sys_menu` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '菜单id',
    `title` varchar(64) NOT NULL COMMENT '菜单名称',
    `rule_name` varchar(255) NOT NULL COMMENT '权限名',
    `level` int(2) NOT NULL DEFAULT '1' COMMENT '所在层级（一级菜单=1）',
    `pid` int(11) NOT NULL DEFAULT '0' COMMENT '上级菜单id（0表示无上级菜单）',
    `icon_url` varchar(255) NOT NULL COMMENT '菜单图标icon的url',
    `url_value` varchar(255) NOT NULL COMMENT '菜单地址',
    `sort` int(10) NOT NULL DEFAULT '50' COMMENT '排序',
    `api_url` varchar(255) NOT NULL COMMENT '接口路径，小写',
    `menu_state` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '显示菜单，1显示，2隐藏',
    `state` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态，1 可以用，2 所有禁止使用',
    `auth_open` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '公开状态，1不公开 2公开',
    `opt_id` bigint(24) DEFAULT NULL COMMENT '创建人id',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `role_name` (`rule_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=187 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (15, '监控中心', 'monitoring_center', 1, 0, 'fa-solid fa-video', '/monitoring_center', 50, '/', 1, 1, 2, NULL, '2023-07-10 17:38:19', '2023-10-31 09:41:07');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (16, '运营中心', 'operations_center', 1, 0, 'fa-solid fa-arrow-trend-up', '/operations_center', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 17:23:41');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (17, '资源中心', 'resource_center', 1, 0, 'fa-solid fa-bag-shopping', '/resource_center', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 14:59:23');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (18, '财务中心', 'finance_center', 1, 0, 'fa-solid fa-file-invoice-dollar', '/finance_center', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 15:40:31');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (19, '会员中心', 'member_center', 1, 0, 'fa-solid fa-crown', '/member_center', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:07:37');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (20, '营销中心', 'marketing_center', 1, 0, 'fa-solid fa-chart-line', '/marketing_center', 50, '/', 2, 1, 1, NULL, '2023-07-10 17:38:19', '2024-01-31 14:23:00');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (21, '交互中心', 'interaction_center', 1, 0, 'fa-solid fa-arrows-rotate', '/interaction_center', 50, '/', 2, 1, 1, NULL, '2023-07-10 17:38:19', '2024-01-31 14:22:48');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (22, '运维中心', 'operations_maintenance_center', 1, 0, 'fa-solid fa-diamond-turn-right', '/operations_maintenance_center', 50, '/', 2, 1, 1, NULL, '2023-07-10 17:38:19', '2024-01-31 14:22:39');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (23, '数据中心', 'data_center', 1, 0, 'fa-solid fa-chart-simple', '/data_center', 50, '/', 2, 1, 1, NULL, '2023-07-10 17:38:19', '2024-01-31 14:22:30');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (24, '系统管理', 'system_management', 1, 0, 'fa-solid fa-gears', '/system_management', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:47:52');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (25, '运营总览', 'monitoring_center_operation_overview', 2, 15, 'fa-solid fa-house', '/monitoring_center/operation_overview', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-17 17:55:12');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (26, '充电监控', 'operations_center_charging_monitoring', 2, 16, 'fa-solid fa-chart-line', '/operations_center/charging_monitoring', 50, '/', 2, 2, 1, NULL, '2023-07-10 17:38:19', '2023-11-16 10:34:49');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (27, '场站监控', 'operations_center_station_monitoring', 2, 16, 'fa-solid fa-charging-station', '/operations_center/station_monitoring', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-11-16 10:33:51');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (28, '设备控制', 'operations_center_device_control', 2, 16, 'fa-solid fa-code-compare', '/operations_center/device_control', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 17:31:51');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (29, '告警监控', 'operations_center_alarm_monitoring', 2, 16, 'fa-solid fa-triangle-exclamation', '/operations_center/alarm_monitoring', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 17:28:41');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (30, '充电站管理', 'resource_center_charging_station_management', 2, 17, 'fa-solid fa-plug-circle-bolt', '/resource_center/charging_station_management', 50, '/admin/stations/stations_list', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 15:23:25');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (31, '充电桩管理', 'resource_center_charging_pile_management', 2, 17, 'fa-solid fa-charging-station', '/resource_center/charging_pile_management', 50, '/admin/piles/piles_list', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 15:23:40');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (32, '充电枪管理', 'resource_center_charging_gun_management', 2, 17, 'fa-solid fa-gas-pump', '/resource_center/charging_gun_management', 50, '/admin/shots/generate_excel', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 15:54:24');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (33, '费率管理', 'resource_center_rate_management', 2, 17, 'fa-solid fa-money-bill-trend-up', '/resource_center/rate_management', 50, '/admin/tariff_group/shots_list', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 15:25:38');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (34, '白名单管理', 'resource_center_whitelist_management', 2, 17, 'fa-solid fa-file-lines', '/resource_center/whitelist_management', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 15:35:12');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (35, '收入总览', 'finance_center_revenue_overview', 2, 18, 'fa-solid fa-file-lines', '/finance_center/revenue_overview', 50, '/', 2, 2, 1, NULL, '2023-07-10 17:38:19', '2023-11-16 10:37:10');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (36, '订单管理', 'finance_center_order_management', 2, 18, 'fa-solid fa-clipboard', '/finance_center/order_management', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 15:42:14');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (37, '结算单管理', 'finance_center_settlement_management', 2, 18, 'fa-solid fa-file-circle-check', '/finance_center/settlement_management', 50, '/', 2, 2, 1, NULL, '2023-07-10 17:38:19', '2023-11-16 10:37:16');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (38, '集团账单', 'finance_center_group_billing', 2, 18, 'fa-solid fa-city', '/finance_center/group_billing', 50, '/', 2, 2, 1, NULL, '2023-07-10 17:38:19', '2023-11-16 10:37:21');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (39, '互联互通订单', 'finance_center_interconnection_order', 2, 18, 'fa-solid fa-money-bill-transfer', '/finance_center/interconnection_order', 50, '/', 2, 2, 1, NULL, '2023-07-10 17:38:19', '2023-11-16 10:37:26');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (40, '发票管理', 'finance_center_invoice_management', 2, 18, 'fa-solid fa-file', '/finance_center/invoice_management', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:05:11');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (41, '对账单管理', 'finance_center_reconciliation_management', 2, 18, 'fa-solid fa-clipboard-check', '/finance_center/reconciliation_management', 50, '/', 2, 2, 1, NULL, '2023-07-10 17:38:19', '2023-11-16 10:37:33');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (42, '运营商管理', 'member_center_operator_management', 2, 19, 'fa-solid fa-user-tie', '/member_center/operator_management', 50, '/admin/menu/corp_list', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:08:13');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (43, '集团管理', 'member_center_group_management', 2, 19, 'fa-solid fa-globe', '/member_center/group_management', 50, '/', 2, 1, 1, NULL, '2023-07-10 17:38:19', '2023-11-17 13:43:04');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (44, '会员管理', 'member_center_member_management', 2, 19, 'fa-solid fa-user-gear', '/member_center/member_management', 50, '/', 2, 1, 1, NULL, '2023-07-10 17:38:19', '2023-11-16 10:38:12');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (45, '钱包管理', 'member_center_wallet_management', 2, 19, 'fa-solid fa-wallet', '/member_center/wallet_management', 50, '/', 2, 1, 1, NULL, '2023-07-10 17:38:19', '2023-11-16 10:38:14');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (46, '卡号管理', 'member_center_card_number_management', 2, 19, 'fa-solid fa-credit-card', '/member_center/card_number_management', 50, '/', 2, 1, 1, NULL, '2023-07-10 17:38:19', '2023-11-16 10:38:17');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (47, '优惠券管理', 'marketing_center_coupon_management', 2, 20, 'fa-solid fa-tag', '/marketing_center/coupon_management', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:13:52');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (48, '中电联标准对接', 'interaction_center_CEPEF_standard_docking', 2, 21, 'fa-solid fa-camera-rotate', '/interaction_center/CEPEF_standard_docking', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:23:29');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (49, '远程控制', 'operations_maintenance_center_remote_control', 2, 22, 'fa-solid fa-hand-pointer', '/operations_maintenance_center/remote_control', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-11-17 14:05:00');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (50, '订单评价', 'operations_maintenance_center_order_evaluation', 2, 22, 'fa-regular fa-pen-to-square', '/operations_maintenance_center/order_evaluation', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-11-17 14:08:07');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (51, '故障报修', 'operations_maintenance_center_fault_repair', 2, 22, 'fa-solid fa-circle-question', '/operations_maintenance_center/fault_repair', 50, '/', 2, 1, 1, NULL, '2023-07-10 17:38:19', '2023-11-16 10:40:30');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (52, '售后服务', 'operations_maintenance_center_after-sales_service', 2, 22, 'fa-solid fa-user-clock', '/operations_maintenance_center/after-sales_service', 50, '/', 2, 1, 1, NULL, '2023-07-10 17:38:19', '2023-11-16 10:40:32');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (53, '经营中心', 'data_center_business_center', 2, 23, 'fa-solid fa-clock-rotate-left', '/data_center/business_center', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:45:16');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (54, '客流分析', 'data_center_passenger_flow_analysis', 2, 23, 'fa-solid fa-bezier-curve', '/data_center/passenger_flow_analysis', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:45:33');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (55, '故障分析', 'data_center_fault_analysis', 2, 23, 'fa-solid fa-triangle-exclamation', '/data_center/fault_analysis', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:45:48');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (56, '报表管理', 'data_center_report_management', 2, 23, 'fa-solid fa-chart-column', '/data_center/report_management', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:47:32');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (57, '权限列表', 'system_management_permission_list', 2, 24, 'fa-solid fa-user-lock', '/system_management/permission_list', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:48:53');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (58, '操作日志', 'system_management_operation_log', 2, 24, 'fa-regular fa-hand-pointer', '/system_management/operation_log', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:50:21');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (59, '充值设置', 'system_management_recharge_option', 2, 24, 'fa-solid fa-sack-dollar', '/system_management/operation_log', 50, '/admin/recharge_option/recharge_option_list', 1, 1, 1, NULL, '2023-07-20 14:20:11', '2023-10-30 16:52:11');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (61, '控制器管理', 'central_controller_management', 2, 17, 'fa-solid fa-tower-cell', '/resource_center/central_controller_management', 50, '/', 2, 2, 1, NULL, '2023-09-22 14:49:42', '2023-11-16 10:36:03');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (62, '权限组', 'system_management_permission_group', 2, 24, 'fa-solid fa-user-group', '/system_management/permission_group', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:52:37');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (63, '管理员列表', 'system_management_admin_list', 2, 24, 'fa-solid fa-address-book', '/system_management/admin_list', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-30 16:57:01');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (64, '测试测试1', 'system_management_admin_list1', 3, 75, 'fa-solid fa-house', '/system_management/admin_list/1', 50, '/', 1, 1, 1, NULL, '2023-07-10 17:38:19', '2023-10-17 17:55:14');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (75, '测试菜单', 'test', 2, 24, 'fa-solid fa-house', '/system_management/test', 50, '/', 2, 1, 1, NULL, '2023-10-12 15:43:23', '2023-11-16 10:38:47');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (79, '测试测试2', 'system_management_admin_list2', 4, 64, 'fa-solid fa-circle-half-stroke', '/', 50, '/', 1, 1, 1, 1, '2023-10-18 11:05:37', '2023-10-19 17:17:53');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (80, '新增管理员', 'system_management_admin_list_add', 3, 63, 'fa-solid fa-circle-half-stroke', '/', 50, '/admin/admin/add', 2, 1, 1, 1, '2023-10-18 14:10:43', '2023-10-18 14:10:43');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (81, '新增管理组', 'system_management_permission_group_add', 3, 62, 'fa-solid fa-circle-half-stroke', '/', 50, '/admin/admingroup/add', 2, 1, 1, 1, '2023-10-18 14:14:00', '2023-10-18 14:14:01');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (82, '管理组列表', 'system_management_permission_group_list', 3, 62, 'fa-solid fa-circle-half-stroke', '/', 50, '/admin/admingroup/admin_group_list', 2, 1, 1, 1, '2023-10-18 14:22:56', '2023-10-18 14:22:57');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (84, '测试测试3', 'system_management_admin_list3', 5, 79, 'fa-solid fa-plus', '/system_management/admin_list/1/2/3', 50, '/', 1, 1, 1, 1, '2023-10-19 17:06:35', '2023-10-19 17:06:35');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (85, '测试测试4', 'system_management_admin_list4', 6, 84, 'fa-solid fa-plus', '/system_management/admin_list/1/2/3/4', 50, '/', 1, 1, 1, 1, '2023-10-19 17:07:41', '2023-10-19 17:07:41');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (86, '测试测试5', 'system_management_admin_list5', 7, 85, 'fa-solid fa-plus', '/system_management/admin_list/1/2/3/4/5', 50, '/', 1, 1, 1, 1, '2023-10-19 17:08:31', '2023-10-19 17:08:31');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (87, '测试测试6', 'system_management_admin_list6', 8, 86, 'fa-solid fa-plus', '/system_management/admin_list/1/2/3/4/5/6', 50, '/', 1, 1, 1, 1, '2023-10-19 17:09:15', '2023-10-19 17:09:15');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (88, '测试测试7', 'system_management_admin_list7', 9, 87, 'fa-solid fa-plus', '/system_management/admin_list/1/2/3/4/5/6/7', 50, '/', 1, 1, 1, 1, '2023-10-19 17:09:54', '2023-10-19 17:09:54');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (89, '测试测试8', 'system_management_admin_list8', 10, 88, 'fa-solid fa-plus', '/system_management/admin_list/1/2/3/4/5/6/7/8', 50, '/', 1, 1, 1, 1, '2023-10-19 17:10:42', '2023-10-19 17:19:26');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (90, '接口:运营总览', 'monitoring_center_operation_overview_api', 2, 15, 'fa-solid fa-arrow-up', '/', 50, '/admin/admingroup/get_rule_list', 2, 1, 1, 1, '2023-10-20 16:47:46', '2024-04-19 16:53:13');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (91, '接口:菜单路由Api', 'admin_menu_user_menu_list', 2, 187, 'fa-solid fa-arrow-up', '/admin_menu_user_menu_list', 50, '/admin/menu/user_menu_list', 2, 1, 1, 1, '2023-10-20 17:09:51', '2024-04-19 16:56:26');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (92, '接口:获取用户路由列表', 'admin_Menu_user_route_list', 2, 187, 'fa-solid fa-arrow-up', '/admin_Menu_user_route_list', 50, '/admin/menu/user_route_list', 2, 1, 1, 1, '2023-10-20 17:20:56', '2024-04-19 16:57:29');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (93, '接口:webSocke', 'admin_login_web_socket', 2, 187, 'fa-solid fa-arrow-up', '/admin_login_web_socket', 50, '/admin/login/web_socket', 2, 1, 1, 1, '2023-10-20 17:27:12', '2024-04-19 16:57:54');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (94, '接口:运营商列表', 'admin/Corp/get_corp_list', 2, 24, 'fa-solid fa-arrow-up', '/admin/Corp/get_corp_list', 50, '/admin/corp/get_corp_list', 2, 1, 1, 1, '2023-11-16 11:05:01', '2024-04-19 17:01:29');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (95, '每月统计列表', 'admin/monitoringCenter/monthlyStatistics', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/monitoringCenter/monthlyStatistics', 50, '/admin/monitoringcenter/monthlystatistics', 2, 1, 1, 1, '2023-11-16 11:06:19', '2023-11-16 11:06:20');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (96, '首页数据概览', 'admin/monitoringCenter/dataOverview', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/monitoringCenter/dataOverview', 50, '/admin/monitoringcenter/dataoverview', 2, 1, 1, 1, '2023-11-16 11:07:15', '2023-11-16 11:07:16');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (97, '告警监控/告警记录列表', 'admin/AlarmRecord/get_list_data', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/AlarmRecord/get_list_data', 50, '/admin/alarmrecord/get_list_data', 2, 1, 1, 1, '2023-11-16 11:07:58', '2023-11-16 11:45:56');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (98, '接口:充电站的月排行榜', 'admin/monitoringCenter/stationsMonthRankingListTop10', 2, 15, 'fa-solid fa-arrow-up', '/admin/monitoringCenter/stationsMonthRankingListTop10', 50, '/admin/monitoringcenter/stationsmonthrankinglisttop10', 2, 1, 1, 1, '2023-11-16 11:10:24', '2024-04-19 16:53:26');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (99, '接口:自动登录', 'admin/Login/auto_login', 2, 187, 'fa-solid fa-arrow-up', '/admin/Login/auto_login', 50, '/admin/login/auto_login', 2, 1, 1, 1, '2023-11-16 11:11:28', '2024-04-19 16:58:21');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (103, '接口:统计数据', 'admin/stationsMonitor/statisticsData', 3, 27, 'fa-solid fa-arrow-up', '/admin/stationsMonitor/statisticsData', 50, '/admin/stationsmonitor/statisticsdata', 2, 1, 1, 1, '2023-11-16 11:19:38', '2024-04-19 17:08:16');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (104, '接口:电量使用情况统计', 'admin/stationsMonitor/electricityUsageStatistics', 3, 27, 'fa-solid fa-arrow-up', '/admin/stationsMonitor/electricityUsageStatistics', 50, '/admin/stationsmonitor/electricityusagestatistics', 2, 1, 1, 1, '2023-11-16 11:20:54', '2024-04-19 17:08:47');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (107, '监控中心/每日统计列表', 'admin/monitoringCenter/dailyStatistics', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/monitoringCenter/dailyStatistics', 50, '/admin/monitoringcenter/dailystatistics', 2, 1, 1, 1, '2023-11-16 11:24:55', '2023-11-16 11:24:56');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (108, '充电站管理/获取充电站排序信息', 'admin/Stations/sort_list_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Stations/sort_list_info', 50, '/admin/stations/sort_list_info', 2, 1, 1, 1, '2023-11-16 11:32:50', '2023-11-16 11:32:50');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (109, '充电站管理/获取充电站列表', 'admin/Stations/get_stations_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Stations/get_stations_list', 50, '/admin/stations/get_stations_list', 2, 1, 1, 1, '2023-11-16 11:36:11', '2023-11-16 11:36:12');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (110, '充电站管理/充电站详情', 'admin/Stations/get_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Stations/get_info', 50, '/admin/stations/get_info', 2, 1, 1, 1, '2023-11-16 11:38:46', '2023-11-16 11:38:46');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (111, '充电站管理/新增充电站', 'admin/Stations/add', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Stations/add', 50, '/admin/stations/add', 2, 1, 1, 1, '2023-11-16 11:39:27', '2023-11-16 11:39:28');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (112, '充电站管理/编辑充电站', 'admin/Stations/edit', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Stations/edit', 50, '/admin/stations/edit', 2, 1, 1, 1, '2023-11-16 11:39:52', '2023-11-16 11:39:53');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (113, '充电站管理/删除充电站', 'admin/Stations/delete', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Stations/delete', 50, '/admin/stations/delete', 2, 1, 1, 1, '2023-11-16 11:41:10', '2023-11-16 11:41:10');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (114, '充电站管理/充电站列表', 'admin/Stations/stations_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Stations/stations_list', 50, '/admin/stations/stations_list', 2, 1, 1, 1, '2023-11-16 11:41:53', '2023-11-16 11:41:54');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (115, '告警监控/删除告警记录', 'admin/AlarmRecord/delete_data', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/AlarmRecord/delete_data', 50, '/admin/alarmrecord/delete_data', 2, 1, 1, 1, '2023-11-16 11:46:28', '2023-11-16 11:46:29');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (116, '发票管理/开票记录', 'admin/electronic_invoice/list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/electronic_invoice/list', 50, '/admin/electronic_invoice/list', 2, 1, 1, 1, '2023-11-16 11:47:52', '2023-11-16 11:47:53');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (117, '发票管理/开票统计', 'admin/electronic_invoice/statistics', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/electronic_invoice/statistics', 50, '/admin/electronic_invoice/statistics', 2, 1, 1, 1, '2023-11-16 11:48:31', '2023-11-16 11:48:31');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (118, '充电枪管理/充电枪列表', 'admin/Shots/shots_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Shots/shots_list', 50, '/admin/shots/shots_list', 2, 1, 1, 1, '2023-11-16 11:50:52', '2023-11-16 11:50:53');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (119, '充电枪管理/获取充电枪排序信息', 'admin/Shots/sort_list_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Shots/sort_list_info', 50, '/admin/shots/sort_list_info', 2, 1, 1, 1, '2023-11-16 11:51:28', '2023-11-16 11:51:29');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (120, '充电枪管理/充电枪详情', 'admin/Shots/get_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Shots/get_info', 50, '/admin/shots/get_info', 2, 1, 1, 1, '2023-11-16 11:51:52', '2023-11-16 11:51:52');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (121, '充电枪管理/新增充电枪', 'admin/Shots/add', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Shots/add', 50, '/admin/shots/add', 2, 1, 1, 1, '2023-11-16 11:52:19', '2023-11-16 11:52:20');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (122, '充电枪管理/编辑充电枪', 'admin/Shots/edit', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Shots/edit', 50, '/admin/shots/edit', 2, 1, 1, 1, '2023-11-16 11:52:45', '2023-11-16 11:52:46');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (123, '充电枪管理/删除充电枪', '/admin/Shots/delete', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Shots/delete', 50, '/admin/shots/delete', 2, 1, 1, 1, '2023-11-16 11:53:09', '2023-11-16 11:53:09');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (124, '充电枪管理/导出充电枪列表Excel', 'admin/Shots/out_shots_list_excel', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Shots/out_shots_list_excel', 50, '/admin/shots/out_shots_list_excel', 2, 1, 1, 1, '2023-11-16 11:53:35', '2023-11-16 11:53:36');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (125, '充电枪管理/批量生成二维码', 'admin/Shots/batch_create_qr', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Shots/batch_create_qr', 50, '/admin/shots/batch_create_qr', 2, 1, 1, 1, '2023-11-16 11:54:06', '2023-11-16 11:54:06');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (126, '充电桩管理/充电桩列表', 'admin/Piles/piles_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Piles/piles_list', 50, '/admin/piles/piles_list', 2, 1, 1, 1, '2023-11-16 11:58:12', '2023-11-16 11:58:12');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (127, '充电桩管理/获取充电桩列表', 'admin/Piles/get_piles_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Piles/get_piles_list', 50, '/admin/piles/get_piles_list', 2, 1, 1, 1, '2023-11-16 11:58:47', '2023-11-16 11:58:48');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (128, '充电桩管理/获取充电桩排序信息', 'admin/Piles/sort_list_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Piles/sort_list_info', 50, '/admin/piles/sort_list_info', 2, 1, 1, 1, '2023-11-16 11:59:37', '2023-11-16 11:59:37');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (129, '充电桩管理/充电桩详情', 'admin/Piles/get_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Piles/get_info', 50, '/admin/piles/get_info', 2, 1, 1, 1, '2023-11-16 12:00:04', '2023-11-16 12:00:05');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (130, '充电桩管理/新增充电桩', 'admin/Piles/add', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Piles/add', 50, '/admin/piles/add', 2, 1, 1, 1, '2023-11-16 12:01:09', '2023-11-16 12:01:09');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (131, '充电桩管理/编辑充电桩', 'admin/Piles/edit', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Piles/edit', 50, '/admin/piles/edit', 2, 1, 1, 1, '2023-11-16 12:02:13', '2023-11-16 12:02:13');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (132, '充电桩管理/删除充电桩', 'admin/Piles/delete', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Piles/delete', 50, '/admin/piles/delete', 2, 1, 1, 1, '2023-11-16 12:02:49', '2023-11-16 12:02:49');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (133, '费率组管理/费率组列表', 'admin/TariffGroup/tariff_group_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/TariffGroup/tariff_group_list', 50, '/admin/tariffgroup/tariff_group_list', 2, 1, 1, 1, '2023-11-16 12:04:05', '2023-11-16 12:04:05');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (134, '费率组管理/获取费率组排序信息', 'admin/TariffGroup/sort_list_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/TariffGroup/sort_list_info', 50, '/admin/tariffgroup/sort_list_info', 2, 1, 1, 1, '2023-11-16 12:04:38', '2023-11-16 12:04:39');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (135, '费率组管理/费率组详情', 'admin/TariffGroup/get_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/TariffGroup/get_info', 50, '/admin/tariffgroup/get_info', 2, 1, 1, 1, '2023-11-16 12:05:02', '2023-11-16 12:05:02');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (136, '费率组管理/新增费率组', 'admin/TariffGroup/add', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/TariffGroup/add', 50, '/admin/tariffgroup/add', 2, 1, 1, 1, '2023-11-16 12:05:26', '2023-11-16 12:05:26');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (137, '费率组管理/编辑费率组', 'admin/TariffGroup/edit', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/TariffGroup/edit', 50, '/admin/tariffgroup/edit', 2, 1, 1, 1, '2023-11-16 12:05:48', '2023-11-16 12:05:48');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (138, '费率组管理/删除费率组', 'admin/TariffGroup/delete', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/TariffGroup/delete', 50, '/admin/tariffgroup/delete', 2, 1, 1, 1, '2023-11-16 12:06:11', '2023-11-16 12:06:12');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (139, '费率组管理/获取费率组列表', 'admin/TariffGroup/get_tariff_group_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/TariffGroup/get_tariff_group_list', 50, '/admin/tariffgroup/get_tariff_group_list', 2, 1, 1, 1, '2023-11-16 12:06:33', '2023-11-16 12:06:34');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (140, '费率组管理/获取时段费率列表', 'admin/TariffGroup/get_period_rate_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/TariffGroup/get_period_rate_list', 50, '/admin/tariffgroup/get_period_rate_list', 2, 1, 1, 1, '2023-11-16 12:06:56', '2023-11-16 12:06:56');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (141, '费率组管理/获取时段费率类型', 'admin/TariffGroup/get_period_rate_type', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/TariffGroup/get_period_rate_type', 50, '/admin/tariffgroup/get_period_rate_type', 2, 1, 1, 1, '2023-11-16 12:07:14', '2023-11-16 12:07:15');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (142, '公共模块/获取主机域名', 'admin/Common/get_host', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Common/get_host', 50, '/admin/common/get_host', 2, 1, 1, 1, '2023-11-16 12:41:35', '2023-11-16 12:41:35');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (143, '接口:上传图片', 'admin/Common/upload_img', 2, 187, 'fa-solid fa-arrow-up', '/admin/Common/upload_img', 50, '/admin/common/upload_img', 2, 1, 1, 1, '2023-11-16 12:41:58', '2024-04-19 16:58:50');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (144, '接口:充电枪状态统计', 'admin/stationsMonitor/shotsStatusStatistics', 3, 27, 'fa-solid fa-arrow-up', '/admin/stationsMonitor/shotsStatusStatistics', 50, '/admin/stationsmonitor/shotsstatusstatistics', 2, 1, 1, 1, '2023-11-16 12:44:35', '2024-04-19 17:10:21');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (145, '订单管理/获取充电订单列表', 'admin/Order/get_order_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Order/get_order_list', 50, '/admin/order/get_order_list', 2, 1, 1, 1, '2023-11-16 12:48:34', '2023-11-16 12:48:35');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (146, '订单管理/获取充电订单详情', 'admin/Order/get_order_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Order/get_order_info', 50, '/admin/order/get_order_info', 2, 1, 1, 1, '2023-11-16 12:48:59', '2023-11-16 12:49:00');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (147, '订单管理/订单修复', 'admin/Order/order_repair', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Order/order_repair', 50, '/admin/order/order_repair', 2, 1, 1, 1, '2023-11-16 12:49:23', '2023-11-16 12:49:23');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (148, '会员中心/运营商管理/运营商列表', 'admin/Corp/corp_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Corp/corp_list', 50, '/admin/corp/corp_list', 2, 1, 1, 1, '2023-11-16 12:51:00', '2023-11-16 12:51:01');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (149, '会员中心/运营商管理/获取运营商排序信息', 'admin/Corp/sort_list_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Corp/sort_list_info', 50, '/admin/corp/sort_list_info', 2, 1, 1, 1, '2023-11-16 12:51:26', '2023-11-16 12:51:27');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (150, '会员中心/运营商管理/运营商详情', 'admin/Corp/get_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Corp/get_info', 50, '/admin/corp/get_info', 2, 1, 1, 1, '2023-11-16 12:51:53', '2023-11-16 12:51:53');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (151, '会员中心/运营商管理/新增运营商', 'admin/Corp/add', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Corp/add', 50, '/admin/corp/add', 2, 1, 1, 1, '2023-11-16 12:52:19', '2023-11-16 12:52:19');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (152, '会员中心/运营商管理/编辑运营商', 'admin/Corp/edit', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Corp/edit', 50, '/admin/corp/edit', 2, 1, 1, 1, '2023-11-16 12:52:46', '2023-11-16 12:52:46');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (153, '会员中心/运营商管理/删除运营商', 'admin/Corp/delete', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Corp/delete', 50, '/admin/corp/delete', 2, 1, 1, 1, '2023-11-16 12:53:15', '2023-11-16 12:53:16');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (154, '会员中心/运营商管理/更新运营商审核状态', 'admin/Corp/update_audit_status', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Corp/update_audit_status', 50, '/admin/corp/update_audit_status', 2, 1, 1, 1, '2023-11-16 12:54:13', '2023-11-16 12:54:13');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (155, '会员中心/运营商管理/更新运营商状态', 'admin/Corp/update_status', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Corp/update_status', 50, '/admin/corp/update_status', 2, 1, 1, 1, '2023-11-16 12:54:39', '2023-11-16 12:54:39');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (156, '管理组/管理组列表', 'admin/AdminGroup/admin_group_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/AdminGroup/admin_group_list', 50, '/admin/admingroup/admin_group_list', 2, 1, 1, 1, '2023-11-16 17:04:50', '2023-11-16 17:04:51');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (157, '管理组/获取管理组排序信息', 'admin/AdminGroup/sort_list_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/AdminGroup/sort_list_info', 50, '/admin/admingroup/sort_list_info', 2, 1, 1, 1, '2023-11-16 17:05:24', '2023-11-16 17:05:24');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (158, '管理组/管理组详情', 'admin/AdminGroup/get_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/AdminGroup/get_info', 50, '/admin/admingroup/get_info', 2, 1, 1, 1, '2023-11-16 17:05:57', '2023-11-16 17:05:57');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (159, '管理组/新增管理组', 'admin/AdminGroup/add', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/AdminGroup/add', 50, '/admin/admingroup/add', 2, 1, 1, 1, '2023-11-16 17:06:30', '2023-11-16 17:06:30');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (160, '管理组/编辑管理组', 'admin/AdminGroup/edit', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/AdminGroup/edit', 50, '/admin/admingroup/edit', 2, 1, 1, 1, '2023-11-16 17:07:04', '2023-11-16 17:07:05');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (161, '管理组/删除管理员', 'admin/AdminGroup/delete', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/AdminGroup/delete', 50, '/admin/admingroup/delete', 2, 1, 1, 1, '2023-11-16 17:07:30', '2023-11-16 17:07:31');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (162, '管理组/获取管理组权限列表', 'admin/AdminGroup/get_rule_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/AdminGroup/get_rule_list', 50, '/admin/admingroup/get_rule_list', 2, 1, 1, 1, '2023-11-16 17:07:55', '2023-11-16 17:07:56');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (163, '管理组/设置管理组权限', 'admin/AdminGroup/set_admin_group_rule', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/AdminGroup/set_admin_group_rule', 50, '/admin/admingroup/set_admin_group_rule', 2, 1, 1, 1, '2023-11-16 17:08:27', '2023-11-16 17:08:28');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (164, '管理员/管理员列表', 'admin/Admin/admin_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Admin/admin_list', 50, '/admin/admin/admin_list', 2, 1, 1, 1, '2023-11-16 17:10:57', '2023-11-16 17:10:58');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (165, '管理员/获取管理员排序信息', 'admin/Admin/sort_list_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Admin/sort_list_info', 50, '/admin/admin/sort_list_info', 2, 1, 1, 1, '2023-11-16 17:13:21', '2023-11-16 17:13:22');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (166, '管理员/管理员详情', 'admin/Admin/get_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Admin/get_info', 50, '/admin/admin/get_info', 2, 1, 1, 1, '2023-11-16 17:14:05', '2023-11-16 17:14:06');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (167, '管理员/新增管理员', 'admin/Admin/add', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Admin/add', 50, '/admin/admin/add', 2, 1, 1, 1, '2023-11-16 17:14:43', '2023-11-16 17:14:44');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (168, '管理员/编辑管理员', 'admin/Admin/edit', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Admin/edit', 50, '/admin/admin/edit', 2, 1, 1, 1, '2023-11-16 17:15:32', '2023-11-16 17:15:33');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (169, '管理员/重设管理员密码', 'admin/Admin/reset_password', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Admin/reset_password', 50, '/admin/admin/reset_password', 2, 1, 1, 1, '2023-11-16 17:16:04', '2023-11-16 17:16:05');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (170, '管理员/删除管理员', 'admin/Admin/delete', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Admin/delete', 50, '/admin/admin/delete', 2, 1, 1, 1, '2023-11-16 17:16:34', '2023-11-16 17:16:35');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (171, '节点管理/获取菜单列表', 'admin/Menu/menu_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Menu/menu_list', 50, '/admin/menu/menu_list', 2, 1, 1, 1, '2023-11-16 17:18:04', '2023-11-16 17:18:04');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (172, '节点管理/获取用户菜单列表', 'admin/Menu/user_menu_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Menu/user_menu_list', 50, '/admin/menu/user_menu_list', 2, 1, 1, 1, '2023-11-16 17:18:38', '2023-11-16 17:18:39');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (173, '节点管理/获取用户路由列表', 'admin/Menu/user_route_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Menu/user_route_list', 50, '/admin/menu/user_route_list', 2, 1, 1, 1, '2023-11-16 17:19:09', '2023-11-16 17:19:09');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (174, '节点管理/菜单详情', 'admin/Menu/get_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Menu/get_info', 50, '/admin/menu/get_info', 2, 1, 1, 1, '2023-11-16 17:25:28', '2023-11-16 17:25:28');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (175, '节点管理/新增菜单', 'admin/Menu/add', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Menu/add', 50, '/admin/menu/add', 2, 1, 1, 1, '2023-11-16 17:26:05', '2023-11-16 17:26:06');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (176, '节点管理/编辑菜单', 'admin/Menu/edit', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Menu/edit', 50, '/admin/menu/edit', 2, 1, 1, 1, '2023-11-16 17:26:39', '2023-11-16 17:26:39');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (177, '节点管理/删除菜单(包括下级菜单)', 'admin/Menu/delete_all', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Menu/delete_all', 50, '/admin/menu/delete_all', 2, 1, 1, 1, '2023-11-16 17:27:24', '2023-11-16 17:27:25');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (178, '节点管理/更新显示菜单状态', 'admin/Menu/update_menu_state', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Menu/update_menu_state', 50, '/admin/menu/update_menu_state', 2, 1, 1, 1, '2023-11-16 17:27:57', '2023-11-16 17:27:57');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (179, '节点管理/更新公开状态', 'admin/Menu/update_auth_open', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Menu/update_auth_open', 50, '/admin/menu/update_auth_open', 2, 1, 1, 1, '2023-11-16 17:28:28', '2023-11-16 17:28:28');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (180, '节点管理/更新菜单可用状态', 'admin/Menu/update_state', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Menu/update_state', 50, '/admin/menu/update_state', 2, 1, 1, 1, '2023-11-16 17:28:54', '2023-11-16 17:28:54');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (181, '节点管理/更新菜单排序', 'admin/Menu/update_sort', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Menu/update_sort', 50, '/admin/menu/update_sort', 2, 1, 1, 1, '2023-11-16 17:29:40', '2023-11-16 17:29:40');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (182, '节点管理/获取父菜单列表', 'admin/Menu/get_pid_list', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/Menu/get_pid_list', 50, '/admin/menu/get_pid_list', 2, 1, 1, 1, '2023-11-16 17:30:08', '2023-11-16 17:30:09');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (183, '接口:获取城市', 'admin/City/get_city', 2, 187, 'fa-solid fa-arrow-up', '/admin/City/get_city', 50, '/admin/city/get_city', 2, 1, 1, 1, '2023-11-16 17:44:22', '2024-04-19 16:59:25');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (184, '运营中心/充电桩控制列表', 'admin/PilesControl/index', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/PilesControl/index', 50, '/admin/pilescontrol/index', 2, 1, 1, 1, '2023-11-17 13:56:33', '2023-11-17 13:56:33');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (185, '运营中心/获取充电桩排序信息', 'admin/PilesControl/sort_list_info', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/PilesControl/sort_list_info', 50, '/admin/pilescontrol/sort_list_info', 2, 1, 1, 1, '2023-11-17 13:57:07', '2023-11-17 13:57:08');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (186, '运营中心/下发费率', 'admin/PilesControl/issue_tariff_group', 1, 0, 'fa-solid fa-circle-half-stroke', '/admin/PilesControl/issue_tariff_group', 50, '/admin/pilescontrol/issue_tariff_group', 2, 1, 1, 1, '2023-11-17 13:57:40', '2023-11-17 13:57:40');
INSERT INTO `sys_menu` (`id`, `title`, `rule_name`, `level`, `pid`, `icon_url`, `url_value`, `sort`, `api_url`, `menu_state`, `state`, `auth_open`, `opt_id`, `create_time`, `update_time`) VALUES (187, '基础服务', 'base_service', 1, 0, 'fa-solid fa-paperclip', '/', 0, '/', 2, 1, 1, 1, '2024-04-19 16:55:08', '2024-04-19 16:55:21');



CREATE TABLE `tariff` (
  `id` int(11) NOT NULL COMMENT '自增id',
  `group_id` int(4) DEFAULT NULL COMMENT '费率组id',
  `group_name` varchar(128) DEFAULT NULL COMMENT '费率组名称',
  `corp_id` bigint(24) DEFAULT NULL COMMENT '运营商编号',
  `station_id` bigint(24) DEFAULT NULL COMMENT '场站id',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `elec_fee` int(8) DEFAULT NULL COMMENT '电费（X元/度）',
  `service_fee` int(8) DEFAULT NULL COMMENT '服务费（X元/度）',
  `currency` int(2) DEFAULT NULL COMMENT '币种：CNY',
  `opt_id` bigint(24) DEFAULT NULL COMMENT '创建人id',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `tariff_group` (
    `id` int(4) NOT NULL AUTO_INCREMENT COMMENT '费率组id（长度2Bytes）',
    `name` varchar(128) NOT NULL COMMENT '费率组名称',
    `sharp_fee` int(8) NOT NULL DEFAULT '0' COMMENT '尖时电费(精确到小数点后5位）',
    `peak_fee` int(8) NOT NULL DEFAULT '0' COMMENT '峰时电费(精确到小数点后5位）',
    `flat_fee` int(8) NOT NULL DEFAULT '0' COMMENT '平时电费(精确到小数点后5位）',
    `valley_fee` int(8) NOT NULL DEFAULT '0' COMMENT '谷时电费(精确到小数点后5位）',
    `sharp_ser_fee` int(8) NOT NULL DEFAULT '0' COMMENT '尖时服务费(精确到小数点后5位）',
    `peak_ser_fee` int(8) NOT NULL DEFAULT '0' COMMENT '峰时服务费(精确到小数点后5位）',
    `flat_ser_fee` int(8) NOT NULL DEFAULT '0' COMMENT '平时服务费(精确到小数点后5位）',
    `valley_ser_fee` int(8) NOT NULL DEFAULT '0' COMMENT '谷时服务费(精确到小数点后5位）',
    `loss_rate` int(2) NOT NULL DEFAULT '0' COMMENT '计损比（%）',
    `period_codes` varchar(255) NOT NULL COMMENT '24小时分为48个时段，将每个时段标识尖峰平谷。尖时段:00,峰时段:01,平时段：02，谷时段：03',
    `opt_id` bigint(24) NOT NULL COMMENT '创建人id',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `period_json_sum` json DEFAULT NULL COMMENT '时段费率，合并',
    `period_json` json DEFAULT NULL COMMENT '时段费率，独立',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1006 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;


CREATE TABLE `user_balance_freeze_log` (
   `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT 'id',
   `user_id` bigint(24) NOT NULL COMMENT '用户id',
   `balance` int(11) NOT NULL DEFAULT '0' COMMENT '冻结余额，单位分',
   `order_id` varchar(64) DEFAULT NULL,
   `memo` varchar(255) DEFAULT NULL COMMENT '备注',
   `state` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1冻结中，2完成',
   `type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '支付类型，1订单，2其他',
   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间',
   `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=975 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `user_balance_log` (
    `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user_id` bigint(24) NOT NULL COMMENT '用户id',
    `amount` int(11) NOT NULL COMMENT '余额变动',
    `memo` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间',
    `type` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1097 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;


CREATE TABLE `users` (
     `id` bigint(24) NOT NULL AUTO_INCREMENT COMMENT '用户id',
     `nickname` varchar(64) NOT NULL DEFAULT '微信用户' COMMENT '用户昵称',
     `phone` varchar(24) NOT NULL DEFAULT '1' COMMENT '用户手机号',
     `freeze_balance` int(11) NOT NULL DEFAULT '0' COMMENT '冻结余额，单位分',
     `balance` int(11) NOT NULL DEFAULT '0' COMMENT '可用余额，单位分',
     `auth_status` tinyint(3) NOT NULL DEFAULT '1' COMMENT '授权状态： 1 未授权 2 已授权',
     `freeze` tinyint(1) NOT NULL DEFAULT '1' COMMENT '账号是否冻结，1-未冻结，2-冻结',
     `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '用户创建时间',
     `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     `gender` int(2) NOT NULL DEFAULT '3' COMMENT '用户性别  1 男 2 女 3 未知',
     `open_id` varchar(64) NOT NULL COMMENT '微信用户open_id',
     `avatar` varchar(500) NOT NULL DEFAULT 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132' COMMENT '用户头像url',
     `country` varchar(128) DEFAULT NULL COMMENT '国家',
     `province` varchar(128) DEFAULT NULL COMMENT '省',
     `city` varchar(128) DEFAULT NULL COMMENT '市',
     `district` varchar(128) DEFAULT NULL COMMENT '区',
     `address` varchar(128) DEFAULT NULL COMMENT '详细地址',
     `amount_charged` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '目前正在充电的订单已使用金额(保留4为小数)',
     `credit_limit` int(11) NOT NULL DEFAULT '0' COMMENT '信用额度(单位:分)',
     PRIMARY KEY (`id`) USING BTREE,
     UNIQUE KEY `open_id` (`open_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1000000000029 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `users_collect_stations` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `user_id` bigint(24) NOT NULL COMMENT '用户id',
      `station_id` bigint(24) NOT NULL COMMENT '场站id',
      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      PRIMARY KEY (`id`) USING BTREE,
      UNIQUE KEY `用户和场站唯一` (`user_id`,`station_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=202 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `wechat_answer_record` (
    `id` char(32) NOT NULL COMMENT 'ID',
    `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='微信应答记录';

