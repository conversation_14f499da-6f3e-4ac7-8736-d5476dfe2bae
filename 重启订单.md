## Composer 组件

```bash
    composer require liehuohuyu/charge-transfer-service-protocol:v1.0.7.alpha.2
```
## 数据库改动

```mysql
CREATE TABLE `restart_order_log`
(
    `id`             int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `order_id`       varchar(64)      NOT NULL COMMENT '充电订单ID',
    `applet_user_id` bigint UNSIGNED  NOT NULL COMMENT '与订单关联的小程序用户ID',
    `op_user_id`     int(10) unsigned NOT NULL COMMENT '操作的后台用户ID',
    `op_client_ip`   varchar(255)     NOT NULL COMMENT '操作员的客户端IP',
    `power_limit`    INT(10) UNSIGNED NOT NULL COMMENT '限制功率(单位:瓦)',
    `create_time`    timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='重启订单日志';
```


## 需要重启的进程

- 读取到的中转服务协议包的消费者
- 发送协议包到中转服务的消费者