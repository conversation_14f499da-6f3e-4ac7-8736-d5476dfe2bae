services:
  mqttx-web:
    build:
      context: .
      args:
        - MQTTX_VERSION=$MQTTX_VERSION
    image: ${DOCKER_IMAGE_PREFIX}${CI_SOURCE_NAME}/mqttx-web:$MQTTX_VERSION-$CI_COMMIT_REF_NAME
    container_name: ${DOCKER_CONTAINER_PREFIX}${CI_SOURCE_NAME}-mqttx-web
    networks:
      - resc-bridge
    extra_hosts:
      - host.docker.internal:host-gateway
    env_file: ../../.env
    environment:
      - TZ=Asia/Shanghai
      - CI_COMMIT_REF_NAME=$CI_COMMIT_REF_NAME
    cpus: '0.50'
    mem_limit: 50M
    # restart: unless-stopped
    restart: on-failure:3
    healthcheck:
      test: [ "CMD-SHELL", "curl -I http://localhost:80/ || exit 1" ]
      interval: 60s
      timeout: 5s
      retries: 3
      start_period: 5s
