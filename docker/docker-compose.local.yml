# 本地测试时, 请复制文件到local.docker-compose.local.yml, 不要直接修改此文件

services:
  nginx:
    extends:
      file: ./cmd/nginx/docker-compose.common.yml
      service: nginx
    volumes:
      - ./cmd/nginx/conf/nginx.local.conf:/etc/nginx/nginx.conf
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
  php:
    extends:
      file: ./cmd/php/docker-compose.common.yml
      service: php-fpm-build
    build:
      context: php
      dockerfile: Dockerfile.local
    volumes:
      - ~/.ssh:/root/.ssh
      - ./cmd/php/etc/php.ini:/usr/local/etc/php/php.ini
      - ./cmd/php/etc/php-fpm.d/zzz-www.conf:/usr/local/etc/php-fpm.d/zzz-www.conf
      - ./cmd/php/etc/php-fpm.d/zzz-www.local.conf:/usr/local/etc/php-fpm.d/zzz-www.local.conf
      - ./php/zzzz-www.conf:/usr/local/etc/php-fpm.d/zzzz-www.conf
      - ./php/docker-php-entrypoint:/usr/local/bin/docker-php-entrypoint
      - ./php/charge-admin-cron:/etc/cron.d/charge-admin-cron
      - ./php/supervisor.ini:/etc/supervisor.d/supervisor.ini
      - ..:/var/www/html
