services:
  nginx:
    volumes:
      # - ./cmd/nginx/logs:/var/log/nginx # 本地测试
      # - /www/wwwlogs/${CI_SOURCE_NAME}/nginx:/var/log/nginx # 线上使用
      - ../public:/var/www/html/public
    ports:
      - "$API_ADMIN_V1_PORT:80"
    networks:
      default:
    depends_on:
      php:
        # condition: service_healthy
        condition: service_started
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://127.0.0.1:80/ || exit 1"]
    working_dir: /var/www/html
  php:
    networks:
      default:
        aliases:
          - php-fpm
    mem_limit: 300M
    command: php-fpm -R
    user: root

networks:
  resc-bridge:
    external: true
    driver: bridge
  default:
