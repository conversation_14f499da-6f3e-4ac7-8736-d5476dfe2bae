# Docker Compose
COMPOSE_PROJECT_NAME=charge-admin

# CI/CD
CI_SOURCE_NAME=charge-admin
# 使用对应的环境的分支名, 保留空可以自动使用脚本定义的环境
CI_COMMIT_REF_NAME=

# Docker Customization
DOCKER_IMAGE_PREFIX=
DOCKER_CONTAINER_PREFIX=
DOCKER_BASE_IMAGE_PREFIX=resc/
# 依赖的基础镜像列表，使用空格分割多个镜像
DOCKER_BASE_IMAGE_LIST="nginx php-fpm"
# 指定编译使用的CPU核心数,保留空可以自动使用脚本获取所有可用核心
DOCKER_BUILD_CORES=

# Ports
API_ADMIN_V1_PORT=10011

# Versions
NGINX_VERSION=1.26
PHP_VERSION=8.0
SWOOLE_VERSION=5.1.6

# Customization
COMPOSER_UPDATE=1
DEBUG_MODE=0
SSH_PRIKEY_FILENAME=
