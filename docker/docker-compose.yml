# 本地测试时, 请复制文件到local.docker-compose.yml, 不要直接修改此文件

services:
  nginx:
    extends:
      file: ./cmd/nginx/docker-compose.common.yml
      service: nginx-build
    build:
      context: nginx
  php:
    extends:
      file: ./cmd/php/docker-compose.common.yml
      service: php-fpm-build
    build:
      context: ..
    volumes:
      - /home/<USER>/.ssh:/root/.ssh # 线上使用
      # - ~/.ssh:/root/.ssh # 本地测试
      - ../.env:/var/www/html/.env
      - ../runtime/:/var/www/html/runtime
      - ../public:/var/www/html/public
      - ../vendor:/var/www/html/vendor
