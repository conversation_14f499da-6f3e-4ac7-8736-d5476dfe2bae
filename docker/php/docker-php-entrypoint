#!/bin/sh

SSH_PRIKEY_FILE=/root/.ssh/$SSH_PRIKEY_FILENAME
if [ -f "$SSH_PRIKEY_FILE" ]; then
    chmod 600 $SSH_PRIKEY_FILE
fi

set -eu

rm -rf runtime/pid

[[ ! -d runtime/pid ]] && mkdir -p runtime/pid
[[ ! -d runtime/crontab ]] && mkdir -p runtime/crontab
[[ ! -d runtime/supervisor ]] && mkdir -p runtime/supervisor
[[ ! -d runtime/supervisor/log ]] && mkdir -p runtime/supervisor/log
[[ ! -d vendor ]] && mkdir -p vendor

if [[ "$COMPOSER_UPDATE" == "" || "$COMPOSER_UPDATE" == "1" || "$COMPOSER_UPDATE" == "true" ]]; then
    updateOpt=""
    if [ "$CI_COMMIT_REF_NAME" != "local" ]; then
        updateOpt="--no-dev -o"
    fi

    composer update $updateOpt
fi

supervisord -c /etc/supervisord.conf

# first arg is `-f` or `--some-option`
if [ "${1#-}" != "$1" ]; then
    set -- php-fpm "$@"
fi

exec "$@"
