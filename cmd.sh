#!/bin/sh
#
# Docker运行控制脚本
#
# <AUTHOR> <EMAIL>
# Version: 1.0 (2025-01-04 19:34:45)

env=local
case "$1" in
    master|test|dev|local) env=$1;;
    *) set -- $env "$@";;
esac

command=$2
if [ "$command" = "" ]; then
    command="help"
fi

set -eu

# POSIX compliant way to cd to the script's parent directory
script_dir="$(cd "$(dirname "$0")" && pwd)"
cd "$script_dir/.."

# 初始化
. ./cmd/src/common/init.sh

# 执行命令
. ./cmd/src/common/run.sh
