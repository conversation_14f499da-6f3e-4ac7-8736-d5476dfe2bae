services:
  python: &python
    extends:
      file: ../docker-compose/common.yml
      service: common
    image: ${DOCKER_BASE_IMAGE_PREFIX}python:${PYTHON_VERSION}-${CI_COMMIT_REF_NAME}
    container_name: ${DOCKER_CONTAINER_PREFIX}${CI_SOURCE_NAME}-python
    mem_limit: 100M
  python-build:
    <<: *python
    build:
      context: .
      args:
        - PYTHON_SELF_BASE_IMAGE=${DOCKER_BASE_IMAGE_PREFIX}python:${PYTHON_VERSION}-${CI_COMMIT_REF_NAME}
    image: ${DOCKER_IMAGE_PREFIX}${CI_SOURCE_NAME}/python:${PYTHON_VERSION}-${CI_COMMIT_REF_NAME}
