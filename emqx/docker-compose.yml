services:
  emqx:
    build:
      context: .
      args:
        - EMQX_VERSION=$EMQX_VERSION
    image: ${DOCKER_IMAGE_PREFIX}${CI_SOURCE_NAME}/emqx:${EMQX_VERSION}-${CI_COMMIT_REF_NAME}
    container_name: ${DOCKER_CONTAINER_PREFIX}${CI_SOURCE_NAME}-emqx
    networks:
      - resc-bridge
    extra_hosts:
      - host.docker.internal:host-gateway
    env_file: ../../.env
    environment:
      - TZ=Asia/Shanghai
      - CI_COMMIT_REF_NAME=$CI_COMMIT_REF_NAME
    cpus: '0.50'
    mem_limit: 400M
    # restart: on-failure:3
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "/opt/emqx/bin/emqx_ctl", "status"]
      interval: 60s
      timeout: 15s
      retries: 3
      start_period: 90s
