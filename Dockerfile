###########################################
# 基础设置
###########################################

# 使用公司自己的PHP基础镜像
ARG PHP_SELF_BASE_IMAGE
FROM $PHP_SELF_BASE_IMAGE

# 指定维护者信息
LABEL maintainer="XJP09_HK <<EMAIL>>"

###########################################
# 安装开发工具
###########################################

# 安装系统工具
# - dcron: 轻量级定时任务调度器，用于执行计划任务
# - supervisor: 进程管理工具，用于PHP进程的监控和管理
RUN apk add --no-cache dcron supervisor

###########################################
# 最终配置
###########################################

# 复制PHP-FPM配置文件
COPY docker/cmd/php/etc/php-fpm.d/*.conf /usr/local/etc/php-fpm.d/

# 复制容器启动初始化脚本
COPY docker/php/docker-php-entrypoint /usr/local/bin/

# 复制系统定时任务配置
COPY docker/php/charge-admin-cron /etc/cron.d/

# 复制进程管理配置
COPY docker/php/supervisor.ini /etc/supervisor.d/

# 复制PHP-FPM进程池配置
COPY docker/php/zzzz-www.conf /usr/local/etc/php-fpm.d/

# 复制应用源代码到Web根目录
COPY . /var/www/html
