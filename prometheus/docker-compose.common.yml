services:
  prometheus: &prometheus
    extends:
      file: ../docker-compose/common.yml
      service: common
    image: ${DOCKER_BASE_IMAGE_PREFIX}prometheus:${PROMETHEUS_VERSION}-${CI_COMMIT_REF_NAME}
    container_name: ${DOCKER_CONTAINER_PREFIX}${CI_SOURCE_NAME}-prometheus
    ports:
      - $PROMETHEUS_PORT:9090
    mem_limit: 300M
    healthcheck:
      test: [ "CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9090/-/healthy || exit 1" ]
  prometheus-build:
    <<: *prometheus
    build:
      context: .
      args:
        - PROMETHEUS_SELF_BASE_IMAGE=${DOCKER_BASE_IMAGE_PREFIX}prometheus:${PROMETHEUS_VERSION}-${CI_COMMIT_REF_NAME}
    image: ${DOCKER_IMAGE_PREFIX}${CI_SOURCE_NAME}/prometheus:${PROMETHEUS_VERSION}-${CI_COMMIT_REF_NAME}
