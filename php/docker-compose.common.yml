services:
  php: &php
    extends:
      file: ../docker-compose/common.yml
      service: common
    container_name: ${DOCKER_CONTAINER_PREFIX}${CI_SOURCE_NAME}-php
    volumes:
      - ../alpine/etc/localtime:/usr/share/zoneinfo/Asia/Shanghai
      # - ../alpine/etc/timezone:/etc/timezone
    mem_limit: 200M
    # restart: on-failure:3
    healthcheck:
      test: [ "CMD-SHELL", "nc -z 127.0.0.1 9000 || exit 1" ]
  php-build: &php-build
    <<: *php
    build:
      context: .
    image: ${DOCKER_IMAGE_PREFIX}${CI_SOURCE_NAME}/php:${PHP_VERSION}-${CI_COMMIT_REF_NAME}
  php-cli:
    <<: *php
    image: ${DOCKER_BASE_IMAGE_PREFIX}php:${PHP_VERSION}-cli-${CI_COMMIT_REF_NAME}
  php-cli-build:
    <<: *php-build
    build:
      args:
        - PHP_SELF_BASE_IMAGE=${DOCKER_BASE_IMAGE_PREFIX}php:${PHP_VERSION}-cli-${CI_COMMIT_REF_NAME}
  php-fpm:
    <<: *php
    image: ${DOCKER_BASE_IMAGE_PREFIX}php:${PHP_VERSION}-fpm-${CI_COMMIT_REF_NAME}
  php-fpm-build:
    <<: *php-build
    build:
      args:
        - PHP_SELF_BASE_IMAGE=${DOCKER_BASE_IMAGE_PREFIX}php:${PHP_VERSION}-fpm-${CI_COMMIT_REF_NAME}
