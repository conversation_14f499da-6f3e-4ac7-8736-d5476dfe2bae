###########################################
# 基础设置
###########################################

# 使用PHP官方的Alpine Linux基础镜像
# - cli: 命令行接口版本，适用于脚本执行、定时任务和后台进程
# - fpm: FastCGI进程管理器，专为高性能Web服务优化，相比apache和cli版本更轻量高效
# - alpine: 基于musl libc和busybox的轻量级Linux发行版，镜像体积小、启动快、资源占用少
ARG PHP_BASE_IMAGE=php:8.1-cli-alpine
FROM $PHP_BASE_IMAGE

# 指定维护者信息
LABEL maintainer="XJP09_HK <<EMAIL>>"

# 指定编译使用的CPU核心数
ARG DOCKER_BUILD_CORES=1
ENV DOCKER_BUILD_CORES=$DOCKER_BUILD_CORES

# 指定Swoole扩展版本以确保稳定性和兼容性
ARG SWOOLE_VERSION=5.1.6
ENV SWOOLE_VERSION=$SWOOLE_VERSION

# 将Alpine的软件源切换为阿里云镜像，加快国内访问速度
RUN set -eux; \
    sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories; \
    apk update; \
    # :

###########################################
# 安装构建工具和依赖
###########################################

# 安装基础构建工具和开发依赖
# - PHPIZE_DEPS: PHP扩展编译必需的构建工具，包含：
#   - autoconf, automake: 自动配置和构建工具
#   - gcc, g++, make: C/C++编译器和构建工具
#   - pkgconfig: 库编译配置工具
# - libzip-dev: ZIP压缩支持，用于：
#   - PHP ZIP扩展的编译依赖
#   - 提供ZIP文件的创建、读取和修改功能
#   - Composer包管理器依赖处理
# - GD图形库依赖：
#   - libwebp-dev: WebP图像格式支持，提供高压缩率的图像处理
#   - libjpeg-turbo-dev: JPEG图像处理的优化实现
#   - libpng-dev: PNG图像格式支持
#   - freetype-dev: TrueType字体渲染支持
# - Swoole网络功能依赖：
#   - curl-dev: 提供异步HTTP客户端支持
#   - c-ares-dev: 异步DNS解析支持，优化网络性能
# RUN set -eux; \
    apk add --no-cache --virtual .build-deps \
        $PHPIZE_DEPS \
        libzip-dev \
        libwebp-dev libjpeg-turbo-dev libpng-dev freetype-dev \
        curl-dev c-ares-dev \
        ; \
    # :

###########################################
# 安装PHP核心扩展
###########################################

# 安装常用核心扩展
# - bcmath: 高精度数学运算支持，用于金融计算等场景
# - mysqli和pdo_mysql: MySQL数据库连接支持，提供原生和PDO两种连接方式
# - pcntl: 进程控制和信号处理支持，用于多进程编程
# - sockets: 网络套接字编程支持，用于底层网络通信
# - zip: ZIP文件压缩和解压支持，用于文件打包处理
# RUN set -eux; \
    docker-php-ext-install -j${DOCKER_BUILD_CORES} \
        bcmath \
        mysqli \
        pdo_mysql \
        pcntl \
        sockets \
        zip \
        ; \
    # :

# 安装并配置GD扩展
# 支持WebP、JPEG和FreeType字体的图像处理功能
# RUN set -eux; \
    docker-php-ext-configure gd \
        --with-webp=/usr/include/webp \
        --with-jpeg=/usr/include \
        --with-freetype=/usr/include/freetype2; \
    docker-php-ext-install -j${DOCKER_BUILD_CORES} gd; \
    # :

###########################################
# 安装PECL扩展
###########################################

# 安装Redis扩展 - 提供Redis缓存服务器的连接支持
# RUN set -eux; \
    MAKEFLAGS="-j${DOCKER_BUILD_CORES}" pecl install redis; \
    docker-php-ext-enable redis; \
    # :

# 安装Swoole扩展 - 高性能网络通信引擎
# RUN set -eux; \
    MAKEFLAGS="-j${DOCKER_BUILD_CORES}" pecl install -D ' \
        enable-sockets="yes" enable-openssl="yes" enable-http2="yes" enable-mysqlnd="yes" \
        enable-swoole-json="yes" enable-swoole-curl="yes" enable-cares="yes" enable-brotli="yes" ' \
        swoole-${SWOOLE_VERSION}; \
    docker-php-ext-enable swoole; \
    # :

# 清理构建依赖并保留运行时必需的库
# 使用scanelf扫描可执行文件和库的依赖关系
# 1. 扫描/usr/local目录下所有二进制文件的共享库依赖
# 2. 提取并去重所需的共享库
# 3. 安装运行时依赖并移除构建工具
# RUN set -eux; \
    runDeps="$( scanelf --needed --nobanner --format '%n#p' --recursive /usr/local \
        | tr ',' '\n' | sort -u \
        | awk 'system("[ -e /usr/local/lib/" $1 " ]") == 0 { next } { print "so:" $1 }' \
    )"; \
    apk add --no-cache $runDeps; \
    apk del --no-network .build-deps

###########################################
# 安装开发工具
###########################################

# 安装Composer包管理器并配置镜像源
RUN set -eux; \
    curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer; \
    composer config -g repo.packagist composer https://mirrors.aliyun.com/composer/

# 安装版本控制工具
RUN apk add --no-cache git openssh-client

###########################################
# 最终配置
###########################################

# 复制PHP配置文件
COPY etc/php.ini /usr/local/etc/php/
