services:
  nginx:
    extends:
      file: base.build.yml
      service: nginx
    build:
      dockerfile: Dockerfile.local
  php-cli:
    extends:
      file: base.build.yml
      service: php-cli
    build:
      dockerfile: Dockerfile.local
  php-fpm:
    extends:
      file: base.build.yml
      service: php-fpm
    build:
      dockerfile: Dockerfile.local
  python:
    extends:
      file: base.build.yml
      service: python
  grafana:
    extends:
      file: base.build.yml
      service: grafana
  loki:
    extends:
      file: base.build.yml
      service: loki
  alloy:
    extends:
      file: base.build.yml
      service: alloy
  prometheus:
    extends:
      file: base.build.yml
      service: prometheus
  rabbitmq:
    extends:
      file: base.build.yml
      service: rabbitmq
  rabbitmq-management:
    extends:
      file: base.build.yml
      service: rabbitmq-management

