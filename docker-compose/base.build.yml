services:
  nginx:
    build:
      context: ../nginx
      args:
        - NGINX_VERSION=$NGINX_VERSION
    image: ${DOCKER_BASE_IMAGE_PREFIX}nginx:${NGINX_VERSION}-${CI_COMMIT_REF_NAME}
  php-cli:
    build:
      context: ../php
      args:
        - PHP_BASE_IMAGE=php:${PHP_VERSION}-cli-alpine
        - DOCKER_BUILD_CORES=$DOCKER_BUILD_CORES
        - SWOOLE_VERSION=${SWOOLE_VERSION:-5.1.6}
    image: ${DOCKER_BASE_IMAGE_PREFIX}php:${PHP_VERSION}-cli-${CI_COMMIT_REF_NAME}
  php-fpm:
    build:
      context: ../php
      args:
        - PHP_BASE_IMAGE=php:${PHP_VERSION}-fpm-alpine
        - DOCKER_BUILD_CORES=$DOCKER_BUILD_CORES
        - SWOOLE_VERSION=${SWOOLE_VERSION:-5.1.6}
    image: ${DOCKER_BASE_IMAGE_PREFIX}php:${PHP_VERSION}-fpm-${CI_COMMIT_REF_NAME}
  python:
    build:
      context: ../python
      args:
        - PYTHON_VERSION=${PYTHON_VERSION}
    image: ${DOCKER_BASE_IMAGE_PREFIX}python:${PYTHON_VERSION}-${CI_COMMIT_REF_NAME}
  grafana:
    build:
      context: ../grafana
      args:
        - GRAFANA_VERSION=$GRAFANA_VERSION
    image: ${DOCKER_BASE_IMAGE_PREFIX}grafana:${GRAFANA_VERSION}-${CI_COMMIT_REF_NAME}
  loki:
    build:
      context: ../loki
      args:
        - LOKI_VERSION=$LOKI_VERSION
    image: ${DOCKER_BASE_IMAGE_PREFIX}loki:${LOKI_VERSION}-${CI_COMMIT_REF_NAME}
  alloy:
    build:
      context: ../alloy
      args:
        - ALLOY_VERSION=$ALLOY_VERSION
    image: ${DOCKER_BASE_IMAGE_PREFIX}alloy:${ALLOY_VERSION}-${CI_COMMIT_REF_NAME}
  prometheus:
    build:
      context: ../prometheus
      args:
        - PROMETHEUS_VERSION=$PROMETHEUS_VERSION
    image: ${DOCKER_BASE_IMAGE_PREFIX}prometheus:${PROMETHEUS_VERSION}-${CI_COMMIT_REF_NAME}
  rabbitmq:
    build:
      context: ../rabbitmq
      dockerfile: base.Dockerfile
      args:
        - RABBITMQ_VERSION=$RABBITMQ_VERSION
        - RABBITMQ_BASE_IMAGE=rabbitmq:${RABBITMQ_VERSION}-alpine
    image: ${DOCKER_BASE_IMAGE_PREFIX}rabbitmq:${RABBITMQ_VERSION}-${CI_COMMIT_REF_NAME}
  rabbitmq-management:
    extends:
      service: rabbitmq
    build:
      args:
        - RABBITMQ_BASE_IMAGE=rabbitmq:${RABBITMQ_VERSION}-management-alpine
    image: ${DOCKER_BASE_IMAGE_PREFIX}rabbitmq:${RABBITMQ_VERSION}-management-${CI_COMMIT_REF_NAME}
