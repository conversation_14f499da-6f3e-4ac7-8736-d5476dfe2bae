<?php

use app\listener\electronic_invoice\WechatFapiaoCompleteEvent;
use app\listener\electronic_invoice\WechatFapiaoTitleCompleteEvent;
use app\listener\event_distribute\ChargeAbnormalEnd;
use app\listener\event_distribute\ChargeNormalEnd;
use app\listener\event_distribute\ChargeNormalStart;
use app\listener\event_distribute\DeviceAbnormalityListener;
use app\listener\event_distribute\ShotsCountChange;
use app\listener\event_distribute\ShotsStatusChange;
use app\listener\event_distribute\UserBalanceChangeListener;
use app\listener\log\HttpEndListener;
use app\listener\log\HttpRunListener;

// 事件定义文件
return [
    'bind' => [
    ],

    'listen' => [
        // 设备异常
        'DeviceAbnormality' => [
            DeviceAbnormalityListener::class
        ],
        // 充电枪数量变更
        'ShotsCountChangeEvent' => [
            ShotsCountChange::class
        ],
        // 充电枪状态上报
        'ShotsStatusChange' => [
            ShotsStatusChange::class,
        ],
        // 充电正常结束
        'ChargeNormalEnd' => [
            ChargeNormalEnd::class,
        ],
        // 充电异常结束
        'ChargeAbnormalEnd' => [
            ChargeAbnormalEnd::class,
        ],
        // 充电正常开启
        'ChargeNormalStart' => [
            ChargeNormalStart::class
        ],
        'WechatFapiaoTitleComplete' => [
            WechatFapiaoTitleCompleteEvent::class
        ],
        'WechatFapiaoComplete' => [
            WechatFapiaoCompleteEvent::class
        ],
        'UserBalanceChange' => [
            UserBalanceChangeListener::class
        ],
        'AppInit' => [],
        'HttpRun' => [
            HttpRunListener::class,
        ],
        'HttpEnd' => [
            HttpEndListener::class,
        ],
        'LogLevel' => [],
        'LogWrite' => [],
    ],

    'subscribe' => [
    ],
];
