<?php
/** @noinspection PhpUnused */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */

namespace app\maintenance\controller;


use app\common\model\File as FileModel;
use hg\apidoc\annotation as Apidoc;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Filesystem;
use think\response\Json;
use app\common\model\City as CityModel;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use app\common\lib\VerifyData;
use Throwable;

#[Apidoc\Title("公共模块")]
class Common extends BaseController
{

    #[
        Apidoc\Title("上传图片"),
        Apidoc\Author("lwj 2023.8.4 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/maintenance/Common/upload_img"),
        Apidoc\Param(name: "file", type: "file", require: true, desc: "文件"),
    ]
    public function upload_img(): Json
    {
        try {
            //获取上传文件表单字段名
            $file      = request()->file('file');

            // 定义验证规则
            $rule = [
                'file' => [
                    // 必须是文件
                    'file',
                    // 必须是图片
                    'image',
                    // 必须是JPG、PNG格式
                    'fileExt:jpg,png',
                    // 文件大小不超过6MB
                    'fileSize:6291456'
                ]
            ];
            $data = ['file' => $file];
            validate($rule)->check($data);

            $file_size = $file->getSize();
            $md5       = $file->md5();
            $sha1      = $file->sha1();
            $type      = $file->getMime();
            $name      = $file->getOriginalName();
            $format    = $file->extension();

            $save_name = Filesystem::disk('public')->putFile('img', $file);

            if($save_name){
                $file_url='storage/'.str_replace('\\','/',$save_name);
                $add_data = [
                    'name'        => $name,
                    'size'        => $file_size,
                    'md5'         => $md5,
                    'sha1'        => $sha1,
                    'type'        => $type,
                    'format'      => $format,
                    'url'         => $file_url,
                    'user_id'     => $this->loginUser->id,
                ];

                $res = (new FileModel)->insert($add_data);
                if($res) return $this->res_success($add_data);
            }
            abort(500, '上传失败');

        } catch (ValidateException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：'.$e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取主机域名"),
        Apidoc\Author("lwj 2023.9.9 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/maintenance/Common/get_host"),
    ]
    public function get_host(): Json
    {
        $res_data=['host'=>config('my.host')];
        return $this->res_success($res_data);
    }

    #[
        Apidoc\Title("获取城市列表"),
        Apidoc\Author("lwj 2023.9.13 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/maintenance/Common/city_list"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name:"data", type:"array", require:true, desc:"充电站列表", children:[
            ['name' => 'city_list', 'type' => 'array', 'desc' => '城市列表', 'children' => [
                ['name' => 'id', 'type' => 'int', 'desc' => '城市id' ],
                ['name' => 'name', 'type' => 'string', 'desc' => '城市名称'],
                ['name' => 's', 'type' => 'string', 'desc' => '城市首字母'],
            ]],
            ['name' => 'city_index', 'type' => 'array', 'desc' => '城市首字母列表'],
            ['name' => 'city_hot', 'type' => 'string', 'desc' => '热门城市列表', 'children' => [
                ['name' => 'id', 'type' => 'int', 'desc' => '城市id' ],
                ['name' => 'name', 'type' => 'string', 'desc' => '城市名称'],
            ]]
        ]),
    ]
    public function city_list(): Json
    {
        $CityModel=new CityModel();

        $city_hot=$CityModel
            ->where('type',2)
            ->where('hot',2)
            ->order('s','asc')
            ->column('id,name');

        $city=$CityModel
            ->where('type',2)
            ->order('s','asc')
            ->column('id,name,s');

        $city_list=arr_group($city,'s');
        $city_index=array_keys($city_list);

        return $this->res_success([
            'city_list'=>$city_list,
            'city_index'=>$city_index,
            'city_hot'=>$city_hot,
        ]);
    }

    #[
        Apidoc\Title("逆地址解析"),
        Apidoc\Author("lwj 2023.9.13 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/maintenance/Common/get_geocoder_location"),
        Apidoc\Param(name:"center_lon", type:"float", desc:"当前经度"),
        Apidoc\Param(name:"center_lat", type:"float", desc:"当前纬度"),

        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name:"data", type:"array", require:true, desc:"充电站列表", children:[
            ['name' => 'nation', 'type' => 'string', 'desc' => '国家'],
            ['name' => 'province', 'type' => 'string', 'desc' => '省'],
            ['name' => 'city', 'type' => 'string', 'desc' => '市，如果当前城市为省直辖县级区划，city与district字段均会返回此城市'],
            ['name' => 'district', 'type' => 'string', 'desc' => '区，可能为空字串'],
            ['name' => 'street', 'type' => 'string', 'desc' => '道路，可能为空字串'],
            ['name' => 'street_number', 'type' => 'string', 'desc' => '门牌，可能为空字串'],
        ]),
    ]
    public function get_geocoder_location(): Json
    {
        $data = $this->request->post();
        $center_lon  = ($data['center_lon'] ?? false) ?: 114.12772;
        $center_lat  = ($data['center_lat'] ?? false) ?: 22.54764;

        $query_params=[
            'location' => $center_lat.','.$center_lon,
            'key' => config('my.qq_map_key'),
        ];
        $queryString = http_build_query($query_params);
        $path='/ws/geocoder/v1/';
        $url = 'https://apis.map.qq.com' . $path . '?' . $queryString;
        $res_arr=send_get_json_to_shuzu($url);
        $region_list=[];
        if(!empty($res_arr['result']['address_component'])) $region_list=$res_arr['result']['address_component'];
        return $this->res_success($region_list);
    }

    #[
        Apidoc\Title("获取驾驶路线"),
        Apidoc\Author("lwj 2023.9.13 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/maintenance/Common/get_driving_line"),
        Apidoc\Param(name:"center_lon", type:"float", desc:"当前经度"),
        Apidoc\Param(name:"center_lat", type:"float", desc:"当前纬度"),

        Apidoc\Param(name:"to_lon", type:"float", desc:"目的地经度"),
        Apidoc\Param(name:"to_lat", type:"float", desc:"目的地纬度"),

        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name:"data", type:"array", require:true, desc:"充电站列表", children:[
            ['name' => 'nation', 'type' => 'string', 'desc' => '国家'],
            ['name' => 'province', 'type' => 'string', 'desc' => '省'],
            ['name' => 'city', 'type' => 'string', 'desc' => '市，如果当前城市为省直辖县级区划，city与district字段均会返回此城市'],
            ['name' => 'district', 'type' => 'string', 'desc' => '区，可能为空字串'],
            ['name' => 'street', 'type' => 'string', 'desc' => '道路，可能为空字串'],
            ['name' => 'street_number', 'type' => 'string', 'desc' => '门牌，可能为空字串'],
        ]),
    ]
    public function get_driving_line(): Json
    {
        $data = $this->request->post();
        $center_lon  = ($data['center_lon'] ?? false) ?: 114.12772;
        $center_lat  = ($data['center_lat'] ?? false) ?: 22.54764;

        $to_lon  = ($data['to_lon'] ?? false) ?: 113.949287;
        $to_lat  = ($data['to_lat'] ?? false) ?: 22.585732;

        $query_params=[
            'from' => $center_lat.','.$center_lon,
            'to' => $to_lat.','.$to_lon,
            'key' => config('my.qq_map_key'),
        ];
        $queryString = http_build_query($query_params);
        $path='/ws/direction/v1/driving/';
        $url = 'https://apis.map.qq.com' . $path . '?' . $queryString;
        $res_arr=send_get_json_to_shuzu($url);

        if(empty($res_arr['result']['routes'][0]['polyline'])) return $this->res_success();
        $polyline=$res_arr['result']['routes'][0]['polyline'];
        $polylineCount = count($polyline);

        for ($i = 2; $i < $polylineCount; $i++) {
            $polyline[$i] = $polyline[$i-2] + $polyline[$i]/1000000;
        }

        $result = [];
        for ($j = 0; $j < count($polyline); $j+=2) {
            $latitude = $polyline[$j];
            $longitude = $polyline[$j+1];
            $result[] = [
                'longitude'=>$longitude,
                'latitude'=>$latitude,
            ];
        }

        return $this->res_success($result);
    }

    #[
        Apidoc\Title("获取协议"),
        Apidoc\Author("lwj 2023.9.18 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/maintenance/Common/get_agreement"),
        Apidoc\Param(name:"name", type:"string", require: true, desc:"协议标题，分类"),
        Apidoc\Returned(name: "content", type: "string", require: true, desc: "协议内容"),
    ]
    public function get_agreement(): Json
    {
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::agreement([
                'name',
            ]));

            $agreement=Db::name('agreement')
                ->where('status',1)
                ->where('name',$verify_data['name'])
                ->field('content')
                ->find();

            return $this->res_success($agreement);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：'.$e->getMessage());
        }
    }

}