<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */

namespace app\maintenance\controller;

use app\common\lib\maintenance\login\TestLogin;
use app\common\lib\maintenance\login\WechatAppletPhoneLogin;
use app\common\lib\VerifyData;
use app\common\model\Users as UsersModel;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\response\Json;
use Throwable;

#[Apidoc\Title("登录")]
class Login extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new UsersModel();
    }


    #[
        Apidoc\Title("微信小程序通过手机号登录"),
        Apidoc\Author("lwj 2023.8.16 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/maintenance/Login/wechat_applet_phone_login"),
        <PERSON><PERSON><PERSON>\Param(name: "openid_code", type: "string", require: true, desc: "openid_code"),
        Apidoc\Param(name: "phone_code", type: "string", require: true, desc: "phone_code"),
        Apidoc\NotHeaders,
        Apidoc\Returned(name: "id", type: "int", desc: "用户id"),
        Apidoc\Returned(name: "nickname", type: "string", desc: "昵称"),
        Apidoc\Returned(name: "avatar", type: "string", desc: "用户头像url"),
        Apidoc\Returned(name: "gender", type: "string", desc: "用户性别  1 男 2 女 3 未知"),
        Apidoc\Returned(name: "phone", type: "string", desc: "用户手机号"),
        Apidoc\Returned(name: "freeze", type: "string", desc: "账号是否冻结，1-未冻结，2-冻结"),
        Apidoc\Returned(name: "balance", type: "string", desc: "余额，单位分"),
        Apidoc\Returned(name: "auth_status", type: "string", desc: "授权状态： 1 未授权 2 已授权"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "用户创建时间"),
        Apidoc\Returned(name: "token", type: "string", desc: "登录令牌"),
        Apidoc\Returned(name: "expire_time", type: "int", desc: "登录令牌有效时间(单位:秒)"),
    ]
    public function wechat_applet_phone_login(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::maintenance_login([
                'openid_code',
                'phone_code',
            ]));

            return (new WechatAppletPhoneLogin($verify_data))->run();

        });
    }

    #[
        Apidoc\Title("小程序自动登录"),
        Apidoc\Author("lwj 2023.9.6 新增"),
        Apidoc\Author("lwj 2023.9.13 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/maintenance/Login/auto_login"),
    ]
    public function auto_login(): Json
    {
        try {
            $user = (array)$this->loginUser;
            unset($user['last_time']);
            unset($user['open_id']);
            return $this->res_success($user, '小程序自动登录成功');
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("登录测试"),
        Apidoc\Author("lwj 2023.8.23 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/maintenance/Login/login_test"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "id"),
        Apidoc\NotHeaders,
    ]
    public function login_test(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::maintenance_login([
                'id',
            ]));

            return (new TestLogin($verify_data))->run();

        });
    }
}