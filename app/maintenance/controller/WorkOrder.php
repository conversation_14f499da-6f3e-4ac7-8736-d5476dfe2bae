<?php
/** @noinspection PhpUnused */

/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */

namespace app\maintenance\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\AdminUsers;
use app\common\model\Users;
use app\common\model\WorkOrderProcessingRecords;
use app\common\model\WorkOrderResponsible;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\db\Query;
use think\response\Json;
use app\common\model\WorkOrder as WorkOrderModel;

#[Apidoc\Title("工单")]
class WorkOrder extends BaseController
{
    #[
        Apidoc\Title("列表数据"),
        Apidoc\Author("cbj 2024.05.06 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/maintenance/work_order/get_list_data"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "每页显示条数"),
        Apidoc\Param(name: "screen_status", type: "int", require: false, desc: "状态 1:开启 2:解决中 3:已解决 4:已关闭"),
        Apidoc\Param(name: "screen_title", type: "string", require: false, desc: "标题"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '工单ID'],
            ['name' => 'title', 'type' => 'string', 'desc' => '工单标题'],
            ['name' => 'priority', 'type' => 'int', 'desc' => '优先级 1:低 2:标准 3:高 4:紧急'],
            ['name' => 'status', 'type' => 'int', 'desc' => '状态 1:开启 2:解决中 3:已解决 4:已关闭'],
            ['name' => 'source', 'type' => 'int', 'desc' => '来源 1:后台创建 2:告警生成 3:用户创建'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
            ['name' => 'update_time', 'type' => 'string', 'desc' => '更新时间'],
        ]),
    ]
    public function get_list_data(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verifyData = v::input($data, VerifyData::maintenance_work_order([
                'page',
                'limit',
                'screen_status',
                'screen_title'
            ]));

            $where = [];
            $where[] = ['wor.admin_user_id', '=', $this->loginUser->id];
            $where[] = ['wor.is_activate', '=', WorkOrderResponsible::IsActivateYes];
            if (!empty($verifyData['screen_status'])) {
                $where[] = ['wo.status', '=', $verifyData['screen_status']];
            }
            if (!empty($verifyData['screen_title'])) {
                $where[] = ['wo.title', 'like', '%' . $verifyData['screen_title'] . '%'];
            }
            $fields = [
                'wo.id', 'wo.title', 'wo.priority', 'wo.status', 'wo.source',
                'wo.create_time', 'wo.update_time'
            ];

            /**
             * @var Query $workOrderRelation
             */
            $workOrderRelation = new WorkOrderResponsible();
            return $workOrderRelation->alias('wor')
                ->leftJoin('work_order wo', 'wo.id = wor.work_order_id')
                ->field($fields)
                ->where($where)
                ->order('wo.create_time', 'desc')
                ->paginate(['list_rows' => $verifyData['limit'], 'page' => $verifyData['page']])
                ->toArray();
        });
    }

    #[
        Apidoc\Title("工单详情"),
        Apidoc\Author("cbj 2024.05.06 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/maintenance/work_order/get_info"),
        Apidoc\Param(name: "id", type: "string", require: true, default: "", desc: "工单ID"),
        Apidoc\Returned(name: "title", type: "string", require: true, default: "", desc: "工单标题"),
        Apidoc\Returned(name: "priority", type: "int", require: true, default: 1, desc: "优先级 1:低 2:标准 3:高 4:紧急"),
        Apidoc\Returned(name: "status", type: "int", require: true, default: 1, desc: "状态 1:开启 2:解决中 3:已解决 4:已关闭"),
        Apidoc\Returned(name: "source", type: "int", require: true, default: 1, desc: "来源 1:手工创建 2:告警生成 3:用户创建"),
        Apidoc\Returned(name: "template_id", type: "int", require: true, default: 1, desc: "模板ID"),
        Apidoc\Returned(name: "template_name", type: "string", require: true, default: 1, desc: "模板名称"),
        Apidoc\Returned(name: "create_user_type", type: "int", require: true, default: 1, desc: "创建者的用户类型 1:后台用户 2:普通用户 3:自动创建"),
        Apidoc\Returned(name: "create_user_id", type: "int", require: true, default: 1, desc: "创建者的用户ID"),
        Apidoc\Returned(name: "create_time", type: "string", require: true, default: "2024-04-29 14:10:15", desc: "创建时间"),
        Apidoc\Returned(name: "update_time", type: "null|string", require: true, default: "2024-04-29 14:10:15", desc: "更新时间"),
    ]
    public function get_info(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::maintenance_work_order([
                'id'
            ]));

            $WorkOrderResponsible = new WorkOrderResponsible();

            $data = $WorkOrderResponsible->getInfo(
                $this->loginUser->id,
                $verifyData['id']
            );
            if (is_null($data)) {
                throw new RuntimeException('无效的工单ID', [], RuntimeException::CodeBusinessException);
            }

            return $data;
        });
    }

    #[
        Apidoc\Title("工单的处理记录"),
        Apidoc\Author("cbj 2024.05.06 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/maintenance/work_order/processing_records"),
        Apidoc\Param(name: "work_order_id", type: "string", require: true, default: "", desc: "工单ID"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "每条条数"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '记录ID'],
            ['name' => 'user_type', 'type' => 'int', 'desc' => '用户类型 1:后台用户 2:普通用户 3:系统'],
            ['name' => 'user_name', 'type' => 'string', 'desc' => '用户称呼'],
            ['name' => 'message', 'type' => 'string', 'desc' => '记录描述'],
            ['name' => 'attachment', 'type' => 'string', 'desc' => '记录的附件'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
        ]),
    ]
    public function processing_records(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::maintenance_work_order([
                'work_order_id',
                'page',
                'limit'
            ]));

            $admin_user_id = (new WorkOrderResponsible())->getActivateResponsible($verifyData['work_order_id']);
            if ($admin_user_id !== $this->loginUser->id) {
                throw new RuntimeException('无效的工单ID', [], RuntimeException::CodeBusinessException);
            }


            // 验证数据权限
            $WorkOrderProcessingRecords = new WorkOrderProcessingRecords();
            $list = $WorkOrderProcessingRecords->where('work_order_id', '=', $verifyData['work_order_id'])
                ->field(['id', 'user_type', 'user_id', 'message', 'attachment', 'create_time'])
                ->order('create_time', 'desc')
                ->order('id', 'desc')
                ->paginate(['list_rows' => $verifyData['limit'], 'page' => $verifyData['page']])
                ->toArray();

            $admin_user_ids = [];
            $applet_user_ids = [];

            foreach ($list['data'] as &$value) {
                if ($value['user_type'] === WorkOrderProcessingRecords::UserTypeAdminUser) {
                    $admin_user_ids[] = $value['user_id'];
                } else if ($value['user_type'] === WorkOrderProcessingRecords::UserTypeNormalUser) {
                    $applet_user_ids[] = $value['user_id'];
                } else {
                    $value['user_name'] = '系统';
                }
            }
            if (!empty($admin_user_ids)) {
                $admin_users_id_to_name_map = (new AdminUsers())->getAdminNamesMap($admin_user_ids);
            }
            if (!empty($applet_user_ids)) {
                $applet_users_id_to_name_map = (new Users())->getUsersNameMap($applet_user_ids);
            }

            foreach ($list['data'] as &$value) {
                if ($value['user_type'] === WorkOrderProcessingRecords::UserTypeAdminUser) {
                    $value['user_name'] = $admin_users_id_to_name_map[$value['user_id']] ?? '未知';
                } else if ($value['user_type'] === WorkOrderProcessingRecords::UserTypeNormalUser) {
                    $value['user_name'] = $applet_users_id_to_name_map[$value['user_id']] ?? '未知';
                } else {
                    $value['user_name'] = '系统';
                }
            }

            return $list;
        });
    }

    #[
        Apidoc\Title("保存工作记录"),
        Apidoc\Author("cbj 2024.05.06 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/maintenance/work_order/save_work_record"),
        Apidoc\Param(name: "work_order_id", type: "string", require: true, default: "", desc: "工单ID"),
        Apidoc\Param(name: "message", type: "string", require: true, default: "", desc: "工作描述"),
        Apidoc\Param(name: "attachment", type: "string", require: true, default: "", desc: "附件(使用公共的上传接口，上传成功后在这里传递路径就好，如果有多个文件，就以','分隔) 最大长度500个字符"),
    ]
    public function save_work_record(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::maintenance_work_order([
                'work_order_id',
                'message',
                'attachment'
            ]));

            $admin_user_id = (new WorkOrderResponsible())->getActivateResponsible($verifyData['work_order_id']);
            if ($admin_user_id !== $this->loginUser->id) {
                throw new RuntimeException('无效的工单ID', [], RuntimeException::CodeBusinessException);
            }


            // 验证数据权限
            $WorkOrderProcessingRecords = new WorkOrderProcessingRecords();
            $WorkOrderProcessingRecords->addRecord(
                $verifyData['work_order_id'],
                WorkOrderProcessingRecords::UserTypeAdminUser,
                $this->loginUser->id,
                $verifyData['message'],
                $verifyData['attachment']
            );
        }, true);
    }

    #[
        Apidoc\Title("完成工单"),
        Apidoc\Author("cbj 2024.05.06 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/maintenance/work_order/complete"),
        Apidoc\Param(name: "work_order_id", type: "string", require: true, default: "", desc: "工单ID"),
    ]
    public function complete(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::maintenance_work_order([
                'work_order_id',
            ]));

            $admin_user_id = (new WorkOrderResponsible())->getActivateResponsible($verifyData['work_order_id']);
            if ($admin_user_id !== $this->loginUser->id) {
                throw new RuntimeException('无效的工单ID', [], RuntimeException::CodeBusinessException);
            }


            $WorkOrderProcessingRecords = new WorkOrderProcessingRecords();
            $WorkOrderProcessingRecords->addRecord(
                $verifyData['work_order_id'],
                WorkOrderProcessingRecords::UserTypeAdminUser,
                $this->loginUser->id,
                WorkOrderProcessingRecords::completeMessage($this->loginUser->id)
            );

            (new WorkOrderModel())->updateStatus($verifyData['work_order_id'], WorkOrderModel::StatusResolved);
        }, true);
    }

    #[
        Apidoc\Title("退出工单"),
        Apidoc\Desc("当工作人员认为这个问题不是他能够处理的时候，可以选择退出工单。"),
        Apidoc\Author("cbj 2024.05.06 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/maintenance/work_order/exit_work_order"),
        Apidoc\Param(name: "work_order_id", type: "string", require: true, default: "", desc: "工单ID"),
    ]
    public function exit_work_order(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::maintenance_work_order([
                'work_order_id',
            ]));

            $WorkOrderResponsibleModel = new WorkOrderResponsible();
            $admin_user_id = $WorkOrderResponsibleModel->getActivateResponsible($verifyData['work_order_id']);
            if ($admin_user_id !== $this->loginUser->id) {
                throw new RuntimeException('无效的工单ID', [], RuntimeException::CodeBusinessException);
            }


            $WorkOrderProcessingRecords = new WorkOrderProcessingRecords();
            $WorkOrderProcessingRecords->addRecord(
                $verifyData['work_order_id'],
                WorkOrderProcessingRecords::UserTypeAdminUser,
                $this->loginUser->id,
                WorkOrderProcessingRecords::exitWorkOrderMessage($this->loginUser->id)
            );

            $WorkOrderResponsibleModel->updateWorkOrderActivateStatus(
                $verifyData['work_order_id'],
                $this->loginUser->id,
                WorkOrderResponsible::IsActivateNot
            );

            (new WorkOrderModel())->updateStatus($verifyData['work_order_id'], WorkOrderModel::StatusOpen);

        }, true);
    }


}