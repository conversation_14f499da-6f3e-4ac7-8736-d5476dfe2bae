<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\maintenance\controller;

use app\common\cache\redis\entity\MaintenanceLoginUser;
use app\common\controller\ApiBase;
use think\App;

class BaseController extends ApiBase
{
    protected ?MaintenanceLoginUser $loginUser = null;

    public function __construct(App $app)
    {
        parent::__construct($app);
        // $loginUser 不为空时，说明已经登录了。
        if (isset($this->request->appletLoginUser)) {
            $this->loginUser = $this->request->appletLoginUser;
        }
    }
}