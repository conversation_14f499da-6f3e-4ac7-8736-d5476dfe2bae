<?php
namespace app\admin\validate;

use think\Validate;


class Stations extends Validate
{
    //定义验证规则
    protected $rule = [
        'id'=>'require',
        'corp_id'=>'require',
        'corp_name'=>'require',
        'name'=>'require',
        'address'=>'require',
        'province'=>'require',
        'city'=>'require',
        'district'=>'require',
        'lonlat'=>'require',
        'pile_num'=>'require',
        'tariff_id'=>'require',
        'tariff_name'=>'require',
        'type'=>'require',
        'status'=>'require',
        'pictures'=>'require',
        'work_time'=>'require',
        'tag_pos'=>'require',
        'tag_park'=>'require',
        'place_rate'=>'require',
        'charge'=>'require',
        'charge_phone'=>'require',
        'tag_toilet'=>'require',
        'tag_canopy'=>'require',
        'tag_rest'=>'require',
        'tag_pnp'=>'require',
        'tag_insure'=>'require',
        'tag_protect'=>'require',
        'tag_ultrafast'=>'require',
        'tag_fast'=>'require',
        'tag_slow'=>'require',
    ];

    //定义验证提示
    protected $message = [
    ];
}
