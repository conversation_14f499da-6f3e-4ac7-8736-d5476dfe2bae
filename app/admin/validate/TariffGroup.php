<?php

namespace app\admin\validate;

use think\Validate;


class TariffGroup extends Validate
{
    //定义验证规则
    protected $rule = [
        'id'             => 'require',
        'name'           => 'require',
        'sharp_fee'      => 'require',
        'peak_fee'       => 'require',
        'flat_fee'       => 'require',
        'valley_fee'     => 'require',
        'sharp_ser_fee'  => 'require',
        'peak_ser_fee'   => 'require',
        'flat_ser_fee'   => 'require',
        'valley_ser_fee' => 'require',
        'loss_rate'      => 'require',
        'period_codes'   => 'require',
        'opt_id'         => 'require',
        'corp_id'         => 'require',
        'corp_name'         => 'require',
    ];

    //定义验证提示
    protected $message = [
    ];
}
