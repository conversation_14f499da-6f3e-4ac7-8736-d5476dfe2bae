<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\entity\WorkOrderAssignHandlerRequest;
use app\common\logic\admin\entity\WorkOrderCreateRequest;
use app\common\logic\admin\WorkOrderAssignHandler;
use app\common\logic\admin\WorkOrderCreate;
use app\common\model\StationDataAuthority;
use app\common\model\WorkOrderProcessingRecords;
use app\common\model\WorkOrderTemplateField;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\response\Json;
use app\common\model\WorkOrder as WorkOrderModel;

#[Apidoc\Title("工单管理/工单")]
class WorkOrder extends BaseController
{
    #[
        Apidoc\Title("工单列表"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order/get_list_data"),
        Apidoc\Param(name: "screen_corp_id", type: "int", require: false, default: null, desc: "运营商ID(运营商账号不需要传递这个参数)"),
        Apidoc\Param(name: "screen_station_id", type: "int", require: false, default: null, desc: "场站ID"),
        Apidoc\Param(name: "screen_device_type", type: "int", require: false, default: null, desc: "设备类型 1:充电桩 2:充电枪 3:能源路由器 9:无"),
        Apidoc\Param(name: "screen_device_id", type: "string", require: false, default: null, desc: "设备编号"),
        Apidoc\Param(name: "screen_status", type: "array", require: false, default: [], desc: "状态 1:开启 2:解决中 3:已解决 4:已关闭"),
        Apidoc\Param(name: "screen_source", type: "int", require: false, default: null, desc: "来源 1:手工创建 2:告警生成 3:用户创建"),
        Apidoc\Param(name: "screen_create_user_type", type: "int", require: false, default: null, desc: "创建者的用户类型 1:后台用户 2:普通用户 3:自动创建"),
        Apidoc\Param(name: "screen_priority", type: "int", require: false, default: null, desc: "优先级 1:低 2:标准 3:高 4:紧急"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "每页显示"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '工单ID'],
            ['name' => 'title', 'type' => 'string', 'desc' => '工单标题'],
            ['name' => 'priority', 'type' => 'int', 'desc' => '优先级 1:低 2:标准 3:高 4:紧急'],
            ['name' => 'status', 'type' => 'int', 'desc' => '状态 1:开启 2:解决中 3:已解决 4:已关闭'],
            ['name' => 'source', 'type' => 'int', 'desc' => '来源 1:手工创建 2:告警生成 3:用户创建'],
            ['name' => 'template_id', 'type' => 'int', 'desc' => '模板ID'],
            ['name' => 'template_name', 'type' => 'string', 'desc' => '模板名称'],
            ['name' => 'create_user_type', 'type' => 'int', 'desc' => '创建者的用户类型 1:后台用户 2:普通用户 3:自动创建'],
            ['name' => 'create_user_id', 'type' => 'int', 'desc' => '创建者的用户ID'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
            ['name' => 'update_time', 'type' => 'null|string', 'desc' => '更新时间'],
            ['name' => 'responsible_user_name', 'type' => 'null|string', 'desc' => '工单负责人的用户名称(为null时标识还没有分配处理人员)'],
            ['name' => 'crop_name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '场站名称'],
            ['name' => 'device_type', 'type' => 'int', 'desc' => '设备类型 1:充电桩 2:充电枪 3:能源路由器 9:无'],
            ['name' => 'device_id', 'type' => 'string', 'desc' => '设备编号'],
        ]),
    ]
    public function get_list_data(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order([
                'screen_corp_id', 'screen_station_id', 'screen_device_type', 'screen_device_id',
                'screen_status', 'screen_source', 'screen_create_user_type',
                'screen_priority', 'page', 'limit',
            ]));

            $verifyData['page'] = $verifyData['page'] ?? 1;
            $verifyData['limit'] = $verifyData['limit'] ?? 10;
            $verifyData['screen_status'] = $verifyData['screen_status'] ?? [];
            $verifyData['screen_source'] = $verifyData['screen_source'] ?? null;
            $verifyData['screen_create_user_type'] = $verifyData['screen_create_user_type'] ?? null;
            $verifyData['screen_priority'] = $verifyData['screen_priority'] ?? null;
            $verifyData['screen_corp_id'] = $verifyData['screen_corp_id'] ?? null;
            $verifyData['screen_station_id'] = $verifyData['screen_station_id'] ?? null;
            $verifyData['screen_device_type'] = $verifyData['screen_device_type'] ?? null;
            $verifyData['screen_device_id'] = !empty($verifyData['screen_device_id']) ? $verifyData['screen_device_id'] : null;
            if ($this->loginUser->corp_id > 0) {
                $verifyData['screen_corp_id'] = $this->loginUser->corp_id;
                if ($this->loginUser->pid > 0) {
                    $screen_station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                    if (!is_null($verifyData['screen_station_id']) && !in_array($verifyData['screen_station_id'], $screen_station_ids)) {
                        $verifyData['screen_station_id'] = [];
                    } else if (is_null($verifyData['screen_station_id'])) {
                        $verifyData['screen_station_id'] = $screen_station_ids;
                    }
                }
            }

            $WorkOrderFieldModel = new WorkOrderModel();
            return $WorkOrderFieldModel->getListData(
                $verifyData['screen_corp_id'],
                $verifyData['screen_station_id'],
                $verifyData['screen_device_type'],
                $verifyData['screen_device_id'],
                $verifyData['screen_status'],
                $verifyData['screen_source'],
                $verifyData['screen_create_user_type'],
                $verifyData['screen_priority'],
                $verifyData['page'],
                $verifyData['limit']
            );
        });
    }


    #[
        Apidoc\Title("工单详情"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order/get_info"),
        Apidoc\Param(name: "id", type: "string", require: true, default: "", desc: "工单ID"),
        Apidoc\Returned(name: "title", type: "string", require: true, default: "", desc: "工单标题"),
        Apidoc\Returned(name: "priority", type: "int", require: true, default: 1, desc: "优先级 1:低 2:标准 3:高 4:紧急"),
        Apidoc\Returned(name: "status", type: "int", require: true, default: 1, desc: "状态 1:开启 2:解决中 3:已解决 4:已关闭"),
        Apidoc\Returned(name: "source", type: "int", require: true, default: 1, desc: "来源 1:手工创建 2:告警生成 3:用户创建"),
        Apidoc\Returned(name: "template_id", type: "int", require: true, default: 1, desc: "模板ID"),
        Apidoc\Returned(name: "template_name", type: "string", require: true, default: 1, desc: "模板名称"),
        Apidoc\Returned(name: "create_user_type", type: "int", require: true, default: 1, desc: "创建者的用户类型 1:后台用户 2:普通用户 3:自动创建"),
        Apidoc\Returned(name: "create_user_id", type: "int", require: true, default: 1, desc: "创建者的用户ID"),
        Apidoc\Returned(name: "create_time", type: "string", require: true, default: "2024-04-29 14:10:15", desc: "创建时间"),
        Apidoc\Returned(name: "update_time", type: "null|string", require: true, default: "2024-04-29 14:10:15", desc: "更新时间"),
        Apidoc\Returned(name: "extra_data", type: "string", require: true, default: "[]", desc: "额外的字段"),
        Apidoc\Returned(name: "responsible_user_id", type: "null|int", require: true, default: null, desc: "工单负责人的用户ID(为null时标识还没有分配处理人员)"),
        Apidoc\Returned(name: "responsible_user_name", type: "null|string", require: true, default: "用户名", desc: "工单负责人的用户名称(为null时标识还没有分配处理人员)"),
        Apidoc\Returned(name: "extra_data_fields", type: "array", desc: "数据", children: [
            ['name' => 'field_id', 'type' => 'int', 'desc' => '字段ID'],
            ['name' => 'sort', 'type' => 'int', 'desc' => '序号(取值范围:0~127)'],
            ['name' => 'name', 'type' => 'string', 'desc' => '字段名称(字符长度:3~20)'],
            ['name' => 'key', 'type' => 'string', 'desc' => '字段键值(字符长度:3~20)'],
            ['name' => 'type', 'type' => 'int', 'desc' => '字段类型 1:单行文本 2:多行文本 3:下拉框 4:多选框 5:数字 6:日期 7:图片'],
            ['name' => 'is_require', 'type' => 'int', 'desc' => '是否必填 1:必填 0:非必填'],
            ['name' => 'options', 'type' => 'string', 'desc' => '字段选项(格式:json字符串)'],
        ]),
    ]
    public function get_info(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order([
                'id'
            ]));

            $WorkOrderModel = new WorkOrderModel();

            $data = $WorkOrderModel->getInfo(
                $verifyData['id']
            );
            if (is_null($data)) {
                throw new RuntimeException('无效的工单ID', [], RuntimeException::CodeBusinessException);
            }

            $WorkOrderTemplateField = new WorkOrderTemplateField();
            $data['extra_data_fields'] = $WorkOrderTemplateField->getFields($data['template_id']);

            return $data;
        });
    }

    #[
        Apidoc\Title("工单的处理记录"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order/processing_records"),
        Apidoc\Param(name: "work_order_id", type: "string", require: true, default: "", desc: "工单ID"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "每条条数"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '记录ID'],
            ['name' => 'message', 'type' => 'string', 'desc' => '记录描述'],
            ['name' => 'attachment', 'type' => 'string', 'desc' => '记录的附件'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
        ]),
    ]
    public function processing_records(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order([
                'work_order_id',
                'page',
                'limit'
            ]));

            // 验证数据权限
            $WorkOrderProcessingRecords = new WorkOrderProcessingRecords();
            return $WorkOrderProcessingRecords->where('work_order_id', '=', $verifyData['work_order_id'])
                ->field(['id', 'message', 'attachment', 'create_time'])
                ->order('create_time', 'desc')
                ->order('id', 'desc')
                ->paginate(['list_rows' => $verifyData['limit'], 'page' => $verifyData['page']])
                ->toArray();
        });
    }

    #[
        Apidoc\Title("创建工单"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order/create_work_order"),
        Apidoc\Param(name: "title", type: "string", require: true, default: "", desc: "工单标题"),
        Apidoc\Param(name: "priority", type: "int", require: true, default: 1, desc: "优先级 1:低 2:标准 3:高 4:紧急"),
        Apidoc\Param(name: "template_id", type: "int", require: true, default: 1, desc: "模板ID"),
        Apidoc\Param(name: "extra_field_data", type: "array", require: true, default: [], desc: "额外字段(格式为:{'field1': 'value1', 'field2': 'value2', ...})"),
        Apidoc\Param(name: "corp_id", type: "int", require: false, default: 1, desc: "运营商ID(只有管理员主账号或管理员子账号才需要传递)"),
        Apidoc\Param(name: "station_id", type: "int", require: true, default: 1, desc: "场站ID"),
        Apidoc\Param(name: "device_type", type: "int", require: true, default: 1, desc: "设备类型 1:充电桩 2:充电枪 3:能源路由器 9:无"),
        Apidoc\Param(name: "device_id", type: "string", require: false, default: "", desc: "设备编号"),
        Apidoc\Returned(name: "new_id", type: "string", require: true, default: "", desc: "工单ID"),
    ]
    public function create_work_order(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            if ($this->loginUser->corp_id > 0) {
                // 验证数据
                $verifyData = v::input($data, VerifyData::work_order([
                    'title', 'priority', 'template_id', 'extra_field_data',
                    'station_id', 'device_type', 'device_id'
                ]));
                $verifyData['corp_id'] = $this->loginUser->corp_id;
            } else {
                $verifyData = v::input($data, VerifyData::work_order([
                    'title', 'priority', 'template_id', 'extra_field_data',
                    'corp_id', 'station_id', 'device_type', 'device_id'
                ]));
            }
            $verifyData['device_id'] = $verifyData['device_id'] ?? '';

            $request = new WorkOrderCreateRequest([
                'title' => $verifyData['title'],
                'priority' => $verifyData['priority'],
                'template_id' => $verifyData['template_id'],
                'extra_field_data' => $verifyData['extra_field_data'],
                'corp_id' => $verifyData['corp_id'],
                'station_id' => $verifyData['station_id'],
                'device_type' => $verifyData['device_type'],
                'device_id' => $verifyData['device_id'],
                'source' => WorkOrderModel::SourceAdminCreate,
                'create_user_type' => WorkOrderModel::CreateUserTypeAdmin,
                'create_user_id' => $this->loginUser->id,
                'create_user_name' => $this->loginUser->name,
            ]);

            return (new WorkOrderCreate($request))->run();
        }, true);
    }


    #[
        Apidoc\Title("指派处理人员"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order/assign_handler"),
        Apidoc\Param(name: "work_order_id", type: "string", require: true, default: "", desc: "工单ID"),
        Apidoc\Param(name: "admin_user_id", type: "int", require: true, default: 0, desc: "被指派的处理人员ID"),
    ]
    public function assign_handler(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order([
                'work_order_id', 'admin_user_id'
            ]));


            $request = new WorkOrderAssignHandlerRequest([
                'work_order_id' => $verifyData['work_order_id'],
                'admin_user_id' => $verifyData['admin_user_id']
            ]);
            return (new WorkOrderAssignHandler($request, $this->loginUser))->run();

        }, true);
    }


}