<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;

use app\common\lib\VerifyData;
use app\common\logic\admin\stations\Add;
use app\common\logic\admin\stations\Delete;
use app\common\logic\admin\stations\Update;
use app\ms\Api;
use app\common\model\ElectricityMeterPowerRecord;
use app\common\model\StationDataAuthority;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use app\common\model\Stations as StationsModel;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\facade\Db;
use think\response\Json;
use Throwable;
use app\common\model\StationsCorpClearing as StationsCorpClearingModel;

#[Apidoc\Title("资源中心/充电站管理")]
class Stations extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new StationsModel();
    }

    #[
        Apidoc\Title("充电站列表"),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.3 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Stations/stations_list"),
        Apidoc\Param(name: "corp_id", type: "int", require: true, desc: "运营商编号"),
        Apidoc\Param(name: "type", type: "int", require: true, desc: "充电站类型：1-公共、2-自营"),
        Apidoc\Param(name: "status", type: "int", require: true, desc: "充电站状态：1-正常使用、2-维护中、3-未开放"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "充电站编号"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "充电站名称"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '场站id'],
            ['name' => 'name', 'type' => 'string', 'desc' => '场站名称'],
            ['name' => 'all_address', 'type' => 'string', 'desc' => '完整场站地址'],
            ['name' => 'pile_num', 'type' => 'int', 'desc' => '桩数'],
            ['name' => 'charge', 'type' => 'string', 'desc' => '负责人'],
            ['name' => 'charge_phone', 'type' => 'string', 'desc' => '负责人手机号'],
            ['name' => 'type', 'type' => 'int', 'desc' => '场站类型：1-公共、2-自营'],
            ['name' => 'status', 'type' => 'int', 'desc' => '运行状态：1-正常使用、2-维护中、3-未开放'],
            ['name' => 'tag_pos', 'type' => 'int', 'desc' => '桩位置标示：1-地上,2-地库'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
//            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'tariff_group_name', 'type' => 'string|null', 'desc' => '费率组名称(类型为null时表示该场站还没有绑定费率)'],
            ['name' => 'is_support_reservation', 'type' => 'int', 'desc' => '是否支持预约充电 0:不支持 1:支持']
        ]),
    ]
    public function stations_list(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
            $order_name = ($data['order_name'] ?? false) ?: 's.id';
            $order_type = ($data['order_type'] ?? false) ?: 'desc';

            // 数据权限
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $data['corp_id'] = $this->loginUser->corp_id;
            } else if ($this->loginUser->corp_id > 0) {
                $data['corp_id'] = $this->loginUser->corp_id;
                $data['id'] = (new StationDataAuthority())->getStationIds($this->loginUser->id);
            }

            // 搜索框搜索
            $where = function ($query) use ($data) {
                $query->where('s.is_del', '=', StationsModel::IsDelNot);

                if (isset($data['id']) && $data['id']) {
                    if (is_array($data['id'])) {
                        $query->where('s.id', 'in', $data['id']);
                    } else if (!empty($data['id'])) {
                        $query->where('s.id', '=', $data['id']);
                    }
                }
                if (isset($data['name']) && $data['name']) {
                    $query->where('s.name', 'like', '%' . $data['name'] . '%');
                }
                if (isset($data['type']) && $data['type']) {
                    $query->where('s.type', $data['type']);
                }
                if (isset($data['status']) && $data['status']) {
                    $query->where('sei.status', $data['status']);
                }

                if (isset($data['corp_id']) && $data['corp_id']) {
                    $query->where('s.corp_id', $data['corp_id']);
                }
                return $query;
            };
            $field = [
                's.id', 's.name', 's.all_address', 's.pile_num', 's.type', 'sei.status', 'sei.tag_pos',
                's.create_time', 'sei.charge', 'sei.charge_phone', 'tg.name as tariff_group_name',
                'c.name as corp_name', 'sei.is_support_reservation'
            ];

            return $this->modelClass->alias('s')
                ->join('corp c', 's.corp_id = c.id')
                ->join('stations_extra_info sei', 'sei.id = s.id')
                ->leftJoin('tariff_group tg', 'sei.tariff_group_id = tg.id')
                ->withAttr('pile_num', function ($value, $data) {
                    $pilesModel = new \app\common\model\Piles();
                    return $pilesModel->getStationPilesCount($data['id']);
                })
                ->field($field)
                ->where($where)
                ->order($order_name, $order_type)
                ->paginate(['list_rows' => $pageSize, 'page' => $page])
                ->toArray();
        });
    }

    #[
        Apidoc\Title("获取充电站排序信息"),
        Apidoc\Author("lwj 2023.8.3 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Stations/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        $res = [
            'order_name' => 's.create_time',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 's.create_time', 'label' => '创建时间'],
                ['value' => 's.id', 'label' => '场站id'],
                ['value' => 's.name', 'label' => '场站名称'],
                ['value' => 's.corp_id', 'label' => '运营商'],
                ['value' => 's.pile_num', 'label' => '桩数'],
                ['value' => 'sei.tariff_group_id', 'label' => '费率组'],
                ['value' => 's.type', 'label' => '场站类型'],
                ['value' => 'sei.status', 'label' => '运行状态'],
                ['value' => 'sei.tag_pos', 'label' => '桩位置标示'],
                ['value' => 'sei.charge', 'label' => '负责人'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("充电站详情"),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.3 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Stations/get_info"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "充电站id"),
        Apidoc\Returned(name: "id", type: "int", desc: "充电站id"),
        Apidoc\Returned(name: "corp_id", type: "int", desc: "运营商编号"),
        Apidoc\Returned(name: "name", type: "string", desc: "场站名称"),
        Apidoc\Returned(name: "city_id", type: "array", desc: "省市区id"),
        Apidoc\Returned(name: "address", type: "string", desc: "场站地址，不必带省市区"),
        Apidoc\Returned(name: "tariff_group_id", type: "int", desc: "费率组id"),
        Apidoc\Returned(name: "type", type: "int", desc: "场站类型：1-公共、2-自营"),
        Apidoc\Returned(name: "status", type: "int", desc: "运行状态：1-正常使用、2-维护中、3-未开放"),
        Apidoc\Returned(name: "pictures", type: "string", desc: "场站图片url集"),
        Apidoc\Returned(name: "work_time", type: "string", desc: "运营时段描述"),
        Apidoc\Returned(name: "tag_pos", type: "int", desc: "桩位置标示：1-地上,2-地库"),
        Apidoc\Returned(name: "tag_park", type: "string", desc: "停车费说明"),
        Apidoc\Returned(name: "place_rate", type: "int", desc: "占位费（X分/分钟）,X*100"),
        Apidoc\Returned(name: "is_require_plate", type: "int", desc: "是否需要车牌：0-不需要,1-需要"),
        Apidoc\Returned(name: "charge", type: "string", desc: "负责人"),
        Apidoc\Returned(name: "charge_phone", type: "string", desc: "负责人手机号"),
        Apidoc\Returned(name: "tag_toilet", type: "int", desc: "场站服务卫生间标识：1-无，2-有"),
        Apidoc\Returned(name: "tag_canopy", type: "int", desc: "场站服务雨棚标识：1-无，2-有"),
        Apidoc\Returned(name: "tag_rest", type: "int", desc: "场站服务休息室标识：1-无，2-有"),
        Apidoc\Returned(name: "tag_pnp", type: "int", desc: "场站特色即插即用标识：1-不支持，2-支持"),
        Apidoc\Returned(name: "tag_insure", type: "int", desc: "场站特色充电保险标识：1-不支持，2-支持"),
        Apidoc\Returned(name: "tag_protect", type: "int", desc: "场站特色充电电池防护标识：1-不支持，2-支持"),
        Apidoc\Returned(name: "tag_ultrafast", type: "int", desc: "场站电桩超快充标识：1-不支持，2-支持"),
        Apidoc\Returned(name: "tag_fast", type: "int", desc: "场站电桩快充标识：1-不支持，2-支持"),
        Apidoc\Returned(name: "tag_slow", type: "int", desc: "场站电桩慢充标识：1-不支持，2-支持"),
        Apidoc\Returned(name: "lonlat", type: "string", desc: "经纬度，前面纬度，后面经度"),
        Apidoc\Returned(name: "is_support_reservation", type: "int", desc: "[新增]是否支持预约充电 0:不支持 1:支持")
    ]
    public function get_info(): Json
    {
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::stations([
                'id',
            ]));

            $field = [
                's.id', 's.corp_id', 's.name', 's.city_id', 's.address', 'sei.tariff_group_id', 's.type', 'sei.status',
                'sei.pictures', 'sei.work_time', 'sei.tag_pos', 'sei.tag_park', 'sei.place_rate', 'sei.charge', 'sei.charge_phone',
                'sei.tag_toilet', 'sei.tag_canopy', 'sei.tag_rest', 'sei.tag_pnp', 'sei.tag_insure', 'sei.tag_protect',
                'sei.tag_ultrafast', 'sei.tag_fast', 'sei.tag_slow', 's.lonlat', "sei.is_support_reservation"
            ];

            // 数据权限
            $where = [];
            $where[] = ['s.is_del', '=', StationsModel::IsDelNot];
            $where[] = ['s.id', '=', $verify_data['id']];
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $where[] = ['s.corp_id', '=', $this->loginUser->corp_id];
            } else if ($this->loginUser->corp_id > 0) {
                $where[] = ['s.corp_id', '=', $this->loginUser->corp_id];
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                $where[] = ['s.id', 'in', $station_ids];
            }

            $result = $this->modelClass
                ->alias('s')
                ->field($field)
                ->join('stations_extra_info sei', 'sei.id = s.id')
                ->withAttr('pictures', function ($value) {
                    if ($value) return $value;
                    return [];
                })
                ->where($where)
                ->find();
            if ($result) {
                // ========== 从微服务查询充电站信息(是否需要用户输入车牌) ==========
                $result2 = Api::send("/device/stations/info",'GET',[
                    'id' => $verify_data['id'],
                ])['data'];
                $result['is_require_plate'] = $result2['is_require_plate'];
                return $this->res_success($result);
            }
            return $this->res_error([], '充电站不存在');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("新增充电站"),
        Apidoc\Desc('只允许运营商账号操作(corp_id > 0)'),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.3 修改, cbj 2024.12.10 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Stations/add"),
        Apidoc\Param(name: "name", type: "string", require: true, default: "", desc: "场站名称"),
        Apidoc\Param(name: "address", type: "string", require: true, default: "", desc: "场站地址"),
        Apidoc\Param(name: "city_id", type: "array", require: true, default: [], desc: "省市区id"),
        Apidoc\Param(name: "lonlat", type: "string", require: true, default: "", desc: "经纬度，前面纬度，后面经度"),
        Apidoc\Param(name: "tariff_group_id", type: "int", require: true, default: 0, desc: "费率组id"),
        Apidoc\Param(name: "type", type: "int", require: true, default: 1, desc: "场站类型：1-公共、2-自营"),
        Apidoc\Param(name: "status", type: "int", require: true, default: 1, desc: "运行状态：1-正常使用、2-维护中、3-未开放"),
        Apidoc\Param(name: "pictures", type: "array", default: [], desc: "场站图片url集"),
        Apidoc\Param(name: "work_time", type: "string", default: "", desc: "运营时段描述"),
        Apidoc\Param(name: "tag_pos", type: "int", require: true, default: 1, desc: "桩位置标示：1-地上,2-地库"),
        Apidoc\Param(name: "tag_park", type: "string", default: "", desc: "停车费说明"),
        Apidoc\Param(name: "place_rate", type: "int", require: true, default: 0, desc: "占位费（X分/分钟）,X*100"),
        Apidoc\Param(name: "charge", type: "string", require: true, default: "", desc: "负责人"),
        Apidoc\Param(name: "charge_phone", type: "string", require: true, default: "", desc: "负责人手机号"),
        Apidoc\Param(name: "tag_toilet", type: "int", require: true, default: 1, desc: "场站服务卫生间标识：1-无，2-有"),
        Apidoc\Param(name: "tag_canopy", type: "int", require: true, default: 1, desc: "场站服务雨棚标识：1-无，2-有"),
        Apidoc\Param(name: "tag_rest", type: "int", require: true, default: 1, desc: "场站服务休息室标识：1-无，2-有"),
        Apidoc\Param(name: "tag_pnp", type: "int", require: true, default: 1, desc: "场站特色即插即用标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_insure", type: "int", require: true, default: 1, desc: "场站特色充电保险标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_protect", type: "int", require: true, default: 1, desc: "场站特色充电电池防护标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_ultrafast", type: "int", require: true, default: 1, desc: "场站电桩超快充标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_fast", type: "int", require: true, default: 1, desc: "场站电桩快充标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_slow", type: "int", require: true, default: 1, desc: "场站电桩慢充标识：1-不支持，2-支持"),
        Apidoc\Param(name: "is_support_reservation", type: "int", require: true, default: 1, desc: "是否支持预约充电 0:不支持 1:支持")
    ]
    public function add(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Add($this->loginUser, $this->request))->run();
        }, true);
    }


    #[
        Apidoc\Title("编辑充电站"),
        Apidoc\Desc('只允许运营商账号操作(corp_id > 0)'),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.3 修改, cbj 2024.12.10 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Stations/edit"),
        Apidoc\Param(name: "id", type: "int", require: true, default: 0, desc: "场站id"),
        Apidoc\Param(name: "name", type: "string", require: true, default: "", desc: "场站名称"),
        Apidoc\Param(name: "address", type: "string", require: true, default: "", desc: "场站地址"),
        Apidoc\Param(name: "city_id", type: "array", require: true, default: [], desc: "省市区id"),
        Apidoc\Param(name: "lonlat", type: "string", require: true, default: "", desc: "经纬度，前面纬度，后面经度"),
        Apidoc\Param(name: "tariff_group_id", type: "int", require: true, default: 0, desc: "费率组id"),
        Apidoc\Param(name: "type", type: "int", require: true, default: 1, desc: "场站类型：1-公共、2-自营"),
        Apidoc\Param(name: "status", type: "int", require: true, default: 1, desc: "运行状态：1-正常使用、2-维护中、3-未开放"),
        Apidoc\Param(name: "pictures", type: "array", default: [], desc: "场站图片url集"),
        Apidoc\Param(name: "work_time", type: "string", default: "", desc: "运营时段描述"),
        Apidoc\Param(name: "tag_pos", type: "int", require: true, default: 1, desc: "桩位置标示：1-地上,2-地库"),
        Apidoc\Param(name: "tag_park", type: "string", default: "", desc: "停车费说明"),
        Apidoc\Param(name: "place_rate", type: "int", require: true, default: 0, desc: "占位费（X分/分钟）,X*100"),
        Apidoc\Param(name: "charge", type: "string", require: true, default: "", desc: "负责人"),
        Apidoc\Param(name: "charge_phone", type: "string", require: true, default: "", desc: "负责人手机号"),
        Apidoc\Param(name: "tag_toilet", type: "int", require: true, default: 1, desc: "场站服务卫生间标识：1-无，2-有"),
        Apidoc\Param(name: "tag_canopy", type: "int", require: true, default: 1, desc: "场站服务雨棚标识：1-无，2-有"),
        Apidoc\Param(name: "tag_rest", type: "int", require: true, default: 1, desc: "场站服务休息室标识：1-无，2-有"),
        Apidoc\Param(name: "tag_pnp", type: "int", require: true, default: 1, desc: "场站特色即插即用标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_insure", type: "int", require: true, default: 1, desc: "场站特色充电保险标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_protect", type: "int", require: true, default: 1, desc: "场站特色充电电池防护标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_ultrafast", type: "int", require: true, default: 1, desc: "场站电桩超快充标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_fast", type: "int", require: true, default: 1, desc: "场站电桩快充标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_slow", type: "int", require: true, default: 1, desc: "场站电桩慢充标识：1-不支持，2-支持"),
        Apidoc\Param(name: "is_support_reservation", type: "int", require: true, default: 1, desc: "是否支持预约充电 0:不支持 1:支持")
    ]
    public function edit(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Update($this->loginUser, $this->request))->run();
        }, true);
    }

    #[
        Apidoc\Title("删除充电站"),
        Apidoc\Desc('只允许运营商账号操作(corp_id > 0)'),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.8 修改, cbj 2024.12.10 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Stations/delete"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "充电站id"),
    ]
    public function delete(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Delete($this->loginUser, $this->request))->run();
        });
    }

    #[
        Apidoc\Title("获取充电站列表"),
        Apidoc\Author("lwj 2023.8.3 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Stations/get_stations_list"),
        Apidoc\Param(name: "corp_id", type: "int", desc: "运营商编号"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "充电站列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电站id'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电站名称']
        ]),
    ]
    public function get_stations_list(): Json
    {
        return $this->get_stations_list_v2();
        $data = $this->request->post();

        // 数据权限
        $where = [];
        $where[] = ['is_del', '=', StationsModel::IsDelNot];
        if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
            $where[] = ['corp_id', '=', $this->loginUser->corp_id];
        } else if ($this->loginUser->corp_id > 0) {
            $where[] = ['corp_id', '=', $this->loginUser->corp_id];
            $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
            $where[] = ['id', 'in', $station_ids];
        } else {
            if (!empty($data['corp_id'])) {
                $where[] = ['corp_id', '=', $data['corp_id']];
            }
        }

        $list = $this->modelClass
            ->where($where)
            ->column('id,name');
        return $this->res_success($list);
    }

    #[
        Apidoc\Title("获取充电站列表"),
        Apidoc\Author("lwj 2023.8.3 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Stations/get_stations_list"),
        Apidoc\Param(name: "corp_id", type: "int", desc: "运营商编号"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "充电站列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电站id'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电站名称']
        ]),
    ]
    public function get_stations_list_v2(): Json
    {
        $data = $this->request->post();
        $data['page'] = $data['page'] ??  1;
        $data['limit'] = $data['limit'] ??  $this->pageSize;
        $data['sort_field'] = $data['order_name'] ??  1;
        $data['sort_type'] = $data['order_type'] ??  2;
        $data['station_ids'] = $this->loginUser->station_ids ?? null;
        // 临时解决方案,因为订单管理中传来的status是数组,后续需要修改
        unset($data['status']);
        // ========== 从设备服务获取场站列表 ========== //
        $response = Api::send('/device/stations/options','GET', $data);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }

    #[
        Apidoc\Title("场站电表功率数据"),
        Apidoc\Author("cbj 2024.01.04 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Stations/electricity_meter_power_record"),
        Apidoc\Param(name: "station_id", type: "int", require: true, desc: "场站ID"),
        Apidoc\Param(name: "centralized_controller_id", type: "string", require: true, desc: "能源路由器ID"),
        Apidoc\Param(name: "start_time", type: "int", require: true, desc: "起始时间"),
        Apidoc\Param(name: "end_time", type: "int", require: true, desc: "结尾时间"),
        Apidoc\Returned(name: "list", type: "array", require: true, desc: "记录列表", children: [
            ['name' => 'a', 'type' => 'int', 'desc' => 'A相有功功率'],
            ['name' => 'b', 'type' => 'int', 'desc' => 'B相有功功率'],
            ['name' => 'c', 'type' => 'int', 'desc' => 'C相有功功率'],
            ['name' => 'total', 'type' => 'int', 'desc' => '总有功功率'],
            ['name' => 'time', 'type' => 'string', 'desc' => '记录时间'],
        ]),
    ]
    public function electricity_meter_power_record(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::stations([
                'station_id',
                'centralized_controller_id',
                'start_time',
                'end_time'
            ]));


            return (new ElectricityMeterPowerRecord())->record_list(
                $this->loginUser,
                $verify_data['station_id'],
                $verify_data['centralized_controller_id'],
                $verify_data['start_time'],
                $verify_data['end_time']
            );
        });
    }

    #[
        Apidoc\Title("获取场站分成列表"),
        Apidoc\Author("lwj 2024.5.11 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/stations/get_stations_clearing_list"),
        Apidoc\NotResponseSuccess,
        Apidoc\Param(name: "station_id", type: "int", require: true, desc: "场站id"),
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "运营商列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '分成比例id'],
            ['name' => 'station_id', 'type' => 'int', 'desc' => '场站id'],
            ['name' => 'corp_id', 'type' => 'int', 'desc' => '运营商id'],
            ['name' => 'ratio_electricity_price', 'type' => 'int', 'desc' => '充电费分成比例,0-100'],
            ['name' => 'ratio_ser_price', 'type' => 'int', 'desc' => '服务费分成比例,0-100'],
        ]),
    ]
    public function get_stations_clearing_list(): Json
    {
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::stations_corp_clearing([
                'station_id',
            ]));
            $list = (new StationsCorpClearingModel())
                ->where('station_id', $verify_data['station_id'])
                ->field('id,station_id,corp_id,ratio_electricity_price,ratio_ser_price')
                ->order('sort_num', 'asc')
                ->select()
                ->toArray();

            return $this->res_success($list);
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("保存场站分成"),
        Apidoc\Author("lwj 2024.5.11 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/stations/save_stations_clearing"),
        Apidoc\NotResponseSuccess,
        Apidoc\Param(name: "station_id", type: "int", require: true, desc: "场站id"),
        Apidoc\Param(name: "clearing_list", type: "array", require: true, desc: "分成列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '分成比例id'],
            ['name' => 'station_id', 'type' => 'int', 'desc' => '场站id'],
            ['name' => 'corp_id', 'type' => 'int', 'desc' => '运营商id'],
            ['name' => 'ratio_electricity_price', 'type' => 'int', 'desc' => '充电费分成比例,0-100'],
            ['name' => 'ratio_ser_price', 'type' => 'int', 'desc' => '服务费分成比例,0-100'],
        ]),
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "运营商列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '分成比例id'],
            ['name' => 'station_id', 'type' => 'int', 'desc' => '场站id'],
            ['name' => 'corp_id', 'type' => 'int', 'desc' => '运营商id'],
            ['name' => 'ratio_electricity_price', 'type' => 'int', 'desc' => '充电费分成比例,0-100'],
            ['name' => 'ratio_ser_price', 'type' => 'int', 'desc' => '服务费分成比例,0-100'],
        ]),
    ]
    public function save_stations_clearing(): Json
    {
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::stations_corp_clearing([
                'station_id',
//                'corp_id',
                'clearing_list',
            ]));

            $clearing_list = $verify_data['clearing_list'];
            $clearing_list_count = count($clearing_list);

            if ($clearing_list_count < 1) return $this->res_error([], '分成比例不能为空');
            $corp_count = count(array_unique(array_column($clearing_list, 'corp_id')));
            if ($corp_count !== $clearing_list_count) {
                return $this->res_error([], '运营商id不能重复');
            }

            $ratio_electricity_price_list = array_column($clearing_list, 'ratio_electricity_price');
            $total_ratio_electricity_price = array_sum($ratio_electricity_price_list);
            if ($total_ratio_electricity_price !== 100) {
                return $this->res_error([], '充电费分成比例之和必须为100,你的总比值：' . $total_ratio_electricity_price);
            }

            $ratio_ser_price_list = array_column($clearing_list, 'ratio_ser_price');
            $total_ratio_ser_price = array_sum($ratio_ser_price_list);
            if ($total_ratio_ser_price !== 100) {
                return $this->res_error([], '服务费分成比例之和必须为100,你的总比值：' . $total_ratio_ser_price);
            }

            $StationsCorpClearingModel = new StationsCorpClearingModel();

            $stations_clearing_list = $StationsCorpClearingModel
                ->where('station_id', $verify_data['station_id'])
                ->field('id,station_id,corp_id,ratio_electricity_price,ratio_ser_price')
                ->select()
                ->toArray();
            trace('保存场站分成:' . $verify_data['station_id'] . ',原始分成数据=》' . json_encode_cn($stations_clearing_list), '信息');

            $y_id = array_column($stations_clearing_list, 'id');
            $n_id = array_column($clearing_list, 'id');
            $del_id = array_diff($y_id, $n_id);

            Db::startTrans();
            try {
                if ($del_id) {
                    trace('保存场站分成:' . $verify_data['station_id'] . ',删除=》' . json_encode_cn($del_id), '信息');
                    $StationsCorpClearingModel
                        ->where('station_id', $verify_data['station_id'])
                        ->whereIn('id', $del_id)
                        ->delete();
                }

                foreach ($clearing_list as $k => $v) {
                    if ($v['id'] > 0) {
                        $StationsCorpClearingModel
                            ->where('id', $v['id'])
                            ->where('station_id', $v['station_id'])
                            ->where('corp_id', $v['corp_id'])
                            ->update([
                                'ratio_electricity_price' => $v['ratio_electricity_price'],
                                'ratio_ser_price' => $v['ratio_ser_price'],
                                'sort_num' => $k,
                            ]);
                    } else {
                        $StationsCorpClearingModel
                            ->insert([
                                'station_id' => $v['station_id'],
                                'corp_id' => $v['corp_id'],
                                'ratio_electricity_price' => $v['ratio_electricity_price'],
                                'ratio_ser_price' => $v['ratio_ser_price'],
                                'sort_num' => $k,
                            ]);
                    }
                }
                Db::commit();

            } catch (Throwable $e) {
                Db::rollback();
                trace('保存场站分成，并回滚数据=》异常：' . $e->getMessage(), '信息');
                return $this->res_error([], '处理异常，并回滚数据：' . $e->getMessage());
            }

            $list = $StationsCorpClearingModel
                ->where('station_id', $verify_data['station_id'])
                ->field('id,station_id,corp_id,ratio_electricity_price,ratio_ser_price')
                ->select()
                ->toArray();

            return $this->res_success($list, '保存成功');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取全部场站列表"),
        Apidoc\Author("lwj 2024.5.17 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/stations/get_stations_list_all"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "充电站列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电站id'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电站名称']
        ]),
    ]
    public function get_stations_list_all(): Json
    {
        $list = $this->modelClass
            ->where('is_del', '=', StationsModel::IsDelNot)
            ->column('id,name');

        return $this->res_success($list);
    }


}