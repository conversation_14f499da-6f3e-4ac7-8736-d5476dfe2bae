<?php


namespace app\admin\controller;

use app\common\controller\ApiBase;
use app\common\lib\VerifyData;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use app\common\model\City as CityModel;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\response\Json;
use Throwable;

#[Apidoc\Title("城市")]
class City  extends ApiBase
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new CityModel();
    }

    #[
        Apidoc\Title("获取城市"),
        Apidoc\Author("lwj 2023.8.2 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/City/get_city"),
        Apidoc\Param(name: "pid", type: "int", require: true, desc: "pid"),
    ]
    public function get_city(): Json
    {
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::gg([
                'pid'
            ]));

            $result = $this->modelClass
                ->where('pid',$verify_data['pid'])
                ->column('id,name');
            if ($result) return $this->res_success($result);
            return $this->res_error([], '无数据');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：'.$e->getMessage());
        }
    }


}