<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\ScanCodeRecords;
use app\common\repositories\CorpOperatingFundsDailyStatistics;
use app\common\repositories\OperatingFundsDailyStatistics;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\db\Query;
use think\response\Json;

#[Apidoc\Title("报表管理")]
class ReportForm extends BaseController
{
    #[
        Apidoc\Title("设备故障记录的导出数据"),
        Apidoc\Author("cbj 2024-08-16 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/report_form/export_device_failure_record"),
        Apidoc\Param(name: "start_time", type: "string", require: true, default: "2024-08-01 12:00:00", desc: "起始时间(格式:YYYY-mm-dd HH:ii:ss)"),
        Apidoc\Param(name: "end_time", type: "string", require: true, default: "2024-08-30 12:00:00", desc: "末尾时间(格式:YYYY-mm-dd HH:ii:ss)"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'date', 'type' => 'string', 'desc' => '日期'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '场站名称'],
            ['name' => 'device_id', 'type' => 'string', 'desc' => '设备编号'],
            ['name' => 'failure_cause', 'type' => 'string', 'desc' => '故障原因'],
            ['name' => 'status', 'type' => 'string', 'desc' => '处理状态'],
        ]),
    ]
    public function export_device_failure_record(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::report_form([
                'start_time',
                'end_time',
            ]));

            $where = [
                ['ar.alarm_time', '>=', strtotime($verify_data['start_time'])],
                ['ar.alarm_time', '<=', strtotime($verify_data['end_time'])],
            ];

            if ($this->loginUser->corp_id > 0) {
                $where[] = ['ar.corp_id', '=', $this->loginUser->corp_id];
            }

            /**
             * @var Query $model
             */
            $model = new \app\common\model\AlarmRecord();
            $data = $model->alias('ar')
                ->leftJoin('stations s', 's.id = ar.station_id')
                ->field([
                    'ar.alarm_time as date', 's.name as station_name',
                    'ar.device_id', 'ar.code as failure_cause',
                    'ar.is_recovery as status'
                ])
                ->withAttr('date', function ($date) {
                    return date("Y-m-d H:i:s", $date);
                })
                ->withAttr('failure_cause', function ($code) {
                    return \app\common\repositories\AlarmRecord::CodeToMessageMap[$code] ?? '未知';
                })
                ->withAttr('status', function ($status) {
                    return \app\common\repositories\AlarmRecord::StatusMessage[$status] ?? '未知';
                })
                ->where($where)
                ->order('ar.id', 'desc')
                ->select()
                ->toArray();


            return ['data' => $data];
        });
    }

    #[
        Apidoc\Title("设备故障记录的列表数据"),
        Apidoc\Author("cbj 2024-08-31 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/report_form/device_failure_record_list"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "显示条数"),
        Apidoc\Param(name: "start_time", type: "string", require: true, default: "2024-08-01 12:00:00", desc: "起始时间(格式:YYYY-mm-dd HH:ii:ss)"),
        Apidoc\Param(name: "end_time", type: "string", require: true, default: "2024-08-30 12:00:00", desc: "末尾时间(格式:YYYY-mm-dd HH:ii:ss)"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'date', 'type' => 'string', 'desc' => '日期'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '场站名称'],
            ['name' => 'device_id', 'type' => 'string', 'desc' => '设备编号'],
            ['name' => 'failure_cause', 'type' => 'string', 'desc' => '故障原因'],
            ['name' => 'status', 'type' => 'string', 'desc' => '处理状态'],
        ]),
    ]
    public function device_failure_record_list(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::report_form([
                'start_time',
                'end_time',
                'limit',
                'page'
            ]));

            $where = [
                ['ar.alarm_time', '>=', strtotime($verify_data['start_time'])],
                ['ar.alarm_time', '<=', strtotime($verify_data['end_time'])],
            ];

            if ($this->loginUser->corp_id > 0) {
                $where[] = ['ar.corp_id', '=', $this->loginUser->corp_id];
            }

            /**
             * @var Query $model
             */
            $model = new \app\common\model\AlarmRecord();
            return $model->alias('ar')
                ->leftJoin('stations s', 's.id = ar.station_id')
                ->field([
                    'ar.alarm_time as date', 's.name as station_name',
                    'ar.device_id', 'ar.code as failure_cause',
                    'ar.is_recovery as status'
                ])
                ->withAttr('date', function ($date) {
                    return date("Y-m-d H:i:s", $date);
                })
                ->withAttr('failure_cause', function ($code) {
                    return \app\common\repositories\AlarmRecord::CodeToMessageMap[$code] ?? '未知';
                })
                ->withAttr('status', function ($status) {
                    return \app\common\repositories\AlarmRecord::StatusMessage[$status] ?? '未知';
                })
                ->where($where)
                ->order('ar.id', 'desc')
                ->paginate(['list_rows' => $verify_data['limit'], 'page' => $verify_data['page']])
                ->toArray();
        });
    }

    #[
        Apidoc\Title("超级管理员的运营资金导出数据"),
        Apidoc\Author("cbj 2024-08-28 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/report_form/export_operating_funds"),
        Apidoc\Param(name: "start_time", type: "string", require: true, default: "2024-08-01", desc: "起始时间(格式:YYYY-mm-dd)"),
        Apidoc\Param(name: "end_time", type: "string", require: true, default: "2024-08-30", desc: "末尾时间(格式:YYYY-mm-dd)"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'date', 'type' => 'string', 'desc' => '日期'],
            ['name' => 'recharge_money', 'type' => 'int', 'desc' => '充值金额(精确到小数点后2位)'],
            ['name' => 'recharge_count', 'type' => 'int', 'desc' => '充值次数'],
            ['name' => 'charging_money', 'type' => 'int', 'desc' => '充电金额(精确到小数点后2位)'],
            ['name' => 'charging_count', 'type' => 'int', 'desc' => '充电次数'],
        ]),
    ]
    public function export_operating_funds(): Json
    {
        return $this->openExceptionCatch(function () {

            if ($this->loginUser->corp_id > 0) {
                throw new RuntimeException('需要超级管理员才能调用', [], RuntimeException::CodeBusinessException);
            }


            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::report_form([
                'start_time',
                'end_time',
            ]));

            return [
                'data' => OperatingFundsDailyStatistics::get_time_range_data($verify_data['start_time'], $verify_data['end_time'])
            ];
        });
    }

    #[
        Apidoc\Title("超级管理员的运营资金列表数据"),
        Apidoc\Author("cbj 2024-08-28 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/report_form/operating_funds_list"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "显示条数"),
        Apidoc\Param(name: "start_time", type: "string", require: true, default: "2024-08-01", desc: "起始时间(格式:YYYY-mm-dd)"),
        Apidoc\Param(name: "end_time", type: "string", require: true, default: "2024-08-30", desc: "末尾时间(格式:YYYY-mm-dd)"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'date', 'type' => 'string', 'desc' => '日期'],
            ['name' => 'recharge_money', 'type' => 'int', 'desc' => '充值金额(精确到小数点后2位)'],
            ['name' => 'recharge_count', 'type' => 'int', 'desc' => '充值次数'],
            ['name' => 'charging_money', 'type' => 'int', 'desc' => '充电金额(精确到小数点后2位)'],
            ['name' => 'charging_count', 'type' => 'int', 'desc' => '充电次数'],
        ]),
    ]
    public function operating_funds_list(): Json
    {
        return $this->openExceptionCatch(function () {

            if ($this->loginUser->corp_id > 0) {
                throw new RuntimeException('需要超级管理员才能调用', [], RuntimeException::CodeBusinessException);
            }


            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::report_form([
                'start_time',
                'end_time',
                'page',
                'limit'
            ]));

            return OperatingFundsDailyStatistics::get_list(
                $verify_data['start_time'], $verify_data['end_time'],
                $verify_data['page'], $verify_data['limit']
            );
        });
    }

    #[
        Apidoc\Title("运营商的运营资金的导出数据"),
        Apidoc\Author("cbj 2024-08-28 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/report_form/export_corp_operating_funds"),
        Apidoc\Param(name: "start_time", type: "string", require: true, default: "2024-08-01", desc: "起始时间(格式:YYYY-mm-dd)"),
        Apidoc\Param(name: "end_time", type: "string", require: true, default: "2024-08-30", desc: "末尾时间(格式:YYYY-mm-dd)"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'date', 'type' => 'string', 'desc' => '日期'],
            ['name' => 'charging_money', 'type' => 'int', 'desc' => '充电金额(精确到小数点后2位)'],
            ['name' => 'charging_count', 'type' => 'int', 'desc' => '充电次数'],
        ]),
    ]
    public function export_corp_operating_funds(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::report_form([
                'start_time',
                'end_time',
            ]));

            return [
                'data' => CorpOperatingFundsDailyStatistics::get_time_range_data($this->loginUser->corp_id, $verify_data['start_time'], $verify_data['end_time'])
            ];
        });
    }

    #[
        Apidoc\Title("运营商的运营资金的列表数据"),
        Apidoc\Author("cbj 2024-08-28 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/report_form/corp_operating_funds_list"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "显示条数"),
        Apidoc\Param(name: "start_time", type: "string", require: true, default: "2024-08-01", desc: "起始时间(格式:YYYY-mm-dd)"),
        Apidoc\Param(name: "end_time", type: "string", require: true, default: "2024-08-30", desc: "末尾时间(格式:YYYY-mm-dd)"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'date', 'type' => 'string', 'desc' => '日期'],
            ['name' => 'charging_money', 'type' => 'int', 'desc' => '充电金额(精确到小数点后2位)'],
            ['name' => 'charging_count', 'type' => 'int', 'desc' => '充电次数'],
        ]),
    ]
    public function corp_operating_funds_list(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::report_form([
                'start_time',
                'end_time',
                'page',
                'limit'
            ]));

            return CorpOperatingFundsDailyStatistics::get_list(
                $this->loginUser->corp_id,
                $verify_data['start_time'],
                $verify_data['end_time'],
                $verify_data['page'],
                $verify_data['limit']
            );
        });
    }


    #[
        Apidoc\Title("运营扫码的导出数据"),
        Apidoc\Author("cbj 2024-08-17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/report_form/export_scan_code_records"),
        Apidoc\Param(name: "start_time", type: "string", require: true, default: "2024-08-01 12:00:00", desc: "起始时间(格式:YYYY-mm-dd HH:ii:ss)"),
        Apidoc\Param(name: "end_time", type: "string", require: true, default: "2024-08-30 12:00:00", desc: "末尾时间(格式:YYYY-mm-dd HH:ii:ss)"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'time', 'type' => 'string', 'desc' => '时间'],
            ['name' => 'phone', 'type' => 'string', 'desc' => '用户手机'],
            ['name' => 'piles_id', 'type' => 'int', 'desc' => '充电桩ID'],
            ['name' => 'shots_number', 'type' => 'int', 'desc' => '充电枪编号'],
            ['name' => 'scan_code_result', 'type' => 'string', 'desc' => '扫码结果'],
        ]),
    ]
    public function export_scan_code_records(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::report_form([
                'start_time',
                'end_time',
            ]));

            if ($this->loginUser->corp_id > 0) {
                $verify_data['corp_id'] = $this->loginUser->corp_id;
            }

            return ['data' => (new ScanCodeRecords())->get_all($verify_data)];
        });
    }

    #[
        Apidoc\Title("运营扫码的列表数据"),
        Apidoc\Author("cbj 2024-08-17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/report_form/scan_code_records_list"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "显示条数"),
        Apidoc\Param(name: "start_time", type: "string", require: true, default: "2024-08-01 12:00:00", desc: "起始时间(格式:YYYY-mm-dd HH:ii:ss)"),
        Apidoc\Param(name: "end_time", type: "string", require: true, default: "2024-08-30 12:00:00", desc: "末尾时间(格式:YYYY-mm-dd HH:ii:ss)"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'time', 'type' => 'string', 'desc' => '时间'],
            ['name' => 'phone', 'type' => 'string', 'desc' => '用户手机'],
            ['name' => 'piles_id', 'type' => 'int', 'desc' => '充电桩ID'],
            ['name' => 'shots_number', 'type' => 'int', 'desc' => '充电枪编号'],
            ['name' => 'scan_code_result', 'type' => 'string', 'desc' => '扫码结果'],
        ]),
    ]
    public function scan_code_records_list(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::report_form([
                'start_time',
                'end_time',
                'page',
                'limit'
            ]));

            if ($this->loginUser->corp_id > 0) {
                $verify_data['corp_id'] = $this->loginUser->corp_id;
            }

            return (new ScanCodeRecords())->get_list($verify_data['page'], $verify_data['limit'], $verify_data);
        });
    }

    #[
        Apidoc\Title("运营订单的导出数据"),
        Apidoc\Author("cbj 2024-08-17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/report_form/export_order_list"),
        Apidoc\Param(name: "start_time", type: "string", require: true, default: "2024-08-01 12:00:00", desc: "起始时间(格式:YYYY-mm-dd HH:ii:ss)"),
        Apidoc\Param(name: "end_time", type: "string", require: true, default: "2024-08-30 12:00:00", desc: "末尾时间(格式:YYYY-mm-dd HH:ii:ss)"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '订单号'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '场站名称'],
            ['name' => 'piles_id', 'type' => 'int', 'desc' => '充电桩ID'],
            ['name' => 'shots_number', 'type' => 'int', 'desc' => '充电枪编号'],
            ['name' => 'start_time', 'type' => 'int', 'desc' => '开始充电的时间'],
            ['name' => 'end_time', 'type' => 'int', 'desc' => '结束充电的时间'],
            ['name' => 'electricity_price', 'type' => 'int', 'desc' => '电费(保留4为小数)'],
            ['name' => 'ser_price', 'type' => 'int', 'desc' => '服务费(保留4为小数)'],
            ['name' => 'coupon_money', 'type' => 'int', 'desc' => '减免金额(小数点后2位)'],
            ['name' => 'pay_money', 'type' => 'int', 'desc' => '实付金额(小数点后2位)'],
            ['name' => 'phone', 'type' => 'string', 'desc' => '用户手机号'],
        ]),
    ]
    public function export_order_list(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::report_form([
                'start_time',
                'end_time',
            ]));

            $where = [
                ['o.trans_end_time', '>=', $verify_data['start_time']],
                ['o.trans_end_time', '<=', $verify_data['end_time']],
            ];

            if ($this->loginUser->corp_id > 0) {
                $where[] = ['o.corp_id', '=', $this->loginUser->corp_id];
            }

            $fields = [
                'o.id', 's.name as station_name', 'o.piles_id',
                'o.sequence as shots_number', 'o.trans_start_time',
                'o.trans_end_time', 'o.electricity_price', 'o.ser_price',
                'o.coupon_money', 'o.pay_money', 'u.phone'
            ];

            /**
             * @var Query $orderModel
             */
            $orderModel = (new \app\common\model\Order());
            $data = $orderModel->alias('o')
                ->leftJoin('users u', 'o.user_id = u.id')
                ->leftJoin('stations s', 's.id = o.station_id')
                ->field($fields)
                ->where($where)
                ->order('o.create_time', 'desc')
                ->select()
                ->toArray();

            return ['data' => $data];
        });
    }


    #[
        Apidoc\Title("运营订单的列表数据"),
        Apidoc\Author("cbj 2024-08-17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/report_form/order_list"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "显示条数"),
        Apidoc\Param(name: "start_time", type: "string", require: true, default: "2024-08-01 12:00:00", desc: "起始时间(格式:YYYY-mm-dd HH:ii:ss)"),
        Apidoc\Param(name: "end_time", type: "string", require: true, default: "2024-08-30 12:00:00", desc: "末尾时间(格式:YYYY-mm-dd HH:ii:ss)"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '订单号'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '场站名称'],
            ['name' => 'piles_id', 'type' => 'int', 'desc' => '充电桩ID'],
            ['name' => 'shots_number', 'type' => 'int', 'desc' => '充电枪编号'],
            ['name' => 'start_time', 'type' => 'int', 'desc' => '开始充电的时间'],
            ['name' => 'end_time', 'type' => 'int', 'desc' => '结束充电的时间'],
            ['name' => 'electricity_price', 'type' => 'int', 'desc' => '电费(保留4为小数)'],
            ['name' => 'ser_price', 'type' => 'int', 'desc' => '服务费(保留4为小数)'],
            ['name' => 'coupon_money', 'type' => 'int', 'desc' => '减免金额(小数点后2位)'],
            ['name' => 'pay_money', 'type' => 'int', 'desc' => '实付金额(小数点后2位)'],
            ['name' => 'phone', 'type' => 'string', 'desc' => '用户手机号'],
        ]),
    ]
    public function order_list(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::report_form([
                'start_time',
                'end_time',
                'page',
                'limit'
            ]));

            $where = [
                ['o.trans_end_time', '>=', $verify_data['start_time']],
                ['o.trans_end_time', '<=', $verify_data['end_time']],
            ];

            if ($this->loginUser->corp_id > 0) {
                $where[] = ['o.corp_id', '=', $this->loginUser->corp_id];
            }

            $fields = [
                'o.id', 's.name as station_name', 'o.piles_id',
                'o.sequence as shots_number', 'o.trans_start_time',
                'o.trans_end_time', 'o.electricity_price', 'o.ser_price',
                'o.coupon_money', 'o.pay_money', 'u.phone'
            ];

            /**
             * @var Query $orderModel
             */
            $orderModel = (new \app\common\model\Order());
            return $orderModel->alias('o')
                ->leftJoin('users u', 'o.user_id = u.id')
                ->leftJoin('stations s', 's.id = o.station_id')
                ->field($fields)
                ->where($where)
                ->order('o.create_time', 'desc')
                ->paginate(['list_rows' => $verify_data['limit'], 'page' => $verify_data['page']])
                ->toArray();
        });
    }

}