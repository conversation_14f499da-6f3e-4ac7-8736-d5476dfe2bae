<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;


use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\response\Json;
use app\common\model\OrderClearing;

#[Apidoc\Title("财务中心/清分")]
class Clearing extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new OrderClearing();
    }

    #[
        Apidoc\Title("订单清分列表"),
        Apidoc\Author("lwj 2024.5.17 新增，lwj 2024.6.13 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Clearing/order_clearing_list"),
        Apid<PERSON>\Param(name: "corp_id", type: "int", require: true, desc: "运营商id"),
        Apidoc\Param(name: "station_id", type: "int", require: true, desc: "场站id"),
        Apidoc\Param(name: "status", type: "int", require: true, desc: "状态，1待结算，2已结算"),
        Apidoc\Param(name: "order_id", type: "string", require: true, desc: "订单id"),
        Apidoc\Param(name: "time_range", type: "array", require: true, desc: "账单日期范围"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => 'id'],
            ['name' => 'order_id', 'type' => 'string', 'desc' => '订单id'],
            ['name' => 'clearing_electricity_price', 'type' => 'int', 'desc' => '应分充电费 - 保留4为小数'],
            ['name' => 'clearing_ser_price', 'type' => 'int', 'desc' => '应分服务费 - 保留4为小数'],
            ['name' => 'status', 'type' => 'int', 'desc' => '状态，1待结算，2已结算'],

            ['name' => 'clearing_time', 'type' => 'string', 'desc' => '结算时间'],
            ['name' => 'order_time', 'type' => 'string', 'desc' => '订单时间(交易结束时间)'],
            ['name' => 'bill_date', 'type' => 'string', 'desc' => '账单日期'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],

            ['name' => 'order_clearing_electricity_price', 'type' => 'int', 'desc' => '订单应分充电费 - 保留4为小数'],
            ['name' => 'order_clearing_ser_price', 'type' => 'int', 'desc' => '订单应分服务费 - 保留4为小数'],
            ['name' => 'ratio_electricity_price', 'type' => 'int', 'desc' => '充电费分成比例'],
            ['name' => 'ratio_ser_price', 'type' => 'int', 'desc' => '服务费分成比例'],

            ['name' => 'stations_name', 'type' => 'string', 'desc' => '场站名称'],
            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
        ]),
    ]
    public function order_clearing_list(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
            $order_name = ($data['order_name'] ?? false) ?: 'a.id';
            $order_type = ($data['order_type'] ?? false) ?: 'desc';

            // 搜索框搜索
            $where = function ($query) use ($data) {
                if ($this->loginUser->corp_id > 0) {
                    $query->where('a.corp_id', '=', $this->loginUser->corp_id);
                }

                if (!empty($data['time_range']) &&
                    is_array($data['time_range']) &&
                    count($data['time_range']) == 2
                ) {
                    $query->where('a.bill_date', '>=', $data['time_range'][0]);
                    $query->where('a.bill_date', '<=', $data['time_range'][1]);
                }
                if (isset($data['order_id']) && $data['order_id']) {
                    $query->where('a.order_id', $data['order_id']);
                }
                if (isset($data['status']) && $data['status']) {
                    $query->where('a.status', $data['status']);
                }
                if (isset($data['station_id']) && $data['station_id']) {
                    $query->where('a.station_id', $data['station_id']);
                }
                if (isset($data['corp_id']) && $data['corp_id']) {
                    $query->where('a.corp_id', $data['corp_id']);
                }
                return $query;
            };

            $sum = $this->modelClass->alias('a')
                ->join('stations b', 'a.station_id = b.id')
                ->join('corp c', 'a.corp_id = c.id')
                ->where($where)
                ->field([
                    'sum(a.clearing_electricity_price) as sum_clearing_electricity_price',
                    'sum(a.clearing_ser_price) as sum_clearing_ser_price',
                ])
                ->find()
                ->toArray();

            $order_clearing = $this->modelClass->alias('a')
                ->join('stations b', 'a.station_id = b.id')
                ->join('corp c', 'a.corp_id = c.id')
                ->field([
                    'a.id',
                    'a.order_id',
                    'a.clearing_electricity_price',
                    'a.clearing_ser_price',
                    'a.status',
                    'a.clearing_time',
                    'a.order_time',
                    'a.bill_date',
                    'a.create_time',
                    'a.order_clearing_electricity_price',
                    'a.order_clearing_ser_price',
                    'a.ratio_electricity_price',
                    'a.ratio_ser_price',
                    'b.name as stations_name',
                    'c.name as corp_name',
                ])
                ->where($where)
                ->order($order_name, $order_type)
                ->paginate(['list_rows' => $pageSize, 'page' => $page])
                ->toArray();

            if ($sum) {
                $order_clearing['sum_clearing_electricity_price'] = (int)$sum['sum_clearing_electricity_price'];
                $order_clearing['sum_clearing_ser_price'] = (int)$sum['sum_clearing_ser_price'];
            }
            return $order_clearing;
        });
    }

    #[
        Apidoc\Title("获取订单清分排序信息"),
        Apidoc\Author("lwj 2024.5.17 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Clearing/order_clearing_sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function order_clearing_sort_list_info(): Json
    {
        $res = [
            'order_name' => 'a.id',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'a.id', 'label' => 'id'],
                ['value' => 'a.order_id', 'label' => '订单'],
                ['value' => 'a.station_id', 'label' => '场站'],
                ['value' => 'a.corp_id', 'label' => '运营商'],
                ['value' => 'a.status', 'label' => '状态'],
                ['value' => 'a.bill_date', 'label' => '账单日期'],
                ['value' => 'a.create_time', 'label' => '创建时间'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("运营商清分列表"),
        Apidoc\Author("lwj 2024.5.23 新增，lwj 2024.6.13 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Clearing/corp_clearing_list"),
        Apidoc\Param(name: "corp_id", type: "int", require: true, desc: "运营商id"),
        Apidoc\Param(name: "station_id", type: "int", require: true, desc: "场站id"),
        Apidoc\Param(name: "status", type: "int", require: true, desc: "状态，1待结算，2已结算"),
        Apidoc\Param(name: "time_range", type: "array", require: true, desc: "账单日期范围"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'clearing_electricity_price', 'type' => 'int', 'desc' => '应分充电费 - 保留4为小数'],
            ['name' => 'clearing_ser_price', 'type' => 'int', 'desc' => '应分服务费 - 保留4为小数'],
            ['name' => 'status', 'type' => 'int', 'desc' => '状态，1待结算，2已结算'],

            ['name' => 'clearing_time', 'type' => 'string', 'desc' => '结算时间'],
            ['name' => 'bill_date', 'type' => 'string', 'desc' => '账单日期'],

            ['name' => 'stations_name', 'type' => 'string', 'desc' => '场站名称'],
            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
        ]),
    ]
    public function corp_clearing_list(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
            $order_name = ($data['order_name'] ?? false) ?: 'a.bill_date';
            $order_type = ($data['order_type'] ?? false) ?: 'desc';

            // 搜索框搜索
            $where = function ($query) use ($data) {
                if ($this->loginUser->corp_id > 0) {
                    $query->where('a.corp_id', '=', $this->loginUser->corp_id);
                }

                if (!empty($data['time_range']) &&
                    is_array($data['time_range']) &&
                    count($data['time_range']) == 2
                ) {
                    $query->where('a.bill_date', '>=', $data['time_range'][0]);
                    $query->where('a.bill_date', '<=', $data['time_range'][1]);
                }
                if (isset($data['status']) && $data['status']) {
                    $query->where('a.status', $data['status']);
                }
                if (isset($data['station_id']) && $data['station_id']) {
                    $query->where('a.station_id', $data['station_id']);
                }
                if (isset($data['corp_id']) && $data['corp_id']) {
                    $query->where('a.corp_id', $data['corp_id']);
                }
                return $query;
            };

            $sum = $this->modelClass->alias('a')
                ->join('stations b', 'a.station_id = b.id')
                ->join('corp c', 'a.corp_id = c.id')
                ->field([
                    'sum(a.clearing_electricity_price) as sum_clearing_electricity_price',
                    'sum(a.clearing_ser_price) as sum_clearing_ser_price',
                ])
                ->where($where)
                ->find()
                ->toArray();

            $order_clearing = $this->modelClass->alias('a')
                ->join('stations b', 'a.station_id = b.id')
                ->join('corp c', 'a.corp_id = c.id')
                ->field([
                    'sum(a.clearing_electricity_price) as clearing_electricity_price',
                    'sum(a.clearing_ser_price) as clearing_ser_price',
                    'group_concat(DISTINCT a.status SEPARATOR ",") as status',
                    'group_concat(DISTINCT a.clearing_time SEPARATOR ",") as clearing_time',
                    'group_concat(DISTINCT a.bill_date SEPARATOR ",") as bill_date',
                    'group_concat(DISTINCT b.name SEPARATOR ",") as stations_name',
                    'c.name as corp_name',
                ])
                ->where($where)
                ->group('a.corp_id')
                ->withAttr('clearing_electricity_price', function ($value) {
                    return intval($value);
                })
                ->withAttr('clearing_ser_price', function ($value) {
                    return intval($value);
                })
                ->withAttr('clearing_time', function ($value) {
                    if (empty($value)) return [];
                    return explode(',', $value);
                })
                ->withAttr('bill_date', function ($value) {
                    if (empty($value)) return [];
                    return explode(',', $value);
                })
                ->withAttr('stations_name', function ($value) {
                    if (empty($value)) return [];
                    return explode(',', $value);
                })
                ->order($order_name, $order_type)
                ->paginate(['list_rows' => $pageSize, 'page' => $page])
                ->toArray();

            if ($sum) {
                $order_clearing['sum_clearing_electricity_price'] = (int)$sum['sum_clearing_electricity_price'];
                $order_clearing['sum_clearing_ser_price'] = (int)$sum['sum_clearing_ser_price'];
            }
            return $order_clearing;
        });
    }

    #[
        Apidoc\Title("获取运营商清分排序信息"),
        Apidoc\Author("lwj 2024.5.24 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Clearing/corp_clearing_sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function corp_clearing_sort_list_info(): Json
    {
        $res = [
            'order_name' => 'a.bill_date',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'a.corp_id', 'label' => '运营商'],
                ['value' => 'a.status', 'label' => '状态'],
                ['value' => 'a.bill_date', 'label' => '账单日期'],
                ['value' => 'a.clearing_time', 'label' => '结算时间'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("场站清分列表"),
        Apidoc\Author("lwj 2024.5.28 新增，lwj 2024.6.13 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Clearing/stations_clearing_list"),
        Apidoc\Param(name: "corp_id", type: "int", require: true, desc: "运营商id"),
        Apidoc\Param(name: "station_id", type: "int", require: true, desc: "场站id"),
        Apidoc\Param(name: "status", type: "int", require: true, desc: "状态，1待结算，2已结算"),
        Apidoc\Param(name: "time_range", type: "array", require: true, desc: "账单日期范围"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'clearing_electricity_price', 'type' => 'int', 'desc' => '应分充电费 - 保留4为小数'],
            ['name' => 'clearing_ser_price', 'type' => 'int', 'desc' => '应分服务费 - 保留4为小数'],
            ['name' => 'status', 'type' => 'int', 'desc' => '状态，1待结算，2已结算'],

            ['name' => 'clearing_time', 'type' => 'string', 'desc' => '结算时间'],
            ['name' => 'bill_date', 'type' => 'string', 'desc' => '账单日期'],

            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'stations_name', 'type' => 'string', 'desc' => '场站名称'],
        ]),
    ]
    public function stations_clearing_list(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
            $order_name = ($data['order_name'] ?? false) ?: 'a.id';
            $order_type = ($data['order_type'] ?? false) ?: 'desc';

            // 搜索框搜索
            $where = function ($query) use ($data) {

                if ($this->loginUser->corp_id > 0) {
                    $query->where('a.corp_id', '=', $this->loginUser->corp_id);
                }

                if (!empty($data['time_range']) &&
                    is_array($data['time_range']) &&
                    count($data['time_range']) == 2
                ) {
                    $query->where('a.bill_date', '>=', $data['time_range'][0]);
                    $query->where('a.bill_date', '<=', $data['time_range'][1]);
                }
                if (isset($data['status']) && $data['status']) {
                    $query->where('a.status', $data['status']);
                }
                if (isset($data['station_id']) && $data['station_id']) {
                    $query->where('a.station_id', $data['station_id']);
                }
                if (isset($data['corp_id']) && $data['corp_id']) {
                    $query->where('a.corp_id', $data['corp_id']);
                }
                return $query;
            };

            $sum = $this->modelClass->alias('a')
                ->join('stations b', 'a.station_id = b.id')
                ->join('corp c', 'a.corp_id = c.id')
                ->field([
                    'sum(a.clearing_electricity_price) as sum_clearing_electricity_price',
                    'sum(a.clearing_ser_price) as sum_clearing_ser_price',
                ])
                ->where($where)
                ->find()
                ->toArray();

            $order_clearing = $this->modelClass->alias('a')
                ->join('stations b', 'a.station_id = b.id')
                ->join('corp c', 'a.corp_id = c.id')
                ->field([
                    'sum(a.clearing_electricity_price) as clearing_electricity_price',
                    'sum(a.clearing_ser_price) as clearing_ser_price',
                    'group_concat(DISTINCT a.status SEPARATOR ",") as status',
                    'group_concat(DISTINCT a.clearing_time SEPARATOR ",") as clearing_time',
                    'group_concat(DISTINCT a.bill_date SEPARATOR ",") as bill_date',
                    'group_concat(DISTINCT c.name SEPARATOR ",") as corp_name',
                    'b.name as stations_name',
                ])
                ->where($where)
                ->group('a.station_id')
                ->withAttr('clearing_electricity_price', function ($value) {
                    return intval($value);
                })
                ->withAttr('clearing_ser_price', function ($value) {
                    return intval($value);
                })
                ->withAttr('clearing_time', function ($value) {
                    if (empty($value)) return [];
                    return explode(',', $value);
                })
                ->withAttr('bill_date', function ($value) {
                    if (empty($value)) return [];
                    return explode(',', $value);
                })
                ->withAttr('corp_name', function ($value) {
                    if (empty($value)) return [];
                    return explode(',', $value);
                })
                ->order($order_name, $order_type)
                ->paginate(['list_rows' => $pageSize, 'page' => $page])
                ->toArray();

            if ($sum) {
                $order_clearing['sum_clearing_electricity_price'] = (int)$sum['sum_clearing_electricity_price'];
                $order_clearing['sum_clearing_ser_price'] = (int)$sum['sum_clearing_ser_price'];
            }
            return $order_clearing;
        });
    }

    #[
        Apidoc\Title("获取场站清分排序信息"),
        Apidoc\Author("lwj 2024.5.28 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Clearing/stations_clearing_sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function stations_clearing_sort_list_info(): Json
    {
        $res = [
            'order_name' => 'a.bill_date',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'a.station_id', 'label' => '场站'],
                ['value' => 'a.status', 'label' => '状态'],
                ['value' => 'a.bill_date', 'label' => '账单日期'],
                ['value' => 'a.clearing_time', 'label' => '结算时间'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("获取订单清分状态列表"),
        Apidoc\Author("lwj 2024.5.17 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Clearing/get_order_clearing_status_list"),
    ]
    public function get_order_clearing_status_list(): Json
    {
        $res = OrderClearing::$status_list;
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("结算清分订单"),
        Apidoc\Author("lwj 2024.5.24 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Clearing/order_settle_accounts"),
        Apidoc\Param(name: "ids", type: "array", require: true, desc: "清分id数组"),
        Apidoc\Param(name: "clearing_time", type: "string", require: true, desc: "结算时间"),
    ]
    public function order_settle_accounts(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::order_clearing([
                'ids',
                'clearing_time',
            ]));

            if ($this->loginUser->corp_id > 0) {
                $where = [
                    ['corp_id', '=', $this->loginUser->corp_id],
                    ['id', 'in', $verify_data['ids']]
                ];
                $count = $this->modelClass->where($where)->count();
                if ($count !== count(array_unique($verify_data['ids']))) {
                    throw new RuntimeException('存在无效的清分ID', [], RuntimeException::CodeBusinessException);
                }
            }

            $id = $this->modelClass
                ->whereIn('id', $verify_data['ids'])
                ->where('status', 2)
                ->value('id');
            if ($id) return $this->res_error(msg: '结算失败，存在已结算的订单', is_open_exception_catch: true);

            $res = $this->modelClass
                ->whereIn('id', $verify_data['ids'])
                ->update([
                    'status' => 2,
                    'clearing_time' => $verify_data['clearing_time'],
                ]);
            if ($res) return $this->res_success(msg: '结算成功', is_open_exception_catch: true);
            return $this->res_error(msg: '结算失败', is_open_exception_catch: true);
        });
    }


}