<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\WorkOrderTemplateField;
use app\common\model\WorkOrderTemplateRelation;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use app\common\model\TariffGroup as TariffGroupModel;
use Respect\Validation\Validator as v;
use think\response\Json;
use app\common\model\WorkOrderTemplate as WorkOrderTemplateModel;

#[Apidoc\Title("工单管理/工单模板")]
class WorkOrderTemplate extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new TariffGroupModel();
    }

    #[
        Apidoc\Title("模板列表"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_template/get_list_data"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "每页显示"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => 'ID'],
            ['name' => 'name', 'type' => 'string', 'desc' => '模板名称(字符长度:3~20)'],
            ['name' => 'describe', 'type' => 'string', 'desc' => '模板描述(字符长度:0~100)'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
            ['name' => 'update_time', 'type' => 'string', 'desc' => '更新时间'],
        ]),
    ]
    public function get_list_data(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order_template([
                'page',
                'limit',
            ]));
            $verifyData['page'] = $verifyData['page'] ?? 1;
            $verifyData['limit'] = $verifyData['limit'] ?? 10;

            $WorkOrderTemplateModel = new WorkOrderTemplateModel();
            $field = ['id', 'name', 'describe', 'create_time', 'update_time'];
            return $WorkOrderTemplateModel->field($field)->paginate([
                'list_rows' => $verifyData['limit'],
                'page' => $verifyData['page']
            ])->toArray();
        });
    }

    #[
        Apidoc\Title("模板选项"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_template/get_options"),
        Apidoc\Returned(name: "options", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => 'ID'],
            ['name' => 'name', 'type' => 'string', 'desc' => '名称'],
        ]),
    ]
    public function get_options(): Json
    {
        return $this->openExceptionCatch(function () {
            $WorkOrderTemplateModel = new WorkOrderTemplateModel();
            $field = ['id', 'name'];
            return $WorkOrderTemplateModel->field($field)->select()->toArray();
        });
    }

    #[
        Apidoc\Title("模板详情"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_template/get_template"),
        Apidoc\Param(name: "id", type: "int", require: true, default: 1, desc: "ID"),
        Apidoc\Returned(name: "name", type: "string", require: true, default: "", desc: "模板名称(字符长度:3~20)"),
        Apidoc\Returned(name: "describe", type: "string", require: true, default: "", desc: "模板介绍(字符长度:0~100)"),
        Apidoc\Returned(name: "create_time", type: "string", require: true, default: "2024-04-29 14:10:15", desc: "创建时间"),
        Apidoc\Returned(name: "update_time", type: "string", require: true, default: "2024-04-29 14:10:15", desc: "更新时间"),
    ]
    public function get_template(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order_template([
                'id'
            ]));

            $WorkOrderFieldModel = new WorkOrderTemplateModel();

            $data = $WorkOrderFieldModel->getTemplate(
                $verifyData['id']
            );
            if (is_null($data)) {
                throw new RuntimeException('无效的模板ID', [], RuntimeException::CodeBusinessException);
            }

            return $data;
        });
    }

    #[
        Apidoc\Title("新增模板"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_template/add_template"),
        Apidoc\Param(name: "name", type: "string", require: true, default: "", desc: "模板名称(字符长度:3~20)"),
        Apidoc\Param(name: "describe", type: "string", require: true, default: "", desc: "模板描述(字符长度:0~100)"),
        Apidoc\Returned(name: "new_id", type: "int", require: true, default: 1, desc: "新增的模板ID"),
    ]
    public function add_template(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order_template([
                'name', 'describe'
            ]));

            $WorkOrderTemplateModel = new WorkOrderTemplateModel();

            // 验证字段名是否存在
            if ($WorkOrderTemplateModel->verifyNameExist($verifyData['name'])) {
                throw new RuntimeException('模板名称已存在', [], RuntimeException::CodeBusinessException);
            }


            $newId = $WorkOrderTemplateModel->addTemplate(
                $verifyData['name'],
                $verifyData['describe'],
            );


            return [
                'new_id' => $newId
            ];
        });
    }

    #[
        Apidoc\Title("更新模板"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_template/update_template"),
        Apidoc\Param(name: "id", type: "int", require: true, default: 1, desc: "模板ID"),
        Apidoc\Param(name: "name", type: "string", require: true, default: "", desc: "模板名称(字符长度:3~20)"),
        Apidoc\Param(name: "describe", type: "string", require: true, default: "", desc: "模板介绍(字符长度:0~100)"),
    ]
    public function update_template(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order_template([
                'id', 'name', 'describe'
            ]));

            $WorkOrderTemplateModel = new WorkOrderTemplateModel();
            if ($WorkOrderTemplateModel->isTemplateExists($verifyData['id']) === false) {
                throw new RuntimeException('无效的模板ID', [], RuntimeException::CodeBusinessException);
            }


            // 验证名是否存在
            if ($WorkOrderTemplateModel->verifyNameExist($verifyData['name'], $verifyData['id'])) {
                throw new RuntimeException('模板名称已存在', [], RuntimeException::CodeBusinessException);
            }

            $result = $WorkOrderTemplateModel->updateTemplate(
                $verifyData['id'],
                $verifyData['name'],
                $verifyData['describe'],
            );
            if ($result === false) {
                throw new RuntimeException('更新失败');
            }
            return [];
        });
    }


    #[
        Apidoc\Title("查询模板的所有字段"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_template/get_fields"),
        Apidoc\Param(name: "template_id", type: "int", require: true, default: 1, desc: "模板ID"),
        Apidoc\Returned(name: "fields", type: "array", desc: "数据", children: [
            ['name' => 'field_id', 'type' => 'int', 'desc' => '字段ID'],
            ['name' => 'sort', 'type' => 'int', 'desc' => '序号(取值范围:0~127)'],
            ['name' => 'name', 'type' => 'string', 'desc' => '字段名称(字符长度:3~20)'],
            ['name' => 'key', 'type' => 'string', 'desc' => '字段键值(字符长度:3~20)'],
            ['name' => 'type', 'type' => 'int', 'desc' => '字段类型 1:单行文本 2:多行文本 3:下拉框 4:多选框 5:数字 6:日期 7:图片'],
            ['name' => 'is_require', 'type' => 'int', 'desc' => '是否必填 1:必填 0:非必填'],
            ['name' => 'options', 'type' => 'string', 'desc' => '字段选项(格式:json字符串)'],
        ]),
    ]
    public function get_fields(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order_template([
                'template_id',
            ]));

            $WorkOrderTemplateField = new WorkOrderTemplateField();
            $fields = $WorkOrderTemplateField->getFields($verifyData['template_id']);

            return [
                'fields' => $fields
            ];
        });
    }


    #[
        Apidoc\Title("为模板添加字段"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_template/add_field"),
        Apidoc\Param(name: "template_id", type: "int", require: true, default: 1, desc: "模板ID"),
        Apidoc\Param(name: "field_id", type: "int", require: true, default: 1, desc: "字段ID"),
        Apidoc\Param(name: "sort", type: "int", require: true, default: 1, desc: "排序(取值范围:0~127)"),
    ]
    public function add_field(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order_template([
                'template_id', 'field_id', 'sort'
            ]));

            $WorkOrderTemplateField = new WorkOrderTemplateField();

            if ($WorkOrderTemplateField->isExist($verifyData['template_id'], $verifyData['field_id']) === true) {
                throw new RuntimeException('字段已存在', [], RuntimeException::CodeBusinessException);
            }


            $WorkOrderTemplateField->addField($verifyData['template_id'], $verifyData['field_id'], $verifyData['sort']);
        });
    }

    #[
        Apidoc\Title("为模板删除字段"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_template/delete_field"),
        Apidoc\Param(name: "template_id", type: "int", require: true, default: 1, desc: "模板ID"),
        Apidoc\Param(name: "field_id", type: "int", require: true, default: 1, desc: "字段ID"),
    ]
    public function delete_field(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order_template([
                'template_id', 'field_id'
            ]));

            $WorkOrderTemplateField = new WorkOrderTemplateField();
            $WorkOrderTemplateField->deleteField($verifyData['template_id'], $verifyData['field_id']);
        });
    }

    #[
        Apidoc\Title("指定模板的默认通知人"),
        Apidoc\Author("cbj 2024.04.30 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_template/set_notice_admin_user_id"),
        Apidoc\Param(name: "template_id", type: "int", require: true, default: 1, desc: "模板ID"),
        Apidoc\Param(name: "corp_id", type: "int", require: false, default: 1, desc: "运营商ID(运营商账号操作时不需要传递)"),
        Apidoc\Param(name: "station_id", type: "int", require: true, default: 1, desc: "场站ID"),
        Apidoc\Param(name: "notice_admin_user_id", type: "int", require: true, default: 1, desc: "默认的通知人(不想通知时，传递0即可)"),
    ]
    public function set_notice_admin_user_id(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            if ($this->loginUser->corp_id > 0) {
                // 验证数据
                $verifyData = v::input($data, VerifyData::work_order_template([
                    'template_id', 'station_id', 'notice_admin_user_id'
                ]));
                $verifyData['corp_id'] = $this->loginUser->corp_id;
            } else {
                // 验证数据
                $verifyData = v::input($data, VerifyData::work_order_template([
                    'template_id', 'corp_id', 'station_id', 'notice_admin_user_id'
                ]));
            }


            $WorkOrderTemplateRelation = new WorkOrderTemplateRelation();
            $result = $WorkOrderTemplateRelation->setDefaultNoticeAdminUserID(
                $verifyData['template_id'], $verifyData['corp_id'],
                $verifyData['station_id'], $verifyData['notice_admin_user_id']
            );
            if ($result === false) throw new RuntimeException('设置失败', [], RuntimeException::CodeDatabaseException);
        }, true);
    }
}