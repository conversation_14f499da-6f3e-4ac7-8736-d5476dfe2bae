<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpUnused */

namespace app\admin\controller;

use app\common\cache\redis\ImageVerificationCodeCache;
use app\common\lib\VerifyData;
use app\ms\Api;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\Container;
use think\exception\ValidateException;
use think\facade\Db;
use think\response\Json;
use lib\admin\GatewayAdmin;
use app\common\cache\redis\AdminUserLogin;

GatewayAdmin::$registerAddress = '127.0.0.1:9102';

#[Apidoc\Title("登录")]
class Login extends BaseController
{
    #[
        Apidoc\Title("获取图片验证码"),
        Apidoc\Author("lwj 2023.7.5 新增"),
        Apid<PERSON>\Method("GET"),
        Apidoc\Url("/admin/Login/get_img_code"),
        Apidoc\Returned("img_code_id", type: "string", require: 1, desc: "验证码id"),
        Apidoc\Returned("img", type: "string", require: 1, desc: "图片base64编码"),
        Apidoc\NotHeaders,
    ]
    public function get_img_code(): Json
    {
        header("content-type:text/html;charset=utf-8");
        $id = uniqid();
        $image_w = 100;
        $image_h = 25;
//        $number=range(1,9);
//        $character=range("Z","A");
//        $result=array_merge($number,$character);
        $result = range(1, 9);
        $string = "";
        $len = count($result);
        $new_number = [];
        for ($i = 0; $i < 4; $i++) {
            //在$result数组中随机取出6个字符
            $new_number[$i] = $result[rand(0, $len - 1)];
            //生成验证码字符串
            $string = $string . $new_number[$i];
        }
        $img_code = $string;
        //创建图片对象
        $check_image = imagecreatetruecolor($image_w, $image_h);
        $white = imagecolorallocate($check_image, 255, 255, 255);
        $black = imagecolorallocate($check_image, 0, 0, 0);
        imagefill($check_image, 0, 0, $white);//设置背景颜色为白色
//        $line_color=imagecolorallocate($check_image, 255, 0, 0);//设直线的颜色
//        imageline($check_image, 0, 10, 100,20, $line_color);//画线
        //加入100个干扰的黑点
        for ($i = 0; $i < 20; $i++) {
            imagesetpixel($check_image, rand(0, $image_w), rand(0, $image_h), $black);
        }
        //在背景图片中循环输出4位验证码
        for ($i = 0; $i < count($new_number); $i++) {
            //设定字符所在位置X坐标
            $x = mt_rand(1, 7) + $image_w * $i / 4;
            //设定字符所在位置Y坐标
            $y = mt_rand(1, (int)($image_h / 4));
            //随机设定字符颜色
            $color = imagecolorallocate($check_image, mt_rand(0, 200), mt_rand(0, 200), mt_rand(0, 200));
            //输入字符到图片中
            imagestring($check_image, 5, $x, $y, $new_number[$i], $color);
        }
//        header('content-type:image/png');
        imagepng($check_image);
        $content = ob_get_clean();
        imagedestroy($check_image);
        $base64 = 'data:image/png;base64,' . base64_encode($content);
        (new ImageVerificationCodeCache())->save($id, $img_code);

        return $this->res_success(['img_code_id' => $id, 'img' => $base64]);
    }

    #[
        Apidoc\Title("后台登录"),
        Apidoc\Author("lwj 2023.7.5 新增 lwj 2023.10.18 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Login/login"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "用户名", mock: "@name"),
        Apidoc\Param(name: "password", type: "string", require: true, desc: "密码"),
        Apidoc\Param(name: "img_code_id", type: "string", require: true, desc: "验证码id"),
        Apidoc\Param(name: "img_code", type: "string", require: true, desc: "图片验证码"),
        Apidoc\NotHeaders,
        Apidoc\Returned(name: "id", type: "int", desc: "管理员ID"),
        Apidoc\Returned(name: "pid", type: "int", desc: "管理员父ID(为0时表示顶级账号)"),
        Apidoc\Returned(name: "name", type: "string", desc: "管理员登录用户名"),
        Apidoc\Returned(name: "avatar", type: "string", desc: "管理员头像url"),
        Apidoc\Returned(name: "phone", type: "string", desc: "管理员手机号"),
        Apidoc\Returned(name: "email", type: "string", desc: "管理员邮箱"),
        Apidoc\Returned(name: "state", type: "int", desc: "状态，1正常，2禁用"),
        Apidoc\Returned(name: "perm_group_id", type: "int", desc: "管理员权限组id"),
        Apidoc\Returned(name: "corp_id", type: "int", desc: "绑定的运营商ID(为0时表示没有绑定运营商)"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "创建时间"),
        Apidoc\Returned(name: "last_time", type: "string", desc: "最近登陆的时间"),
        Apidoc\Returned(name: "token", type: "string", desc: "令牌"),
        Apidoc\Returned(name: "expire_time", type: "int", desc: "令牌有效期(秒级时间戳)"),
    ]
    public function login(): Json
    {
        try {
            if ($this->request->isPost()) {
                $data = $this->request->post();
                $verify_data = v::input($data, VerifyData::login([
                    'name',
                    'password',
                    'img_code',
                    'img_code_id'
                ]));

                $cache_code = (new ImageVerificationCodeCache())->get($verify_data['img_code_id']);
                if (!$cache_code) return $this->res_error([], '验证码已失效');
                if ($cache_code !== $verify_data['img_code']) return $this->res_error([], '验证码错误');


                $user = Db::name('admin_users')
                    ->where('name', $verify_data['name'])
                    ->where('state', 1)
                    ->field('id,pid,corp_id,name,password,avatar,perm_group_id,phone,email,state,create_time')
                    ->find();
                if (!$user) return $this->res_error([], '用户不存在或被禁用');
                if (!password_verify($verify_data['password'], $user['password'])) return $this->res_error([], '密码错误');
                $last_time = microtime();
                $token = sha1($last_time . $user['name'] . '-' . $user['password']);
                $user['last_time'] = $last_time;
                unset($user['password']);
                $AdminUserLogin = app(AdminUserLogin::class);
                $expireTime = $AdminUserLogin->saveUserLoginData($token, $user);
                if (is_null($expireTime)) {
                    return $this->res_success([], '登录失败');
                }

                // ========== 从权限管理服务获取信息 ========== //
                $res = Api::send("/auth/admin",'GET',[],['Authorization'=>$token])['data'];
                $user['station_ids'] = $res['station_ids'] ?? "";
                $AdminUserLogin->saveUserLoginData($token, $user);

                $user['expire_time'] = $expireTime;
                $user['token'] = $token;
                unset($user['last_time']);
                return $this->res_success($user, '登录成功');
            } else {
                return $this->res_error([], '错误请求');
            }
        } catch (ValidateException $e) {
            // 验证失败 输出错误信息
            return $this->res_error([], $e->getError());
        }
    }

    #[
        Apidoc\Title("自动登录"),
        Apidoc\Author("lwj 2023.7.27 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Login/auto_login"),
    ]
    public function auto_login(): Json
    {
        return $this->res_success([], '自动登录成功');
    }

    #[
        Apidoc\Title("后台登录websocket"),
        Apidoc\Author("lwj 2023.8.1 新增，lwj 2023.8.24 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Login/web_socket"),
        Apidoc\Param(name: "client_id", type: "string", require: true, desc: "客户端id"),
        Apidoc\Param(name: "ws_group", type: "string", require: true, desc: "组名"),
    ]
    public function web_socket(): Json
    {
        if ($this->request->isPost()) {
            $client_id = $this->request->post('client_id');
            $ws_group = $this->request->post('ws_group');
            if (!$ws_group) return $this->res_error([], '参数错误');

            $login_id = session_create_id();
            $session_data = [
                'ws_group' => $ws_group,
                'user_id' => $this->loginUser->id,
                'user_name' => $this->loginUser->name,
                'token' => $this->loginUser->token,
                'login_id' => $login_id,
            ];
            GatewayAdmin::bindUid($client_id, $this->loginUser->id);
            GatewayAdmin::joinGroup($client_id, '后台');
            GatewayAdmin::joinGroup($client_id, $this->loginUser->token);
            GatewayAdmin::joinGroup($client_id, $ws_group);
            GatewayAdmin::joinGroup($client_id, $ws_group . $this->loginUser->token);
            GatewayAdmin::joinGroup($client_id, $ws_group . $this->loginUser->id);
            GatewayAdmin::updateSession($client_id, $session_data);
            unset($session_data['token']);
            return $this->res_success($session_data, '登录成功');
        } else {
            return $this->res_error([], '错误请求');
        }

    }

    #[
        Apidoc\Title("登录测试-强行登录，仅测试使用，正式请删除"),
        Apidoc\Author("lwj 2023.9.27 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Login/login_test"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "用户名"),
        Apidoc\Param(name: "password", type: "string", require: true, desc: "接口文档登陆密码"),
        Apidoc\NotHeaders,
        Apidoc\Returned(name: "id", type: "int", desc: "管理员ID"),
        Apidoc\Returned(name: "pid", type: "int", desc: "管理员父ID(为0时表示顶级账号)"),
        Apidoc\Returned(name: "name", type: "string", desc: "管理员登录用户名"),
        Apidoc\Returned(name: "avatar", type: "string", desc: "管理员头像url"),
        Apidoc\Returned(name: "phone", type: "string", desc: "管理员手机号"),
        Apidoc\Returned(name: "email", type: "string", desc: "管理员邮箱"),
        Apidoc\Returned(name: "state", type: "int", desc: "状态，1正常，2禁用"),
        Apidoc\Returned(name: "perm_group_id", type: "int", desc: "管理员权限组id"),
        Apidoc\Returned(name: "corp_id", type: "int", desc: "绑定的运营商ID(为0时表示没有绑定运营商)"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "创建时间"),
        Apidoc\Returned(name: "last_time", type: "string", desc: "最近登陆的时间"),
        Apidoc\Returned(name: "token", type: "string", desc: "令牌"),
        Apidoc\Returned(name: "expire_time", type: "int", desc: "令牌有效期(秒级时间戳)"),
    ]
    public function login_test(): Json
    {
        if ($this->request->isPost()) {
            $password = $this->request->post('password');
            if ($password !== config('apidoc.auth.password')) {
                return Container::getInstance()->invokeClass(Json::class, ['404 Not Found', 404]);
            }

            $id = $this->request->post('id');
            $user = Db::name('admin_users')
                ->where('id', $id)
                ->field('id,pid,corp_id,name,password,avatar,perm_group_id,phone,email,state,create_time')
                ->find();
            if (!$user) return $this->res_error([], '用户不存在');
            $last_time = microtime();
            $token = sha1($last_time . $user['name'] . '-' . $user['password']);
            $user['last_time'] = $last_time;
            unset($user['password']);
            $AdminUserLogin = app(AdminUserLogin::class);
            $expireTime = $AdminUserLogin->saveUserLoginData($token, $user);
            if (is_null($expireTime)) {
                return $this->res_error([], '登录失败');
            }
            $user['expire_time'] = $expireTime;
            $user['token'] = $token;
            unset($user['last_time']);
            return $this->res_success($user, '登录成功');
        } else {
            return Container::getInstance()->invokeClass(Json::class, ['404 Not Found', 404]);
        }
    }


}