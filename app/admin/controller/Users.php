<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;

//use app\common\controller\ApiBase;
//use app\common\lib\exception\RuntimeException;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\Order as OrderModel;
use app\common\traits\Curd;
use app\ms\Api;
use hg\apidoc\annotation as Apidoc;
use app\common\model\Users as UsersModel;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\db\Query;
use think\facade\Db;
use think\response\Json;
use Throwable;

#[Apidoc\Title("用户中心/用户列表")]
class Users extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new UsersModel();
    }

    #[
        Apidoc\Title("用户列表"),
        Apidoc\Author("lwj 2024.6.14 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Users/<USER>"),
        Apidoc\Param(name: "id", type: "int", desc: "用户id"),
        Apidoc\Param(name: "nickname", type: "string", desc: "用户昵称"),
        Apidoc\Param(name: "phone", type: "string", desc: "用户手机号"),

        Apidoc\Param(name: "auth_status", type: "array|int", desc: "授权状态： 1 未授权 2 已授权"),
        Apidoc\Param(name: "freeze", type: "array|int", desc: "账号是否冻结，1-未冻结，2-冻结"),
        Apidoc\Param(name: "time_range", type: "array", desc: "日期范围"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '用户id'],
            ['name' => 'nickname', 'type' => 'string', 'desc' => '用户昵称'],
            ['name' => 'phone', 'type' => 'string', 'desc' => '用户手机号'],

            ['name' => 'freeze_balance', 'type' => 'int', 'desc' => '冻结余额，单位分'],
            ['name' => 'balance', 'type' => 'int', 'desc' => '可用余额，单位分'],

            ['name' => 'auth_status', 'type' => 'int', 'desc' => '授权状态： 1 未授权 2 已授权'],
            ['name' => 'freeze', 'type' => 'int', 'desc' => '账号是否冻结，1-未冻结，2-冻结'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],

            ['name' => 'avatar', 'type' => 'string', 'desc' => '用户头像'],
            ['name' => 'credit_limit', 'type' => 'int', 'desc' => '信用额度(单位:分)'],
        ]),
    ]
    public function users_list(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }
        $data = $this->request->post();
        $page = ($data['page'] ?? false) ?: 1;
        $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
        $order_name = ($data['order_name'] ?? false) ?: 'create_time';
        $order_type = ($data['order_type'] ?? false) ?: 'desc';

        // 搜索框搜索
        $where = function ($query) use ($data) {
            if (isset($data['id']) && $data['id']) {
                $query->where('id', $data['id']);
            }
            if (isset($data['nickname']) && $data['nickname']) {
                $query->where('nickname', 'like', '%' . $data['nickname'] . '%');
            }
            if (isset($data['phone']) && $data['phone']) {
                $query->where('phone', 'like', '%' . $data['phone'] . '%');
            }

            if (isset($data['auth_status']) && $data['auth_status']) {
                if (is_array($data['auth_status'])) {
                    $query->where('auth_status', 'in', $data['auth_status']);
                } else {
                    $query->where('auth_status', $data['auth_status']);
                }
            }
            if (isset($data['freeze']) && $data['freeze']) {
                if (is_array($data['freeze'])) {
                    $query->where('freeze', 'in', $data['freeze']);
                } else {
                    $query->where('freeze', $data['freeze']);
                }
            }

            if (!empty($data['time_range']) &&
                is_array($data['time_range']) &&
                count($data['time_range']) == 2
            ) {
                $query->where('create_time', '>=', $data['time_range'][0]);
                $query->where('create_time', '<=', $data['time_range'][1]);
            }
            return $query;
        };

        $list = $this->modelClass
            ->field([
                'id',
                'nickname',
                'phone',
                'freeze_balance',
                'balance',
                'auth_status',
                'freeze',
                'create_time',
                'avatar',
                'amount_charged',
                'credit_limit',
            ])
            ->where($where)
            ->order($order_name, $order_type)
            ->paginate(['list_rows' => $pageSize, 'page' => $page]);

        return $this->res_success($list);
    }

    #[
        Apidoc\Title("获取用户列表排序信息"),
        Apidoc\Author("lwj 2024.6.14 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Users/<USER>"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        $res = [
            'order_name' => 'create_time',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'create_time', 'label' => '创建时间'],
                ['value' => 'id', 'label' => '用户id'],
                ['value' => 'phone', 'label' => '用户手机号'],
                ['value' => 'freeze_balance', 'label' => '冻结余额'],
                ['value' => 'balance', 'label' => '可用余额'],
                ['value' => 'auth_status', 'label' => '授权状态'],
                ['value' => 'freeze', 'label' => '账号是否冻结'],
                ['value' => 'amount_charged', 'label' => '正在充电使用金额'],
                ['value' => 'credit_limit', 'label' => '信用额度'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("用户详情"),
        Apidoc\Author("lwj 2024.6.14 新增,lwj 2024.6.14 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Users/<USER>"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "用户id"),

        Apidoc\Returned(name: "id", type: "string", desc: "用户id"),
        Apidoc\Returned(name: "nickname", type: "string", desc: "昵称"),
        Apidoc\Returned(name: "phone", type: "string", desc: "手机号"),
        Apidoc\Returned(name: "freeze_balance", type: "int", desc: "冻结余额，单位分"),
        Apidoc\Returned(name: "balance", type: "int", desc: "可用余额，单位分"),
        Apidoc\Returned(name: "auth_status", type: "int", desc: "授权状态：1未授权，2已授权，3授权失败"),
        Apidoc\Returned(name: "freeze", type: "int", desc: "账号是否冻结：1冻结，2未冻结"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "创建时间"),
        Apidoc\Returned(name: "update_time", type: "string", desc: "更新时间"),
        Apidoc\Returned(name: "gender", type: "int", desc: "性别：1男，2女"),
        Apidoc\Returned(name: "open_id", type: "string", desc: "open_id"),
        Apidoc\Returned(name: "avatar", type: "string", desc: "头像"),
        Apidoc\Returned(name: "country", type: "string", desc: "国家"),
        Apidoc\Returned(name: "province", type: "string", desc: "省份"),
        Apidoc\Returned(name: "city", type: "string", desc: "城市"),
        Apidoc\Returned(name: "district", type: "string", desc: "区县"),
        Apidoc\Returned(name: "address", type: "string", desc: "详细地址"),
        Apidoc\Returned(name: "amount_charged", type: "int", desc: "正在充电使用金额，单位分"),
        Apidoc\Returned(name: "credit_limit", type: "int", desc: "信用额度，单位分"),

        Apidoc\Returned(name: "pay_order_sum_price", type: "int", desc: "累计充值金额，单位分"),
        Apidoc\Returned(name: "pay_order_count", type: "int", desc: "累计充值笔数"),

        Apidoc\Returned(name: "charging_order_sum_consumption_amount", type: "int", desc: "累计充电消费金额(包含服务费与充电费) - 保留4为小数"),
        Apidoc\Returned(name: "charging_order_count", type: "int", desc: "累计订单数、次数"),
        Apidoc\Returned(name: "charging_order_charge_duration", type: "int", desc: "累计充电时长，(单位:秒)"),
    ]
    public function get_info(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::users([
                'id',
            ]));

            $result = $this->modelClass
                ->field([
                    'id',
                    'nickname',
                    'phone',
                    'freeze_balance',
                    'balance',
                    'auth_status',
                    'freeze',
                    'create_time',
                    'update_time',
                    'gender',
                    'open_id',
                    'avatar',
                    'country',
                    'province',
                    'city',
                    'district',
                    'address',
                    'amount_charged',
                    'credit_limit',
                ])
                ->find($verify_data['id']);
            if ($result) {
                $user_pay_order = Db::name('pay_order')
                    ->where('user_id', $verify_data['id'])
                    ->where('state', 3)
                    ->group('user_id')
                    ->field([
                        'sum(price) as pay_order_sum_price',
                        'count(*) as pay_order_count',
                    ])
                    ->find();

                if ($user_pay_order) {
                    $result['pay_order_sum_price'] = (int)$user_pay_order['pay_order_sum_price'];
                    $result['pay_order_count'] = (int)$user_pay_order['pay_order_count'];
                } else {
                    $result['pay_order_sum_price'] = 0;
                    $result['pay_order_count'] = 0;
                }

                $user_charging_order = Db::name('order')
                    ->where('user_id', $verify_data['id'])
                    ->whereIn('status', [5, 6, 7])
                    ->group('user_id')
                    ->field([
                        'sum(consumption_amount) as charging_order_sum_consumption_amount',
                        'count(*) as charging_order_count',
                        'sum(charge_duration) as charging_order_charge_duration',
                    ])
                    ->find();

                if ($user_charging_order) {
                    $result['charging_order_sum_consumption_amount'] = (int)$user_charging_order['charging_order_sum_consumption_amount'];
                    $result['charging_order_count'] = (int)$user_charging_order['charging_order_count'];
                    $result['charging_order_charge_duration'] = (int)$user_charging_order['charging_order_charge_duration'];
                } else {
                    $result['charging_order_sum_consumption_amount'] = 0;
                    $result['charging_order_count'] = 0;
                    $result['charging_order_charge_duration'] = 0;
                }

                return $this->res_success($result);
            }
            return $this->res_error([], '用户不存在');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("用户统计"),
        Apidoc\Author("lwj 2024.6.18 新增,lwj 2024.7.1 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Users/<USER>"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "用户id"),

        Apidoc\Returned(name: "user_recharge_sum_price", type: "int", desc: "充值总金额，单位分"),
        Apidoc\Returned(name: "user_sum_consumption_amount", type: "int", desc: "消费总金额，单位分"),
        Apidoc\Returned(name: "user_sum_refund_price_wait", type: "int", desc: "待退款总金额，单位分"),
        Apidoc\Returned(name: "user_sum_refund_price", type: "int", desc: "已退款总金额，单位分"),
        Apidoc\Returned(name: "user_balance", type: "int", desc: "用户目前余额，单位分"),
        Apidoc\Returned(name: "sum", type: "int", desc: "汇总：充值总金额 - 消费总金额 - 待退款总金额 - 已退款总金额 - 用户目前余额 = 汇总，单位分"),
    ]
    public function user_statistics(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::users([
                'id',
            ]));
            $user_id = $verify_data['id'];

            $user = $this->modelClass
                ->field([
                    'id',
                    'balance',
                ])
                ->find($user_id);
            if (!$user) return $this->res_error([], '用户不存在');

            $user_balance = (int)$user->balance;

            $user_recharge_sum_price = (int)Db::name('pay_order')
                ->where('user_id', $user_id)
                ->where('state', 3)
                ->sum('price');

            $user_sum_consumption_amount = (int)Db::name('order')
                ->where('user_id', $user_id)
                ->whereIn('status', [5, 6, 7])
                ->sum('pay_money');

            $user_sum_refund_price_wait = (int)Db::name('refund_order')
                ->where('user_id', $user_id)
                ->whereIn('state', [1, 5])
                ->sum('refund_price');

            $user_sum_refund_price = (int)Db::name('refund_order')
                ->where('user_id', $user_id)
                ->where('state', 20)
                ->sum('refund_price');

            $sum = $user_recharge_sum_price
                - $user_sum_consumption_amount
                - $user_sum_refund_price_wait
                - $user_sum_refund_price
                - $user_balance;

            // 获取用户最近一单信息,连接station表获取场站名称
            $last_order = Db::name('order')
                ->leftJoin('stations', 'order.station_id = stations.id')
                ->field([
                    'order.id',
                    'order.create_time',
                    'stations.name as station_name',
                ])
                ->where('user_id', $user_id)
                ->order('create_time', 'desc')
                ->find();

            return $this->res_success([
                'user_recharge_sum_price' => $user_recharge_sum_price,
                'user_sum_consumption_amount' => $user_sum_consumption_amount,
                'user_sum_refund_price_wait' => $user_sum_refund_price_wait,
                'user_sum_refund_price' => $user_sum_refund_price,
                'user_balance' => $user_balance,
                'last_order' => $last_order,
                'sum' => $sum,
            ]);
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }


    #[
        Apidoc\Title("根据充电订单ID查询用户信息"),
        Apidoc\Author("cbj 2024.09.12 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/users/get_user_info_by_order_id"),
        Apidoc\Param(name: "order_id", type: "int", require: true, desc: "充电订单ID"),
        Apidoc\Returned(name: "phone", type: "int", require: true, desc: "手机号"),
        Apidoc\Returned(name: "balance", type: "int", require: true, desc: "用户余额(单位:分)"),
        Apidoc\Returned(name: "reason_for_stop_text", type: "string", require: true, desc: "这个订单的停止原因文本"),
    ]
    public function get_user_info_by_order_id(): ?Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            // 验证数据
            $verify_data = v::input($data, VerifyData::users([
                'order_id',
            ]));

            $where = [
                ['o.id', '=', $verify_data['order_id']]
            ];
            if ($this->loginUser->corp_id > 0) {
                $where[] = ['o.corp_id', '=', $this->loginUser->corp_id];
            }

            /**
             * @var Query $model
             */
            $model = (new OrderModel());
            $data = $model->alias('o')
                ->leftJoin('users u', 'u.id = o.user_id')
                ->field(['u.phone', 'u.balance'])
                ->where($where)
                ->find();
            if (empty($data)) {
                throw new RuntimeException("无效订单ID", [], RuntimeException::CodeBusinessException);
            }

            return $data->toArray();
        });
    }

    #[
        Apidoc\Title("查询用户等级列表"),
        Apidoc\Author("cccq 2025.3.15 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/users/level_list"),
        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页数量"),
        Apidoc\Param(name: "name", type: "string", require: false, desc: "等级名称"),
    ]
    public function level_list(): ?Json
    {
        $input = $this->request->get();
        $response = Api::send("/auth/level/list",'GET',[
            'page' => $input['page'] ?? '',
            'limit' => $input['limit'] ?? '',
            'name' => $input['name'] ?? '',
        ]);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }

    #[
        Apidoc\Title("查询用户等级列表"),
        Apidoc\Author("cccq 2025.3.15 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/users/level_options"),
        Apidoc\Param(name: "station_id", type: "int", require: true, desc: "场站ID"),
    ]
    public function level_options(): ?Json
    {
        $input = $this->request->get();
        $response = Api::send("/auth/level/options",'GET',[
           'station_id' => $input['station_id'] ?? '',
        ]);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }

    #[
        Apidoc\Title("查询用户等级详情"),
        Apidoc\Author("cccq 2025.3.15 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/users/level_info"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "等级ID"),
    ]
    public function level_info(): ?Json
    {
        $response = Api::send("/auth/level/info",'GET',[
            'id' => $this->request->get('id') ?? '',
        ]);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }

    #[
        Apidoc\Title("添加用户等级"),
        Apidoc\Author("cccq 2025.3.15 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/users/level_add"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "等级名称"),
        Apidoc\Param(name: "discount", type: "int", require: true, desc: "折扣率"),
        Apidoc\Param(name: "station_id", type: "int", require: true, desc: "站点ID"),
        Apidoc\Param(name: "is_default", type: "int", require: true, desc: "是否默认,0:否,1:是"),
    ]
    public function level_add(): ?Json
    {
        $input = $this->request->post();
        $response = Api::send("/auth/level/info",'POST',[
            'name' => $input['name'] ?? '',
            'discount' => $input['discount'] ?? '',
            'station_id' => $input['station_id'] ?? '',
            'is_default' => $input['is_default'] ?? '',
        ]);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }

    #[
        Apidoc\Title("更新用户等级"),
        Apidoc\Author("cccq 2025.3.15 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/users/level_update"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "等级ID"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "等级名称"),
        Apidoc\Param(name: "discount", type: "int", require: true, desc: "折扣率"),
        Apidoc\Param(name: "is_default", type: "int", require: true, desc: "是否默认,0:否,1:是"),
    ]
    public function level_update(): ?Json
    {
        $input = $this->request->post();
        $response = Api::send("/auth/level/info",'PUT',[
            'id' => $input['id'] ?? '',
            'name' => $input['name'] ?? '',
            'discount' => $input['discount'] ?? '',
            'is_default' => $input['is_default'] ?? '',
        ]);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }

    #[
        Apidoc\Title("删除用户等级"),
        Apidoc\Author("cccq 2025.3.15 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/users/level_delete"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "等级ID"),
    ]
    public function level_delete(): ?Json
    {
        $response = Api::send("/auth/level/info",'DELETE',[
            'id' => $this->request->post('id') ?? '',
        ]);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }
}