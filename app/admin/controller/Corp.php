<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\model\AdminUsers;
use app\common\model\AdminUsers as AdminUsersModel;
use app\common\model\CorpAdminGroup;
use app\common\traits\Curd;
use app\ms\Api;
use hg\apidoc\annotation as Apidoc;
use app\common\model\Corp as CorpModel;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\db\Query;
use think\response\Json;
use Throwable;

#[Apidoc\Title("会员中心/运营商管理")]
class Corp extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new CorpModel();
    }


    #[
        Apidoc\Title("运营商列表"),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.3 修改, cbj 2024.04.26 更新"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Corp/corp_list"),
        Apidoc\Param(name: "id", type: "int", desc: "运营商编号"),
        Apidoc\Param(name: "name", type: "string", desc: "运营商名称"),
        Apidoc\Param(name: "status", type: "int", desc: "运营商状态，1-关闭，2-开启"),
        Apidoc\Param(name: "audit_status", type: "int", desc: "审核状态，1-未审核，2-审核通过"),

        Apidoc\Param(name: "page", type: "int", desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '运营商编号'],
            ['name' => 'name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'icon_url', 'type' => 'string', 'desc' => '运营商图标url'],
            ['name' => 'province', 'type' => 'string', 'desc' => '省'],
            ['name' => 'city', 'type' => 'string', 'desc' => '市'],
            ['name' => 'district', 'type' => 'string', 'desc' => '区'],
            ['name' => 'parent_name', 'type' => 'string', 'desc' => '上级运营商'],
            ['name' => 'audit_status', 'type' => 'int', 'desc' => '审核状态，1-未审核，2-审核通过'],
            ['name' => 'contact', 'type' => 'string', 'desc' => '联系人'],
            ['name' => 'phone', 'type' => 'string', 'desc' => '联系电话'],
            ['name' => 'opt_id', 'type' => 'int', 'desc' => '创建人id'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
            ['name' => 'status', 'type' => 'int', 'desc' => '运营商状态，1-关闭，2-开启']
        ]),
    ]
    public function corp_list(): Json
    {
        $data = $this->request->post();
        $page = ($data['page'] ?? false) ?: 1;
        $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
        $order_name = ($data['order_name'] ?? false) ?: 'a.id';
        $order_type = ($data['order_type'] ?? false) ?: 'desc';
        $data['id'] = (int)$data['id'];

        // 如果是运营商管理员则只能查询自己的数据
        if ($this->loginUser->corp_id > 0) {
            if (!empty($data['id']) && $this->loginUser->corp_id !== $data['id']) {
                $data['id'] = -1;
            } else {
                $data['id'] = $this->loginUser->corp_id;
            }
        }

        $where = function (Query $query) use ($data) {
            if (isset($data['id']) && $data['id']) {
                $query->where('a.id', $data['id']);
            }
            if (isset($data['name']) && $data['name']) {
                $query->where('a.name', 'like', '%' . $data['name'] . '%');
            }
            if (isset($data['audit_status']) && $data['audit_status']) {
                $query->where('a.audit_status', $data['audit_status']);
            }
            if (isset($data['status']) && $data['status']) {
                $query->where('a.status', $data['status']);
            }
            return $query;
        };

        $list = $this->modelClass->alias('a')
            ->leftJoin('corp b', 'a.parent_id = b.id')
            ->field('a.id,a.name,a.icon_url,a.province,a.city,a.district,a.parent_id,a.audit_status,a.contact,a.phone,a.opt_id,a.create_time,a.status,b.name as parent_name')
            ->where($where)
            ->order($order_name, $order_type)
            ->paginate(['list_rows' => $pageSize, 'page' => $page]);

        return $this->res_success($list);
    }

    #[
        Apidoc\Title("获取运营商排序信息"),
        Apidoc\Author("lwj 2023.8.3 新增, cbj 2024.04.26 更新"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Corp/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        $res = [
            'order_name' => 'a.id',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'a.id', 'label' => '运营商编号'],
                ['value' => 'a.name', 'label' => '运营商名称'],
//                ['value' => 'a.audit_status', 'label' => '审核状态'],
//                ['value' => 'a.status', 'label' => '运营商状态'],
                ['value' => 'a.create_time', 'label' => '创建时间'],
                ['value' => 'a.parent_id', 'label' => '上级运营商'],
                ['value' => 'a.contact', 'label' => '联系人'],
            ],
        ];
        return $this->res_success($res);
    }


    #[
        Apidoc\Title("运营商详情"),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.3 修改, cbj 2024.04.26 更新"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Corp/get_info"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "运营商id"),
        Apidoc\Returned(name: "id", type: "int", desc: "运营商编号"),
        Apidoc\Returned(name: "name", type: "string", desc: "运营商名称"),
        Apidoc\Returned(name: "icon_url", type: "string", desc: "运营商图标url"),
        Apidoc\Returned(name: "city_id", type: "array", desc: "省市区id"),
        Apidoc\Returned(name: "parent_id", type: "int", desc: "上级运营商编号"),
        Apidoc\Returned(name: "contact", type: "string", desc: "联系人"),
        Apidoc\Returned(name: "phone", type: "string", desc: "联系电话"),
        Apidoc\Returned(name: "city_id", type: "array", desc: "省市区id(格式:[省份ID, 城市ID, 区域ID])"),
        Apidoc\Returned(name: "province", type: "string", desc: "省份名称"),
        Apidoc\Returned(name: "city", type: "string", desc: "城市名称"),
        Apidoc\Returned(name: "district", type: "string", desc: "区域名称"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "创建时间"),
        Apidoc\Returned(name: "update_time", type: "string", desc: "更新时间"),
    ]
    public function get_info(): Json
    {
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::corp([
                'id',
            ]));

            if ($this->loginUser->corp_id > 0) {
                if ($verify_data['id'] !== $this->loginUser->corp_id) {
                    return $this->res_error([], '运营商不存在');
                }
            }

            $result = $this->modelClass
                ->field('id,name,icon_url,city_id,contact,phone,city_id,province,city,district,create_time')
                ->find($verify_data['id']);
            if ($result) return $this->res_success($result);
            return $this->res_error([], '运营商不存在');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }


    #[
        Apidoc\Title("添加运营商"),
        Apidoc\Desc('只允许超级管理员操作(corp == 0 and pid == 0)'),
        Apidoc\Author("cbj 2024.04.24 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/corp/add"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "运营商名称"),
        Apidoc\Param(name: "city_id", type: "array", require: true, desc: "地址ID"),
        Apidoc\Param(name: "contact", type: "string", require: true, desc: "联系人"),
        Apidoc\Param(name: "phone", type: "string", require: true, desc: "联系电话"),
        Apidoc\Param(name: "admin_username", type: "string", require: true, desc: "管理员用户名"),
        Apidoc\Param(name: "admin_password", type: "string", require: true, desc: "管理员密码"),
        Apidoc\Returned(name: "corp_id", type: "int", require: true, desc: "运营商ID")
    ]
    public function add(): Json
    {
        return $this->openExceptionCatch(function () {
            if ($this->loginUser->corp_id > 0 || $this->loginUser->pid > 0) {
                throw new RuntimeException('只允许超级管理员操作', [], RuntimeException::CodeBusinessException);
            }

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::service_corp([
                'name', 'city_id', 'contact', 'phone',
                'admin_username', 'admin_password'
            ]));

            LogCollector::collectorRunLog('$verify_data-city_id' . json_encode($verify_data['city_id']));
            $city = get_city_for_city_id($verify_data['city_id']);
            LogCollector::collectorRunLog('$city' . json_encode($city));
            if (!$city) throw new RuntimeException("获取省市区失败", [], RuntimeException::CodeBusinessException);
            $verify_data = array_merge($verify_data, $city);

            $AdminUsersModel = new AdminUsersModel();
            if ($AdminUsersModel->isUsernameAlreadyExists($verify_data['admin_username']) === true) {
                throw new RuntimeException('用户名已经被使用', [], RuntimeException::CodeBusinessException);
            }

            $CorpModel = new CorpModel();
            $corp_id = $CorpModel->addCorpGetId(
                $verify_data['name'], '', $verify_data['province'],
                $verify_data['city'], $verify_data['district'], $verify_data['contact'], $verify_data['phone'],
                $verify_data['city_id']
            );
            (new CorpAdminGroup())->bindCorpAdminGroupIds($corp_id, [6]);

            if (empty($corp_id)) throw new RuntimeException('添加运营商失败', [], RuntimeException::CodeBusinessException);

            // 添加关联的管理员
            $admin_user = $AdminUsersModel->createAdminUser(
                $verify_data['admin_username'],
                '',
                '',
                AdminUsers::AdminGroupCorpAdministrator,
                $verify_data['admin_password'],
                $corp_id
            );

            // ========== 同步管理员到微服务 ==========
            $admin_user['service_id'] = 1;
            $admin_user['phone'] = $verify_data['phone'];
            $admin_user['email'] = $admin_user['name'].'@resmartcharge.com';
            Api::send("/auth/admin", "POST", $admin_user,[],'id,service_id');

            // 生成默认的费率组
            // $tariffGroupModel = app(\app\common\model\TariffGroup::class);
            // $tariffGroupModel->insertGetId([
            //     'name' => '默认费率组',
            //     'sharp_fee' => 130000,
            //     'peak_fee' => 130000,
            //     'flat_fee' => 130000,
            //     'valley_fee' => 130000,
            //     'sharp_ser_fee' => 20000,
            //     'peak_ser_fee' => 20000,
            //     'flat_ser_fee' => 20000,
            //     'valley_ser_fee' => 20000,
            //     'loss_rate' => 0,
            //     'period_codes' => '030303030303030303030303030302020202020200000000000000000000000000000000000101010101010101010101',
            //     'opt_id' => 0,
            //     'create_time' => date('Y-m-d H:i:s'),
            //     'period_json_sum' => '[{"fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "00:00～07:00", "sum_fee": 150000, "end_time": "07:00", "start_time": "00:00"}, {"fee": {"fee": 130000, "ser_fee": 20000}, "rate": "02", "period": "07:00～10:00", "sum_fee": 150000, "end_time": "10:00", "start_time": "07:00"}, {"fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "10:00～18:30", "sum_fee": 150000, "end_time": "18:30", "start_time": "10:00"}, {"fee": {"fee": 130000, "ser_fee": 20000}, "rate": "01", "period": "18:30～24:00", "sum_fee": 150000, "end_time": "24:00", "start_time": "18:30"}]',
            //     'period_json' => '[{"id": 1, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "00:00～00:30", "sum_fee": 150000, "end_time": "00:30", "start_time": "00:00"}, {"id": 2, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "00:30～01:00", "sum_fee": 150000, "end_time": "01:00", "start_time": "00:30"}, {"id": 3, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "01:00～01:30", "sum_fee": 150000, "end_time": "01:30", "start_time": "01:00"}, {"id": 4, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "01:30～02:00", "sum_fee": 150000, "end_time": "02:00", "start_time": "01:30"}, {"id": 5, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "02:00～02:30", "sum_fee": 150000, "end_time": "02:30", "start_time": "02:00"}, {"id": 6, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "02:30～03:00", "sum_fee": 150000, "end_time": "03:00", "start_time": "02:30"}, {"id": 7, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "03:00～03:30", "sum_fee": 150000, "end_time": "03:30", "start_time": "03:00"}, {"id": 8, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "03:30～04:00", "sum_fee": 150000, "end_time": "04:00", "start_time": "03:30"}, {"id": 9, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "04:00～04:30", "sum_fee": 150000, "end_time": "04:30", "start_time": "04:00"}, {"id": 10, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "04:30～05:00", "sum_fee": 150000, "end_time": "05:00", "start_time": "04:30"}, {"id": 11, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "05:00～05:30", "sum_fee": 150000, "end_time": "05:30", "start_time": "05:00"}, {"id": 12, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "05:30～06:00", "sum_fee": 150000, "end_time": "06:00", "start_time": "05:30"}, {"id": 13, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "06:00～06:30", "sum_fee": 150000, "end_time": "06:30", "start_time": "06:00"}, {"id": 14, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "03", "period": "06:30～07:00", "sum_fee": 150000, "end_time": "07:00", "start_time": "06:30"}, {"id": 15, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "02", "period": "07:00～07:30", "sum_fee": 150000, "end_time": "07:30", "start_time": "07:00"}, {"id": 16, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "02", "period": "07:30～08:00", "sum_fee": 150000, "end_time": "08:00", "start_time": "07:30"}, {"id": 17, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "02", "period": "08:00～08:30", "sum_fee": 150000, "end_time": "08:30", "start_time": "08:00"}, {"id": 18, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "02", "period": "08:30～09:00", "sum_fee": 150000, "end_time": "09:00", "start_time": "08:30"}, {"id": 19, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "02", "period": "09:00～09:30", "sum_fee": 150000, "end_time": "09:30", "start_time": "09:00"}, {"id": 20, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "02", "period": "09:30～10:00", "sum_fee": 150000, "end_time": "10:00", "start_time": "09:30"}, {"id": 21, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "10:00～10:30", "sum_fee": 150000, "end_time": "10:30", "start_time": "10:00"}, {"id": 22, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "10:30～11:00", "sum_fee": 150000, "end_time": "11:00", "start_time": "10:30"}, {"id": 23, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "11:00～11:30", "sum_fee": 150000, "end_time": "11:30", "start_time": "11:00"}, {"id": 24, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "11:30～12:00", "sum_fee": 150000, "end_time": "12:00", "start_time": "11:30"}, {"id": 25, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "12:00～12:30", "sum_fee": 150000, "end_time": "12:30", "start_time": "12:00"}, {"id": 26, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "12:30～13:00", "sum_fee": 150000, "end_time": "13:00", "start_time": "12:30"}, {"id": 27, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "13:00～13:30", "sum_fee": 150000, "end_time": "13:30", "start_time": "13:00"}, {"id": 28, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "13:30～14:00", "sum_fee": 150000, "end_time": "14:00", "start_time": "13:30"}, {"id": 29, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "14:00～14:30", "sum_fee": 150000, "end_time": "14:30", "start_time": "14:00"}, {"id": 30, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "14:30～15:00", "sum_fee": 150000, "end_time": "15:00", "start_time": "14:30"}, {"id": 31, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "15:00～15:30", "sum_fee": 150000, "end_time": "15:30", "start_time": "15:00"}, {"id": 32, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "15:30～16:00", "sum_fee": 150000, "end_time": "16:00", "start_time": "15:30"}, {"id": 33, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "16:00～16:30", "sum_fee": 150000, "end_time": "16:30", "start_time": "16:00"}, {"id": 34, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "16:30～17:00", "sum_fee": 150000, "end_time": "17:00", "start_time": "16:30"}, {"id": 35, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "17:00～17:30", "sum_fee": 150000, "end_time": "17:30", "start_time": "17:00"}, {"id": 36, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "17:30～18:00", "sum_fee": 150000, "end_time": "18:00", "start_time": "17:30"}, {"id": 37, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "00", "period": "18:00～18:30", "sum_fee": 150000, "end_time": "18:30", "start_time": "18:00"}, {"id": 38, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "01", "period": "18:30～19:00", "sum_fee": 150000, "end_time": "19:00", "start_time": "18:30"}, {"id": 39, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "01", "period": "19:00～19:30", "sum_fee": 150000, "end_time": "19:30", "start_time": "19:00"}, {"id": 40, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "01", "period": "19:30～20:00", "sum_fee": 150000, "end_time": "20:00", "start_time": "19:30"}, {"id": 41, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "01", "period": "20:00～20:30", "sum_fee": 150000, "end_time": "20:30", "start_time": "20:00"}, {"id": 42, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "01", "period": "20:30～21:00", "sum_fee": 150000, "end_time": "21:00", "start_time": "20:30"}, {"id": 43, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "01", "period": "21:00～21:30", "sum_fee": 150000, "end_time": "21:30", "start_time": "21:00"}, {"id": 44, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "01", "period": "21:30～22:00", "sum_fee": 150000, "end_time": "22:00", "start_time": "21:30"}, {"id": 45, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "01", "period": "22:00～22:30", "sum_fee": 150000, "end_time": "22:30", "start_time": "22:00"}, {"id": 46, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "01", "period": "22:30～23:00", "sum_fee": 150000, "end_time": "23:00", "start_time": "22:30"}, {"id": 47, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "01", "period": "23:00～23:30", "sum_fee": 150000, "end_time": "23:30", "start_time": "23:00"}, {"id": 48, "fee": {"fee": 130000, "ser_fee": 20000}, "rate": "01", "period": "23:30～24:00", "sum_fee": 150000, "end_time": "24:00", "start_time": "23:30"}]',
            //     'update_time' => date('Y-m-d H:i:s'),
            //     'corp_id' => $corp_id
            // ]);

            return [
                'new_id' => $corp_id
            ];
        }, true);
    }

    #[
        Apidoc\Title("更新运营商"),
        Apidoc\Desc('只允许超级管理员操作(corp == 0 and pid == 0)'),
        Apidoc\Author("cbj 2024.04.24 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/corp/update"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "运营商ID"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "运营商名称"),
        Apidoc\Param(name: "icon_url", type: "string", require: true, desc: "图标地址"),
        Apidoc\Param(name: "city_id", type: "array", require: true, desc: "地址ID"),
        Apidoc\Param(name: "contact", type: "string", require: true, desc: "联系人"),
        Apidoc\Param(name: "phone", type: "string", require: true, desc: "联系电话"),
//        Apidoc\Param(name: "admin_group_ids", type: "string", require: true, desc: "添加管理员时能够选择的管理员分组(多个以','分隔)"),
    ]
    public function update(): Json
    {
        return $this->openExceptionCatch(function () {

            if ($this->loginUser->corp_id > 0 || $this->loginUser->pid > 0) {
                throw new RuntimeException('只允许超级管理员操作', [], RuntimeException::CodeBusinessException);
            }

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::service_corp([
                'id', 'name', 'icon_url', 'city_id', 'contact', 'phone',
            ]));

            $city = get_city_for_city_id($verify_data['city_id']);
            if (!$city) throw new RuntimeException("获取省市区失败", [], RuntimeException::CodeBusinessException);
            $verify_data = array_merge($verify_data, $city);

            $CorpModel = new CorpModel();
            $result = $CorpModel->updateCorpData(
                $verify_data['id'], $verify_data['name'], $verify_data['icon_url'], $verify_data['province'],
                $verify_data['city'], $verify_data['district'], $verify_data['contact'], $verify_data['phone'],
                $verify_data['city_id']
            );

//            $CorpAdminGroupModel = new CorpAdminGroup();
//            $CorpAdminGroupModel->removeCorpAllAdminGroupIds($verify_data['id']);
//            if (!empty($verify_data['admin_group_ids'])) {
//                $CorpAdminGroupModel->bindCorpAdminGroupIds($verify_data['id'], explode(',', $verify_data['admin_group_ids']));
//            }


            LogCollector::collectorRunLog('$result= ' . $result);
            if ($result === false) throw new RuntimeException('更新运营商失败', [], RuntimeException::CodeBusinessException);

            return [];
        });
    }

//    #[
//        Apidoc\Title("删除运营商"),
//        Apidoc\Desc('只允许超级管理员操作(corp == 0 and pid == 0)'),
//        Apidoc\Author("cbj 2024.04.24 新增"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/admin/corp/delete"),
//        Apidoc\Param(name: "id", type: "int", require: true, desc: "运营商ID"),
//    ]
//    public function delete(): Json
//    {
//        return $this->openExceptionCatch(function () {
//
//            if ($this->loginUser->corp_id > 0 || $this->loginUser->pid > 0) {
//                throw new RuntimeException('只允许超级管理员操作', [], RuntimeException::CodeBusinessException);
//            }
//
//            $data = $this->request->post();
//            $verify_data = v::input($data, VerifyData::service_corp([
//                'id',
//            ]));
//
//            $CorpModel = new CorpModel();
//            $result = $CorpModel->deleteCorpData($verify_data['id']);
//            if ($result === false) throw new RuntimeException('删除运营商失败', [], RuntimeException::CodeBusinessException);
//
//            // 删除与之关联的管理员账号
//            $AdminUsersModel = new AdminUsersModel();
//            $delRow = $AdminUsersModel->deleteCorpAdminUser($verify_data['id']);
//            LogCollector::collectorRunLog(sprintf('$delRow = %s', $delRow));
//
//        }, true);
//    }

    #[
        Apidoc\Title("获取运营商列表"),
        Apidoc\Author("lwj 2023.8.1 新增, cbj 2024.04.26 更新"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Corp/get_corp_list"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "运营商列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '运营商编号'],
            ['name' => 'name', 'type' => 'string', 'desc' => '运营商名称']
        ]),
    ]
    public function get_corp_list(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            $list = $this->modelClass
                ->where('id', '=', $this->loginUser->corp_id)
                ->column('id,name');
        } else {
            $list = $this->modelClass
                ->column('id,name');
        }

        return $this->res_success($list);
    }

    #[
        Apidoc\Title("更新运营商审核状态(接口暂停使用)"),
        Apidoc\Author("lwj 2023.8.2 新增, cbj 2024.04.26 更新"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Corp/update_audit_status"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "运营商id"),
        Apidoc\Param(name: "audit_status", type: "int", require: true, desc: "审核状态，1-未审核，2-审核通过"),
    ]
    public function update_audit_status(): Json
    {
        return $this->res_error([], '接口暂停使用');
//
//        if ($this->loginUser->corp_id > 0) {
//            return $this->res_error([], '没有权限');
//        }
//
//        try {
//            $data = $this->request->post();
//            $verify_data = v::input($data, VerifyData::corp([
//                'id',
//                'audit_status',
//            ]));
//
//            $info = $this->modelClass->find($verify_data['id']);
//            if (!$info) return $this->res_error([], '运营商不存在');
//            $info->audit_status = $verify_data['audit_status'];
//            $res = $info->save();
//
//            if ($res) return $this->res_success([], '更新成功');
//            return $this->res_error([], '更新失败');
//        } catch (ValidationException $e) {
//            return $this->res_error([], $e->getMessage());
//        } catch (Throwable $e) {
//            return $this->res_error([], '处理异常：' . $e->getMessage());
//        }
    }

    #[
        Apidoc\Title("更新运营商状态(接口暂停使用)"),
        Apidoc\Author("lwj 2023.8.2 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Corp/update_status"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "运营商id"),
        Apidoc\Param(name: "status", type: "int", require: true, desc: "运营商状态，1-关闭，2-开启"),
    ]
    public function update_status(): Json
    {
        return $this->res_error([], '接口暂停使用');
//        if ($this->loginUser->corp_id > 0) {
//            return $this->res_error([], '没有权限');
//        }
//
//        try {
//            $data = $this->request->post();
//            $verify_data = v::input($data, VerifyData::corp([
//                'id',
//                'status',
//            ]));
//
//            $info = $this->modelClass->find($verify_data['id']);
//            if (!$info) return $this->res_error([], '运营商不存在');
//            $info->status = $verify_data['status'];
//            $res = $info->save();
//
//                if ($res) return $this->res_success([], '更新成功');
//            return $this->res_error([], '更新失败');
//        } catch (ValidationException $e) {
//            return $this->res_error([], $e->getMessage());
//        } catch (Throwable $e) {
//            return $this->res_error([], '处理异常：' . $e->getMessage());
//        }
    }

    #[
        Apidoc\Title("获取全部运营商列表"),
        Apidoc\Author("lwj 2024.5.11 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Corp/get_corp_list_all"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "运营商列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '运营商编号'],
            ['name' => 'name', 'type' => 'string', 'desc' => '运营商名称']
        ]),
    ]
    public function get_corp_list_all(): Json
    {
        $list = $this->modelClass
            ->column('id,name');

        return $this->res_success($list);
    }

}
