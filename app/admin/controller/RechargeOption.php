<?php /** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\admin\controller;

use hg\apidoc\annotation as Apidoc;
use think\response\Json;
use app\common\model\RechargeList as RechargeOptionModel;

#[Apidoc\Title("系统管理/充值设置")]
class RechargeOption extends BaseController
{
    #[
        Apidoc\Title("充值选项设置"),
        Apidoc\Author("swk 2023.7.20 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/recharge_option/recharge_option_list"),
        Apidoc\Returned(name: "id", type: "string", desc: "id"),
        Apidoc\Returned(name: "original_price", type: "string", desc: "原价"),
        Apidoc\Returned(name: "price", type: "string", desc: "售价"),
        Apidoc\Returned(name: "name", type: "string", rdesc: "充值名称"),
        Apidoc\Returned(name: "explain", type: "string", desc: "充值说明"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "添加时间"),
    ]
    public function recharge_option_list(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        $query = new RechargeOptionModel();
        $list = $query->select();
        return $this->res_success($list);
    }

    #[
        Apidoc\Title("详情"),
        Apidoc\Author("swk 2023.7.20 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/recharge_option/get_info"),
        Apidoc\Param(name: "id", type: "string", require: true, desc: "id"),
    ]
    public function get_info(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        $id = $this->request->post('id');
        if (empty($id)) return $this->res_error([], '参数错误');
        $result = RechargeOptionModel::find($id);
        if ($result) {
            return $this->res_success($result);
        } else {
            return $this->res_error([], '数据不存在');
        }
    }

    #[
        Apidoc\Title("新增管充值选项"),
        Apidoc\Author("swk 2023.7.20 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/recharge_option/add"),

        Apidoc\Param(name: "original_price", type: "string", desc: "原价"),
        Apidoc\Param(name: "price", type: "string", desc: "售价"),
        Apidoc\Param(name: "name", type: "string", rdesc: "充值名称"),
        Apidoc\Param(name: "explain", type: "string", desc: "充值说明"),

    ]
    public function add(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        // 保存数据
        if ($this->request->isPost()) {
            $data = $this->request->post([
                'original_price',
                'price',
                'name',
                'explain',
            ], null, 'trim');
            // 验证
            $result = $this->validate($data, 'RechargeOption');
            // 验证失败 输出错误信息
            if (true !== $result) return $this->res_error([], $result);
            $query = new RechargeOptionModel;
            if ($query->save($data)) {
                return $this->res_success([], '新增成功');
            } else {
                return $this->res_error([], '新增失败');
            }
        } else {
            return $this->res_error([], '错误请求');
        }
    }


    #[
        Apidoc\Title("编辑管充值选项"),
        Apidoc\Author("swk 2023.7.20 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/recharge_option/edit"),
        Apidoc\Param(name: "original_price", type: "string", desc: "原价"),
        Apidoc\Param(name: "price", type: "string", desc: "售价"),
        Apidoc\Param(name: "name", type: "string", rdesc: "充值名称"),
        Apidoc\Param(name: "explain", type: "string", desc: "充值说明"),
    ]
    public function edit(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        // 保存数据
        if ($this->request->isPost()) {
            $data = $this->request->post([
                'id',
                'original_price',
                'price',
                'name',
                'explain',
            ], null, 'trim');

            if (empty($data['id'])) return $this->res_error([], '参数错误');
            // 验证
            $result = $this->validate($data, 'RechargeOption');
            // 验证失败 输出错误信息
            if (true !== $result) return $this->res_error([], $result);
            $query = new RechargeOptionModel();
            $info = $query->where(['id' => $data['id']])->find();
            if (!$info) return $this->res_error([], '数据不存在');

            if ($info->save($data)) {
                return $this->res_success($result, '编辑成功');
            } else {
                return $this->res_error([], '编辑失败');
            }
        } else {
            return $this->res_error([], '错误请求');
        }
    }


    #[
        Apidoc\Title("删除数据"),
        Apidoc\Author("swk 2023.7.20 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/recharge_option/delete"),
        Apidoc\Param(name: "id", type: "string", require: true, desc: "id"),
    ]
    public function delete(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        $id = $this->request->post('id');
        if (RechargeOptionModel::destroy($id)) {
            return $this->res_success([], '删除成功');
        } else {
            return $this->res_error([], '删除失败');

        }
    }

}