<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\WorkOrderTemplateField;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use app\common\model\TariffGroup as TariffGroupModel;
use Respect\Validation\Validator as v;
use think\response\Json;
use app\common\model\WorkOrderField as WorkOrderFieldModel;

#[Apidoc\Title("工单管理/自定义字段")]
class WorkOrderField extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new TariffGroupModel();
    }

    #[
        Apidoc\Title("字段列表"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_field/get_list_data"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "每页显示"),
        Apidoc\Param(name: "screen_template_id", type: "int", require: false, default: null, desc: "工单模板ID"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => 'ID'],
            ['name' => 'name', 'type' => 'string', 'desc' => '名称'],
            ['name' => 'key', 'type' => 'string', 'desc' => '字段键值'],
            ['name' => 'type', 'type' => 'int', 'desc' => '字段类型 1:单行文本 2:多行文本 3:下拉框 4:多选框 5:数字 6:日期 7:图片'],
            ['name' => 'is_require', 'type' => 'int', 'desc' => '是否必填 1:是 0:否'],
            ['name' => 'options', 'type' => 'string', 'desc' => '字段选项(格式:json字符串)'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
            ['name' => 'update_time', 'type' => 'string', 'desc' => '更新时间'],
        ]),
    ]
    public function get_list_data(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order_field([
                'page',
                'limit',
                'screen_template_id'
            ]));
            $verifyData['page'] = $verifyData['page'] ?? 1;
            $verifyData['limit'] = $verifyData['limit'] ?? 10;

            $where = [];
            if (!empty($verifyData['screen_template_id'])) {
                $where[] = ['template_id', '=', $verifyData['screen_template_id']];
            }

            $WorkOrderFieldModel = new WorkOrderFieldModel();
            $field = ['id', 'name', 'key', 'type', 'is_require', 'options', 'create_time', 'update_time'];
            return $WorkOrderFieldModel->where($where)
                ->field($field)
                ->paginate(['list_rows' => $verifyData['limit'], 'page' => $verifyData['page']])
                ->toArray();
        });
    }

    #[
        Apidoc\Title("字段选项"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_field/get_options"),
        Apidoc\Returned(name: "options", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => 'ID'],
            ['name' => 'name', 'type' => 'string', 'desc' => '名称'],
        ]),
    ]
    public function get_options(): Json
    {
        return $this->openExceptionCatch(function () {
            $WorkOrderFieldModel = new WorkOrderFieldModel();
            $field = ['id', 'name'];
            return $WorkOrderFieldModel->field($field)->select()->toArray();
        });
    }

    #[
        Apidoc\Title("字段详情"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_field/get_field"),
        Apidoc\Param(name: "id", type: "int", require: true, default: 1, desc: "字段ID"),
        Apidoc\Returned(name: "name", type: "string", require: true, default: "", desc: "字段名称"),
        Apidoc\Returned(name: "key", type: "string", require: true, default: "", desc: "字段键值"),
        Apidoc\Returned(name: "type", type: "int", require: true, default: 1, desc: "字段类型 1:单行文本 2:多行文本 3:下拉框 4:多选框 5:数字 6:日期 7:图片"),
        Apidoc\Returned(name: "is_require", type: "int", require: true, default: 1, desc: "是否必填 1:是 0:否"),
        Apidoc\Returned(name: "options", type: "array", require: true, default: [], desc: "新增字段的ID"),
        Apidoc\Returned(name: "create_time", type: "string", require: true, default: "2024-04-29 14:10:15", desc: "创建时间"),
        Apidoc\Returned(name: "update_time", type: "string", require: true, default: "2024-04-29 14:10:15", desc: "更新时间"),
    ]
    public function get_field(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order_field([
                'id'
            ]));

            $WorkOrderFieldModel = new WorkOrderFieldModel();

            $data = $WorkOrderFieldModel->getField(
                $verifyData['id']
            );
            if (is_null($data)) {
                throw new RuntimeException('无效的字段ID', [], RuntimeException::CodeBusinessException);
            }

            return $data;
        });
    }

    #[
        Apidoc\Title("新增字段"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_field/add_field"),
        Apidoc\Param(name: "name", type: "string", require: true, default: "", desc: "字段名称"),
        Apidoc\Param(name: "key", type: "string", require: true, default: "", desc: "字段键值"),
        Apidoc\Param(name: "type", type: "int", require: true, default: 1, desc: "字段类型 1:单行文本 2:多行文本 3:下拉框 4:多选框 5:数字 6:日期 7:图片"),
        Apidoc\Param(name: "is_require", type: "int", require: true, default: 1, desc: "是否必填 1:是 0:否"),
        Apidoc\Param(name: "options", type: "array", require: true, default: [], desc: "字段选项 当字段类型为下拉框或多选框时，存储选项值。(格式:[{'name': '示例1', value: 1}, ...])"),
        Apidoc\Returned(name: "new_id", type: "int", require: true, default: 1, desc: "新增字段的ID"),
    ]
    public function add_field(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order_field([
                'name', 'key', 'type', 'is_require', 'options'
            ]));
            $verifyData['options'] = $verifyData['options'] ?? [];

            $WorkOrderFieldModel = new WorkOrderFieldModel();

            // 验证字段名是否存在
            if ($WorkOrderFieldModel->verifyNameExist($verifyData['name'])) {
                throw new RuntimeException('字段名称已存在', [], RuntimeException::CodeBusinessException);
            }
            // 验证字段键值是否存在
            if ($WorkOrderFieldModel->verifyKeyExist($verifyData['key'])) {
                throw new RuntimeException('字段键值已存在', [], RuntimeException::CodeBusinessException);
            }

            $newId = $WorkOrderFieldModel->addField(
                $verifyData['name'],
                $verifyData['key'],
                $verifyData['type'],
                $verifyData['is_require'],
                $verifyData['options']
            );


            return [
                'new_id' => $newId
            ];
        });
    }

    #[
        Apidoc\Title("更新字段"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_field/update_field"),
        Apidoc\Param(name: "id", type: "int", require: true, default: "", desc: "字段ID"),
        Apidoc\Param(name: "name", type: "string", require: true, default: "", desc: "字段名称"),
        Apidoc\Param(name: "key", type: "string", require: true, default: "", desc: "字段键值"),
        Apidoc\Param(name: "type", type: "int", require: true, default: 1, desc: "字段类型 1:单行文本 2:多行文本 3:下拉框 4:多选框 5:数字 6:日期 7:图片"),
        Apidoc\Param(name: "is_require", type: "int", require: true, default: 1, desc: "是否必填 1:是 0:否"),
        Apidoc\Param(name: "options", type: "array", require: true, default: [], desc: "字段选项 当字段类型为下拉框或多选框时，存储选项值。(格式:[{'name': '示例1', value: 1}, ...])"),
    ]
    public function update_field(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order_field([
                'id', 'name', 'key', 'type', 'is_require', 'options'
            ]));

            $WorkOrderFieldModel = new WorkOrderFieldModel();
            if ($WorkOrderFieldModel->isFieldExists($verifyData['id']) === false) {
                throw new RuntimeException('无效的字段ID', [], RuntimeException::CodeBusinessException);
            }


            // 验证字段名是否存在
            if ($WorkOrderFieldModel->verifyNameExist($verifyData['name'], $verifyData['id'])) {
                throw new RuntimeException('字段名称已存在', [], RuntimeException::CodeBusinessException);
            }
            // 验证字段键值是否存在
            if ($WorkOrderFieldModel->verifyKeyExist($verifyData['key'], $verifyData['id'])) {
                throw new RuntimeException('字段键值已存在', [], RuntimeException::CodeBusinessException);
            }

            $result = $WorkOrderFieldModel->updateField(
                $verifyData['id'],
                $verifyData['name'],
                $verifyData['key'],
                $verifyData['type'],
                $verifyData['is_require'],
                $verifyData['options']
            );
            if ($result === false) {
                throw new RuntimeException('更新失败');
            }
            return [];
        });
    }

    #[
        Apidoc\Title("删除字段"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/work_order_field/delete_field"),
        Apidoc\Param(name: "id", type: "int", require: true, default: 0, desc: "字段ID"),
    ]
    public function delete_field(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            // 验证数据
            $verifyData = v::input($data, VerifyData::work_order_field([
                'id'
            ]));


            $count = (new WorkOrderTemplateField())->getFieldBindTemplateCount($verifyData['id']);
            if ($count > 0) {
                throw new RuntimeException('该字段已经绑定模板，请将该字段从模板中解除绑定后，再删除字段。');
            }

            $WorkOrderFieldModel = new WorkOrderFieldModel();
            $result = $WorkOrderFieldModel->deleteField($verifyData['id']);

            if ($result === false) throw new RuntimeException('删除失败');

            return [];
        });
    }

}