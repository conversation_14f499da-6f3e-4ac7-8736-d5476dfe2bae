<?php
/** @noinspection PhpUnused */

namespace app\admin\controller;

use app\common\logic\admin\charging_qrcode\ChargingQrcodeList;
use app\common\logic\admin\charging_qrcode\GenerateQrcode;
use hg\apidoc\annotation as Apidoc;
use think\response\Json;

#[Apidoc\Title("充电二维码")]
class ChargingQrcode extends BaseController
{

    #[
        Apidoc\Title("生成二维码"),
        Apidoc\Desc("只允许超级管理员生成二维码(pid == 0 && corp_id == 0)"),
        Apidoc\Author("cbj 2024.10.16 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/charging_qrcode/generate_qrcode"),
        Apidoc\Param(name: "generate_count", type: "int", require: true, default: 1, desc: "生成数量, 最大限制:1000, 最小限制:1"),
        <PERSON>pid<PERSON>\Param(name: "url_prefix", type: "string", require: true, default: "https://admin.resmartcharge.com/applet", desc: "链接前缀"),
        Apidoc\Param(name: "logo", type: "file", require: false, desc: "Logo图片(要求:尺寸必须是120px*120px, 图片类型需要是png或jpg)"),
        Apidoc\Returned(name: "generate_qrcode_list", type: "array", require: false, desc: "生成的二维码列表", children: [
            ['name' => 'id', 'type' => 'int', 'require' => true, 'desc' => 'ID'],
            ['name' => 'url', 'type' => 'string', 'require' => true, 'desc' => '二维码对应的链接'],
            ['name' => 'path', 'type' => 'string', 'require' => true, 'desc' => '二维码图片链接(需要在前面拼接上接口域名)'],
            ['name' => 'create_time', 'type' => 'string', 'require' => true, 'desc' => '创建时间'],
        ]),
    ]
    public function generate_qrcode(): Json
    {
        return $this->openExceptionCatch(function () {

            return (new GenerateQrcode($this->loginUser, $this->request))->run();

        }, true);
    }

    #[
        Apidoc\Title("二维码列表排序信息"),
        Apidoc\Author("cbj 2024.10.16 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/charging_qrcode/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "int", require: true, desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "int", require: true, desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_field_options", type: "array", require: true, desc: "排序字段选项", children: [
            ['name' => 'value', 'type' => 'int', 'desc' => '排序字段值'],
            ['name' => 'label', 'type' => 'int', 'desc' => '排序字段名称'],
        ]),
        Apidoc\Returned(name: "sort_type_options", type: "array", require: true, desc: "排序类型选项", children: [
            ['name' => 'value', 'type' => 'int', 'desc' => '排序类型值'],
            ['name' => 'label', 'type' => 'int', 'desc' => '排序类型名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        return $this->openExceptionCatch(function () {
            return [
                'order_name' => ChargingQrcodeList::SortFieldsOptions[0]['value'],
                'order_type' => ChargingQrcodeList::SortTypeDesc,
                'sort_field_options' => ChargingQrcodeList::SortFieldsOptions,
                'sort_type_options' => ChargingQrcodeList::SortTypeOptions
            ];
        });
    }

    #[
        Apidoc\Title("二维码列表"),
        Apidoc\Author("cbj 2024.10.16 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/charging_qrcode/list"),
        Apidoc\Param(name: "filter_max_id", type: "int", require: false, desc: "过滤ID最大值"),
        Apidoc\Param(name: "filter_min_id", type: "int", require: false, desc: "过滤ID最小值"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "限制数量"),
        Apidoc\Param(name: "sort_field", type: "int", require: true, default: 1, desc: "排序类型"),
        Apidoc\Param(name: "sort_type", type: "int", require: true, default: 1, desc: "排序字段"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'require' => true, 'desc' => 'ID'],
            ['name' => 'url', 'type' => 'string', 'require' => true, 'desc' => '二维码对应的链接'],
            ['name' => 'path', 'type' => 'string', 'require' => true, 'desc' => '二维码图片链接(需要在前面拼接上接口域名)'],
            ['name' => 'create_time', 'type' => 'string', 'require' => true, 'desc' => '创建时间'],
        ]),
    ]
    public function list(): Json
    {
        return $this->openExceptionCatch(function () {

            return (new ChargingQrcodeList($this->loginUser, $this->request))->get_list_data();

        });
    }
}