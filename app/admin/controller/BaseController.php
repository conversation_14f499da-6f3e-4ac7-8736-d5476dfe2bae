<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\admin\controller;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\controller\ApiBase;
use think\App;

class BaseController extends ApiBase
{
    protected ?AdminLoginUser $loginUser = null;

    public function __construct(App $app)
    {
        parent::__construct($app);
        // $loginUser 不为空时，说明已经登录了。
        if (isset($this->request->adminLoginUser)) {
            $this->loginUser = $this->request->adminLoginUser;
        }
    }
}