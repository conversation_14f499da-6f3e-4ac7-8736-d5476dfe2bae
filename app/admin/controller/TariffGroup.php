<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;

use app\common\lib\VerifyData;
use app\common\logic\admin\tariff_group\Add;
use app\common\logic\admin\tariff_group\Delete;
use app\common\logic\admin\tariff_group\Update;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use app\common\model\TariffGroup as TariffGroupModel;
use app\common\model\PeriodRate as PeriodRateModel;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\response\Json;
use Throwable;

#[Apidoc\Title("资源中心/费率组管理")]
class TariffGroup extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new TariffGroupModel();
    }

    #[
        Apidoc\Title("费率组列表"),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.7 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/TariffGroup/tariff_group_list"),
        Apidoc\Param(name: "corp_id", type: "int", require: true, desc: "运营商编号"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "费率组id"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "费率组名称"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '费率组id'],
            ['name' => 'name', 'type' => 'string', 'desc' => '费率组名称'],
            ['name' => 'sharp_fee', 'type' => 'float', 'desc' => '尖时电费(精确到小数点后5位)元'],
            ['name' => 'peak_fee', 'type' => 'float', 'desc' => '峰时电费(精确到小数点后5位)元'],
            ['name' => 'flat_fee', 'type' => 'float', 'desc' => '平时电费(精确到小数点后5位)元'],
            ['name' => 'valley_fee', 'type' => 'float', 'desc' => '谷时电费(精确到小数点后5位)元'],
            ['name' => 'sharp_ser_fee', 'type' => 'float', 'desc' => '尖时服务费(精确到小数点后5位)元'],
            ['name' => 'peak_ser_fee', 'type' => 'float', 'desc' => '峰时服务费(精确到小数点后5位)元'],
            ['name' => 'flat_ser_fee', 'type' => 'float', 'desc' => '平时服务费(精确到小数点后5位)元'],
            ['name' => 'valley_ser_fee', 'type' => 'float', 'desc' => '谷时服务费(精确到小数点后5位)元'],
            ['name' => 'surcharge', 'type' => 'float', 'desc' => '附加费(精确到小数点后5位,不参与折扣)元'],
            ['name' => 'loss_rate', 'type' => 'int', 'desc' => '计损比（%）'],
            ['name' => 'period_codes', 'type' => 'array', 'desc' => '24小时分为48个时段', 'children' => [
                ['name' => 'period', 'type' => 'string', 'desc' => '时段'],
                ['name' => 'rate', 'type' => 'string', 'desc' => '费率类型'],
            ]],
            ['name' => 'corp', 'type' => 'array', 'desc' => '运营商', 'children' => [
                ['name' => 'id', 'type' => 'int', 'desc' => '运营商编号'],
                ['name' => 'name', 'type' => 'string', 'desc' => '运营商名称'],
            ]],
        ]),
    ]
    public function tariff_group_list(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            if (empty($data['corp_id'])) $data['corp_id'] = 0;

            return (new TariffGroupModel())->getListData($this->loginUser, $data);
        });
    }

    #[
        Apidoc\Title("获取费率组排序信息"),
        Apidoc\Author("lwj 2023.8.4 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/TariffGroup/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        $res = [
            'order_name' => 'tg.id',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'tg.id', 'label' => '费率组id'],
                ['value' => 'tg.corp_id', 'label' => '运营商'],
                ['value' => 'tg.name', 'label' => '费率组名称'],
                ['value' => 'tg.sharp_fee', 'label' => '尖时电费'],
                ['value' => 'tg.peak_fee', 'label' => '峰时电费'],
                ['value' => 'tg.flat_fee', 'label' => '平时电费'],
                ['value' => 'tg.valley_fee', 'label' => '谷时电费'],
                ['value' => 'tg.sharp_ser_fee', 'label' => '尖时服务费'],
                ['value' => 'tg.peak_ser_fee', 'label' => '峰时服务费'],
                ['value' => 'tg.flat_ser_fee', 'label' => '平时服务费'],
                ['value' => 'tg.valley_ser_fee', 'label' => '谷时服务费'],
                ['value' => 'tg.surcharge', 'label' => '附加费'],
                ['value' => 'tg.loss_rate', 'label' => '计损比'],
                ['value' => 'tg.create_time', 'label' => '创建时间'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("费率组详情"),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.7 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/TariffGroup/get_info"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "费率组id"),
        Apidoc\Returned(name: "id", type: "int", desc: "费率组id"),
        Apidoc\Returned(name: "name", type: "string", desc: "费率组名称"),
        Apidoc\Returned(name: "sharp_fee", type: "float", desc: "尖时电费(精确到小数点后5位)元"),
        Apidoc\Returned(name: "peak_fee", type: "float", desc: "峰时电费(精确到小数点后5位)元"),
        Apidoc\Returned(name: "flat_fee", type: "float", desc: "平时电费(精确到小数点后5位)元"),
        Apidoc\Returned(name: "valley_fee", type: "float", desc: "谷时电费(精确到小数点后5位)元"),
        Apidoc\Returned(name: "sharp_ser_fee", type: "float", desc: "尖时服务费(精确到小数点后5位)元"),
        Apidoc\Returned(name: "peak_ser_fee", type: "float", desc: "峰时服务费(精确到小数点后5位)元"),
        Apidoc\Returned(name: "flat_ser_fee", type: "float", desc: "平时服务费(精确到小数点后5位)元"),
        Apidoc\Returned(name: "valley_ser_fee", type: "float", desc: "谷时服务费(精确到小数点后5位)元"),
        Apidoc\Returned(name: "surcharge", type: "float", desc: "附加费(精确到小数点后5位,不参与折扣)元"),
        Apidoc\Returned(name: "loss_rate", type: "int", desc: "计损比（%）"),
        Apidoc\Returned(name: "period_codes", type: "array", desc: "时段费率", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => 'id'],
            ['name' => 'period', 'type' => 'string', 'desc' => '时段'],
            ['name' => 'rate', 'type' => 'string', 'desc' => '费率类型'],
        ]),
    ]
    public function get_info(): Json
    {
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::tariff_group([
                'id',
            ]));

            // 数据权限
            $where = [];
            $where[] = ['id', '=', $verify_data['id']];
            if ($this->loginUser->corp_id > 0) {
                $where[] = ['corp_id', '=', $this->loginUser->corp_id];
            }


            $result = $this->modelClass
                ->field('id,name,sharp_fee,peak_fee,flat_fee,valley_fee,sharp_ser_fee,peak_ser_fee,flat_ser_fee,valley_ser_fee,surcharge,loss_rate,period_codes,corp_id')
                ->withAttr('period_codes', function ($value, $data) {
                    return period_codes_to_period_rate_list_all($data['period_codes']);
                })
                ->withAttr('sharp_fee', function ($value) {
                    return int_to_decimal($value);
                })
                ->withAttr('peak_fee', function ($value) {
                    return int_to_decimal($value);
                })
                ->withAttr('flat_fee', function ($value) {
                    return int_to_decimal($value);
                })
                ->withAttr('valley_fee', function ($value) {
                    return int_to_decimal($value);
                })
                ->withAttr('sharp_ser_fee', function ($value) {
                    return int_to_decimal($value);
                })
                ->withAttr('peak_ser_fee', function ($value) {
                    return int_to_decimal($value);
                })
                ->withAttr('flat_ser_fee', function ($value) {
                    return int_to_decimal($value);
                })
                ->withAttr('valley_ser_fee', function ($value) {
                    return int_to_decimal($value);
                })
                ->withAttr('surcharge', function ($value) {
                    return int_to_decimal($value);
                })
                ->where($where)
                ->find()
                ->toArray();
//            unset($result['period_codes']);

            if ($result) return $this->res_success($result);
            return $this->res_error([], '运营商不存在');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }


    #[
        Apidoc\Title("新增费率组"),
        Apidoc\Desc('只允许运营商账号操作(corp_id > 0)'),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.28 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/TariffGroup/add"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "费率组名称"),
        Apidoc\Param(name: "sharp_fee", type: "int", require: true, default: 0, desc: "尖时电费(精确到小数点后5位)元"),
        Apidoc\Param(name: "peak_fee", type: "int", require: true, default: 0, desc: "峰时电费(精确到小数点后5位)元"),
        Apidoc\Param(name: "flat_fee", type: "int", require: true, default: 0, desc: "平时电费(精确到小数点后5位)元"),
        Apidoc\Param(name: "valley_fee", type: "int", require: true, default: 0, desc: "谷时电费(精确到小数点后5位)元"),
        Apidoc\Param(name: "sharp_ser_fee", type: "int", require: true, default: 0, desc: "尖时服务费(精确到小数点后5位)元"),
        Apidoc\Param(name: "peak_ser_fee", type: "int", require: true, default: 0, desc: "峰时服务费(精确到小数点后5位)元"),
        Apidoc\Param(name: "flat_ser_fee", type: "int", require: true, default: 0, desc: "平时服务费(精确到小数点后5位)元"),
        Apidoc\Param(name: "valley_ser_fee", type: "int", require: true, default: 0, desc: "谷时服务费(精确到小数点后5位)元"),
        Apidoc\Param(name: "surcharge", type: "int", require: true, default: 0, desc: "附加费(精确到小数点后5位,不参与折扣)元"),
        Apidoc\Param(name: "loss_rate", type: "int", require: true, default: 0, desc: "计损比（%）"),
        Apidoc\Param(name: "period_codes", type: "array", require: true, desc: "时段费率", children: [
            ['name' => 'id', 'type' => 'int', 'require' => true, 'desc' => 'id'],
            ['name' => 'period', 'type' => 'string', 'require' => true, 'desc' => '时段'],
            ['name' => 'rate', 'type' => 'string', 'require' => true, 'desc' => '费率类型'],
        ]),
        Apidoc\Returned(name: "new_id", type: "int", require: true, desc: "费率组ID"),

    ]
    public function add(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Add($this->loginUser, $this->request))->run();
        });
    }


    #[
        Apidoc\Title("编辑费率组"),
        Apidoc\Desc('只允许运营商账号操作(corp_id > 0)'),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.28 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/TariffGroup/edit"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "费率组id"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "费率组名称"),
        Apidoc\Param(name: "sharp_fee", type: "float", require: true, desc: "尖时电费(精确到小数点后5位)元"),
        Apidoc\Param(name: "peak_fee", type: "float", require: true, desc: "峰时电费(精确到小数点后5位)元"),
        Apidoc\Param(name: "flat_fee", type: "float", require: true, desc: "平时电费(精确到小数点后5位)元"),
        Apidoc\Param(name: "valley_fee", type: "float", require: true, desc: "谷时电费(精确到小数点后5位)元"),
        Apidoc\Param(name: "sharp_ser_fee", type: "float", require: true, desc: "尖时服务费(精确到小数点后5位)元"),
        Apidoc\Param(name: "peak_ser_fee", type: "float", require: true, desc: "峰时服务费(精确到小数点后5位)元"),
        Apidoc\Param(name: "flat_ser_fee", type: "float", require: true, desc: "平时服务费(精确到小数点后5位)元"),
        Apidoc\Param(name: "valley_ser_fee", type: "float", require: true, desc: "谷时服务费(精确到小数点后5位)元"),
        Apidoc\Param(name: "surcharge", type: "float", require: true, desc: "附加费(精确到小数点后5位,不参与折扣)元"),
        Apidoc\Param(name: "loss_rate", type: "int", require: true, desc: "计损比（%）"),
        Apidoc\Param(name: "period_codes", type: "array", require: true, desc: "时段费率", children: [
            ['name' => 'id', 'type' => 'int', 'require' => true, 'desc' => 'id'],
            ['name' => 'period', 'type' => 'string', 'require' => true, 'desc' => '时段'],
            ['name' => 'rate', 'type' => 'string', 'require' => true, 'desc' => '费率类型'],
        ]),
    ]
    public function edit(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Update($this->loginUser, $this->request))->run();
        }, true);
    }

    #[
        Apidoc\Title("删除费率组"),
        Apidoc\Desc('只允许运营商账号操作(corp_id > 0)'),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.1 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/TariffGroup/delete"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "费率组id"),
    ]
    public function delete(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Delete($this->loginUser, $this->request))->run();
        });
    }

    #[
        Apidoc\Title("获取费率组列表"),
        Apidoc\Author("lwj 2023.8.7 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/TariffGroup/get_tariff_group_list"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "费率组列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '费率组id'],
            ['name' => 'name', 'type' => 'string', 'desc' => '费率组名称']
        ]),
    ]
    public function get_tariff_group_list(): Json
    {
        $where = [];
        if ($this->loginUser->corp_id > 0) {
            $where[] = ['corp_id', '=', $this->loginUser->corp_id];
        }

        $list = $this->modelClass
            ->where($where)
            ->order('id')
            ->column('id,name');
        return $this->res_success($list);
    }

    #[
        Apidoc\Title("获取时段费率列表"),
        Apidoc\Author("lwj 2023.7.26 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/TariffGroup/get_period_rate_list"),
    ]
    public function get_period_rate_list(): Json
    {
        return $this->openExceptionCatch(function () {
            return PeriodRateModel::order('id', 'asc')
                ->field('id,period,rate')
                ->select()
                ->toArray();
        });
    }

    #[
        Apidoc\Title("获取时段费率类型"),
        Apidoc\Author("lwj 2023.7.26 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/TariffGroup/get_period_rate_type"),
    ]
    public function get_period_rate_type(): Json
    {
        return $this->res_success(config('common_data.period_rate_type'));
    }

}