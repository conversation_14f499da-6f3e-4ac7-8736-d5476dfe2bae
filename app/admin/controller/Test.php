<?php


namespace app\admin\controller;

use hg\apidoc\annotation as Apidoc;
use app\common\controller\ApiBase;
use Endroid\QrCode\Builder\Builder;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevelHigh;
use Endroid\QrCode\RoundBlockSizeMode\RoundBlockSizeModeMargin;
use Endroid\QrCode\Writer\PngWriter;
use Picqer\Barcode\BarcodeGeneratorPNG;
use app\common\model\Piles as PilesModel;
use Predis\Client as Predis;
use think\facade\Db;
use think\response\Json;

#[Apidoc\Title("测试")]
class Test extends ApiBase
{
    public function okj()
    {
        // 生成二维码
        $qrCode = Builder::create()
            ->writer(new PngWriter())
            ->writerOptions([])
            ->data('12345') // 内容
            ->encoding(new Encoding('UTF-8'))
            ->errorCorrectionLevel(new ErrorCorrectionLevelHigh())
            ->size(300) // 尺寸
            ->margin(10) // 边距
            ->roundBlockSizeMode(new RoundBlockSizeModeMargin())
            ->validateResult(false)
            ->build();

        // 获取Base64
        $base64Image = $qrCode->getDataUri();
        echo $base64Image;
    }

    // 生成条形码
    public function tgy()
    {
        $generator = new BarcodeGeneratorPNG();
        $barcode = $generator->getBarcode('74588874584', $generator::TYPE_CODE_128);
        $base64 = 'data:image/png;base64,' . base64_encode($barcode);
        echo $base64;
    }

    public function jhhh()
    {
        $period_codes = "030303030303030303030303030302020202020200000000000000000000000000000000000101010101010101010302";
        $res = period_codes_to_period_rate_list($period_codes);
        return json($res);

        $stations = (new \app\common\model\TariffGroup)->withAttr('period_codes3', function ($value, $data) {
            return period_codes_to_period_rate_list_all($data['period_codes']);
        })
            ->select();

//        $stations = StationsModel::alias('a')
//            ->join('corp b', 'a.corp_id = b.id')
//            ->field('a.*,b.name as corp_name')
//            ->limit(1)
//            ->select();

//        $stations = CorpModel::with('stations')
//
//            ->select();
        return $this->res_success($stations);
    }


    public function sdadsa()
    {
        $list = PilesModel::with(['stations', 'corp'])
            ->select();
        return $this->res_success($list);
    }

    public function jh()
    {
        $period_codes = "020303010303030303030303030302020202020200000000000000000000000000000000020101010101010101010101";
        $res = period_codes_to_period_rate_list($period_codes);
        return json($res);
    }

    public function dfsg()
    {
        $redis = new Predis(config('my.redis2'));
        $redis->hset('测试', '测试1', 548777);

        $res = intval($redis->hget('测试', '测试1'));
        var_dump($res);
//        return json(['v'=>$res]);
    }

    public function ccc()
    {
        $city = Db::name('city')
            ->alias('a')
            ->whereExists(function ($query) {
                $query->table('city')
                    ->alias('b')
                    ->where('b.id=a.pid')
                    ->where('pid', 0);
            })
            ->select();
        return $this->res_success($city);
    }

    #[
        Apidoc\Title("测试发送邮件"),
        Apidoc\Author("cbj"),
        Apidoc\Desc('2023.12.12 新增'),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/test/sendMail"),
    ]
    public function sendMail(): Json
    {

        // 消息
        $message = "Line 1\r\nLine 2\r\nLine 3";

        // 如果任何一行超过 70 个字符，应该使用 wordwrap()
        $message = wordwrap($message, 70, "\r\n");

        $from = "<EMAIL>";   // 邮件发送者
        $headers = "From:" . $from;         // 头部信息设置

        // 发送12
        $result = mail('<EMAIL>', 'My Subject', $message, $headers);


        return $this->res_success([$result]);
    }
}