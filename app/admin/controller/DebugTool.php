<?php

namespace app\admin\controller;

use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\DebugToolRestartOrder;
use hg\apidoc\annotation as Apidoc;
use think\response\Json;

#[Apidoc\Title("调试工具")]
class DebugTool extends BaseController
{

    #[
        Apidoc\Title("重启订单"),
        Apidoc\Author("cbj 2024.09.20 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/debug_tool/restart_order"),
        Apidoc\Param(name: "transaction_serial_number", type: "string", require: true, default: "20230105000400012401181929490014", desc: "订单号"),
        Apidoc\Param(name: "power_limit", type: "int", require: true, default: 7000, desc: "限制功率(单位:瓦)"),
        Apidoc\Returned(name: "transaction_serial_number", type: "string", require: true, desc: "订单号"),
    ]
    public function restart_order(): Json
    {
        return $this->openExceptionCatch(function () {
            if ($this->loginUser->corp_id > 0) {
                throw new RuntimeException('没有权限');
            }
            $data = $this->request->post();

            return (new DebugToolRestartOrder())->run($this->loginUser->id, $data);
        }, true);
    }
}