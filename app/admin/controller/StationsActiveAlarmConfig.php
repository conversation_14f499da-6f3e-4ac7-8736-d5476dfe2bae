<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\StationDataAuthority;
use hg\apidoc\annotation as Apidoc;
use app\common\model\Stations as StationsModel;
use Respect\Validation\Validator as v;
use think\response\Json;
use app\common\repositories\StationsActiveAlarmConfig as StationsActiveAlarmConfigRepositories;

#[Apidoc\Title("场站主动告警/场站配置")]
class StationsActiveAlarmConfig extends BaseController
{
    #[
        Apidoc\Title("场站列表"),
        Apidoc\Author("cbj 2024.05.16 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/stations_active_alarm_config/get_list_data"),
        Apidoc\Param(name: 'screen_corp_id', type: 'int', require: false, default: null, desc: '运营商ID'),
        Apidoc\Param(name: 'screen_station_id', type: 'int', require: false, default: null, desc: '充电站ID'),
        Apidoc\Param(name: "page", type: "int", require: false, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: false, default: 10, desc: "每页显示"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", require: false, desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '场站ID'],
            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'station_name', 'type' => 'int', 'desc' => '场站名称'],
        ]),
    ]
    public function get_list_data(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verify_data = v::input($data, VerifyData::station_active_alarm_config([
                'screen_corp_id', 'screen_station_id', 'page', 'limit'
            ]));

            // 如果是运营商管理员账号
            $where = [];
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $verify_data['screen_corp_id'] = $this->loginUser->corp_id;
                // 如果是运营商子账号
            } else if ($this->loginUser->corp_id > 0) {
                $verify_data['screen_corp_id'] = $this->loginUser->corp_id;
                // 查询当前子账号拥有的场站权限
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                if (empty($verify_data['screen_station_id'])) {
                    $verify_data['screen_station_id'] = $station_ids;
                } else {
                    // 如果用户对场站进行了筛选，则检查选择的场站是否具体权限。如果没，有则返回:"无效场站ID"。
                    if (in_array($verify_data['screen_station_id'], $station_ids) === false) {
                        $verify_data['screen_station_id'] = [0];
                    }
                }
            }

            if (!empty($verify_data['screen_station_id'])) {
                if (is_array($verify_data['screen_station_id'])) {
                    $where[] = ['s.id', 'in', $verify_data['screen_station_id']];
                } else {
                    $where[] = ['s.id', '=', $verify_data['screen_station_id']];
                }
            }
            if (!empty($verify_data['screen_corp_id'])) {
                $where[] = ['s.corp_id', '=', $verify_data['screen_corp_id']];
            }

            $StationsModel = app(StationsModel::class);
            return $StationsModel->alias('s')
                ->join('corp c', 'c.id = s.corp_id', 'LEFT')
                ->where($where)
                ->field(['s.id', 's.name as station_name', 'c.name as corp_name'])
                ->paginate(['list_rows' => $verify_data['limit'], 'page' => $verify_data['page']])
                ->toArray();
        });
    }

    #[
        Apidoc\Title("获取主动告警配置"),
        Apidoc\Author("cbj 2024.05.16 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/stations_active_alarm_config/get_active_alarm_config"),
        Apidoc\Param(name: 'station_id', type: 'int', require: true, default: 1, desc: '充电站ID'),

        Apidoc\Returned(name: "station_id", type: "int", require: true, desc: "场站ID"),
        Apidoc\Returned(name: "station_name", type: "int", require: true, desc: "场站名称"),
        Apidoc\Returned(name: "corp_id", type: "int", require: true, desc: "运营商ID"),
        Apidoc\Returned(name: "enterprise_wechat_key", type: "string", require: true, desc: "企业微信机器人密钥"),
        Apidoc\Returned(name: "email", type: "string", require: true, desc: "邮箱地址(用于接收每日场站告警汇总报告)"),
        Apidoc\Returned(name: "sf_is_generate_work_order", type: "int", require: true, desc: "充电枪故障告警 - 是否自动生成工单 1:是 2:否"),
        Apidoc\Returned(name: "sf_auto_dispatch_user_id", type: "int", require: true, desc: "充电枪故障告警 - 自动派发给指定工作人员"),

        Apidoc\Returned(name: "cso_is_generate_work_order", type: "int", require: true, desc: "充电服务离线告警 - 是否自动生成工单 1:是 2:否"),
        Apidoc\Returned(name: "cso_auto_dispatch_user_id", type: "int", require: true, desc: "充电服务离线告警 - 自动派发给指定工作人员"),

        Apidoc\Returned(name: "remp_is_generate_work_order", type: "int", require: true, desc: "读取电表功率告警 - 是否自动生成工单 1:是 2:否"),
        Apidoc\Returned(name: "remp_auto_dispatch_user_id", type: "int", require: true, desc: "读取电表功率告警 - 自动派发给指定工作人员"),

        Apidoc\Returned(name: "po_is_generate_work_order", type: "int", require: true, desc: "充电桩离线告警 - 是否自动生成工单 1:是 2:否"),
        Apidoc\Returned(name: "po_auto_dispatch_user_id", type: "int", require: true, desc: "充电桩离线告警 - 自动派发给指定工作人员"),

        Apidoc\Returned(name: "ccsr_is_generate_work_order", type: "int", require: true, desc: "能源路由器资源告警 - 是否自动生成工单 1:是 2:否"),
        Apidoc\Returned(name: "ccsr_auto_dispatch_user_id", type: "int", require: true, desc: "能源路由器资源告警 - 自动派发给指定工作人员"),

        Apidoc\Returned(name: "update_time", type: "string", require: true, desc: "配置更新时间"),
    ]
    public function get_active_alarm_config(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verify_data = v::input($data, VerifyData::station_active_alarm_config([
                'station_id'
            ]));

            $data = StationsActiveAlarmConfigRepositories::find($verify_data['station_id']);
            if (empty($data)) {
                throw new RuntimeException('无效场站ID', [], RuntimeException::CodeBusinessException);
            }

            $station_name = (new StationsModel())->getStationName($data->station_id);

            return array_merge($data->toArray(), ['station_name' => $station_name]);
        });
    }

    #[
        Apidoc\Title("更新主动告警配置"),
        Apidoc\Author("cbj 2024.05.16 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/stations_active_alarm_config/update_active_alarm_config"),
        Apidoc\Param(name: 'station_id', type: 'int', require: true, default: 1, desc: '充电站ID'),

        Apidoc\Param(name: "enterprise_wechat_key", type: "string", require: true, desc: "企业微信机器人密钥"),
        Apidoc\Param(name: "email", type: "string", require: true, desc: "邮箱地址(用于接收每日场站告警汇总报告)"),
        Apidoc\Param(name: "sf_is_generate_work_order", type: "int", require: true, desc: "充电枪故障告警 - 是否自动生成工单 1:是 2:否"),
        Apidoc\Param(name: "sf_auto_dispatch_user_id", type: "int", require: true, desc: "充电枪故障告警 - 自动派发给指定工作人员"),

        Apidoc\Param(name: "cso_is_generate_work_order", type: "int", require: true, desc: "充电服务离线告警 - 是否自动生成工单 1:是 2:否"),
        Apidoc\Param(name: "cso_auto_dispatch_user_id", type: "int", require: true, desc: "充电服务离线告警 - 自动派发给指定工作人员"),

        Apidoc\Param(name: "remp_is_generate_work_order", type: "int", require: true, desc: "读取电表功率告警 - 是否自动生成工单 1:是 2:否"),
        Apidoc\Param(name: "remp_auto_dispatch_user_id", type: "int", require: true, desc: "读取电表功率告警 - 自动派发给指定工作人员"),

        Apidoc\Param(name: "po_is_generate_work_order", type: "int", require: true, desc: "充电桩离线告警 - 是否自动生成工单 1:是 2:否"),
        Apidoc\Param(name: "po_auto_dispatch_user_id", type: "int", require: true, desc: "充电桩离线告警 - 自动派发给指定工作人员"),

        Apidoc\Param(name: "ccsr_is_generate_work_order", type: "int", require: true, desc: "能源路由器资源告警 - 是否自动生成工单 1:是 2:否"),
        Apidoc\Param(name: "ccsr_auto_dispatch_user_id", type: "int", require: true, desc: "能源路由器资源告警 - 自动派发给指定工作人员"),

    ]
    public function update_active_alarm_config(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verify_data = v::input($data, VerifyData::station_active_alarm_config([
                'station_id', 'enterprise_wechat_key', 'email',
                'sf_is_generate_work_order', 'sf_auto_dispatch_user_id',
                'cso_is_generate_work_order', 'cso_auto_dispatch_user_id',
                'remp_is_generate_work_order', 'remp_auto_dispatch_user_id',
                'po_is_generate_work_order', 'po_auto_dispatch_user_id',
                'ccsr_is_generate_work_order', 'ccsr_auto_dispatch_user_id',
            ]));
            $verify_data['email'] = $verify_data['email'] ?? '';

            $isExistence = StationsActiveAlarmConfigRepositories::isExistence($verify_data['station_id']);
            if (!$isExistence) {
                throw new RuntimeException('无效场站ID', [], RuntimeException::CodeBusinessException);
            }


            StationsActiveAlarmConfigRepositories::updateConfig($verify_data['station_id'], $verify_data);

        });
    }

}