<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\admin\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\ExceptionLogCollector;
use app\common\lib\order\ExportSpreadsheet;
use app\common\lib\VerifyData;
use app\common\model\Order as OrderModel;
use app\common\model\StationDataAuthority;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use think\helper\Str;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\response\Json;
use Throwable;

#[Apidoc\Title("财务中心/订单管理")]
class Order extends BaseController
{
    use Curd;

    /**
     * @var OrderModel $modelClass
     */

    public function initialize(): void
    {
        $this->modelClass = new OrderModel();
    }


    #[
        Apidoc\Title("获取导出的数据"),
        <PERSON>pidoc\Author("cbj 2024.08.16 新增"),
        Apid<PERSON>\Method("POST"),
        Apidoc\Url("/admin/order/get_export_data"),
        Apidoc\Param(name: "id", type: "string", desc: "交易流水号"),
        Apidoc\Param(name: 'corp_id', type: 'int', desc: '运营商ID'),
        Apidoc\Param(name: 'station_id', type: 'int', desc: '充电站ID'),
        Apidoc\Param(name: 'sequence', type: 'int', desc: '充电枪编号'),
        Apidoc\Param(name: 'phone', type: 'int', desc: '手机号'),
        Apidoc\Param(name: "status", type: "array", desc: "[变更]订单状态 1:下单 3:充电中 5:完成 6:异常结束 7:自动结算 8:预约"),
        Apidoc\Param(name: "start_time", type: "string", desc: "开始时间"),
        Apidoc\Param(name: "end_time", type: "string", desc: "结束时间"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '交易流水号'],
            ['name' => 'type', 'type' => 'int', 'desc' => '[新增]订单类型 1:立即充电 2:预约充电'],
            ['name' => 'sequence', 'type' => 'int', 'desc' => '枪号'],
            ['name' => 'pay_mode', 'type' => 'int', 'desc' => '支付方式：1微信、2支付宝、3零钱、4会员卡'],
            ['name' => 'status', 'type' => 'int', 'desc' => '[变更]订单状态 1:下单 3:充电中 5:完成 6:异常结束 7:自动结算 8:预约'],
            ['name' => 'pay_money', 'type' => 'int', 'desc' => '订单实付金额（分）'],
            ['name' => 'trans_end_time', 'type' => 'string', 'desc' => '充电桩交易结束时间'],
            ['name' => 'electricity_total', 'type' => 'int', 'desc' => '总电量，精确小数点后4位'],
            ['name' => 'vin_code', 'type' => 'string', 'desc' => '电动汽车唯一标识'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '充电站名称'],
            ['name' => 'build_type', 'type' => 'int', 'desc' => '生成类型，1小程序，2后台模拟'],
            ['name' => 'reason_for_stop_text', 'type' => 'string', 'desc' => '停止原因文本'],
            ['name' => 'freeze_status', 'type' => 'int', 'desc' => '冻结金额状态，1冻结，2解冻'],
            ['name' => 'reservation_time', 'type' => 'null|string', 'desc' => '[新增]预约充电时间(非预约充电订单该字段值为null)']
        ]),
        Apidoc\Returned(name: "summary", type: "array", require: true, desc: "统计数据", children: [
            ['name' => 'pay_money', 'type' => 'int', 'desc' => '所有订单的实付金额 - 单位分(小数点后2位)'],
            ['name' => 'electricity_total', 'type' => 'int', 'desc' => '所有订单的总电量 - 精确小数点后4位']
        ]),
    ]
    public function get_export_data(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::get_order_list([
                'id',
                'corp_id',
                'station_id',
                'sequence',
                'phone',
                'status',
                'start_time',
                'end_time',
            ]));

            // 数据权限限制
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $verifyData['corp_id'] = $this->loginUser->corp_id;
            } else if ($this->loginUser->corp_id > 0) {
                $verifyData['corp_id'] = $this->loginUser->corp_id;
                // 查询当前子账号拥有的场站权限
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                if (!empty($verifyData['station_id'])) {
                    // 如果筛选的场站 当前账号不具备权限，那么不能让他查询到数据。
                    if (in_array($verifyData['station_id'], $station_ids) === false) {
                        $verifyData['station_id'] = [];
                    }
                } else {
                    $verifyData['station_id'] = $station_ids;
                }
            }

            $OrderModel = (new OrderModel());

            return [
                'data' => $OrderModel->get_export_data_array($verifyData),
                'summary' => $OrderModel->get_order_summary($verifyData)
            ];
        });
    }

    #[
        Apidoc\Title("获取充电订单列表"),
        Apidoc\Desc('按页返回所有充电订单列表的数据。'),
        Apidoc\Author("cbj 2023.10.7 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Order/get_order_list"),
        Apidoc\Param(name: "id", type: "string", desc: "交易流水号"),
        Apidoc\Param(name: 'corp_id', type: 'int', desc: '运营商ID'),
        Apidoc\Param(name: 'station_id', type: 'int', desc: '充电站ID'),
        Apidoc\Param(name: 'sequence', type: 'int', desc: '充电枪编号'),
        Apidoc\Param(name: 'phone', type: 'int', desc: '手机号'),

        Apidoc\Param(name: "status", type: "array", desc: "[变更]订单状态 1:下单 3:充电中 5:完成 6:异常结束 7:自动结算 8:预约"),
        Apidoc\Param(name: "start_time", type: "string", desc: "开始时间"),
        Apidoc\Param(name: "end_time", type: "string", desc: "结束时间"),

        Apidoc\Param(name: "page", type: "int", require: false, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: false, default: 10, desc: "每页显示"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", require: false, desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '交易流水号'],
            ['name' => 'type', 'type' => 'int', 'desc' => '[新增]订单类型 1:立即充电 2:预约充电'],
            ['name' => 'sequence', 'type' => 'int', 'desc' => '枪号'],
            ['name' => 'pay_mode', 'type' => 'int', 'desc' => '支付方式：1微信、2支付宝、3零钱、4会员卡'],
            ['name' => 'status', 'type' => 'int', 'desc' => '[变更]订单状态 1:下单 3:充电中 5:完成 6:异常结束 7:自动结算 8:预约'],
            ['name' => 'pay_money', 'type' => 'int', 'desc' => '订单实付金额（分）'],
            ['name' => 'trans_end_time', 'type' => 'string', 'desc' => '充电桩交易结束时间'],
            ['name' => 'electricity_total', 'type' => 'int', 'desc' => '总电量，精确小数点后4位'],
            ['name' => 'vin_code', 'type' => 'string', 'desc' => '电动汽车唯一标识'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '充电站名称'],
            ['name' => 'build_type', 'type' => 'int', 'desc' => '生成类型，1小程序，2后台模拟'],
            ['name' => 'reason_for_stop_text', 'type' => 'string', 'desc' => '停止原因文本'],
            ['name' => 'freeze_status', 'type' => 'int', 'desc' => '冻结金额状态，1冻结，2解冻'],
            ['name' => 'reservation_time', 'type' => 'null|string', 'desc' => '[新增]预约充电时间(非预约充电订单该字段值为null)']
        ]),
    ]
    public function get_order_list(): Json
    {
        try {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::get_order_list([
                'id',
                'corp_id',
                'station_id',
                'sequence',
                'phone',
                'status',
                'start_time',
                'end_time',
                'page',
                'limit',
            ]));

            $page = ($verifyData['page'] ?? false) ?: 1;
            $pageSize = ($verifyData['limit'] ?? false) ?: $this->pageSize;

            // 数据权限限制
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $verifyData['corp_id'] = $this->loginUser->corp_id;
            } else if ($this->loginUser->corp_id > 0) {
                $verifyData['corp_id'] = $this->loginUser->corp_id;
                // 查询当前子账号拥有的场站权限
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                if (!empty($verifyData['station_id'])) {
                    // 如果筛选的场站 当前账号不具备权限，那么不能让他查询到数据。
                    if (in_array($verifyData['station_id'], $station_ids) === false) {
                        $verifyData['station_id'] = [];
                    }
                } else {
                    $verifyData['station_id'] = $station_ids;
                }
            }

            $list = (new OrderModel())->get_list_data($verifyData, $page, $pageSize);

            return $this->res_success($list);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("导出电子表格"),
        Apidoc\Author("cbj 2024.01.04 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Order/export_spreadsheet"),
        Apidoc\Param(name: "id", type: "string", desc: "交易流水号"),
        Apidoc\Param(name: 'corp_id', type: 'int', desc: '运营商ID'),
        Apidoc\Param(name: 'station_id', type: 'int', desc: '充电站ID'),
        Apidoc\Param(name: 'sequence', type: 'int', desc: '充电枪编号'),
        Apidoc\Param(name: 'phone', type: 'int', desc: '手机号'),

        Apidoc\Param(name: "status", type: "array", desc: "[变更]订单状态 1:下单 3:充电中 5:完成 6:异常结束 7:自动结算 8:预约"),
        Apidoc\Param(name: "start_time", type: "string", desc: "开始时间"),
        Apidoc\Param(name: "end_time", type: "string", desc: "结束时间"),
        Apidoc\Returned(name: "file_uri", type: "string", desc: "导出的文件Uri"),
    ]
    public function export_spreadsheet(): ?Json
    {
        try {
            $data = $this->request->post();
            // 验证数据
            $data = v::input($data, VerifyData::get_order_list([
                'id',
                'corp_id',
                'station_id',
                'sequence',
                'phone',
                'status',
                'start_time',
                'end_time',
            ]));

            // 数据权限限制
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $data['corp_id'] = $this->loginUser->corp_id;
            } else if ($this->loginUser->corp_id > 0) {
                $data['corp_id'] = $this->loginUser->corp_id;
                // 查询当前子账号拥有的场站权限
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                if (!empty($data['station_id'])) {
                    if (in_array($data['station_id'], $station_ids) === false) {
                        $data['station_id'] = [];
                    }
                } else {
                    $data['station_id'] = $station_ids;
                }
            }

            $file_path = (new ExportSpreadsheet($data, Str::random(10)))->run();

            return $this->res_success([
                'file_uri' => $file_path
            ]);

        } catch (ValidationException $e) {
            ExceptionLogCollector::collect($e);
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("充电订单汇总"),
        Apidoc\Author("cbj 2024.01.04 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Order/get_order_summary"),
        Apidoc\Param(name: "id", type: "string", desc: "交易流水号"),
        Apidoc\Param(name: 'corp_id', type: 'int', desc: '运营商ID'),
        Apidoc\Param(name: 'station_id', type: 'int', desc: '充电站ID'),
        Apidoc\Param(name: 'sequence', type: 'int', desc: '充电枪编号'),
        Apidoc\Param(name: 'phone', type: 'int', desc: '手机号'),

        Apidoc\Param(name: "status", type: "array", desc: "[变更]订单状态 1:下单 3:充电中 5:完成 6:异常结束 7:自动结算 8:预约"),
        Apidoc\Param(name: "start_time", type: "string", desc: "开始时间"),
        Apidoc\Param(name: "end_time", type: "string", desc: "结束时间"),

        Apidoc\Returned(name: "pay_money", type: "int", require: true, desc: "总的金额(单位:分)"),
        Apidoc\Returned(name: "electricity_total", type: "int", require: true, desc: "总的总电量(单位:kW | 精确小数点后4位)"),
    ]
    public function get_order_summary(): Json
    {
        try {
            $data = $this->request->post();
            // 验证数据
            $data = v::input($data, VerifyData::get_order_list([
                'id',
                'corp_id',
                'station_id',
                'sequence',
                'phone',
                'status',
                'start_time',
                'end_time',
            ]));
            // 数据权限限制
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $data['corp_id'] = $this->loginUser->corp_id;
            } else if ($this->loginUser->corp_id > 0) {
                $data['corp_id'] = $this->loginUser->corp_id;
                // 查询当前子账号拥有的场站权限
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                if (!empty($data['station_id'])) {
                    if (in_array($data['station_id'], $station_ids) === false) {
                        $data['station_id'] = [];
                    }
                } else {
                    $data['station_id'] = $station_ids;
                }
            }

            $list = (new OrderModel())->get_order_summary($data);

            return $this->res_success($list);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取充电订单详情"),
        Apidoc\Desc('返回指定订单的详细数据。'),
        Apidoc\Author("cbj 2023-10-8 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Order/get_order_info"),
        Apidoc\Param(name: "id", type: "string", require: true, desc: "交易流水号(长度必须为32位)"),

        Apidoc\Returned(name: "id", type: "string", desc: "交易流水号"),
        Apidoc\Returned(name: "type", type: "int", desc: "[变更]订单类型 1:立即充电 2:预约充电"),
        Apidoc\Returned(name: "pay_mode", type: "int", desc: "支付方式：1微信、2支付宝、3零钱、4会员卡"),
        Apidoc\Returned(name: "status", type: "int", desc: "[变更]订单状态 1:下单 3:充电中 5:完成 6:异常结束 7:自动结算 8:预约"),
        Apidoc\Returned(name: "money", type: "int", desc: "订单应付金额(单位:分)"),
        Apidoc\Returned(name: "pay_money", type: "int", desc: "订单实付金额(单位:分)"),
        Apidoc\Returned(name: "coupon_money", type: "int", desc: "订单优惠金额(单位:分)"),
        Apidoc\Returned(name: "freeze_money", type: "int", desc: "订单冻结金额(单位:分)"),
        Apidoc\Returned(name: "currency", type: "int", desc: "币种：1:CNY,2:USD"),
        Apidoc\Returned(name: "card_id", type: "int", desc: "会员卡id"),
        Apidoc\Returned(name: "user_id", type: "int", desc: "订单创建用户id"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "订单创建时间"),
        Apidoc\Returned(name: "trans_start_time", type: "string", desc: "充电桩交易开始时间"),
        Apidoc\Returned(name: "trans_end_time", type: "string", desc: "充电桩交易结束时间"),
        Apidoc\Returned(name: "electricity_price", type: "int", desc: "充电费用(单位:分)"),
        Apidoc\Returned(name: "ser_price", type: "int", desc: "服务费用(单位:分)"),
        Apidoc\Returned(name: "refund", type: "int", desc: "退款(单位:分)"),
        Apidoc\Returned(name: "stations_name", type: "string", desc: "场站名称"),
        Apidoc\Returned(name: "shot_id", type: "int", desc: "枪id"),
        Apidoc\Returned(name: "electricity_total", type: "int", desc: "总电量，精确小数点后4位"),
        Apidoc\Returned(name: "is_collect", type: "bool", desc: "是否收藏"),
        Apidoc\Returned(name: "abnormal_alarm_node", type: "int", desc: "异常告警节点 0:无 1:充电管理 2:调度算法 3:驱动服务 4:充电桩 5:中转服务 6:业务平台"),
        Apidoc\Returned(name: "reservation_time", type: "string|null", desc: "[新增]预约充电(当订单类型不是预约充电时，该字段值为null)"),

    ]
    public function get_order_info(): Json
    {
        try {
            $data = $this->request->post();
            // 验证数据
            $data = v::input($data, VerifyData::get_order_info([
                'id',
            ]));

            $field = [
                'a.id',
                'a.type',
                'a.pay_mode',
                'a.status',
                'a.money',
                'a.pay_money',
                'a.coupon_money',
                'a.freeze_money',
                'a.currency',
                'a.card_id',
                'a.user_id',
                'a.create_time',
                'a.trans_start_time',
                'a.trans_end_time',
                'a.electricity_price',
                'a.ser_price',
                'a.freeze_money-a.pay_money as refund',
                'b.name as stations_name',
                'a.shot_id',
                'a.electricity_total',
                'a.station_id',
                'g.id as is_collect',
                'a.abnormal_alarm_node',
                'a.reservation_time'
            ];

            // 数据权限限制
            $where = [];
            $where[] = ['a.id', '=', $data['id']];
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $where[] = ['a.corp_id', '=', $this->loginUser->corp_id];
            } else if ($this->loginUser->corp_id > 0) {
                $where[] = ['a.corp_id', '=', $this->loginUser->corp_id];
                // 查询当前子账号拥有的场站权限
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                $where[] = ['a.station_id', '=', $station_ids];
            }


            $info = $this->modelClass->alias('a')
                ->append(['price', 'ser_price', 'refund'])
                ->join('stations b', 'a.station_id = b.id')
                ->leftJoin('users_collect_stations g', 'a.station_id = g.station_id')
                ->where($where)
                ->field($field)
                ->withAttr('is_collect', function ($value) {
                    if ($value) return true;
                    return false;
                })
                ->find();
            if (!$info) return $this->res_error([], '订单不存在');
            $info['electricity_price'] = ceil($info['electricity_price'] / 100);
            $info['ser_price'] = ceil($info['ser_price'] / 100);
            return $this->res_success($info);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    // 订单修复
    #[
        Apidoc\Title("订单修复(暂停使用)"),
        Apidoc\Author("cbj 2023.10.8 新增, cbj 2023.11.2 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Order/order_repair"),
        Apidoc\Param(name: "id", type: "string", desc: "交易流水号(长度必须为32位)"),
        Apidoc\Param(name: 'update_status', type: 'int', desc: '修改后的订单状态'),
        Apidoc\Param(name: 'deduction_balance', type: 'int', desc: '扣除余额(单位:分)'),
        Apidoc\Param(name: 'electricity', type: 'int', desc: '充电量(单位:Kw.h)'),
    ]
    public function order_repair(): Json
    {
        return $this->res_error('暂停使用');

//        try {
//            $data = $this->request->post();
//            // 验证数据
//            $data = v::input($data, VerifyData::order_repair([
//                'id',
//                'update_status',
//                'deduction_balance',
//                'electricity'
//            ]));
//            // 除以10000后单位为：Kw.h
//            $data['electricity'] *= 10000;
//
//            // 查询订单数据
//            $order_data = $this->modelClass
//                ->where('id', $data['id'])
//                ->field('corp_id, station_id, status, freeze_money, user_id, freeze_status, trans_start_time')
//                ->find();
//            if (!$order_data) return $this->res_error([], '订单不存在');
//
//            if ($data['deduction_balance'] > $order_data['freeze_money']) {
//                return $this->res_error([], '扣除余额不能超过订单冻结金额');
//            }
//
//            Db::startTrans();
//            $end_time = time();
//            $charge_duration = ($end_time - strtotime($data['trans_start_time']));
//            $UnfreezeUserBalanceAndPayOrder = new UnfreezeUserBalanceAndPayOrder(
//                $order_data['user_id']
//                , $data['deduction_balance']
//                , $data['id']
//                , $order_data['freeze_money']
//                , $order_data['freeze_status']
//                , $data['electricity']
//                , $end_time
//                , $charge_duration
//            );
//            $UnfreezeUserBalanceAndPayOrder->set_order_status($data['update_status'])->run();
//
//            event('ChargeAbnormalEnd', new ChargeAbnormalEndEvent([
//                'abnormal_reason' => ChargeAbnormalEndEvent::ABNORMAL_REASON_UNKNOWN,
//                'order_id' => $data['id'],
//                'corp_id' => $order_data['corp_id'],
//                'station_id' => $order_data['station_id'],
//                'pay_money' => $data['deduction_balance'],
//                'electricity_total' => $data['electricity'],
//                'charge_duration' => $charge_duration
//            ]));
//
//            Db::commit();
//            return $this->res_success();
//
//        } catch (ValidationException $e) {
//            Db::rollback();
//            return $this->res_error([], $e->getMessage());
//        } catch (Throwable $e) {
//            Db::rollback();
//            if ($e->getCode() === UnfreezeUserBalanceAndPayOrder::BusinessCode) {
//                return $this->res_error([], $e->getMessage());
//            }
//            return $this->res_error([], '处理异常：' . $e->getMessage());
//        }
    }


    #[
        Apidoc\Title("充电订单选项"),
        Apidoc\Author("cbj 2024.09.12 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/order/options"),
        Apidoc\Param(name: "filter_piles_id", type: "int", require: false, desc: "筛选充电桩ID"),
        Apidoc\Returned(name: "options", type: "array", desc: "选项", children: [
            ['name' => 'name', 'type' => 'string', 'desc' => '订单ID'],
            ['name' => 'id', 'type' => 'string', 'desc' => '订单ID'],
        ]),
    ]
    public function options(): ?Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            // 验证数据
            $verify_data = v::input($data, VerifyData::order([
                'filter_piles_id',
            ]));

            $where = [];
            if ($this->loginUser->corp_id > 0) {
                $where[] = ['corp_id', '=', $this->loginUser->corp_id];
            }
            if (!empty($verify_data['filter_piles_id'])) {
                $where[] = ['piles_id', '=', $verify_data['filter_piles_id']];
            }

            $options = (new OrderModel())->field(['id as name', 'id'])->where($where)->select()->toArray();
            return [
                'options' => $options
            ];
        });
    }

    #[
        Apidoc\Title("获取订单状态信息"),
        Apidoc\Author("cbj 2024.09.20 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/order/get_order_status_info"),
        Apidoc\Param(name: "order_id", type: "string", require: true, desc: "订单ID"),
        Apidoc\Returned(name: "status", type: "int", desc: "订单状态 1:下单 3:充电中 5:完成 6:异常 7:强制结算 8:预约"),
        Apidoc\Returned(name: "reason_for_stop_text", type: "string", desc: "停止原因文本"),
    ]
    public function get_order_status_info(): ?Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verify_data = v::input($data, VerifyData::order([
                'order_id',
            ]));

            $data = app(OrderModel::class)->getOrder($verify_data['order_id'], ['status', 'reason_for_stop_text']);
            if (empty($data)) {
                throw new RuntimeException('无效订单ID', [], RuntimeException::CodeBusinessException);
            }

            return $data;
        });
    }

}