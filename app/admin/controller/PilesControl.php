<?php
/** @noinspection PhpUnused */

namespace app\admin\controller;

use app\common\lib\Help;
use app\common\lib\VerifyData;
use app\common\model\StationDataAuthority;
use app\common\traits\Curd;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\BasePackage;
use hg\apidoc\annotation as Apidoc;
use app\common\model\Piles as PilesModel;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\response\Json;
use Throwable;


#[Apidoc\Title("运营中心/充电桩控制")]
class PilesControl extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new PilesModel();
    }

    #[
        Apidoc\Title("充电桩控制列表"),
        Apidoc\Author("lwj 2023.8.10 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/PilesControl/index"),
        Apidoc\Param(name: "corp_id", type: "int", require: true, desc: "运营商编号"),
        Apidoc\Param(name: "station_id", type: "string", require: true, desc: "场站id"),
        Apidoc\Param(name: "id", type: "string", require: true, desc: "充电桩编号，14位数字"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "充电桩名称"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电桩编号，14位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '充电站名称'],
            ['name' => 'ac_dc', 'type' => 'int', 'desc' => '交直流：0表示直流桩，1表示交流桩'],
            ['name' => 'type', 'type' => 'int', 'desc' => '桩类型：1-慢充，2-快充，3超快充'],
            ['name' => 'shot_num', 'type' => 'int', 'desc' => '枪数量'],
        ]),
    ]
    public function index(): Json
    {
        $data = $this->request->post();
        $page = ($data['page'] ?? false) ?: 1;
        $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
        $order_name = ($data['order_name'] ?? false) ?: 'a.id';
        $order_type = ($data['order_type'] ?? false) ?: 'desc';

        // 数据权限限制
        if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
            $data['corp_id'] = $this->loginUser->corp_id;
        } else if ($this->loginUser->corp_id > 0) {
            $data['corp_id'] = $this->loginUser->corp_id;
            // 查询当前子账号拥有的场站权限
            $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
            if (!empty($data['station_id'])) {
                if (in_array($data['station_id'], $station_ids) === false) {
                    $data['station_id'] = [];
                }
            } else {
                $data['station_id'] = $station_ids;
            }
        }

        // 搜索框搜索
        $where = function ($query) use ($data) {
            if (isset($data['id']) && $data['id']) {
                $query->where('a.id', $data['id']);
            }
            if (isset($data['name']) && $data['name']) {
                $query->where('a.name', 'like', '%' . $data['name'] . '%');
            }
            if (isset($data['corp_id']) && $data['corp_id']) {
                $query->where('a.corp_id', $data['corp_id']);
            }
            if (isset($data['station_id'])) {
                if (is_array($data['station_id'])) {
                    $query->where('a.station_id', 'in', $data['station_id']);
                } else if (!empty($data['station_id'])) {
                    $query->where('a.station_id', '=', $data['station_id']);
                }
            }
            return $query;
        };

        $list = $this->modelClass->alias('a')
            ->join('corp b', 'a.corp_id = b.id')
            ->join('stations c', 'a.station_id = c.id')
            ->field('a.id,a.name,a.ac_dc,a.type,a.shot_num,b.name as corp_name,c.name as station_name')
            ->where($where)
            ->order($order_name, $order_type)
            ->withAttr('shot_num', function ($value, $data) {
                $shotsModel = new \app\common\model\Shots();
                return $shotsModel->where('piles_id', '=', $data['id'])->count();
            })
            ->paginate(['list_rows' => $pageSize, 'page' => $page]);

        return $this->res_success($list);
    }

    #[
        Apidoc\Title("获取充电桩排序信息"),
        Apidoc\Author("lwj 2023.8.10 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/PilesControl/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        $res = [
            'order_name' => 'a.id',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'a.id', 'label' => '充电桩编号'],
                ['value' => 'a.name', 'label' => '充电桩名称'],
                ['value' => 'a.corp_id', 'label' => '运营商'],
                ['value' => 'a.station_id', 'label' => '充电站'],
                ['value' => 'a.ac_dc', 'label' => '交直流'],
                ['value' => 'a.type', 'label' => '桩类型'],
                ['value' => 'a.shot_num', 'label' => '枪数量'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("下发费率"),
        Apidoc\Author("lwj 2023.8.15 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/PilesControl/issue_tariff_group"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "充电桩编号，14位数字"),
    ]
    public function issue_tariff_group(): Json
    {
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::piles([
                'id',
            ]));

            // 数据权限限制
            $where = [];
            $where[] = ['a.id', '=', $verify_data['id']];
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $where[] = ['a.corp_id', '=', $this->loginUser->corp_id];
            } else if ($this->loginUser->corp_id > 0) {
                $where[] = ['a.corp_id', '=', $this->loginUser->corp_id];
                // 查询当前子账号拥有的场站权限
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                $where[] = ['a.station_id', 'in', $station_ids];
            }

            $count = $this->modelClass->alias('a')
                ->where($where)
                ->count();
            if ($count === 0) {
                return $this->res_error([], '无效充电桩编号');
            }

            $tariff_group = $this->modelClass->alias('a')
                ->join('stations_extra_info sei', 'a.station_id = sei.id')
                ->join('tariff_group c', 'sei.tariff_group_id = c.id')
                ->field('a.id as piles_id,c.id as billing_mode_id,c.sharp_fee,c.peak_fee,c.flat_fee,c.valley_fee,
                c.sharp_ser_fee,c.peak_ser_fee,c.flat_ser_fee,c.valley_ser_fee,c.loss_rate,c.period_codes')
                ->where($where)
                ->find()
                ->toArray();
//            return $this->res_success($tariff_group);
            if (!$tariff_group) return $this->res_error([], '请先绑定计费模型');
//            $user_data = [
//                'token' => $this->loginUser->token,
//                'type' => '后台',
//            ];

            Help::sendToTransferService(BasePackage::TypeBillingModelSetting, $tariff_group);

            return $this->res_success();

//            return SendGetApi::billing_model_setting($tariff_group, $user_data);
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

//    #[
//        Apidoc\Title("充电桩工作参数设置"),
//        Apidoc\Author("lwj 2023.8.21 新增"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/admin/PilesControl/charging_station_parameter_setting"),
//        Apidoc\Param(name: "piles_id", type: "int", require: true, desc: "充电桩编号，14位数字"),
//        Apidoc\Param(name: "allow_work", type: "int", require: true, desc: "是否允许工作"),
//        Apidoc\Param(name: "maximum_allowed_output_power", type: "int", require: true, desc: "充电桩最大允许输出功率，30到100"),
//    ]
//    public function charging_station_parameter_setting(): Json
//    {
//        try {
//            $user=$this->request->token_user;
//            $data = $this->request->post();
//            $verify_data = v::input($data, VerifyData::chargingPile([
//                'id',
//            ]));
//            $tariff_group = $this->modelClass->alias('a')
//                ->join('stations b', 'a.station_id = b.id')
//                ->join('tariff_group c', 'b.tariff_group_id = c.id')
//                ->field('a.id as piles_id,c.id as billing_mode_id,c.sharp_fee,c.peak_fee,c.flat_fee,c.valley_fee,c.sharp_ser_fee,c.peak_ser_fee,c.flat_ser_fee,c.valley_ser_fee,c.loss_rate,c.period_codes')
//                ->where('a.id',$verify_data['id'])
//                ->withAttr('piles_id', function($value) {
//                    return hex_auto_fill_0(strval($value),14);
//                })
//                ->withAttr('billing_mode_id', function($value) {
//                    return hex_auto_fill_0(strval($value),4);
//                })
//                ->find()
//                ->toArray();
////            return $this->res_success($tariff_group);
//            if(!$tariff_group) return $this->res_error([], '请先绑定计费模型');
//            $res= SendGetApi::billing_model_setting($tariff_group,$user['token']);
//            if($res) return $res;
//            return $this->res_error([], '下发失败');
//        } catch (ValidationException $e) {
//            return $this->res_error([], $e->getMessage());
//        } catch (Throwable $e) {
//            return $this->res_error([], '处理异常：'.$e->getMessage());
//        }
//    }


}