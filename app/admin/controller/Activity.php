<?php

namespace app\admin\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\ActivityStations;
use app\common\model\ActivityTime;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\response\Json;

#[Apidoc\Title("活动管理")]
class Activity extends BaseController
{
    #[
        Apidoc\Title("添加活动"),
        Apidoc\Desc("目前先限制活动只允许 corp_id > 0 并且 pid == 0 的用户创建"),
        Apidoc\Author("cbj 2024.08.07 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/activity/add"),
        Apidoc\Param(name: "title", type: "string", require: true, desc: "活动标题(最多20个字符)"),
        Apidoc\Param(name: "describe", type: "string", require: true, desc: "活动介绍(最多250个字符)"),
        Apidoc\Param(name: "discount", type: "int", require: true, desc: "优惠折扣(精确到小数点后1位) | 范围: 0 ~ 100 (98表示9.8折)"),
        Apidoc\Param(name: "station_id", type: "int", require: true, desc: "指定参与活动的场站ID"),
        Apidoc\Param(name: "start_time", type: "string", require: true, desc: "活动的开始时间(格式:YYYY-mm-dd HH:ii:ss)"),
        Apidoc\Param(name: "end_time", type: "string", require: true, desc: "活动的结束时间(格式:YYYY-mm-dd HH:ii:ss)"),
        Apidoc\Returned(name: "activity_id", type: "int", require: true, desc: "新建的活动ID"),
    ]
    public function add(): Json
    {
        return $this->openExceptionCatch(function () {

            if ($this->loginUser->corp_id <= 0 || $this->loginUser->pid > 0) {
                throw new RuntimeException('没有权限', [], RuntimeException::CodeBusinessException);
            }

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::activity([
                'title', 'describe', 'discount', 'station_id',
                'start_time', 'end_time'
            ]));

            $activity_id = (new \app\common\model\Activity())->addActivity(
                $this->loginUser,
                $verify_data['title'],
                $verify_data['describe'],
                $verify_data['discount']
            );
            (new ActivityStations())->addStations($activity_id, [$verify_data['station_id']]);
            (new ActivityTime())->addTimes($activity_id, [[
                'start_time' => $verify_data['start_time'],
                'end_time' => $verify_data['end_time']]
            ]);

            return ['activity_id' => $activity_id];
        }, true);
    }


    #[
        Apidoc\Title("更新活动"),
        Apidoc\Desc("目前先限制活动只允许 corp_id > 0 并且 pid == 0 的用户更新"),
        Apidoc\Author("cbj 2024.08.07 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/activity/update"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "活动ID"),
        Apidoc\Param(name: "title", type: "string", require: true, desc: "活动标题(最多20个字符)"),
        Apidoc\Param(name: "describe", type: "string", require: true, desc: "活动介绍(最多250个字符)"),
        Apidoc\Param(name: "discount", type: "int", require: true, desc: "优惠折扣(精确到小数点后1位) | 范围: 0 ~ 100 (98表示9.8折)"),
        Apidoc\Param(name: "station_id", type: "int", require: true, desc: "指定参与活动的场站ID"),
        Apidoc\Param(name: "start_time", type: "string", require: true, desc: "活动的开始时间(格式:YYYY-mm-dd HH:ii:ss)"),
        Apidoc\Param(name: "end_time", type: "string", require: true, desc: "活动的结束时间(格式:YYYY-mm-dd HH:ii:ss)"),
        Apidoc\Param(name: "status", type: "int", require: true, desc: "活动状态 1:开启 0:关闭(可以理解为活动不举行了)"),
    ]
    public function update(): Json
    {
        return $this->openExceptionCatch(function () {

            if ($this->loginUser->corp_id <= 0 || $this->loginUser->pid > 0) {
                throw new RuntimeException('没有权限', [], RuntimeException::CodeBusinessException);
            }

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::activity([
                'id', 'title', 'describe', 'discount',
                'station_id', 'start_time', 'end_time', 'status'
            ]));

            $activityModel = (new \app\common\model\Activity());
            $activityModel->updateActivity(
                $verify_data['id'],
                $verify_data['title'],
                $verify_data['describe'],
                $verify_data['discount'],
                $verify_data['status']
            );


            $activityStationsModel = new ActivityStations();
            $activityStationsModel->removeActivityRelationAllStations($verify_data['id']);
            $activityStationsModel->addStations($verify_data['id'], [$verify_data['station_id']]);
            $activityTimeModel = new ActivityTime();
            $activityTimeModel->removeActivityRelationAllTime($verify_data['id']);
            $activityTimeModel->addTimes($verify_data['id'], [[
                'start_time' => $verify_data['start_time'],
                'end_time' => $verify_data['end_time']]
            ]);

        }, true);
    }

    #[
        Apidoc\Title("删除活动"),
        Apidoc\Desc("目前先限制活动只允许 corp_id > 0 并且 pid == 0 的用户更新"),
        Apidoc\Author("cbj 2024.08.08 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/activity/delete"),
        Apidoc\Param(name: "activity_id", type: "int", require: true, desc: "活动ID"),
    ]
    public function delete(): Json
    {
        return $this->openExceptionCatch(function () {

            if ($this->loginUser->corp_id <= 0 || $this->loginUser->pid > 0) {
                throw new RuntimeException('没有权限', [], RuntimeException::CodeBusinessException);
            }

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::activity([
                'activity_id',
            ]));

            (new \app\common\model\Activity())->removeActivity($verify_data['activity_id']);

            $activityStationsModel = new ActivityStations();
            $activityStationsModel->removeActivityRelationAllStations($verify_data['activity_id']);
            $activityTimeModel = new ActivityTime();
            $activityTimeModel->removeActivityRelationAllTime($verify_data['activity_id']);

        }, true);
    }

    #[
        Apidoc\Title("活动列表"),
        Apidoc\Author("cbj 2024.08.08 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/activity/list"),
        Apidoc\Param(name: "filter_title", type: "string", require: false, desc: "筛选活动标题(模糊查询)"),
        Apidoc\Param(name: "filter_station_id", type: "int", require: false, desc: "筛选场站ID"),
        Apidoc\Param(name: "filter_status", type: "int", require: false, desc: "筛选活动状态 1:开启 0:关闭"),
        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "显示条数"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'require' => true, 'desc' => 'ID'],
            ['name' => 'title', 'type' => 'string', 'require' => true, 'desc' => '标题'],
            ['name' => 'describe', 'type' => 'string', 'require' => true, 'desc' => '介绍'],
            ['name' => 'discount', 'type' => 'int', 'require' => true, 'desc' => '优惠折扣(精确到小数点后1位) | 范围: 0 ~ 100 (98表示9.8折)'],
            ['name' => 'status', 'type' => 'int', 'require' => true, 'desc' => '活动状态 1:开启 0:关闭'],
            ['name' => 'create_username', 'type' => 'string', 'require' => true, 'desc' => '创建活动的用户的名称'],
            ['name' => 'create_time', 'type' => 'string', 'require' => true, 'desc' => '创建时间'],
            ['name' => 'update_time', 'type' => 'string', 'require' => true, 'desc' => '更新时间'],
            ['name' => 'start_time', 'type' => 'string', 'require' => true, 'desc' => '活动开始时间'],
            ['name' => 'end_time', 'type' => 'string', 'require' => true, 'desc' => '活动结束时间'],
            ['name' => 'station_name', 'type' => 'string', 'require' => true, 'desc' => '场站名称']
        ]),
    ]
    public function list(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::activity([
                'filter_title', 'filter_status', 'filter_station_id', 'page', 'limit', 'order_name', 'order_type'
            ]));

            if ($this->loginUser->corp_id > 0) {
                $verify_data['filter_corp_id'] = $this->loginUser->corp_id;
            }

            return (new \app\common\model\Activity())->getListData(
                $verify_data, $verify_data['page'], $verify_data['limit'],
                $verify_data['order_name'], $verify_data['order_type']
            );

        }, true);
    }

    #[
        Apidoc\Title("获取排序信息"),
        Apidoc\Author("cbj 2024.08.08 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/activity/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        $res = [
            'order_name' => 'a.create_time',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'a.create_time', 'label' => '创建时间'],
                ['value' => 'at.start_time', 'label' => '活动开始时间'],
                ['value' => 'at.end_time', 'label' => '活动结束时间'],
                ['value' => 'a.id', 'label' => '活动ID'],
            ],
        ];
        return $this->res_success($res);
    }


    #[
        Apidoc\Title("活动详情"),
        Apidoc\Author("cbj 2024.08.08 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/activity/info"),
        Apidoc\Param(name: "activity_id", type: "int", require: true, desc: "活动ID"),
        Apidoc\Returned(name: "id", type: "int", require: true, desc: "活动ID"),
        Apidoc\Returned(name: "title", type: "string", require: true, desc: "活动标题"),
        Apidoc\Returned(name: "describe", type: "string", require: true, desc: "活动介绍"),
        Apidoc\Returned(name: "discount", type: "int", require: true, desc: "优惠折扣(精确到小数点后1位) | 范围: 0 ~ 100 (98表示9.8折)"),
        Apidoc\Returned(name: "status", type: "int", require: true, desc: "活动状态 1:开启 0:关闭"),
        Apidoc\Returned(name: "create_user_id", type: "int", require: true, desc: "创建活动的用户的ID"),
        Apidoc\Returned(name: "create_username", type: "string", require: true, desc: "创建活动的用户的名称"),
        Apidoc\Returned(name: "create_time", type: "string", require: true, desc: "创建时间"),
        Apidoc\Returned(name: "update_time", type: "string", require: true, desc: "更新时间"),
        Apidoc\Returned(name: "start_time", type: "string", require: true, desc: "活动开始时间"),
        Apidoc\Returned(name: "end_time", type: "string", require: true, desc: "活动结束时间"),
        Apidoc\Returned(name: "station_name", type: "string", require: true, desc: "场站名称"),
        Apidoc\Returned(name: "station_id", type: "int", require: true, desc: "场站ID"),
    ]
    public function info(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::activity([
                'activity_id'
            ]));

            if ($this->loginUser->corp_id > 0) {
                $corp_id = $this->loginUser->corp_id;
            } else {
                $corp_id = null;
            }

            return (new \app\common\model\Activity())->getInfoData(
                $verify_data['activity_id'], $corp_id
            );
        });
    }


}