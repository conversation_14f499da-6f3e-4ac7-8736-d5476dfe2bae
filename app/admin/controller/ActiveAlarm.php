<?php
/** @noinspection PhpUnused */

namespace app\admin\controller;

use app\common\lib\VerifyData;
use app\common\repositories\ActiveAlarmConfig;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\response\Json;


#[Apidoc\Title("主动告警管理")]
class ActiveAlarm extends BaseController
{
    #[
        Apidoc\Title("停止原因选项"),
        Apidoc\Author("cbj 2024.02.22 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/active_alarm/reason_for_stop_options"),
        Apidoc\Returned(name: "options", type: "array", require: true, desc: "停止原因选项", children: [
            ['name' => 'code', 'type' => 'int', 'desc' => '停止原因代码', 'require' => true],
            ['name' => 'msg', 'type' => 'string', 'desc' => '停止原因描述', 'require' => true],
        ]),
    ]
    public function reason_for_stop_options(): Json
    {
        $list = [
            0x40 => "充电完成，APP远程停止",
            0x41 => "充电完成，SOC达到100%",
            0x42 => "充电完成，充电电量满足设定条件",
            0x43 => "充电完成，充电金额满足设定条件",
            0x44 => "充电完成，充电时间满足设定条件",
            0x45 => "充电完成，手动停止充电",
            0x46 => "充电完成，其他方式（预留）",
            0x47 => "充电完成，其他方式（预留）",
            0x48 => "充电完成，其他方式（预留）",
            0x49 => "充电完成，其他方式（预留）",
            0x4A => "充电启动失败，充电桩控制系统故障(需要重启或自动恢复)",
            0x4B => "充电启动失败，控制导引断开",
            0x4C => "充电启动失败，断路器跳位",
            0x4D => "充电启动失败，电表通信中断",
            0x4E => "充电启动失败，余额不足",
            0x4F => "充电启动失败，充电模块故障",
            0x50 => "充电启动失败，急停开入",
            0x51 => "充电启动失败，防雷器异常",
            0x52 => "充电启动失败，BMS未就绪",
            0x53 => "充电启动失败，温度异常",
            0x54 => "充电启动失败，电池反接故障",
            0x55 => "充电启动失败，电子锁异常",
            0x56 => "充电启动失败，合闸失败",
            0x57 => "充电启动失败，绝缘异常",
            0x58 => "充电启动失败，预留",
            0x59 => "充电启动失败，接收 BMS 握手报文 BHM 超时",
            0x5A => "充电启动失败，接收 BMS 和车辆的辨识报文超时 BRM",
            0x5B => "充电启动失败，接收电池充电参数报文超时 BCP",
            0x5C => "充电启动失败，接收 BMS 完成充电准备报文超时 BRO AA",
            0x5D => "充电启动失败，接收电池充电总状态报文超时 BCS",
            0x5E => "充电启动失败，接收电池充电要求报文超时 BCL",
            0x5F => "充电启动失败，接收电池充电电流报文超时 BCC",
            0x60 => "充电启动失败，GB2015 电池在 BHM 阶段有电压不允许充电",
            0x61 => "充电启动失败，GB2015 辨识阶段在 BRO_AA 时候电池实际电压与 BCP 报文电池电压差距大于 5%",
            0x62 => "充电启动失败，B2015 充电机在预充电阶段从 BRO_AA 变成 BRO_00 状态",
            0x63 => "充电启动失败，接收主机配置报文超时",
            0x64 => "充电启动失败，充电机未准备就绪,我们没有回 CRO AA，对应老国标",
            0x65 => "充电启动失败，（其他原因）预留",
            0x66 => "充电启动失败，（其他原因）预留",
            0x67 => "充电启动失败，（其他原因）预留",
            0x68 => "充电启动失败，（其他原因）预留",
            0x69 => "充电启动失败，（其他原因）预留",
            0x6A => "充电异常中止，系统闭锁",
            0x6B => "充电异常中止，导引断开",
            0x6C => "充电异常中止，断路器跳位",
            0x6D => "充电异常中止，电表通信中断",
            0x6E => "充电异常中止，余额不足",
            0x6F => "充电异常中止，交流保护动作",
            0x70 => "充电异常中止，直流保护动作",
            0x71 => "充电异常中止，充电模块故障",
            0x72 => "充电异常中止，急停开入",
            0x73 => "充电异常中止，防雷器异常",
            0x74 => "充电异常中止，温度异常",
            0x75 => "充电异常中止，输出异常",
            0x76 => "充电异常中止，充电无流",
            0x77 => "充电异常中止，电子锁异常",
            0x78 => "充电异常中止，预留",
            0x79 => "充电异常中止，总充电电压异常",
            0x7A => "充电异常中止，总充电电流异常",
            0x7B => "充电异常中止，单体充电电压异常",
            0x7C => "充电异常中止，电池组过温",
            0x7D => "充电异常中止，最高单体充电电压异常",
            0x7E => "充电异常中止，最高电池组过温",
            0x7F => "充电异常中止，BMV 单体充电电压异常",
            0x80 => "充电异常中止，BMT 电池组过温",
            0x81 => "充电异常中止，电池状态异常停止充电",
            0x82 => "充电异常中止，车辆发报文禁止充电",
            0x83 => "充电异常中止，充电桩断电",
            0x84 => "充电异常中止，接收电池充电总状态报文超时",
            0x85 => "充电异常中止，接收电池充电要求报文超时",
            0x86 => "充电异常中止，接收电池状态信息报文超时",
            0x87 => "充电异常中止，接收 BMS 中止充电报文超时",
            0x88 => "充电异常中止，接收 BMS 充电统计报文超时",
            0x89 => "充电异常中止，接收对侧 CCS 报文超时",
            0x8A => "充电异常中止，（其他原因）预留",
            0x8B => "充电异常中止，（其他原因）预留",
            0x8C => "充电异常中止，（其他原因）预留",
            0x8D => "充电异常中止，（其他原因）预留",
            0x8E => "充电异常中止，（其他原因）预留",
            0x8F => "充电异常中止，（其他原因）预留",
            0x90 => "未知原因停止",
            0x100 => '调度算法结束充电失败',
            0x101 => '下调功率失败',
        ];
        $options = [];
        foreach ($list as $code => $msg) {
            $options[] = ['code' => $code, 'msg' => $msg];
        }
        return $this->res_success(['options' => $options]);
    }

    #[
        Apidoc\Title("获取主动告警配置"),
        Apidoc\Author("cbj 2024.02.22 新增"),
        Apidoc\Method("POST"),
        Apidoc\Desc("
        - 充电枪故障告警 - 充电停止原因白名单：应用于交易记录上报，当停止原因不存在于白名单时，等待检测脚本主动告警；\r\n
        - 能源路由器资源告警 - CPU使用率警报值(单位:百分比)：当能源路由器上报的CPU使用率高于该配置项时，等待检测脚本主动告警；\r\n
        - 能源路由器资源告警 - 剩余内存警报值(单位:MB)：当能源路由器上报的剩余可用内存低于该配置项时，等待检测脚本主动告警；\r\n
        - 能源路由器资源告警 - 剩余磁盘警报值(单位:MB)：当能源路由器上报的剩余可用磁盘低于该配置项时，等待检测脚本主动告警；
        "),
        Apidoc\Url("/admin/active_alarm/get_config"),
        Apidoc\Returned(name: "sf_reason_for_stop_white_list", type: "array", desc: "充电枪故障告警 - 充电停止原因白名单(多个以#分隔)"),
        Apidoc\Returned(name: "ccsr_cpu_alarm_value", type: "int", desc: "能源路由器资源告警 - CPU使用率警报值(单位:百分比)"),
        Apidoc\Returned(name: "ccsr_memory_alarm_value", type: "int", desc: "能源路由器资源告警 - 剩余内存警报值(单位:MB)"),
        Apidoc\Returned(name: "ccsr_disk_alarm_value", type: "int", desc: "能源路由器资源告警 - 剩余磁盘警报值(单位:MB)"),
        Apidoc\Returned(name: "update_time", type: "string", desc: "更新时间"),
    ]
    public function get_config(): Json
    {
        return $this->openExceptionCatch(function () {
            return ActiveAlarmConfig::find()->toArray();
        });
    }

    #[
        Apidoc\Title("更新主动告警配置"),
        Apidoc\Author("cbj 2024.02.22 新增"),
        Apidoc\Desc("
        - 充电枪故障告警 - 充电停止原因白名单：应用于交易记录上报，当停止原因不存在于白名单时，等待检测脚本主动告警；\r\n
        - 能源路由器资源告警 - CPU使用率警报值(单位:百分比)：当能源路由器上报的CPU使用率高于该配置项时，等待检测脚本主动告警；\r\n
        - 能源路由器资源告警 - 剩余内存警报值(单位:MB)：当能源路由器上报的剩余可用内存低于该配置项时，等待检测脚本主动告警；\r\n
        - 能源路由器资源告警 - 剩余磁盘警报值(单位:MB)：当能源路由器上报的剩余可用磁盘低于该配置项时，等待检测脚本主动告警；
        "),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/active_alarm/update_config"),
        Apidoc\Param(name: "sf_reason_for_stop_white_list", type: "array", desc: "充电枪故障告警 - 充电停止原因白名单(多个以#分隔)"),
        Apidoc\Param(name: "ccsr_cpu_alarm_value", type: "int", desc: "能源路由器资源告警 - CPU使用率警报值(单位:百分比)"),
        Apidoc\Param(name: "ccsr_memory_alarm_value", type: "int", desc: "能源路由器资源告警 - 剩余内存警报值(单位:MB)"),
        Apidoc\Param(name: "ccsr_disk_alarm_value", type: "int", desc: "能源路由器资源告警 - 剩余磁盘警报值(单位:MB)"),
    ]
    public function update_config(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::active_alarm([
                'sf_reason_for_stop_white_list',
                'ccsr_cpu_alarm_value',
                'ccsr_memory_alarm_value',
                'ccsr_disk_alarm_value'
            ]));

            $verify_data['sf_reason_for_stop_white_list'] = implode('#', $verify_data['sf_reason_for_stop_white_list']);

            ActiveAlarmConfig::updateConfigData($verify_data);
            return [];
        }, true);

    }
}