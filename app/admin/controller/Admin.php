<?php
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */

/** @noinspection PhpUnused */

namespace app\admin\controller;

use app\common\cache\redis\AdminUserLogin;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\logic\admin\AdminAdd;
use app\common\logic\admin\AdminEdit;
use app\common\logic\admin\entity\AdminAddRequest;
use app\common\logic\admin\entity\AdminEditRequest;
use app\common\model\AdminUsersRelationApplet;
use app\common\model\StationDataAuthority;
use app\common\traits\Curd;
use app\ms\Api;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\db\Query;
use think\response\Json;
use app\common\model\AdminUsers as AdminUsersModel;
use Throwable;

#[Apidoc\Title("系统管理/管理员")]
class Admin extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new AdminUsersModel();
    }

    #[
        Apidoc\Title("管理员列表"),
        Apidoc\Author("lwj 2023.10.18 新增, cbj 2024.12.24 更新"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Admin/admin_list"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "管理员id"),
        Apidoc\Param(name: "corp_id", type: "int", require: true, desc: "运营商ID"),
        Apidoc\Param(name: "filter_name", type: "string", require: true, desc: "[变更]过滤用户名"),
        Apidoc\Param(name: "phone", type: "string", require: true, desc: "手机号"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '管理员ID'],
            ['name' => 'pid', 'type' => 'int', 'desc' => '管理员上级ID'],
            ['name' => 'corp_name', 'type' => 'int|null', 'desc' => '运营商ID(为null时表示没有绑定运营商)'],
            ['name' => 'name', 'type' => 'string', 'desc' => '用户名'],
            ['name' => 'avatar', 'type' => 'string', 'desc' => '头像网址'],
            ['name' => 'phone', 'type' => 'string', 'desc' => '手机号'],
            ['name' => 'email', 'type' => 'string', 'desc' => '邮箱'],
            ['name' => 'perm_group_id', 'type' => 'int', 'desc' => '管理员权限组id'],
            ['name' => 'state', 'type' => 'int', 'desc' => '状态，1正常，2禁用'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
            ['name' => 'group_name', 'type' => 'string', 'desc' => '管理者名称'],
        ]),
    ]
    public function admin_list(): Json
    {
        return $this->openExceptionCatch(function () {
            LogCollector::collectorRunLog(sprintf('这次调用%s接口', '管理员列表'));
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::admin_user_list([
                'id', 'corp_id', 'filter_name', 'phone', 'page', 'limit',
                'order_name', 'order_type'
            ]));

            $page = ($verify_data['page'] ?? false) ?: 1;
            $pageSize = ($verify_data['limit'] ?? false) ?: $this->pageSize;
            $order_name = ($verify_data['order_name'] ?? false) ?: 'a.id';
            $order_type = ($verify_data['order_type'] ?? false) ?: 'desc';


            // 搜索框搜索
            $where = function (Query $query) use ($verify_data) {
                if (isset($verify_data['id']) && $verify_data['id']) {
                    $query->where('a.id', $verify_data['id']);
                }
                if (isset($verify_data['filter_name']) && $verify_data['filter_name']) {
                    $query->where('a.name', 'like', '%' . $verify_data['filter_name'] . '%');
                }
                if (isset($verify_data['phone']) && $verify_data['phone']) {
                    $query->where('a.phone', $verify_data['phone']);
                }
                if (isset($verify_data['email']) && $verify_data['email']) {
                    $query->where('a.email', $verify_data['email']);
                }
                if (isset($verify_data['state']) && $verify_data['state']) {
                    $query->where('a.state', $verify_data['state']);
                }


                if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                    $query->where('a.pid', '=', $this->loginUser->id);
                } else if ($this->loginUser->corp_id > 0) {
                    $query->where('a.id', '=', $this->loginUser->id);
                }

                if ($this->loginUser->corp_id === 0 && !empty($verify_data['corp_id'])) {
                    $query->where('a.corp_id', '=', $verify_data['corp_id']);
                }

                return $query;
            };

            $field = [
                'a.id', 'a.pid', 'a.name', 'a.avatar', 'a.phone', 'a.email', 'a.state',
                'a.create_time', 'b.group_name', 'c.name as corp_name'
            ];
            return $this->modelClass->alias('a')
                ->join('admin_auth_group b', 'a.perm_group_id = b.id', 'LEFT')
                ->join('corp c', 'c.id = a.corp_id', 'LEFT')
                ->field($field)
                ->where($where)
                ->order($order_name, $order_type)
                ->paginate(['list_rows' => $pageSize, 'page' => $page]);
        });
    }

    #[
        Apidoc\Title("管理员选项"),
        Apidoc\Author("cbj 2024.05.08 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Admin/get_options"),

        Apidoc\Returned(name: "options", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '管理员ID'],
            ['name' => 'name', 'type' => 'string', 'desc' => '用户名'],
        ]),
    ]
    public function get_options(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '管理员选项'));

        // 搜索框搜索
        $where = function (Query $query) {

            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $query->where('corp_id', '=', $this->loginUser->corp_id);
            } else if ($this->loginUser->corp_id > 0) {
                $query->where('id', '=', $this->loginUser->id);
            }

            return $query;
        };

        $field = [
            'id', 'name'
        ];
        $options = $this->modelClass->field($field)
            ->where($where)
            ->select()
            ->toArray();

        return $this->res_success(['options' => $options]);
    }

    #[
        Apidoc\Title("获取管理员排序信息"),
        Apidoc\Author("lwj 2023.10.18 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Admin/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '获取管理员排序信息'));
        $res = [
            'order_name' => 'a.id',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'a.id', 'label' => '管理员ID'],
                ['value' => 'a.pid', 'label' => '父ID'],
                ['value' => 'a.corp_id', 'label' => '运营商'],
                ['value' => 'a.state', 'label' => '状态'],
                ['value' => 'a.perm_group_id', 'label' => '管理组'],
                ['value' => 'a.create_time', 'label' => '创建时间'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("管理员详情"),
        Apidoc\Author("lwj 2023.10.18 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Admin/get_info"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "管理员id"),
        Apidoc\Returned(name: "id", type: "int", desc: "管理员id"),
        Apidoc\Returned(name: "name", type: "string", desc: "管理员登录用户名"),
        Apidoc\Returned(name: "avatar", type: "string", desc: "管理员头像url"),
        Apidoc\Returned(name: "phone", type: "string", desc: "管理员手机号"),
        Apidoc\Returned(name: "email", type: "string", desc: "管理员邮箱"),
        Apidoc\Returned(name: "state", type: "int", desc: "状态，1正常，2禁用"),
        Apidoc\Returned(name: "perm_group_id", type: "int", desc: "管理员权限组ID"),
        Apidoc\Returned(name: "group_name", type: "int", desc: "管理员权限组名称"),
        Apidoc\Returned(name: "corp_id", type: "int", desc: "绑定的运营商ID"),
        Apidoc\Returned(name: "corp_name", type: "string", desc: "绑定的运营商名称"),
        Apidoc\Returned(name: "station_ids", type: "array", desc: "能够管理的场站"),
    ]
    public function get_info(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '管理员详情'));
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::admin_user([
                'id',
            ]));
            $verify_data['id'] = (int)$verify_data['id'];

            // 运营商的管理员
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                // 如果是查询自己的数据
                if ($this->loginUser->id === $verify_data['id']) {
                    $result = $this->modelClass->getDetailsData($verify_data['id']);
                    // 如果不是查询自己的数据，那么需要验证该账号是否属于Ta的子账号。
                } else {
                    $result = $this->modelClass->getDetailsData($verify_data['id'], $this->loginUser->id);
                }
                // 运营商的子账号(子允许查询自己的账号数据)
            } else if ($this->loginUser->corp_id > 0) {
                if ($this->loginUser->id === $verify_data['id']) {
                    $result = $this->modelClass->getDetailsData($verify_data['id']);
                } else {
                    $result = null;
                }
                // 超级管理员(不限制查询)
            } else {
                $result = $this->modelClass->getDetailsData($verify_data['id']);
            }
            if ($result) {
                $result['station_ids'] = (new StationDataAuthority())->getStationIds($verify_data['id']);
                return $this->res_success($result);
            }
            return $this->res_error([], '管理员不存在');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("新增管理员"),
        Apidoc\Author("lwj 2023.10.18 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Admin/add"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "管理员登录用户名"),
//        Apidoc\Param(name: "avatar", type: "string", require: true,  desc: "管理员头像url"),
        Apidoc\Param(name: "phone", type: "string", require: true, desc: "管理员手机号"),
        Apidoc\Param(name: "email", type: "string", require: true, desc: "管理员邮箱"),
        Apidoc\Param(name: "perm_group_id", type: "int", require: true, desc: "管理员权限组id"),
        Apidoc\Param(name: "password", type: "string", require: true, desc: "密码，长度5到20"),
        Apidoc\Param(name: "station_ids", type: "array", require: false, desc: "能看管理的场站(如果是运营商账号时才需要传递)"),
    ]
    public function add(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '新增管理员'));
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();

            $verify_data = v::input($data, VerifyData::admin_user([
                'name',
                'phone',
                'email',
                'perm_group_id',
                'password',
                'station_ids'
            ]));


            $verify_data['admin_user_id'] = $this->loginUser->id;
            $verify_data['admin_user_pid'] = $this->loginUser->pid;
            $verify_data['corp_id'] = $this->loginUser->corp_id;

            $request = new AdminAddRequest($verify_data);

            // 已同步到权限管理微服务
            return (new AdminAdd($request))->run();
        }, true, sprintf('lock_key_add_or_edit_admin_%s', $this->loginUser->id));
    }

    #[
        Apidoc\Title("编辑管理员"),
        Apidoc\Author("lwj 2023.10.18 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Admin/edit"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "管理员id"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "管理员登录用户名"),
        Apidoc\Param(name: "phone", type: "string", require: true, desc: "管理员手机号"),
        Apidoc\Param(name: "email", type: "string", require: true, desc: "管理员邮箱"),
        Apidoc\Param(name: "perm_group_id", type: "int", require: true, desc: "管理员权限组id"),
        Apidoc\Param(name: "station_ids", type: "array", require: true, desc: "能看管理的场站(如果被编辑的管理员corp_id大于0，需要传递用户选择的场站ID；如果等于0，传递空数组就好。)"),
    ]
    public function edit(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '编辑管理员'));
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::admin_user([
                'id',
                'name',
                'phone',
                'email',
                'perm_group_id',
                'station_ids'
            ]));

            $request = new AdminEditRequest($verify_data);

            // 已同步到权限管理微服务
            return (new AdminEdit($this->loginUser, $request))->run();
        }, true, sprintf('lock_key_add_or_edit_admin_%s', $this->loginUser->id));
    }

    #[
        Apidoc\Title("重设管理员密码"),
        Apidoc\Author("lwj 2023.10.18 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Admin/reset_password"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "管理员id"),
        Apidoc\Param(name: "password", type: "string", require: true, desc: "密码"),
    ]
    public function reset_password(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '重设管理员密码'));
        try {
            if ($this->loginUser->pid > 0) {
                return $this->res_error([], '没有权限');
            }

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::admin_user([
                'id',
                'password',
            ]));

            $id = $verify_data['id'];
            $verify_data['password'] = password_hash($verify_data['password'], PASSWORD_DEFAULT);

            if ($this->loginUser->corp_id > 0) {
                $res = $this->modelClass
                    ->where('id', $id)
                    ->where('corp_id', '=', $this->loginUser->corp_id)
                    ->update($verify_data);
            } else {
                $res = $this->modelClass->where('id', $id)->update($verify_data);
            }

            // ========== 将数据同步到微服务 ==========
            $verify_data['service_id'] = 1;
            Api::send("/auth/admin", "PUT", $verify_data,[],'id,service_id');

            if ($res) return $this->res_success([], '编辑成功');
            return $this->res_error([], '编辑失败');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("删除管理员"),
        Apidoc\Author("lwj 2023.10.18 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Admin/delete"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "管理员id"),
    ]
    public function delete(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '删除管理员'));
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::admin_user([
                'id',
            ]));

            if ($this->loginUser->id == $verify_data['id']) throw new RuntimeException('不能删除当前用户');

            if ($this->loginUser->corp_id > 0) {
                $res = $this->modelClass
                    ->where('id', $verify_data['id'])
                    ->where('corp_id', '=', $this->loginUser->corp_id)
                    ->delete();
            } else {
                $res = $this->modelClass->where('id', $verify_data['id'])->delete();
                (new AdminUsersRelationApplet())->unbindApplet($verify_data['id']);
            }
            if ($res) {
                // ========== 将数据同步到微服务 ==========
                $verify_data['service_id'] = 1;
                Api::send("/auth/admin", "DELETE", $verify_data,[],'id,service_id');
//                (new AdminToCorp)->where('admin_id', $verify_data['id'])->delete();
                return $this->res_success(msg: '删除成功', is_open_exception_catch: true);
            }
            throw new RuntimeException('删除失败');
        }, true);
    }

    #[
        Apidoc\Title("当前登陆的管理员信息"),
        Apidoc\Author("lwj 2023.10.18 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Admin/current_user_info"),
        Apidoc\Returned(name: "id", type: "int", desc: "管理员id"),
        Apidoc\Returned(name: "name", type: "string", desc: "管理员登录用户名"),
        Apidoc\Returned(name: "avatar", type: "string", desc: "管理员头像url"),
        Apidoc\Returned(name: "phone", type: "string", desc: "管理员手机号"),
        Apidoc\Returned(name: "email", type: "string", desc: "管理员邮箱"),
        Apidoc\Returned(name: "state", type: "int", desc: "状态，1正常，2禁用"),
        Apidoc\Returned(name: "perm_group_id", type: "int", desc: "管理员权限组id"),
        Apidoc\Returned(name: "group_name", type: "int", desc: "管理员权限组名称"),
        Apidoc\Returned(name: "corp_id", type: "int", desc: "绑定的运营商ID"),
        Apidoc\Returned(name: "corp_name", type: "string", desc: "绑定的运营商名称"),
    ]
    public function current_user_info(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '当前登陆的管理元信息'));
        try {
            $result = $this->modelClass->getDetailsData($this->loginUser->id);
            if ($result) return $this->res_success($result);
            return $this->res_error([], '管理员不存在');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("重设当前管理员密码"),
        Apidoc\Desc('重设成功后，需要跳转到登陆页面，引用用户使用新密码再次登陆。'),
        Apidoc\Author("lwj 2023.10.18 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Admin/reset_current_user_password"),
        Apidoc\Param(name: "password", type: "string", require: true, desc: "密码"),
        Apidoc\Param(name: "old_password", type: "string", require: true, desc: "旧密码"),
    ]
    public function reset_current_user_password(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '重设当前管理员密码'));
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::admin_user([
                'password',
                'old_password'
            ]));

            $userPassword = $this->modelClass->where('id', '=', $this->loginUser->id)->value('password');
            if (!password_verify($verify_data['old_password'], $userPassword)) {
                return $this->res_error([], '旧密码不正确');
            }

            $newPassword = password_hash($verify_data['password'], PASSWORD_DEFAULT);

            $res = $this->modelClass->where('id', '=', $this->loginUser->id)->update([
                'password' => $newPassword,
                'update_time' => date('Y-m-d H:i:s')
            ]);
            if ($res) {
                $AdminUserLogin = app(AdminUserLogin::class);
                $AdminUserLogin->removeUserLoginData($this->request->header('authorization'));
                // ========== 将数据同步到微服务 ==========
                $verify_data['service_id'] = 1;
                $verify_data['password'] = $newPassword;
                Api::send("/auth/admin", "PUT", $verify_data,[],'id,service_id');
                return $this->res_success([], '重设成功');
            }
            return $this->res_error([], '重设失败');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("验证用户名是否已经被使用"),
        Apidoc\Author("cbj 2024.04.24 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/admin/verify_username"),
        Apidoc\Param(name: "username", type: "string", require: true, desc: "管理员用户名"),
        Apidoc\Returned(name: "result", type: "bool", require: true, desc: "验证结果 false:用户名可以使用 true:用户名已经被使用"),
    ]
    public function verify_username(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::service_admin_user([
                'username',
            ]));

            if ((new AdminUsersModel())->isUsernameAlreadyExists($verify_data['username']) === false) {
                return $this->res_success(data: ['result' => false], msg: '用户名可以使用', is_open_exception_catch: true);
            }
            return $this->res_success(data: ['result' => true], msg: '用户名已经被使用', is_open_exception_catch: true);
        });
    }
}
