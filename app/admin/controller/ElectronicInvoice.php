<?php
/** @noinspection ALL */

namespace app\admin\controller;

use app\common\controller\ApiBase;
use app\common\lib\ExceptionLogCollector;
use app\common\lib\VerifyData;
use app\common\model\ElectronicInvoiceApplyRecord;
use app\common\traits\Curd;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use hg\apidoc\annotation as Apidoc;
use think\response\Json;

#[Apidoc\Title("财务中心/发票管理")]
class ElectronicInvoice extends BaseController
{
    use Curd;

    #[
        Apidoc\Title("开票记录"),
        Apidoc\Author("cbj 2023.10.26 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/electronic_invoice/list"),
        Apidoc\Param(name: "id", type: "string", require: false, default: '', desc: "订单发票申请ID"),
        Apidoc\Param(name: 'nickname', type: 'string', require: false, default: '', desc: '用户昵称'),
        Apid<PERSON>\Param(name: 'title_type', type: 'int', require: false, desc: '抬头类型 0:个人 1:单位'),
        Apidoc\Param(name: 'title_name', type: 'string', require: false, default: '', desc: '抬头名称'),
        Apidoc\Param(name: 'taxpayer_id', type: 'string', require: false, default: '', desc: '纳税人识别号'),
        Apidoc\Param(name: 'stage', type: 'int', require: false, desc: '阶段 0:用户发起申请 1:用户填写抬头完成 2:开具发票完成'),
        Apidoc\Param(name: 'start_time', type: 'string', require: false, default: "", desc: '起始时间(格式:YYYY/mm/dd HH:ii:ss)'),
        Apidoc\Param(name: 'end_time', type: 'string', require: false, default: "", desc: '结束时间(格式:YYYY/mm/dd HH:ii:ss)'),

        Apidoc\Param(name: "page", type: "int", require: false, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: false, default: 10, desc: "每页显示"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", require: false, desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '订单发票申请ID'],
            ['name' => 'user_id', 'type' => 'int', 'desc' => '用户ID'],
            ['name' => 'nickname', 'type' => 'int', 'desc' => '用户昵称'],
            ['name' => 'total_amount', 'type' => 'int', 'desc' => '发票总金额(单位:分)'],
            ['name' => 'title_type', 'type' => 'int', 'desc' => '抬头类型 0:个人 1:单位'],
            ['name' => 'title_name', 'type' => 'string', 'desc' => '抬头名称'],
            ['name' => 'taxpayer_id', 'type' => 'string', 'desc' => '纳税人识别号(抬头类型为单位时不为空)'],
            ['name' => 'stage', 'type' => 'int', 'desc' => '阶段 0:用户发起申请 1:用户填写抬头完成 2:开具发票完成'],
            ['name' => 'create_time', 'type' => 'int', 'desc' => '创建时间(单位:秒)'],
            ['name' => 'update_time', 'type' => 'int', 'desc' => '更新时间(单位:秒)'],
        ]),
    ]
    public function list(): Json
    {
        try {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::electronicInvoice([
                'id',
                'nickname',
                'title_type',
                'title_name',
                'taxpayer_id',
                'stage',
                'start_time',
                'end_time',
                'page',
                'limit',
            ]));
            if (!empty($verifyData['start_time'])) $verifyData['start_time'] = strtotime($verifyData['start_time']);
            if (!empty($verifyData['end_time'])) $verifyData['end_time'] = strtotime($verifyData['end_time']);

            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;

            $ElectronicInvoiceApplyRecord = app(ElectronicInvoiceApplyRecord::class);
            $list = $ElectronicInvoiceApplyRecord->list($verifyData, $page, $pageSize);

            return $this->res_success($list);
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (\Throwable $e) {
            ExceptionLogCollector::collect($e);
            return $this->res_error([], '服务器异常');
        }
    }

    #[
        Apidoc\Title("开票统计"),
        Apidoc\Author("cbj 2023.10.26 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/electronic_invoice/statistics"),
        Apidoc\Param(name: 'nickname', type: 'string', require: false, default: '', desc: '用户昵称'),
        Apidoc\Param(name: 'stage', type: 'int', require: false, desc: '阶段 0:用户发起申请 1:用户填写抬头完成 2:开具发票完成'),

        Apidoc\Param(name: "page", type: "int", require: false, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: false, default: 10, desc: "每页显示"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", require: false, desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '订单发票申请ID'],
            ['name' => 'user_id', 'type' => 'int', 'desc' => '用户ID'],
            ['name' => 'nickname', 'type' => 'int', 'desc' => '用户昵称'],
            ['name' => 'total_amount', 'type' => 'int', 'desc' => '发票总金额(单位:分)'],
            ['name' => 'count', 'type' => 'int', 'desc' => '开票次数'],
            ['name' => 'stage', 'type' => 'int', 'desc' => '阶段 0:用户发起申请 1:用户填写抬头完成 2:开具发票完成'],
        ]),
    ]
    public function statistics(): Json
    {
        try {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::electronicInvoice([
                'nickname',
                'stage',
                'page',
                'limit',
            ]));

            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;

            $ElectronicInvoiceApplyRecord = app(ElectronicInvoiceApplyRecord::class);
            $list = $ElectronicInvoiceApplyRecord->statistics($verifyData, $page, $pageSize);

            return $this->res_success($list);
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (\Throwable $e) {
            ExceptionLogCollector::collect($e);
            return $this->res_error([], '服务器异常');
        }
    }
}