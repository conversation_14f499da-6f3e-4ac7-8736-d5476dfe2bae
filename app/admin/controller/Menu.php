<?php
/** @noinspection PhpUnused */

/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */

namespace app\admin\controller;

use app\common\lib\VerifyData;
use app\common\logic\admin\menu\Export;
use app\common\logic\admin\menu\Import;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use app\common\model\Menu as MenuModel;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\response\Json;
use Throwable;
use util\Tree;

#[Apidoc\Title("系统管理/节点管理")]
class Menu extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new MenuModel();
    }

    #[
        Apidoc\Title("导入菜单"),
        Apidoc\Author("cbj 2024.12.12 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Menu/import"),
    ]
    public function import(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Import($this->loginUser, $this->request))->run();
        }, true);
    }

    #[
        Apidoc\Title("导出菜单"),
        Apidoc\Author("cbj 2024.12.12 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Menu/export"),
        Apidoc\Returned(name: 'url', type: 'string', require: true, desc: '菜单数据文件的URL'),
    ]
    public function export(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Export($this->loginUser, $this->request))->run();
        }, true);
    }

    #[
        Apidoc\Title("获取菜单列表"),
        Apidoc\Author("lwj 2023.10.11 新增,lwj 2024.8.28 修改"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Menu/menu_list"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "充电站列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '菜单id'],
            ['name' => 'title', 'type' => 'string', 'desc' => '菜单名称'],
            ['name' => 'rule_name', 'type' => 'string', 'desc' => '权限名'],
            ['name' => 'level', 'type' => 'int', 'desc' => '所在层级（一级菜单=1）'],
            ['name' => 'pid', 'type' => 'int', 'desc' => '上级菜单id（0表示无上级菜单）'],
            ['name' => 'icon_url', 'type' => 'string', 'desc' => '菜单图标icon的url'],
            ['name' => 'url_value', 'type' => 'string', 'desc' => '前端地址'],
            ['name' => 'api_url', 'type' => 'string', 'desc' => '接口路径'],
            ['name' => 'sort', 'type' => 'int', 'desc' => '排序'],
            ['name' => 'menu_state', 'type' => 'int', 'desc' => '显示菜单，1显示，2隐藏'],
            ['name' => 'state', 'type' => 'int', 'desc' => '状态，1 可以用，2 所有禁止使用'],
            ['name' => 'auth_open', 'type' => 'int', 'desc' => '1不公开 2公开'],
            ['name' => 'child', 'type' => 'array', 'desc' => '子节点，可以无穷嵌套'],
        ]),
    ]
    public function menu_list(): Json
    {
        // 运营商账号
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }
        $menu = Tree::toLayer(
            $this->modelClass
                ->order('sort,pid,id')
                ->column('id,pid,title,rule_name,url_value,api_url,icon_url,sort,menu_state,state,auth_open,level')
        );
        return $this->res_success($menu);
    }

    #[
        Apidoc\Title("获取用户菜单列表"),
        Apidoc\Author("lwj 2023.10.11 新增,lwj 2024.8.28 修改"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Menu/user_menu_list"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "充电站列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '菜单id'],
            ['name' => 'title', 'type' => 'string', 'desc' => '菜单名称'],
            ['name' => 'rule_name', 'type' => 'string', 'desc' => '权限名'],
            ['name' => 'level', 'type' => 'int', 'desc' => '所在层级（一级菜单=1）'],
            ['name' => 'pid', 'type' => 'int', 'desc' => '上级菜单id（0表示无上级菜单）'],
            ['name' => 'icon_url', 'type' => 'string', 'desc' => '菜单图标icon的url'],
            ['name' => 'url_value', 'type' => 'string', 'desc' => '前端地址'],
            ['name' => 'api_url', 'type' => 'string', 'desc' => '接口路径'],
            ['name' => 'sort', 'type' => 'int', 'desc' => '排序'],
            ['name' => 'menu_state', 'type' => 'int', 'desc' => '显示菜单，1显示，2隐藏'],
            ['name' => 'state', 'type' => 'int', 'desc' => '状态，1 可以用，2 所有禁止使用'],
            ['name' => 'auth_open', 'type' => 'int', 'desc' => '1不公开 2公开'],
            ['name' => 'child', 'type' => 'array', 'desc' => '子节点，可以无穷嵌套'],
        ]),
    ]
    public function user_menu_list(): Json
    {
        // 获取节点数据
        $menu = $this->modelClass
            ->append(['show'])
            ->order('sort,pid,id')
            ->where('state', 1)
            ->where('menu_state', 1)
            ->where(function ($query) {
                $query->where('auth_open', 2)
                    ->whereOr(function ($query) {
                        $query->where('auth_open', 1)
                            ->whereExists(function ($query) {
                                $query->table('admin_auth_group_rules')
                                    ->where('admin_auth_group_rules.rule_name=sys_menu.rule_name')
                                    ->where('group_id', $this->loginUser->perm_group_id);
                            });
                    });
            })
            ->withAttr('show', function () {
                return false;
            })
            ->column('id,pid,title,rule_name,url_value,api_url,icon_url,sort,menu_state,state,auth_open,level');

//        return $this->res_success($menu);

        $list = Tree::toLayer($menu);
        return $this->res_success($list);
    }

    #[
        Apidoc\Title("获取用户路由列表"),
        Apidoc\Author("lwj 2023.10.11 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Menu/user_route_list"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "充电站列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '菜单id'],
            ['name' => 'title', 'type' => 'string', 'desc' => '菜单名称'],
            ['name' => 'rule_name', 'type' => 'string', 'desc' => '权限名'],
            ['name' => 'level', 'type' => 'int', 'desc' => '所在层级（一级菜单=1）'],
            ['name' => 'pid', 'type' => 'int', 'desc' => '上级菜单id（0表示无上级菜单）'],
            ['name' => 'icon_url', 'type' => 'string', 'desc' => '菜单图标icon的url'],
            ['name' => 'url_value', 'type' => 'string', 'desc' => '前端地址'],
            ['name' => 'api_url', 'type' => 'string', 'desc' => '接口路径'],
            ['name' => 'sort', 'type' => 'int', 'desc' => '排序'],
            ['name' => 'menu_state', 'type' => 'int', 'desc' => '显示菜单，1显示，2隐藏'],
            ['name' => 'state', 'type' => 'int', 'desc' => '状态，1 可以用，2 所有禁止使用'],
            ['name' => 'auth_open', 'type' => 'int', 'desc' => '1不公开 2公开'],
            ['name' => 'child', 'type' => 'array', 'desc' => '子节点，可以无穷嵌套'],
        ]),
    ]
    public function user_route_list(): Json
    {
        // 获取节点数据
        $menu = $this->modelClass
            ->append(['show'])
            ->order('pid,id')
            ->where('state', 1)
            ->where('menu_state', 1)
            ->where(function ($query) {
                $query->where('auth_open', 2)
                    ->whereOr(function ($query) {
                        $query->where('auth_open', 1)
                            ->whereExists(function ($query) {
                                $query->table('admin_auth_group_rules')
                                    ->where('admin_auth_group_rules.rule_name=sys_menu.rule_name')
                                    ->where('group_id', $this->loginUser->perm_group_id);
                            });
                    });
            })
            ->column('id,pid,url_value as path,title');

        $list = Tree::toRoute($menu);

        return $this->res_success($list);
    }

    #[
        Apidoc\Title("菜单详情"),
        Apidoc\Author("lwj 2023.10.11 新增，lwj 2023.10.17 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Menu/get_info"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "菜单id"),

        Apidoc\Returned(name: "id", type: "int", desc: "菜单id"),
        Apidoc\Returned(name: "pid", type: "int", desc: "上级菜单id（0表示无上级菜单）"),
        Apidoc\Returned(name: "title", type: "string", desc: "菜单名称"),
        Apidoc\Returned(name: "rule_name", type: "string", desc: "权限名"),
        Apidoc\Returned(name: "url_value", type: "string", desc: "菜单地址"),
        Apidoc\Returned(name: "api_url", type: "string", desc: "接口路径"),
        Apidoc\Returned(name: "icon_url", type: "string", desc: "菜单图标icon的url"),
        Apidoc\Returned(name: "sort", type: "int", desc: "排序"),
        Apidoc\Returned(name: "menu_state", type: "int", desc: "显示菜单，1显示，2隐藏"),
        Apidoc\Returned(name: "auth_open", type: "int", desc: "1不公开 2公开"),
    ]
    public function get_info(): Json
    {
        // 运营商账号
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::sys_menu([
                'id',
            ]));

            $result = $this->modelClass
                ->field('id,pid,title,rule_name,url_value,api_url,icon_url,sort,menu_state,auth_open')
                ->find($verify_data['id']);
            if ($result) return $this->res_success($result);
            return $this->res_error([], '菜单不存在');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("新增菜单"),
        Apidoc\Author("lwj 2023.10.11 新增，lwj 2023.10.18 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Menu/add"),
        Apidoc\Param(name: "title", type: "string", require: true, desc: "菜单名称"),
        Apidoc\Param(name: "rule_name", type: "string", require: true, desc: "权限名"),
        Apidoc\Param(name: "pid", type: "int", require: true, desc: "上级菜单id（0表示无上级菜单）"),
        Apidoc\Param(name: "icon_url", type: "string", require: true, desc: "菜单图标icon的url"),
        Apidoc\Param(name: "url_value", type: "string", require: true, desc: "菜单地址"),
        Apidoc\Param(name: "api_url", type: "string", require: true, desc: "接口路径，小写"),
        Apidoc\Param(name: "sort", type: "int", require: true, desc: "排序"),
        Apidoc\Param(name: "menu_state", type: "int", require: true, desc: "显示菜单，1显示，2隐藏"),
        Apidoc\Param(name: "auth_open", type: "int", require: true, desc: "1不公开 2公开"),
    ]
    public function add(): Json
    {
        // 运营商账号
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::sys_menu([
                'title',
                'rule_name',
                'pid',
                'icon_url',
                'url_value',
                'api_url',
                'sort',
                'menu_state',
                'auth_open',
            ]));

            $info = $this->modelClass
                ->where('rule_name', $verify_data['rule_name'])
                ->field('id')
                ->find();
            if ($info) return $this->res_error([], '权限名不能重复');

            if ($verify_data['pid'] === 0) {
                $verify_data['level'] = 1;
            } else {
                $pid_info = $this->modelClass
                    ->where('id', $verify_data['pid'])
                    ->field('id,level')
                    ->find();
                if (!$pid_info) return $this->res_error([], '上级菜单不存在');
                $verify_data['level'] = $pid_info['level'] + 1;
            }

            $verify_data['api_url'] = strtolower($verify_data['api_url']);
            $verify_data['opt_id'] = $this->loginUser->id;
            $verify_data['create_time'] = date('Y-m-d H:i:s');
            $res = $this->modelClass->create($verify_data);
            if ($res) return $this->res_success([], '新增成功');
            return $this->res_error([], '新增失败');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }


    #[
        Apidoc\Title("编辑菜单"),
        Apidoc\Author("lwj 2023.10.11 新增，lwj 2023.10.18 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Menu/edit"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "菜单id"),
        Apidoc\Param(name: "title", type: "string", require: true, desc: "菜单名称"),
        Apidoc\Param(name: "rule_name", type: "string", require: true, desc: "权限名"),
        Apidoc\Param(name: "pid", type: "int", require: true, desc: "上级菜单id（0表示无上级菜单）"),
        Apidoc\Param(name: "icon_url", type: "string", require: true, desc: "菜单图标icon的url"),
        Apidoc\Param(name: "url_value", type: "string", require: true, desc: "菜单地址"),
        Apidoc\Param(name: "api_url", type: "string", require: true, desc: "接口路径，小写"),
        Apidoc\Param(name: "sort", type: "int", require: true, desc: "排序"),
        Apidoc\Param(name: "menu_state", type: "int", require: true, desc: "显示菜单，1显示，2隐藏"),
        Apidoc\Param(name: "auth_open", type: "int", require: true, desc: "1不公开 2公开"),
    ]
    public function edit(): Json
    {
        // 运营商账号
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::sys_menu([
                'id',
                'title',
                'rule_name',
                'pid',
                'icon_url',
                'url_value',
                'api_url',
                'sort',
                'menu_state',
                'auth_open',
            ]));

            $id = $verify_data['id'];
            unset($verify_data['id']);

            $info = $this->modelClass
                ->where('rule_name', $verify_data['rule_name'])
                ->where('id', '<>', $id)
                ->field('id')->find();
            if ($info) return $this->res_error([], '权限名不能重复');

            if ($verify_data['pid'] === 0) {
                $verify_data['level'] = 1;
            } else {
                $pid_info = $this->modelClass->where('id', $verify_data['pid'])->field('id,level')->find();
                if (!$pid_info) return $this->res_error([], '上级菜单不存在');
                $verify_data['level'] = $pid_info['level'] + 1;
            }

            $verify_data['api_url'] = strtolower($verify_data['api_url']);

            $res = $this->modelClass->where('id', $id)->update($verify_data);
            if ($res) return $this->res_success([], '编辑成功');
            return $this->res_error([], '编辑失败');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("删除菜单(包括下级菜单)"),
        Apidoc\Author("lwj 2023.10.11 新增,lwj 2023.10.17 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Menu/delete_all"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "菜单id"),
    ]
    public function delete_all(): Json
    {
        // 运营商账号
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::sys_menu([
                'id',
            ]));

            // 获取该节点的所有后辈节点id
            $menu_childs = $this->modelClass->getChildsId($verify_data['id']);

            // 要删除的所有节点id
            $all_ids = array_merge([$verify_data['id']], $menu_childs);

            // 删除节点destroy
            $res = $this->modelClass->destroy($all_ids);
            if ($res) return $this->res_success([], '删除成功');
            return $this->res_error([], '删除失败');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("删除菜单"),
        Apidoc\Author("lwj 2023.10.11 新增,lwj 2023.10.17 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Menu/delete"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "菜单id"),
    ]
    public function delete(): Json
    {
        // 运营商账号
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::sys_menu([
                'id',
            ]));

            $pid_info = $this->modelClass->where('pid', $verify_data['id'])->field('id,level')->find();
            if ($pid_info) return $this->res_error([], '请先删除下级菜单');

            // 删除节点destroy
            $res = $this->modelClass->destroy($verify_data['id']);
            if ($res) return $this->res_success([], '删除成功');
            return $this->res_error([], '删除失败');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("更新显示菜单状态"),
        Apidoc\Author("lwj 2023.10.17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Menu/update_menu_state"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "菜单id"),
        Apidoc\Param(name: "menu_state", type: "int", require: true, desc: "显示菜单，1显示，2隐藏"),
    ]
    public function update_menu_state(): Json
    {
        // 运营商账号
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::sys_menu([
                'id',
                'menu_state',
            ]));

            $info = $this->modelClass->find($verify_data['id']);
            if (!$info) return $this->res_error([], '菜单不存在');
            $info->menu_state = $verify_data['menu_state'];
            $res = $info->save();

            if ($res) return $this->res_success([], '更新成功');
            return $this->res_error([], '更新失败');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("更新公开状态"),
        Apidoc\Author("lwj 2023.10.17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Menu/update_auth_open"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "菜单id"),
        Apidoc\Param(name: "auth_open", type: "int", require: true, desc: "公开状态，1不公开 2公开"),
    ]
    public function update_auth_open(): Json
    {
        // 运营商账号
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::sys_menu([
                'id',
                'auth_open',
            ]));

            $info = $this->modelClass->find($verify_data['id']);
            if (!$info) return $this->res_error([], '菜单不存在');
            $info->auth_open = $verify_data['auth_open'];
            $res = $info->save();

            if ($res) return $this->res_success([], '更新成功');
            return $this->res_error([], '更新失败');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("更新菜单可用状态"),
        Apidoc\Author("lwj 2023.10.17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Menu/update_state"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "菜单id"),
        Apidoc\Param(name: "state", type: "int", require: true, desc: "状态，1 可以用，2 所有禁止使用"),
    ]
    public function update_state(): Json
    {
        // 运营商账号
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::sys_menu([
                'id',
                'state',
            ]));

            $info = $this->modelClass->find($verify_data['id']);
            if (!$info) return $this->res_error([], '菜单不存在');
            $info->state = $verify_data['state'];
            $res = $info->save();

            if ($res) return $this->res_success([], '更新成功');
            return $this->res_error([], '更新失败');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("更新菜单排序"),
        Apidoc\Author("lwj 2023.10.17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Menu/update_sort"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "菜单id"),
        Apidoc\Param(name: "sort", type: "int", require: true, desc: "排序"),
    ]
    public function update_sort(): Json
    {
        // 运营商账号
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::sys_menu([
                'id',
                'sort',
            ]));

            $info = $this->modelClass->find($verify_data['id']);
            if (!$info) return $this->res_error([], '菜单不存在');
            $info->sort = $verify_data['sort'];
            $res = $info->save();

            if ($res) return $this->res_success([], '更新成功');
            return $this->res_error([], '更新失败');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取父菜单列表"),
        Apidoc\Author("lwj 2023.10.17 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Menu/get_pid_list"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "充电站列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '菜单id'],
            ['name' => 'pid', 'type' => 'int', 'desc' => '上级菜单id（0表示无上级菜单）'],
            ['name' => 'title', 'type' => 'string', 'desc' => '菜单名称'],
            ['name' => 'level', 'type' => 'int', 'desc' => '所在层级（一级菜单=1）'],
        ]),
    ]
    public function get_pid_list(): Json
    {
        // 运营商账号
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        $menu = Tree::tree_title(
            $this->modelClass
                ->order('pid,sort,id')
                ->column('id,pid,title')
        );
        array_unshift($menu, ['id' => 0, 'pid' => 0, 'title' => '默认顶级', 'level' => 0]);
        return $this->res_success($menu);
    }


    #[
        Apidoc\Title("获取用户权限列表"),
        Apidoc\Author("cbj 2024.05.14 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Menu/user_auth_list"),
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "充电站列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '菜单id'],
            ['name' => 'rule_name', 'type' => 'string', 'desc' => '权限名'],
        ]),
    ]
    public function user_auth_list(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new MenuModel())->userAuthList($this->loginUser->perm_group_id);
        });
    }

}