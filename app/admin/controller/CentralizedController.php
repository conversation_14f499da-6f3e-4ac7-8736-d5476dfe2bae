<?php
/** @noinspection PhpUnused */

namespace app\admin\controller;

use app\ms\Api;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use think\response\Json;

#[Apidoc\Title("资源中心/能源路由器")]
class CentralizedController extends BaseController
{
    use Curd;

    #[
        Apidoc\Title("场站的能源路由器选项"),
        Apidoc\Author("cbj 2024.01.04 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/centralized_controller/get_station_centralized_controller_options"),
        Apidoc\Query(name: 'station_id', type: 'int', require: false, desc: '场站ID'),
        Apidoc\Returned(name: 'list', type: 'array', require: true, desc: '列表', children: [
            ['name' => 'id', 'type' => 'string', 'require' => false, 'desc' => '能源路由器ID'],
            ['name' => 'name', 'type' => 'string', 'require' => false, 'desc' => '能源路由器名称']
        ]),
    ]
    public function get_station_centralized_controller_options(): Json
    {
        $data = $this->request->post();
        $data["group_id"] = $this->loginUser->perm_group_id;
        $data["user_id"] = $this->loginUser->id;
        if (isset($data["station_id"])){
            $data["station_id"] = intval($data["station_id"]);
        }
        if (isset($data["is_bind_station"])){
            $data["is_bind_station"] = intval($data["is_bind_station"]);
        }

        // ========== 从设备服务获取能源路由器选项 ========== //
        $response = Api::send('/device/centralized/options','GET', $data);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }

//    #[
//        Apidoc\Title("场站的能源路由器选项"),
//        Apidoc\Author("cbj 2024.01.04 新增"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/admin/centralized_controller/get_station_centralized_controller_options"),
//        Apidoc\Param(name: 'station_id', type: 'int', require: true, desc: '场站ID'),
//        Apidoc\Returned(name: 'list', type: 'array', require: true, desc: '列表', children: [
//            ['name' => 'id', 'type' => 'string', 'require' => false, 'desc' => '能源路由器ID'],
//            ['name' => 'name', 'type' => 'string', 'require' => false, 'desc' => '能源路由器名称']
//        ]),
//    ]
//    public function get_station_centralized_controller_options(): Json
//    {
//        return $this->openExceptionCatch(function () {
//            $data = $this->request->post();
//            $verify_data = v::input($data, VerifyData::centralizedController([
//                'station_id',
//            ]));
//
//            $list = (new \app\common\model\CentralizedController())->getStationCentralizedControllerOptions($this->loginUser, $verify_data['station_id']);
//            return [
//                'list' => $list
//            ];
//        });
//    }

    #[
        Apidoc\Title("获取监控信息"),
        Apidoc\Author("cccq 2024.01.20 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/centralized_controller/monitor"),
        Apidoc\Query(name: "centralized_id", type: 'string', require: true, desc: '能源路由器ID'),
    ]
    public function monitor(): Json
    {
        $data = $this->request->post();
        // ========== 从设备服务获取监控信息 ========== //
        $response = Api::send('/device/centralized/monitor','GET', $data);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }
}