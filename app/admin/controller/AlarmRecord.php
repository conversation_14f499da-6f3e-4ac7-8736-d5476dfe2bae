<?php
/** @noinspection PhpUnused */

namespace app\admin\controller;

use app\common\lib\ExceptionLogCollector;
use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\model\StationDataAuthority;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\response\Json;
use Throwable;

#[Apidoc\Title("运营中心/告警监控")]
class AlarmRecord extends BaseController
{
    use Curd;

    #[
        Apidoc\Title("告警记录列表"),
        Apidoc\Author("cbj 2023.11.2 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/AlarmRecord/get_list_data"),
        Apidoc\Param(name: 'corp_id', type: 'int', require: false, default: null, desc: '运营商ID'),
        Apidoc\Param(name: 'station_id', type: 'int', require: false, default: null, desc: '充电站ID'),
        Apidoc\Param(name: 'device_type', type: 'int', require: false, default: null, desc: '设备类型 1:充电枪 2:充电桩 3:能源路由器'),
        Apidoc\Param(name: 'device_id', type: 'int', require: false, default: null, desc: '设备编号'),
        Apidoc\Param(name: 'type', type: 'int', require: false, default: null, desc: '告警类型 1:实时监测数据告警 2:交易记录告警 3:充电服务离线告警 4:读取电表功率告警 5:充电桩离线告警 6:能源路由器资源告警'),
        Apidoc\Param(name: 'code', type: 'string', require: false, default: null, desc: '告警代码'),
        Apidoc\Param(name: 'alarm_start_time', type: 'string', require: false, default: null, desc: '告警起始时间(格式:YYYY-mm-dd)'),
        Apidoc\Param(name: 'alarm_end_time', type: 'string', require: false, default: null, desc: '告警结尾时间(格式:YYYY-mm-dd)'),
//        Apidoc\Param(name: 'level', type: 'int', require: false, default: 0, desc: '告警等级 1:一级  2:二级'),
//        Apidoc\Param(name: 'status', type: 'int', require: false, default: 0, desc: '处理状态 1:创建 2:处理中 3:处理完成'),
        Apidoc\Param(name: 'is_recovery', type: 'int', require: false, default: null, desc: '恢复状态 1:未恢复 2:已恢复'),

        Apidoc\Param(name: "page", type: "int", require: false, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: false, default: 10, desc: "每页显示"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", require: false, desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => 'ID'],
            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'station_name', 'type' => 'int', 'desc' => '场站名称'],
            ['name' => 'device_id', 'type' => 'int', 'desc' => '告警设备id，与类型对应：充电枪ID、充电桩ID、能源路由器ID'],
            ['name' => 'device_type', 'type' => 'int', 'desc' => '设备类型 1:充电枪 2:充电桩 3:能源路由器'],
            ['name' => 'type', 'type' => 'int', 'desc' => '告警类型 1:实时监测数据告警 2:交易记录告警 3:充电服务离线告警 4:读取电表功率告警 5:充电桩离线告警 6:能源路由器资源告警'],
            ['name' => 'code', 'type' => 'string', 'desc' => '告警代码'],
            ['name' => 'alarm_time', 'type' => 'int', 'desc' => '告警的时间'],
            ['name' => 'is_recovery', 'type' => 'int', 'desc' => '是否已恢复 1:未恢复 2:已恢复'],
            ['name' => 'recovery_time', 'type' => 'int', 'desc' => '恢复的时间'],
        ]),
    ]
    public function get_list_data(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '告警记录列表'));
        try {
            $data = $this->request->post();
            // 验证数据
            $verify_data = v::input($data, VerifyData::alarm_record([
                'corp_id', 'station_id', 'device_type', 'device_id', 'type',
                'is_recovery', 'code', 'alarm_start_time', 'alarm_end_time',
                'page', 'limit'
            ]));
            // 如果是运营商管理员账号
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $verify_data['corp_id'] = $this->loginUser->corp_id;
                // 如果是运营商子账号
            } else if ($this->loginUser->corp_id > 0) {
                $verify_data['corp_id'] = $this->loginUser->corp_id;
                // 查询当前子账号拥有的场站权限
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                if (empty($verify_data['station_id'])) {
                    $verify_data['station_id'] = $station_ids;
                } else {
                    // 如果用户对场站进行了筛选，则检查选择的场站是否具体权限。如果没，有则返回:"无效场站ID"。
                    if (in_array($verify_data['station_id'], $station_ids) === false) {
                        return $this->res_error([], '无效场站ID');
                    }
                }
            }

            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;

//            $AlarmRecord = app(\app\common\model\AlarmRecord::class);
            $list = \app\common\repositories\AlarmRecord::getListData($verify_data, $page, $pageSize);
//            $list = $AlarmRecord->getListData($verify_data, $page, $pageSize);
            return $this->res_success($list);
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            return $this->res_error([], '服务器异常');
        }
    }

    #[
        Apidoc\Title("删除告警记录(接口已废弃)"),
        Apidoc\Author("cbj 2023.11.2 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/AlarmRecord/delete_data"),
        Apidoc\Param(name: 'id', type: 'int', require: true, desc: '告警记录ID'),
    ]
    public function delete_data(): Json
    {
        return $this->res_error('接口已废弃');
    }
}