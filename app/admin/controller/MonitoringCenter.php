<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpUnusedLocalVariableInspection */
/** @noinspection PhpUnused */

namespace app\admin\controller;

use app\common\cache\redis\StationsMonthRankingListDispatch;
use app\common\lib\ChargeDataStatistics;
use app\common\lib\ExceptionLogCollector;
use app\common\lib\VerifyData;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\response\Json;
use Throwable;
use think\facade\Db;

#[Apidoc\Title("监控中心")]
class MonitoringCenter extends BaseController
{
    use Curd;


    #[
        Apidoc\Title("场站最近七天的充电数据"),
        <PERSON>pid<PERSON>\Author("cbj 2024.08.23 新增"),
        Apid<PERSON>\Method("POST"),
        Apidoc\Url("/admin/monitoringCenter/last_7_days_charging_data"),
        Apidoc\Param(name: "filter_station_id", type: "int", require: true, default: 0, desc: "充电场站ID"),
        Apidoc\Returned(name: "data", type: "array", desc: "统计数据", children: [
            ['name' => 'last_7_days_data', 'type' => 'array', 'desc' => '最近七天的充电数据', 'children' => [
                ['name' => 'day', 'type' => 'string', 'desc' => '日期'],
                ['name' => 'money', 'type' => 'int', 'desc' => '充电收入(单位:分)'],
                ['name' => 'electricity_total', 'type' => 'int', 'desc' => '充电量(精确到小数点后4位 | 单位：kW.h)'],
            ]],
        ]),
    ]
    public function last_7_days_charging_data(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::statistics([
                'filter_station_id',
            ]));

            $ChargeDataStatistics = new ChargeDataStatistics();
            return $ChargeDataStatistics->last_7_days_charging_data($verifyData['filter_station_id'], $this->loginUser->corp_id);
        });
    }

    #[
        Apidoc\Title("首页数据概览"),
        Apidoc\Author("cbj 2023.10.19 新增, cbj 2023.11.15 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/monitoringCenter/dataOverview"),
        Apidoc\Param(name: "corp_id", type: "int", require: false, default: 0, desc: "运营商编号 不传递这个参数表示查询所有运营商的"),

        Apidoc\Returned(name: "data", type: "array", desc: "统计数据", children: [
            ['name' => 'daily_charge_income', 'type' => 'int', 'desc' => '当天充电收入(单位:分)'],
            ['name' => 'daily_electricity_total', 'type' => 'int', 'desc' => '当天充电量(精确到小数点后4位 | 单位：kW.h)'],
            ['name' => 'daily_charge_duration', 'type' => 'int', 'desc' => '当天充电时长(单位:秒)'],
            ['name' => 'average_charge_income', 'type' => 'int', 'desc' => '最近十天充电收入平均值(单位:分)'],
            ['name' => 'average_electricity_total', 'type' => 'int', 'desc' => '最近十天充电量平均值(精确到小数点后4位 | 单位：kW.h)'],
            ['name' => 'last_10_days_data', 'type' => 'array', 'desc' => '最近十天的充电数据', 'children' => [
                ['name' => 'day', 'type' => 'string', 'desc' => '日期'],
                ['name' => 'money', 'type' => 'int', 'desc' => '充电收入(单位:分)'],
                ['name' => 'electricity_total', 'type' => 'int', 'desc' => '充电量(精确到小数点后4位 | 单位：kW.h)'],
            ]],
            ['name' => 'this_year_charge_income', 'type' => 'int', 'desc' => '全年充电收入(单位:分)'],
            ['name' => 'this_year_electricity_total', 'type' => 'int', 'desc' => '全年充电量(精确到小数点后4位 | 单位：kW.h)'],
            ['name' => 'total_shots_count', 'type' => 'int', 'desc' => '充电枪总数'],
            ['name' => 'online_shots_count', 'type' => 'int', 'desc' => '充电枪在线数量'],
            ['name' => 'alarm_record_count', 'type' => 'int', 'desc' => '未处理完成的告警记录数量']
        ]),
    ]
    public function dataOverview(): Json
    {
        $data = $this->request->post();
        // 验证数据
        $verifyData = v::input($data, VerifyData::statistics([
            'corp_id',
        ]));
        $verifyData['corp_id'] = !empty($verifyData['corp_id']) ? $verifyData['corp_id'] : 0;

        if ($this->loginUser->corp_id > 0) {
            $verifyData['corp_id'] = $this->loginUser->corp_id;
        }

        try {
            $ChargeDataStatistics = new ChargeDataStatistics();
            $result = $ChargeDataStatistics->dataOverview($verifyData['corp_id']);
            return $this->res_success($result);
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            return $this->res_error([], '服务器异常');
        }
    }

    #[
        Apidoc\Title("每月统计列表"),
        Apidoc\Author("cbj 2023.10.19 新增, cbj 2023.11.15 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/monitoringCenter/monthlyStatistics"),
        Apidoc\Param(name: "year", type: "int", require: true, desc: "年份 可查询范围:2023-2100"),
        Apidoc\Param(name: "corp_id", type: "int", require: false, desc: "运营商编号 不传递这个参数表示查询所有运营商的"),

        Apidoc\Returned(name: "data", type: "array", desc: "统计数据", children: [
            ['name' => 'money', 'type' => 'int', 'desc' => '营业额(单位:分)'],
            ['name' => 'electricity_total', 'type' => 'int', 'desc' => '充电量(精确到小数点后4位 | 单位：kW.h)'],
            ['name' => 'order_count', 'type' => 'int', 'desc' => '充电订单数量'],
            ['name' => 'month', 'type' => 'int', 'desc' => '当月时间']
        ]),
    ]
    public function monthlyStatistics(): Json
    {
        $data = $this->request->post();
        // 验证数据
        $verifyData = v::input($data, VerifyData::statistics([
            'year',
            'corp_id',
        ]));
        $verifyData['corp_id'] = !empty($verifyData['corp_id']) ? $verifyData['corp_id'] : 0;

        if ($this->loginUser->corp_id > 0) {
            $verifyData['corp_id'] = $this->loginUser->corp_id;
        }

        $ChargeDataStatistics = new ChargeDataStatistics();
        try {
            $result = $ChargeDataStatistics->monthlyStatistics($verifyData['year'], $verifyData['corp_id']);
            return $this->res_success($result);
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            return $this->res_success([], '服务器异常');
        }


    }

    #[
        Apidoc\Title("每日统计列表"),
        Apidoc\Author("cbj 2023.10.19 新增, cbj 2023.11.15 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/monitoringCenter/dailyStatistics"),
        Apidoc\Param(name: "year", type: "int", require: true, desc: "年份 可查询范围:2023-2100"),
        Apidoc\Param(name: "month", type: "int", require: true, desc: "月份 可查询范围:1-12"),
        Apidoc\Param(name: "corp_id", type: "int", require: false, desc: "运营商编号 不传递这个参数表示查询所有运营商的"),

        Apidoc\Returned(name: "data", type: "array", desc: "统计数据", children: [
            ['name' => 'money', 'type' => 'int', 'desc' => '营业额(单位:分)'],
            ['name' => 'electricity_total', 'type' => 'int', 'desc' => '充电量(精确到小数点后4位 | 单位：kW.h)'],
            ['name' => 'order_count', 'type' => 'int', 'desc' => '充电订单数量'],
            ['name' => 'day', 'type' => 'int', 'desc' => '当天时间'],
            ['name' => 'corp_id', 'type' => 'int', 'desc' => '运营商ID']
        ]),
    ]
    public function dailyStatistics(): Json
    {
        $data = $this->request->post();
        // 验证数据
        $verifyData = v::input($data, VerifyData::statistics([
            'year',
            'month',
            'corp_id',
        ]));
        $verifyData['corp_id'] = !empty($verifyData['corp_id']) ? $verifyData['corp_id'] : 0;
        if ($this->loginUser->corp_id > 0) {
            $verifyData['corp_id'] = $this->loginUser->corp_id;
        }

        $ChargeDataStatistics = new ChargeDataStatistics();
        try {
            $result = $ChargeDataStatistics->dailyStatistics($verifyData['year'], $verifyData['month'], $verifyData['corp_id']);
            return $this->res_success($result);
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            return $this->res_error([], '服务器异常');
        }


    }

    #[
        Apidoc\Title("查询充电站的月排行榜前10名"),
        Apidoc\Desc('每月月初重置榜单'),
        Apidoc\Author("cbj 2023.10.20 新增, cbj 2023.11.15 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/monitoringCenter/stationsMonthRankingListTop10"),
        Apidoc\Param(name: "type", type: "string", require: true, desc: "榜单类型 charge_income:营业额(充电收入) electricity:充电量 order_count:订单量 utilization_rate:利用率"),
        Apidoc\Param(name: "corp_id", type: "int", require: false, desc: "运营商编号 不传递这个参数表示查询所有运营商的"),

        Apidoc\Returned(name: "list", type: "array", desc: "统计数据", children: [
            ['name' => 'corp_id', 'type' => 'int', 'desc' => '运营商ID'],
            ['name' => 'station_id', 'type' => 'int', 'desc' => '充电站ID'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '充电站名称'],
            ['name' => 'value', 'type' => 'int', 'desc' => '排行榜数值(营业额(单位:分)/充电量(精确到小数点后4位 | 单位：kW.h)/订单量/利用率[暂时没有完成])'],
        ]),
    ]
    public function stationsMonthRankingListTop10(): Json
    {
        $data = $this->request->post();
        // 验证数据
        $verifyData = v::input($data, VerifyData::statistics([
            'type',
            'corp_id',
        ]));
        $verifyData['corp_id'] = !empty($verifyData['corp_id']) ? $verifyData['corp_id'] : 0;
        if ($this->loginUser->corp_id > 0) {
            $verifyData['corp_id'] = $this->loginUser->corp_id;
        }

        $dispatch = app(StationsMonthRankingListDispatch::class);
        $list = $dispatch->rankingListTop10($verifyData['type'], $verifyData['corp_id']);

        return $this->res_success([
            'list' => $list
        ]);
    }

    #[
        Apidoc\Title("按区域汇总返回场站数据"),
        Apidoc\Author("lwj 2024.8.30 新增,lwj 2024.9.18 修改"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/monitoringCenter/getCityStations"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'total', 'type' => 'int', 'desc' => '总枪数'],
            ['name' => 'child', 'type' => 'array', 'desc' => '子节点', 'children' => [
                ['name' => 'total', 'type' => 'int', 'desc' => '总枪数'],
                ['name' => 'child', 'type' => 'array', 'desc' => '子节点', 'children' => [
                    ['name' => 'name', 'type' => 'string', 'desc' => '场站名'],
                    ['name' => 'guns', 'type' => 'int', 'desc' => '枪数'],
                ]]
            ]],
        ]),
    ]
    public function getCityStations(): Json
    {
        try {
            $where = [];
            if ($this->loginUser->corp_id > 0) {
                $where[] = ['corp_id', '=', $this->loginUser->corp_id];
            }
            $stations_shots = Db::name('shots')
                ->where($where)
                ->group('station_id')
                ->column('count(id) as count', 'station_id');

            $stations = Db::name('stations')
                ->where($where)
                ->field('id,name,province,city')
                ->withAttr('guns', function ($value, $data) use ($stations_shots) {
                    return $stations_shots[$data['id']] ?? 0;
                })
                ->select()
                ->toArray();

            $stations2 = arr_group($stations, 'province');
            $new_stations = [];


            foreach ($stations2 as $k => $v) {
                $city_stations = arr_group($v, 'city');
//                $new_stations[$k]['total']=array_sum(array_column($v,'guns'));
                $new_stations[$k]['total'] = count($v);
                foreach ($city_stations as $kk => $vv) {
                    $new_stations[$k]['child'][$kk] = [
                        'total' => array_sum(array_column($vv, 'guns')),
                        'child' => array_map(function ($item) {
                            return [
                                "name" => $item["name"],
                                "guns" => $item["guns"]
                            ];
                        }, $vv)
                    ];
                }
            }

            return $this->res_success($new_stations);
        } catch (Throwable $e) {
            return $this->res_error([], '服务器异常');
        }
    }
}