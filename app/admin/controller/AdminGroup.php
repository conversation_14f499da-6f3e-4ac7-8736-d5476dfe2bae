<?php
/** @noinspection PhpUnused */

/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */

namespace app\admin\controller;

use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\model\AdminAuthGroup as AdminAuthGroupModel;
use app\common\model\AdminAuthGroupRules as AdminAuthGroupRulesModel;
use app\common\model\AdminUsers;
use app\common\model\CorpAdminGroup;
use app\common\traits\Curd;
use app\ms\Api;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\db\Query;
use think\response\Json;
use Throwable;

#[Apidoc\Title("系统管理/管理组")]
class AdminGroup extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new AdminAuthGroupModel();
    }

    #[
        Apidoc\Title("管理组列表"),
        Apidoc\Author("lwj 2023.10.17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/AdminGroup/admin_group_list"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "组id"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "组名"),
        Apidoc\Param(name: "state", type: "int", require: true, desc: "状态，1正常，2禁用"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '组id'],
            ['name' => 'group_name', 'type' => 'string', 'desc' => '组名'],
            ['name' => 'state', 'type' => 'int', 'desc' => '状态，1正常，2禁用'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
        ]),
    ]
    public function admin_group_list(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '管理组列表'));

        // 如果是运营商类型的管理员暂时先不让他们查询角色数据。
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        $data = $this->request->post();
        $page = ($data['page'] ?? false) ?: 1;
        $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
        $order_name = ($data['order_name'] ?? false) ?: 'id';
        $order_type = ($data['order_type'] ?? false) ?: 'desc';

        // 搜索框搜索
        $where = function (Query $query) use ($data) {
            if (isset($data['id']) && $data['id']) {
                $query->where('id', $data['id']);
            }
            if (isset($data['name']) && $data['name']) {
                $query->where('group_name', 'like', '%' . $data['name'] . '%');
            }
            if (isset($data['state']) && $data['state']) {
                $query->where('state', $data['state']);
            }

            return $query;
        };

        $list = $this->modelClass
            ->field('id,group_name,state,create_time')
            ->where($where)
            ->order($order_name, $order_type)
            ->paginate(['list_rows' => $pageSize, 'page' => $page]);

        return $this->res_success($list);
    }


    #[
        Apidoc\Title("获取管理组排序信息"),
        Apidoc\Author("lwj 2023.10.17 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/AdminGroup/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '获取管理组排序信息'));

        // 如果是运营商类型的管理员暂时先不让他们查询角色数据。
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        $res = [
            'order_name' => 'id',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'id', 'label' => '管理员id'],
                ['value' => 'state', 'label' => '状态'],
                ['value' => 'group_name', 'label' => '组名'],
                ['value' => 'create_time', 'label' => '创建时间'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("管理组详情"),
        Apidoc\Author("lwj 2023.10.17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/AdminGroup/get_info"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "组id"),

        Apidoc\Returned(name: "id", type: "int", desc: "组id"),
        Apidoc\Returned(name: "group_name", type: "string", desc: "组名"),
        Apidoc\Returned(name: "state", type: "int", desc: "状态，1正常，2禁用"),
    ]
    public function get_info(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '管理组详情'));

        // 如果是运营商类型的管理员暂时先不让他们查询角色数据。
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::admin_group([
                'id',
            ]));

            $result = $this->modelClass
                ->field('id,group_name,state')
                ->find($verify_data['id']);
            if ($result) return $this->res_success($result);
            return $this->res_error([], '管理员不存在');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("新增管理组"),
        Apidoc\Author("lwj 2023.10.17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/AdminGroup/add"),
        Apidoc\Param(name: "group_name", type: "string", require: true, desc: "组名"),
    ]
    public function add(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '新增管理组'));

        // 如果是运营商类型的管理员暂时先不让他们查询角色数据。
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::admin_group([
                'group_name',
            ]));

            $info = $this->modelClass
                ->field('id')
                ->where('group_name', $verify_data['group_name'])
                ->find();
            if ($info) return $this->res_error([], '组名已存在');

            $verify_data['create_time'] = date('Y-m-d H:i:s');
            $res = $this->modelClass->create($verify_data);

            // ========== 将数据同步到微服务 ==========
            $res['service_id'] = 1;
            Api::send("/auth/admin/group", "POST", $res,[],'id,service_id');
            if ($res) return $this->res_success([], '新增成功');
            return $this->res_error([], '新增失败');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("编辑管理组"),
        Apidoc\Author("lwj 2023.10.17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/AdminGroup/edit"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "组id"),
        Apidoc\Param(name: "group_name", type: "string", require: true, desc: "组名"),
    ]
    public function edit(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '编辑管理组'));

        // 如果是运营商类型的管理员暂时先不让他们查询角色数据。
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::admin_group([
                'id',
                'group_name',
            ]));

            $id = $verify_data['id'];
            unset($verify_data['id']);

            $info = $this->modelClass
                ->field('id')
                ->where('id', '<>', $id)
                ->where('group_name', $verify_data['group_name'])
                ->find();
            if ($info) return $this->res_error([], '组名已存在');

            $res = $this->modelClass->where('id', $id)->update($verify_data);

            // ========== 将数据同步到微服务 ==========
            $res['service_id'] = 1;
            Api::send("/auth/admin/group", "PUT", $res,[],'id,service_id');
            if ($res) return $this->res_success([], '编辑成功');
            return $this->res_error([], '编辑失败');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("删除管理员"),
        Apidoc\Author("lwj 2023.10.17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/AdminGroup/delete"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "管理员id"),
    ]
    public function delete(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '删除管理员'));

        // 如果是运营商类型的管理员暂时先不让他们查询角色数据。
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::admin_group([
                'id',
            ]));

            $admin_user = (new AdminUsers)->where('perm_group_id', $verify_data['id'])->value('id');
            if ($admin_user) return $this->res_error([], '请先删除已绑定的管理员：' . $admin_user);

            $res = $this->modelClass->where('id', $verify_data['id'])->delete();

            // ========== 将数据同步到微服务 ==========
            $verify_data['service_id'] = 1;
            Api::send("/auth/admin/group", "DELETE", $verify_data,[],'id,service_id');
            if ($res) return $this->res_success([], '删除成功');
            return $this->res_error([], '删除失败');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取管理组权限列表"),
        Apidoc\Author("lwj 2023.10.17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/AdminGroup/get_rule_list"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "组id"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "数据", children: [
            ['name' => 'list', 'type' => 'array', 'desc' => '权限列表'],
        ]),
    ]
    public function get_rule_list(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '获取管理组权限列表'));

        // 如果是运营商类型的管理员暂时先不让他们查询角色数据。
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            if ($this->loginUser->corp_id > 0) {
                return $this->res_success(['list' => []]);
            }
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::admin_group([
                'id',
            ]));

            $list = $this->modelClass->alias('a')
                ->join('admin_auth_group_rules b', 'a.id = b.group_id')
                ->where('a.id', $verify_data['id'])
                ->column('b.rule_name');
            return $this->res_success(['list' => $list]);
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("设置管理组权限"),
        Apidoc\Author("lwj 2023.10.18 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/AdminGroup/set_admin_group_rule"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "组id"),
        Apidoc\Param(name: "rule", type: "array", require: true, desc: "权限数组"),
    ]
    public function set_admin_group_rule(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '设置管理组权限'));

        // 如果是运营商类型的管理员暂时先不让他们查询角色数据。
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::admin_group([
                'id',
                'rule',
            ]));

            $group = $this->modelClass->field('id')->find($verify_data['id']);
            if (!$group) return $this->res_error([], '管理组不存在');

            $group_rule = new AdminAuthGroupRulesModel;

            if (!$verify_data['rule']) {
                $group_rule->where('group_id', $verify_data['id'])->delete();
                return $this->res_success([], '设置成功');
            }

            $rule_name = $group_rule
                ->where('group_id', $verify_data['id'])
                ->column('rule_name');

            $del_rule = array_diff($rule_name, $verify_data['rule']);
            if ($del_rule) {
                $group_rule
                    ->where('group_id', $verify_data['id'])
                    ->whereIn('rule_name', $del_rule)
                    ->delete();
            }

            $add_rule = array_diff($verify_data['rule'], $rule_name);
            if ($add_rule) {
                $add_data = [];
                foreach ($add_rule as $v) {
                    $add_data[] = [
                        'group_id' => $verify_data['id'],
                        'rule_name' => $v,
                    ];
                }
                $group_rule->insertAll($add_data);
            }
            return $this->res_success([], '设置成功');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }


    #[
        Apidoc\Title("管理组选项"),
        Apidoc\Author("cbj 2024.04.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/AdminGroup/get_options"),
        Apidoc\Returned(name: "options", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '组id'],
            ['name' => 'group_name', 'type' => 'string', 'desc' => '组名'],
        ]),
    ]
    public function get_options(): Json
    {
        LogCollector::collectorRunLog(sprintf('这次调用%s接口', '管理组选项'));

        $where = [];
        // 如果是运营商类型的管理员暂时先不让他们查询角色数据。
        if ($this->loginUser->corp_id > 0) {
            $admin_group_ids = (new CorpAdminGroup())->getCorpAdminGroupIds($this->loginUser->corp_id);
            $where[] = ['id', 'in', $admin_group_ids];
        }


        $options = $this->modelClass
            ->field('id,group_name')
            ->where($where)
            ->select()
            ->toArray();

        return $this->res_success([
            'options' => $options
        ]);
    }


}