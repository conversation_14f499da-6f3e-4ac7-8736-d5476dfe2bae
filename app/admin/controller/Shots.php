<?php
/** @noinspection PhpUnused */

/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */

namespace app\admin\controller;

use app\common\lib\ExceptionLogCollector;
use app\common\lib\VerifyData;
use app\common\logic\admin\shots\Add;
use app\common\logic\admin\shots\Delete;
use app\common\logic\admin\shots\Update;
use app\common\model\StationDataAuthority;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use app\common\model\Shots as ShotsModel;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;

use think\response\Json;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use Throwable;

#[Apidoc\Title("资源中心/充电枪管理")]
class Shots extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new ShotsModel();
    }

    #[
        Apidoc\Title("充电枪列表"),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.8 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Shots/shots_list"),
        Apidoc\Param(name: "corp_id", type: "int", require: true, desc: "运营商编号"),
        Apidoc\Param(name: "station_id", type: "string", require: true, desc: "场站id"),
        Apidoc\Param(name: "piles_id", type: "string", require: true, desc: "充电桩编号，14位数字"),
        Apidoc\Param(name: "is_online", type: "int", require: true, desc: "枪在线状态：1-离线，2-在线"),
        Apidoc\Param(name: "work_status", type: "int", require: true, desc: "枪工作状态：1-空闲，2-充电中"),
        Apidoc\Param(name: "link_status", type: "int", require: true, desc: "枪连接状态：1-无连接，2-连接中"),
        Apidoc\Param(name: "id", type: "string", require: true, desc: "充电枪编号，16位数字"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "充电枪名称"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电枪编号，16位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电枪名称'],
            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '充电站名称'],
            ['name' => 'piles_name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'sequence', 'type' => 'int', 'desc' => '枪序号'],
            ['name' => 'qrcode', 'type' => 'string', 'desc' => '枪二维码url'],
            ['name' => 'port_num', 'type' => 'int', 'desc' => '虚拟枪号，用于485访问的地址'],
            ['name' => 'is_online', 'type' => 'int', 'desc' => '枪在线状态：1-离线，2-在线'],
            ['name' => 'work_status', 'type' => 'int', 'desc' => '枪工作状态：1-空闲，2-充电中'],
            ['name' => 'link_status', 'type' => 'int', 'desc' => '枪连接状态：1-无连接，2-连接中'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
        ]),
    ]
    public function shots_list(): Json
    {
        $data = $this->request->post();
        $page = ($data['page'] ?? false) ?: 1;
        $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
        $order_name = ($data['order_name'] ?? false) ?: 's.id';
        $order_type = ($data['order_type'] ?? false) ?: 'desc';

        // 数据权限
        if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
            $data['corp_id'] = $this->loginUser->corp_id;
        } else if ($this->loginUser->corp_id > 0) {
            $data['corp_id'] = $this->loginUser->corp_id;
            // 查询当前子账号拥有的场站权限
            $data['station_id'] = (new StationDataAuthority())->getStationIds($this->loginUser->id);
        }

        // 搜索框搜索
        $where = function ($query) use ($data) {
            $query->where('p.is_del', '=', \app\common\model\Piles::IsDelNot);
            $query->where('s.is_del', '=', ShotsModel::IsDelNot);

            if (isset($data['id']) && $data['id']) {
                $query->where('s.id', $data['id']);
            }
            if (isset($data['name']) && $data['name']) {
                $query->where('s.name', 'like', '%' . $data['name'] . '%');
            }
            if (isset($data['corp_id']) && $data['corp_id']) {
                $query->where('s.corp_id', $data['corp_id']);
            }
            if (isset($data['station_id']) && $data['station_id']) {
                if (is_array($data['station_id'])) {
                    $query->where('s.station_id', 'in', $data['station_id']);
                } else {
                    $query->where('s.station_id', '=', $data['station_id']);
                }
            }
            if (isset($data['piles_id']) && $data['piles_id']) {
                $query->where('s.piles_id', $data['piles_id']);
            }
            if (isset($data['is_online']) && $data['is_online']) {
                if ($data['is_online'] === 1) {
                    $data['is_online'] = 2;
                } else {
                    $data['is_online'] = 1;
                }
                $query->where('ps.is_online', $data['is_online']);
            }
            if (isset($data['work_status']) && $data['work_status']) {
                $query->where('ss.work_status', $data['work_status']);
            }
            if (isset($data['link_status']) && $data['link_status']) {
                $query->where('s.link_status', $data['link_status']);
            }
            return $query;
        };


        $fields = [
            's.id', 's.name', 's.sequence', 'sq.qrcode', 's.port_num', 'ps.is_online',
            's.status', 'ss.work_status', 's.link_status', 's.create_time',
            'c.name as corp_name', 'sta.name as station_name', 'p.name as piles_name'
        ];

        $list = $this->modelClass->alias('s')
            ->leftJoin('shots_status ss', 'ss.id = s.id')
            ->leftJoin('shots_qrcode sq', 'sq.id = s.id')
            ->leftJoin('corp c', 's.corp_id = c.id')
            ->join('stations sta', 's.station_id = sta.id')
            ->join('piles p', 's.piles_id = p.id')
            ->join('piles_status ps', 'ps.id = s.piles_id')
            ->withAttr('is_online', function ($value) {
                if ($value === 1) {
                    return 2;
                } else {
                    return 1;
                }
            })
            ->field($fields)
            ->where($where)
            ->order($order_name, $order_type)
            ->paginate(['list_rows' => $pageSize, 'page' => $page])
            ->toArray();

        return $this->res_success($list);
    }

    #[
        Apidoc\Title("获取充电枪排序信息"),
        Apidoc\Author("lwj 2023.8.8 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Shots/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        $res = [
            'order_name' => 's.create_time',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 's.create_time', 'label' => '创建时间'],
                ['value' => 's.id', 'label' => '充电枪编号'],
                ['value' => 's.name', 'label' => '充电枪名称'],
                ['value' => 's.corp_id', 'label' => '运营商'],
                ['value' => 's.station_id', 'label' => '充电站'],
                ['value' => 's.piles_id', 'label' => '充电桩'],
                ['value' => 's.sequence', 'label' => '枪序号'],
                ['value' => 'ps.is_online', 'label' => '枪在线状态'],
                ['value' => 'ss.work_status', 'label' => '枪工作状态'],
                ['value' => 's.link_status', 'label' => '枪连接状态'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("充电枪详情"),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.8 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Shots/get_info"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "充电枪编号，16位数字"),
        Apidoc\Returned(name: "id", type: "int", desc: "充电枪编号，16位数字"),
        Apidoc\Returned(name: "name", type: "string", desc: "充电枪名称"),
        Apidoc\Returned(name: "corp_id", type: "string", desc: "运营商编号"),
        Apidoc\Returned(name: "station_id", type: "string", desc: "充电站编号"),
        Apidoc\Returned(name: "piles_id", type: "string", desc: "充电桩编号，14位数字"),
        Apidoc\Returned(name: "sequence", type: "int", desc: "枪序号"),
        Apidoc\Returned(name: "qrcode", type: "string", desc: "枪二维码url"),
        Apidoc\Returned(name: "port_num", type: "int", desc: "虚拟枪号，用于485访问的地址"),
    ]
    public function get_info(): Json
    {
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::shots([
                'id',
            ]));

            // 数据权限
            $where = [];
            $where[] = ['s.id', '=', $verify_data['id']];
            $where[] = ['p.is_del', '=', \app\common\model\Piles::IsDelNot];
            $where[] = ['s.is_del', '=', ShotsModel::IsDelNot];
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $where[] = ['s.corp_id', '=', $this->loginUser->corp_id];
            } else if ($this->loginUser->corp_id > 0) {
                $where[] = ['s.corp_id', '=', $this->loginUser->corp_id];
                // 查询当前子账号拥有的场站权限
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                $where[] = ['s.station_id', 'in', $station_ids];
            }

            $fields = [
                's.id', 's.name', 's.corp_id', 's.station_id', 's.piles_id', 's.sequence',
                'sq.qrcode', 's.port_num', 'ps.is_online', 'ss.work_status', 's.link_status'
            ];

            $result = $this->modelClass
                ->alias('s')
                ->leftJoin('shots_status ss', 'ss.id = s.id')
                ->leftJoin('shots_qrcode sq', 'sq.id = s.id')
                ->leftJoin('piles p', 'p.id = s.piles_id')
                ->leftJoin('piles_status ps', 's.piles_id = ps.id')
                ->withAttr('is_online', function ($value) {
                    if ($value == 1) {
                        return 2;
                    } else {
                        return 1;
                    }
                })
                ->field($fields)
                ->where($where)
                ->find();
            if ($result) return $this->res_success($result);
            return $this->res_error([], '充电枪不存在');
        } catch (ValidationException $e) {
            ExceptionLogCollector::collect($e);
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("新增充电枪"),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.8 修改, cbj 2024.12.10 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/shots/add"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "充电枪名称"),
        Apidoc\Param(name: "piles_id", type: "string", require: true, desc: "充电桩编号，14位数字"),
        Apidoc\Param(name: "sequence", type: "int", require: true, desc: "枪序号"),
        Apidoc\Returned(name: "new_id", type: "int", require: true, desc: "充电枪ID")
    ]
    public function add(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Add($this->loginUser, $this->request))->run();
        }, true);
    }

    #[
        Apidoc\Title("编辑充电枪"),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.8 修改, cbj 2024.12.10 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Shots/edit"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "充电枪编号，16位数字"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "充电枪名称"),
    ]
    public function edit(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Update($this->loginUser, $this->request))->run();
        });
    }

    #[
        Apidoc\Title("删除充电枪"),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.8 修改, cbj 2024.12.10 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Shots/delete"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "充电枪编号，16位数字"),
    ]
    public function delete(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Delete($this->loginUser, $this->request))->run();
        }, true);
    }

    #[
        Apidoc\Title("导出充电枪列表Excel"),
        Apidoc\Author("lwj 2023.8.8 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Shots/out_shots_list_excel"),
        Apidoc\Param(name: "corp_id", type: "int", require: true, desc: "运营商编号"),
        Apidoc\Param(name: "station_id", type: "string", require: true, desc: "场站id"),
        Apidoc\Param(name: "piles_id", type: "string", require: true, desc: "充电桩编号，14位数字"),
        Apidoc\Param(name: "is_online", type: "int", require: true, desc: "枪在线状态：1-离线，2-在线"),
        Apidoc\Param(name: "work_status", type: "int", require: true, desc: "枪工作状态：1-空闲，2-充电中"),
        Apidoc\Param(name: "link_status", type: "int", require: true, desc: "枪连接状态：1-无连接，2-连接中"),
        Apidoc\Param(name: "id", type: "string", require: true, desc: "充电枪编号，16位数字"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "充电枪名称"),

        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "url", type: "string", require: true, desc: "网址"),
        Apidoc\Returned(name: "title", type: "string", require: true, desc: "标题"),
    ]
    public function out_shots_list_excel(): Json
    {
        try {
            $data = $this->request->post();
            $order_name = ($data['order_name'] ?? false) ?: 's.id';
            $order_type = ($data['order_type'] ?? false) ?: 'desc';
            if ($order_name === 'a.id') {
                $order_name = 's.id';
            }

            // 数据权限
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $data['corp_id'] = $this->loginUser->corp_id;
            } else if ($this->loginUser->corp_id > 0) {
                $data['corp_id'] = $this->loginUser->corp_id;
                // 查询当前子账号拥有的场站权限
                $data['station_id'] = (new StationDataAuthority())->getStationIds($this->loginUser->id);
            }

            // 搜索框搜索
            $where = function ($query) use ($data) {
                $query->where('p.is_del', '=', \app\common\model\Piles::IsDelNot);
                $query->where('s.is_del', '=', ShotsModel::IsDelNot);

                if (isset($data['id']) && $data['id']) {
                    $query->where('s.id', $data['id']);
                }
                if (isset($data['name']) && $data['name']) {
                    $query->where('s.name', 'like', '%' . $data['name'] . '%');
                }
                if (isset($data['corp_id']) && $data['corp_id']) {
                    $query->where('s.corp_id', $data['corp_id']);
                }
                if (isset($data['station_id'])) {
                    if (is_array($data['station_id'])) {
                        $query->where('s.station_id', 'in', $data['station_id']);
                    } else if (!empty($data['station_id'])) {
                        $query->where('s.station_id', '=', $data['station_id']);
                    }
                }
                if (isset($data['piles_id']) && $data['piles_id']) {
                    $query->where('s.piles_id', $data['piles_id']);
                }
                if (isset($data['is_online']) && $data['is_online']) {
                    if ($data['is_online'] == 1) {
                        $data['is_online'] = 2;
                    } else {
                        $data['is_online'] = 1;
                    }
                    $query->where('ps.is_online', $data['is_online']);
                }
                if (isset($data['work_status']) && $data['work_status']) {
                    $query->where('s.work_status', $data['work_status']);
                }
                if (isset($data['link_status']) && $data['link_status']) {
                    $query->where('s.link_status', $data['link_status']);
                }
                return $query;
            };

            $fields = [
                's.id', 's.name', 's.sequence', 'sq.qrcode', 's.port_num', 'ps.is_online',
                'ss.work_status', 's.link_status', 's.create_time', 'c.name as corp_name',
                'sta.name as station_name', 'p.name as piles_name'
            ];

            $list = $this->modelClass->alias('s')
                ->join('corp c', 's.corp_id = c.id')
                ->join('stations sta', 's.station_id = sta.id')
                ->join('piles p', 's.piles_id = p.id')
                ->join('piles_status ps', 'ps.id = s.piles_id')
                ->leftJoin('shots_status ss', 'ss.id = s.id')
                ->leftJoin('shots_qrcode sq', 'sq.id = s.id')
                ->withAttr('is_online', function ($value) {
                    if ($value == 1) {
                        return 2;
                    } else {
                        return 1;
                    }
                })
                ->field($fields)
                ->where($where)
                ->order($order_name, $order_type)
                ->select()
                ->toArray();


            if (!$list) return $this->res_error([], '无数据');

            $header = ['充电枪编号', '充电枪名称', '运营商名称', '充电站名称', '充电桩名称', '枪序号', '虚拟枪号', '枪二维码'];
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // 设置列宽度
            $sheet->getColumnDimension('A')->setWidth(20);
            $sheet->getColumnDimension('B')->setWidth(20);
            $sheet->getColumnDimension('C')->setWidth(30);
            $sheet->getColumnDimension('D')->setWidth(20);
            $sheet->getColumnDimension('E')->setWidth(20);
            $sheet->getColumnDimension('F')->setWidth(20);
            $sheet->getColumnDimension('G')->setWidth(20);
            $sheet->getColumnDimension('H')->setWidth(20);

            foreach ($header as $k => $h) {
                $column = Coordinate::stringFromColumnIndex($k + 1); // 将索引转换为列字母
                $coordinate = $column . '1'; // 列字母和行号的组合
                $sheet->setCellValue($coordinate, $h);
            }

            $row = 2;
            foreach ($list as $v) {
                $sheet->getRowDimension($row)->setRowHeight(100);
                $sheet->setCellValueExplicit('A' . $row, $v['id'], DataType::TYPE_STRING)
                    ->setCellValue('B' . $row, $v['name'])
                    ->setCellValue('C' . $row, $v['corp_name'])
                    ->setCellValue('D' . $row, $v['station_name'])
                    ->setCellValue('E' . $row, $v['piles_name'])
                    ->setCellValue('F' . $row, $v['sequence'])
                    ->setCellValue('G' . $row, $v['port_num']);

                $drawing = new Drawing();
                $drawing->setName('QRCode');
                $drawing->setDescription('QRCode');
                $drawing->setPath($v['qrcode']);
                $drawing->setCoordinates('H' . $row);
                $drawing->setWidth(100);
                $drawing->setHeight(100);
                $drawing->setOffsetX(20); // 设置水平偏移量
                $drawing->setOffsetY(15); // 设置垂直偏移量
                $drawing->setWorksheet($sheet);

                $row++;
            }

            // 设置所有单元格的垂直居中和水平居中
            $sheet->getStyle('A1:H' . ($row - 1))->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
            $sheet->getStyle('A1:H' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

            $Xlsx = new Xlsx($spreadsheet);
            $dir = 'excel/xlsx/';
            $dir2 = public_path() . '/excel/xlsx/';
            $file_name = '充电枪列表' . date('Ymd_His') . '.xlsx';
            $file = public_path() . $dir . $file_name;
            if (!is_dir($dir2)) mkdir($dir2, 0777, true);
            $url = config('my.host') . $dir . $file_name;
            $Xlsx->save($file);

            return $this->res_success(['url' => $url, 'title' => $file_name]);
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }


    #[
        Apidoc\Title("批量生成二维码(已废弃)"),
        Apidoc\Author("lwj 2023.10.7 新增, cbj 2024.11.20 已废弃"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Shots/batch_create_qr"),
    ]
    public function batch_create_qr(): Json
    {
        return $this->res_error([], '已废弃');
    }


}