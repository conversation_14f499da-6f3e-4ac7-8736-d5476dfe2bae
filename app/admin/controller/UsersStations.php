<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;

use app\ms\Api;
use hg\apidoc\annotation as Apidoc;
use think\response\Json;

#[Apidoc\Title("用户中心\场站用户")]
class UsersStations extends BaseController
{

    #[
        Apidoc\Title("场站用户列表"),
        Apidoc\Author("cccq 2025.3.15 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/UsersStations/list"),
        Apidoc\Param(name: "page", type: "string", require: true, default: "1", desc: "页码"),
        Apidoc\Param(name: "limit", type: "string", require: true, default: "10", desc: "每页数量"),
        Apidoc\Param(name: "station_ids", type: "string", require: true, default: "1013,1016", desc: "站点ID，多个用逗号分隔"),
        Apid<PERSON>\Param(name: "phone", type: "string", require: true, default: "12345678901", desc: "手机号"),
        Apidoc\Param(name: "name", type: "string", require: true, default: "cq", desc: "用户名称"),
    ]
    public function list(): ?Json
    {
        $input = $this->request->get();
        $response = Api::send("/auth/user/stations", 'GET', [
            'page' => $input['page'] ?? 1,
            'limit' => $input['limit'] ?? 10,
            'station_ids' => $input['station_ids'] ?? '',
            'phone' => $input['phone'] ?? '',
            'name' => $input['name'] ?? '',
            'plate' => $input['plate'] ?? '',
        ]);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }

    #[
        Apidoc\Title("添加场站用户"),
        Apidoc\Author("cccq 2025.3.15 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/UsersStations/add"),
        Apidoc\Param(name: "plate", type: "string", require: true, desc: "车牌号"),
        Apidoc\Param(name: "phone", type: "string", require: true, desc: "手机号"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "用户名称"),
        Apidoc\Param(name: "station_id", type: "integer", require: true, desc: "站点ID"),
        Apidoc\Param(name: "level_id", type: "integer", require: true, desc: "等级ID"),
        Apidoc\Param(name: "opt_id", type: "integer", require: true, desc: "操作人ID"),
        Apidoc\Param(name: "remark", type: "string", require: false, desc: "备注"),
    ]
    public function add(): ?Json
    {
        $input = $this->request->post();
        $response = Api::send("/auth/user/stations", 'POST', [
            'plate' => $input['plate'] ?? '',
            'phone' => $input['phone'] ?? '',
            'name' => $input['name'] ?? '',
            'station_id' => $input['station_id'] ?? '',
            'level_id' => $input['level_id'] ?? '',
            'opt_id' => $input['opt_id'] ?? $this->loginUser->id,
            'remark' => $input['remark'] ?? '',
        ]);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }

    #[
        Apidoc\Title("更新场站用户"),
        Apidoc\Author("cccq 2025.3.15 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/UsersStations/update"),
        Apidoc\Param(name: "id", type: "integer", require: true, desc: "用户ID"),
        Apidoc\Param(name: "plate", type: "string", require: false, desc: "车牌号"),
        Apidoc\Param(name: "phone", type: "string", require: false, desc: "手机号"),
        Apidoc\Param(name: "name", type: "string", require: false, desc: "用户名称"),
        Apidoc\Param(name: "level_id", type: "integer", require: false, desc: "等级ID"),
        Apidoc\Param(name: "remark", type: "string", require: false, desc: "备注"),
    ]
    public function update(): ?Json
    {
        $input = $this->request->post();
        $response = Api::send("/auth/user/stations", 'PUT', [
            'id' => $input['id'],
            'plate' => $input['plate'] ?? '',
            'phone' => $input['phone'] ?? '',
            'name' => $input['name'] ?? '',
            'level_id' => $input['level_id'] ?? '',
            'remark' => $input['remark'] ?? 1,
        ]);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }

    #[
        Apidoc\Title("删除场站用户"),
        Apidoc\Author("cccq 2025.3.15 新增"),
        Apidoc\Method("DELETE"),
        Apidoc\Url("/admin/UsersStations/delete"),
        Apidoc\Param(name: "id", type: "integer", require: true, desc: "用户ID"),
    ]
    public function delete(): ?Json
    {
        $input = $this->request->post();
        $response = Api::send("/auth/user/stations", 'DELETE', [
            'id' => $input['id'] ?? '',
        ]);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }
}