<?php
/** @noinspection PhpUnusedLocalVariableInspection */

/** @noinspection PhpUnused */

namespace app\admin\controller;

use app\common\lib\VerifyData;
use app\common\model\Order;
use app\common\model\ShotsStatus;
use app\common\model\StationDataAuthority;
use app\common\model\StationsMonthStatistics;
use app\common\model\StationsStats;
use app\common\model\StationsYearStatistics;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\response\Json;

#[Apidoc\Title("运营中心/场站监控")]
class StationsMonitor extends BaseController
{
    use Curd;

    #[
        Apidoc\Title("电量使用情况统计"),
        Apidoc\Author("cbj 2023.10.20 新增, cbj 2023.11.2 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/stationsMonitor/electricityUsageStatistics"),
        Apidoc\Param(name: "corp_id", type: "int", require: false, default: 0, desc: "运营商ID 传递为0时表示统计所有运营商的"),
        Apidoc\Param(name: "station_id", type: "int", require: true, default: 0, desc: "充电站ID 传递为0时表示统计所有充电站的"),
        Apidoc\Returned(name: "data", type: "array", desc: "统计数据", children: [
            ['name' => 'sharp_electricity', 'type' => 'int', 'desc' => '尖电量(精确到小数点后4位 | 单位：kW.h)'],
            ['name' => 'peak_electricity', 'type' => 'int', 'desc' => '峰电量(精确到小数点后4位 | 单位：kW.h)'],
            ['name' => 'flat_electricity', 'type' => 'int', 'desc' => '平电量(精确到小数点后4位 | 单位：kW.h)'],
            ['name' => 'valley_electricity', 'type' => 'int', 'desc' => '谷电量(精确到小数点后4位 | 单位：kW.h)'],
        ]),
    ]
    public function electricityUsageStatistics(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            if (empty($data['station_id'])) $data['station_id'] = 0;
            if (empty($data['corp_id'])) $data['corp_id'] = 0;
            // 验证数据
            $verifyData = v::input($data, VerifyData::statistics([
                'station_id',
                'corp_id',
            ]));
            $verifyData['corp_id'] = !empty($verifyData['corp_id']) ? $verifyData['corp_id'] : 0;
            $verifyData['station_id'] = !empty($verifyData['station_id']) ? $verifyData['station_id'] : 0;

            // 数据权限
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $verifyData['corp_id'] = $this->loginUser->corp_id;
            } else if ($this->loginUser->corp_id > 0) {
                $verifyData['corp_id'] = $this->loginUser->corp_id;
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                if (!empty($verifyData['station_id']) && in_array($verifyData['station_id'], $station_ids) === false) {
                    $verifyData['station_id'] = [];
                }
            }
            $OrderModel = app(Order::class);
            return $OrderModel->electricityUsageStatistics($verifyData['corp_id'], $verifyData['station_id']);
        });
    }

    #[
        Apidoc\Title("充电枪状态统计"),
        Apidoc\Author("cbj 2023.10.20 新增, cbj 2023.11.2 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/stationsMonitor/shotsStatusStatistics"),
        Apidoc\Param(name: "corp_id", type: "int", require: false, default: 0, desc: "运营商ID 传递为0时表示统计所有运营商的"),
        Apidoc\Param(name: "station_id", type: "int", require: false, default: 0, desc: "充电站ID 传递为0时表示统计所有充电站的"),
        Apidoc\Returned(name: "data", type: "array", desc: "统计数据", children: [
            ['name' => 'status', 'type' => 'int', 'desc' => '充电枪状态 -1:总数量 0:离线 1:故障 2:空闲 3:充电'],
            ['name' => 'count', 'type' => 'int', 'desc' => '数量'],
        ]),
    ]
    public function shotsStatusStatistics(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            if (empty($data['corp_id'])) $data['corp_id'] = 0;
            if (empty($data['station_id'])) $data['station_id'] = 0;
            // 验证数据
            $verifyData = v::input($data, VerifyData::statistics([
                'station_id',
                'corp_id',
            ]));
            $verifyData['corp_id'] = !empty($verifyData['corp_id']) ? $verifyData['corp_id'] : 0;
            $verifyData['station_id'] = !empty($verifyData['station_id']) ? $verifyData['station_id'] : 0;

            // 数据权限
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $verifyData['corp_id'] = $this->loginUser->corp_id;
            } else if ($this->loginUser->corp_id > 0) {
                $verifyData['corp_id'] = $this->loginUser->corp_id;
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                if (!empty($verifyData['station_id'])) {
                    if (in_array($verifyData['station_id'], $station_ids) === false) {
                        $verifyData['station_id'] = [];
                    }
                } else {
                    $verifyData['station_id'] = $station_ids;
                }

            }
            $ShotsStatusModel = app(ShotsStatus::class);
            return $ShotsStatusModel->statusStatistics($verifyData['corp_id'], $verifyData['station_id']);
        });

    }

    #[
        Apidoc\Title("统计数据"),
        Apidoc\Author("cbj 2023.10.21 新增, cbj 2023.11.2 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/stationsMonitor/statisticsData"),
        Apidoc\Param(name: "corp_id", type: "int", require: false, default: 0, desc: "运营商ID 传递为0时表示查询所有运营商"),
        Apidoc\Param(name: "station_id", type: "int", require: false, default: 0, desc: "充电站ID 传递为0时表示查询所有充电站"),

        Apidoc\Returned(name: "data", type: "array", desc: "统计数据", children: [
            ['name' => 'todayData', 'type' => 'array', 'desc' => '当天统计数据', 'children' => [
                ['name' => 'money', 'type' => 'int', 'desc' => '充电收入(单位:分)'],
                ['name' => 'electricity_total', 'type' => 'int', 'desc' => '充电量(精确到小数点后4位 | 单位:kW.h)'],
                ['name' => 'service_money', 'type' => 'int', 'desc' => '服务费(精确到小数点后5位 | 单位:元)'],
                ['name' => 'order_count', 'type' => 'int', 'desc' => '充电订单数量'],
            ]],
            ['name' => 'thisMonthData', 'type' => 'array', 'desc' => '当月统计数据', 'children' => [
                ['name' => 'money', 'type' => 'int', 'desc' => '充电收入(单位:分)'],
                ['name' => 'electricity_total', 'type' => 'int', 'desc' => '充电量(精确到小数点后4位 | 单位:kW.h)'],
                ['name' => 'service_money', 'type' => 'int', 'desc' => '服务费(精确到小数点后5位 | 单位:元)'],
                ['name' => 'order_count', 'type' => 'int', 'desc' => '充电订单数量'],
            ]],
            ['name' => 'thisYearData', 'type' => 'array', 'desc' => '今年统计数据', 'children' => [
                ['name' => 'money', 'type' => 'int', 'desc' => '充电收入(单位:分)'],
                ['name' => 'electricity_total', 'type' => 'int', 'desc' => '充电量(精确到小数点后4位 | 单位:kW.h)'],
                ['name' => 'service_money', 'type' => 'int', 'desc' => '服务费(精确到小数点后5位 | 单位:元)'],
                ['name' => 'order_count', 'type' => 'int', 'desc' => '充电订单数量'],
            ]],
            ['name' => 'accumulateData', 'type' => 'array', 'desc' => '累计统计数据', 'children' => [
                ['name' => 'money', 'type' => 'int', 'desc' => '充电收入(单位:分)'],
                ['name' => 'electricity_total', 'type' => 'int', 'desc' => '充电量(精确到小数点后4位 | 单位:kW.h)'],
                ['name' => 'service_money', 'type' => 'int', 'desc' => '服务费(精确到小数点后5位 | 单位:元)'],
                ['name' => 'order_count', 'type' => 'int', 'desc' => '充电订单数量'],
            ]],
        ]),
    ]
    public function statisticsData(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            if (empty($data['corp_id'])) $data['corp_id'] = 0;
            if (empty($data['station_id'])) $data['station_id'] = 0;
            // 验证数据
            $verifyData = v::input($data, VerifyData::statistics([
                'corp_id',
                'station_id'
            ]));
            $verifyData['corp_id'] = !empty($verifyData['corp_id']) ? $verifyData['corp_id'] : 0;
            $verifyData['station_id'] = !empty($verifyData['station_id']) ? $verifyData['station_id'] : 0;

            // 数据权限
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $verifyData['corp_id'] = $this->loginUser->corp_id;
            } else if ($this->loginUser->corp_id > 0) {
                $verifyData['corp_id'] = $this->loginUser->corp_id;
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                if (!empty($verifyData['station_id'])) {
                    if (in_array($verifyData['station_id'], $station_ids) === false) {
                        $verifyData['station_id'] = [];
                    }
                } else {
                    $verifyData['station_id'] = $station_ids;
                }
            }

            $Order = app(Order::class);
            $todayData = $Order->thisDayStatistic($verifyData['corp_id'], $verifyData['station_id']);
            $StationsStats = app(StationsStats::class);
            $thisMonthData = $StationsStats->thisMonthStatistic($verifyData['corp_id'], $verifyData['station_id']);

            $StationsMonthStatistics = app(StationsMonthStatistics::class);
            $thisYearData = $StationsMonthStatistics->thisYearStatistic($verifyData['corp_id'], $verifyData['station_id']);

            $StationsYearStatistics = app(StationsYearStatistics::class);
            $accumulateData = $StationsYearStatistics->accumulateStatistic($verifyData['corp_id'], $verifyData['station_id']);

            $thisMonthData['money'] += $todayData['money'];
            $thisMonthData['electricity_total'] += $todayData['electricity_total'];
            $thisMonthData['service_money'] += $todayData['service_money'];
            $thisMonthData['order_count'] += $todayData['order_count'];

            $thisYearData['money'] += $thisMonthData['money'];
            $thisYearData['electricity_total'] += $thisMonthData['electricity_total'];
            $thisYearData['service_money'] += $thisMonthData['service_money'];
            $thisYearData['order_count'] += $thisMonthData['order_count'];

            $accumulateData['money'] += $thisYearData['money'];
            $accumulateData['electricity_total'] += $thisYearData['electricity_total'];
            $accumulateData['service_money'] += $thisYearData['service_money'];
            $accumulateData['order_count'] += $thisYearData['order_count'];

            return [
                'todayData' => $todayData,
                'thisMonthData' => $thisMonthData,
                'thisYearData' => $thisYearData,
                'accumulateData' => $accumulateData,
            ];
        });
    }
}