<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;

use app\common\lib\VerifyData;
use app\common\logic\admin\piles\Add;
use app\common\logic\admin\piles\Delete;
use app\common\logic\admin\piles\Update;
use app\ms\Api;
use app\common\model\Piles as PilesModel;
use app\common\model\Shots;
use app\common\model\StationDataAuthority;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\db\Query;
use think\response\Json;
use Throwable;

#[Apidoc\Title("资源中心/充电桩管理")]
class Piles extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new PilesModel();
    }

    #[
        Apidoc\Title("充电桩列表"),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.8 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Piles/piles_list"),
        Apidoc\Param(name: "corp_id", type: "int", require: true, desc: "运营商编号"),
        Apidoc\Param(name: "station_id", type: "string", require: true, desc: "场站id"),
        Apidoc\Param(name: "id", type: "string", require: true, desc: "充电桩编号，14位数字"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "充电桩名称"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电桩编号，14位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '充电站名称'],
            ['name' => 'ac_dc', 'type' => 'int', 'desc' => '交直流：0表示直流桩，1表示交流桩'],
            ['name' => 'type', 'type' => 'int', 'desc' => '桩类型：1-慢充，2-快充，3超快充'],
            ['name' => 'model', 'type' => 'string', 'desc' => '桩型号'],
            ['name' => 'power', 'type' => 'int', 'desc' => '桩功率(单位: w)'],
            ['name' => 'shot_num', 'type' => 'int', 'desc' => '枪数量'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
            ['name' => 'is_online', 'type' => 'int', 'desc' => '是否在线 1:在线 2:离线']
        ]),
    ]
    public function piles_list(): Json
    {
        $data = $this->request->post();
        $page = ($data['page'] ?? false) ?: 1;
        $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
        $order_name = ($data['order_name'] ?? false) ?: 'p.id';
        $order_type = ($data['order_type'] ?? false) ?: 'desc';

        // 数据权限限制
        if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
            $data['corp_id'] = $this->loginUser->corp_id;
        } else if ($this->loginUser->corp_id > 0) {
            $data['corp_id'] = $this->loginUser->corp_id;
            // 查询当前子账号拥有的场站权限
            $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
            if (!empty($data['station_id'])) {
                $data['station_id'] = array_values(array_intersect($station_ids, [$data['station_id']]));
            } else {
                $data['station_id'] = $station_ids;
            }
        }

        // 搜索框搜索
        $where = function ($query) use ($data) {
            $query->where('p.is_del', '=', PilesModel::IsDelNot);

            if (isset($data['id']) && $data['id']) {
                $query->where('p.id', $data['id']);
            }
            if (isset($data['name']) && $data['name']) {
                $query->where('p.name', 'like', '%' . $data['name'] . '%');
            }
            if (isset($data['corp_id']) && $data['corp_id']) {
                $query->where('p.corp_id', $data['corp_id']);
            }
            if (isset($data['station_id']) && $data['station_id']) {
                if (is_array($data['station_id'])) {
                    $query->where('p.station_id', 'in', $data['station_id']);
                } else {
                    $query->where('p.station_id', '=', $data['station_id']);
                }
            }
            return $query;
        };

        $list = $this->modelClass->alias('p')
            ->leftJoin('corp c', 'p.corp_id = c.id')
            ->join('stations sta', 'p.station_id = sta.id')
            ->leftJoin('piles_status ps', 'ps.id = p.id')
            ->field([
                'p.id', 'p.name', 'p.ac_dc', 'p.type', 'p.model', 'p.power',
                'p.shot_num', 'p.comm_type', 'ps.is_online', 'p.comm_corp',
                'p.create_time', 'c.name as corp_name', 'sta.name as station_name'
            ])
            ->where($where)
            ->order($order_name, $order_type)
            ->paginate(['list_rows' => $pageSize, 'page' => $page]);

        return $this->res_success($list);
    }

    #[
        Apidoc\Title("场站充电桩监控"),
        Apidoc\Author("cbj 2024-08-21 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Piles/station_piles_monitor"),
        Apidoc\Param(name: "station_id", type: "int", require: true, desc: "场站id"),
        Apidoc\Returned(name: "a", type: "array", desc: "A相充电桩列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电桩编号，14位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'is_online', 'type' => 'int', 'desc' => '在线状态 1:在线 2:离线'],
            ['name' => 'shots', 'type' => 'array', 'desc' => '充电枪', 'children' => [
                ['name' => 'shots_number', 'type' => 'int', 'desc' => '充电枪编号'],
                ['name' => 'work_status', 'type' => 'int', 'desc' => '工作状态 1:空闲 2:充电'],
                ['name' => 'is_fault', 'type' => 'int', 'desc' => '故障状态 1:正常 2:故障'],
            ]]
        ]),
        Apidoc\Returned(name: "b", type: "array", desc: "B相充电桩列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电桩编号，14位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'is_online', 'type' => 'int', 'desc' => '在线状态 1:在线 2:离线'],
            ['name' => 'shots', 'type' => 'array', 'desc' => '充电枪', 'children' => [
                ['name' => 'shots_number', 'type' => 'int', 'desc' => '充电枪编号'],
                ['name' => 'work_status', 'type' => 'int', 'desc' => '工作状态 1:空闲 2:充电'],
                ['name' => 'is_fault', 'type' => 'int', 'desc' => '故障状态 1:正常 2:故障'],
            ]]
        ]),
        Apidoc\Returned(name: "c", type: "array", desc: "C相充电桩列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电桩编号，14位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'is_online', 'type' => 'int', 'desc' => '在线状态 1:在线 2:离线'],
            ['name' => 'shots', 'type' => 'array', 'desc' => '充电枪', 'children' => [
                ['name' => 'shots_number', 'type' => 'int', 'desc' => '充电枪编号'],
                ['name' => 'work_status', 'type' => 'int', 'desc' => '工作状态 1:空闲 2:充电'],
                ['name' => 'is_fault', 'type' => 'int', 'desc' => '故障状态 1:正常 2:故障'],
            ]]
        ]),
    ]
    public function station_piles_monitor(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::piles([
                'station_id',
            ]));

            // 数据权限限制
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $verify_data['p.corp_id'] = $this->loginUser->corp_id;
            } else if ($this->loginUser->corp_id > 0) {
                $verify_data['p.corp_id'] = $this->loginUser->corp_id;
                // 查询当前子账号拥有的场站权限
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                if (!in_array($verify_data['station_id'], $station_ids)) {
                    return ['a' => [], 'b' => [], 'c' => []];
                }
                $verify_data['p.station_id'] = $station_ids;
            }

            // 搜索框搜索
            $where = function ($query) use ($verify_data) {
                $query->where('p.is_del', '=', PilesModel::IsDelNot);

                if (isset($verify_data['corp_id']) && $verify_data['corp_id']) {
                    $query->where('p.corp_id', $verify_data['corp_id']);
                }
                if (isset($verify_data['station_id']) && $verify_data['station_id']) {
                    if (is_array($verify_data['station_id'])) {
                        $query->where('p.station_id', 'in', $verify_data['station_id']);
                    } else {
                        $query->where('p.station_id', '=', $verify_data['station_id']);
                    }
                }
                return $query;
            };

            // piles -> piles_status -> shots -> shots_status
            $fields = [
                'p.id', 'p.name', 'ps.is_online', 'p.phase_name'
            ];
            $pilesList = $this->modelClass->alias('p')
                ->leftJoin('piles_status ps', 'ps.id = p.id')
                ->field($fields)
                ->where($where)
                ->select()
                ->toArray();
            $pilesIds = array_column($pilesList, 'id');

            $shotsMap = (new Shots())->batchGetPilesShotsStatus($pilesIds);
            foreach ($pilesList as $key => $value) {
                if (!isset($shotsMap[$value['id']])) continue;
                $pilesList[$key]['shots'] = $shotsMap[$value['id']];
            }

            $result = [];
            foreach ($pilesList as $piles) {
                $result[$piles['phase_name']][] = $piles;
            }


            return $result;
        });
    }

    #[
        Apidoc\Title("获取充电桩排序信息"),
        Apidoc\Author("lwj 2023.8.8 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Piles/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        $res = [
            'order_name' => 'p.create_time',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'p.create_time', 'label' => '创建时间'],
                ['value' => 'p.id', 'label' => '充电桩编号'],
                ['value' => 'p.name', 'label' => '充电桩名称'],
                ['value' => 'p.corp_id', 'label' => '运营商'],
                ['value' => 'p.station_id', 'label' => '充电站'],
                ['value' => 'p.ac_dc', 'label' => '交直流'],
                ['value' => 'p.type', 'label' => '桩类型'],
                ['value' => 'p.model', 'label' => '桩型号'],
                ['value' => 'p.power', 'label' => '桩功率'],
                ['value' => 'p.shot_num', 'label' => '枪数量'],
                ['value' => 'p.comm_type', 'label' => '通讯类型'],
                ['value' => 'p.comm_corp', 'label' => '通讯运营商'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("充电桩详情"),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.8 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Piles/get_info"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "充电桩编号，14位数字"),
        Apidoc\Returned(name: "id", type: "int", desc: "充电桩编号，14位数字"),
        Apidoc\Returned(name: "name", type: "string", desc: "充电桩名称"),
        Apidoc\Returned(name: "corp_id", type: "int", desc: "运营商id"),
        Apidoc\Returned(name: "station_id", type: "int", desc: "充电站id"),
        Apidoc\Returned(name: "ac_dc", type: "int", desc: "交直流：0表示直流桩，1表示交流桩"),
        Apidoc\Returned(name: "type", type: "int", desc: "桩类型：1-慢充，2-快充，3超快充"),
        Apidoc\Returned(name: "model", type: "string", desc: "桩型号"),
        Apidoc\Returned(name: "power", type: "int", desc: "桩功率（w）"),
        Apidoc\Returned(name: "comm_id", type: "string", desc: "认证id:4G模块IMEI号,mac地址，根据通讯类型区分"),
        Apidoc\Returned(name: "shot_num", type: "int", desc: "枪数量"),
        Apidoc\Returned(name: "phase_name", type: "string", desc: "所属相线(a|b|c)")
    ]
    public function get_info(): Json
    {
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::piles([
                'id',
            ]));

            // 数据权限限制
            $where = [];
            $where[] = ['p.is_del', '=', PilesModel::IsDelNot];
            $where[] = ['p.id', '=', $verify_data['id']];
            if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
                $data['p.corp_id'] = $this->loginUser->corp_id;
                $where[] = ['p.corp_id', '=', $this->loginUser->corp_id];
            } else if ($this->loginUser->corp_id > 0) {
                $where[] = ['p.corp_id', '=', $this->loginUser->corp_id];
                // 查询当前子账号拥有的场站权限
                $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
                $where[] = ['p.station_id', 'in', $station_ids];
            }

            $result = $this->modelClass
                ->alias('p')
                ->leftJoin('piles_status ps', 'ps.id = p.id')
                ->field([
                    'p.id', 'p.name', 'p.corp_id', 'p.station_id', 'p.ac_dc', 'p.type',
                    'p.model', 'p.power', 'p.comm_id', 'p.shot_num', 'p.phase_name'
                ])
                ->where($where)
                ->find();
            if ($result) return $this->res_success($result);
            return $this->res_error([], '充电桩不存在');
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("新增充电桩"),
        Apidoc\Desc('只允许运营商账号操作(corp_id > 0)'),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.8 修改, cbj 2024.12.10 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Piles/add"),
        Apidoc\Param(name: "station_id", type: "int", require: true, desc: "充电站id"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "充电桩编号，14位数字"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "充电桩名称"),
        Apidoc\Param(name: "ac_dc", type: "int", require: true, desc: "交直流：0表示直流桩，1表示交流桩"),
        Apidoc\Param(name: "type", type: "int", require: true, desc: "桩类型：1-慢充，2-快充，3超快充"),
        Apidoc\Param(name: "model", type: "string", require: true, desc: "桩型号"),
        Apidoc\Param(name: "power", type: "int", require: true, desc: "桩功率（w）"),
        Apidoc\Param(name: "comm_id", type: "string", require: true, desc: "认证id:4G模块IMEI号,mac地址，根据通讯类型区分"),
        Apidoc\Param(name: "phase_name", type: "string", require: false, desc: "所属相线(a|b|c)"),
    ]
    public function add(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Add($this->loginUser, $this->request))->run();
        }, true);
    }

    #[
        Apidoc\Title("编辑充电桩"),
        Apidoc\Desc('只允许运营商账号操作(corp_id > 0)'),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.8 修改, cbj 2024.12.10 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Piles/edit"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "充电桩编号，14位数字"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "充电桩名称"),
        Apidoc\Param(name: "station_id", type: "int", require: true, desc: "充电站id"),
        Apidoc\Param(name: "ac_dc", type: "int", require: true, desc: "交直流：0表示直流桩，1表示交流桩"),
        Apidoc\Param(name: "type", type: "int", require: true, desc: "桩类型：1-慢充，2-快充，3超快充"),
        Apidoc\Param(name: "model", type: "string", require: true, desc: "桩型号"),
        Apidoc\Param(name: "power", type: "int", require: true, desc: "桩功率（w）"),
        Apidoc\Param(name: "comm_id", type: "string", require: true, desc: "认证id:4G模块IMEI号,mac地址，根据通讯类型区分"),
        Apidoc\Param(name: "phase_name", type: "string", require: false, desc: "所属相线(a|b|c)"),
    ]
    public function edit(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Update($this->loginUser, $this->request))->run();
        }, true);
    }

    #[
        Apidoc\Title("删除充电桩"),
        Apidoc\Desc('只允许运营商账号操作(corp_id > 0)'),
        Apidoc\Author("swk 2023.7.11 新增， lwj 2023.8.8 修改, cbj 2024.12.10 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Piles/delete"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "充电桩编号，14位数字"),
    ]
    public function delete(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Delete($this->loginUser, $this->request))->run();
        }, true);
    }

    #[
        Apidoc\Title("获取充电桩列表"),
        Apidoc\Author("lwj 2023.8.8 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Piles/get_piles_list"),
        Apidoc\Param(name: "station_id", type: "int", desc: "充电站编号"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "充电桩列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电桩编号'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电桩名称']
        ]),
    ]
    public function get_piles_list(): Json
    {
        $data = $this->request->post();

        // 数据权限限制
        $where = [
            ['p.is_del', '=', PilesModel::IsDelNot]
        ];
        if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
            $where[] = ['p.corp_id', '=', $this->loginUser->corp_id];
        } else if ($this->loginUser->corp_id > 0) {
            $where[] = ['p.corp_id', '=', $this->loginUser->corp_id];
            // 查询当前子账号拥有的场站权限
            $station_ids = (new StationDataAuthority())->getStationIds($this->loginUser->id);
            $where[] = ['p.station_id', 'in', $station_ids];
        }

        if (!empty($data['station_id'])) {
            $where[] = ['p.station_id', '=', $data['station_id']];
        }

        /**
         * @var Query $model
         */
        $model = new PilesModel();
        $options = $model->alias('p')
            ->where($where)
            ->column('p.id,p.name');
        return $this->res_success($options);
    }

    #[
        Apidoc\Title("场站充电桩监控"),
        Apidoc\Author("cccq 2025.01.20 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Piles/monitor"),
        Apidoc\Query(name: "station_id", type: "int", require: false, desc: "场站id"),
        Apidoc\Query(name: "is_bind_station", type: "int", require: false, desc: "是否已绑定场站 0:否 1:是"),
        Apidoc\Returned(name: "a", type: "array", desc: "A相充电桩列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电桩编号，14位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'is_online', 'type' => 'int', 'desc' => '在线状态 1:在线 2:离线'],
            ['name' => 'shots', 'type' => 'array', 'desc' => '充电枪', 'children' => [
                ['name' => 'shots_number', 'type' => 'int', 'desc' => '充电枪编号'],
                ['name' => 'is_online', 'type' => 'int', 'desc' => '枪在线状态：1-离线，2-在线'],
                ['name' => 'online_time', 'type' => 'int', 'desc' => '在线状态更新时间'],
                ['name' => 'work_status', 'type' => 'int', 'desc' => '工作状态 1:空闲 2:充电'],
                ['name' => 'work_time', 'type' => 'int', 'desc' => '工作状态更新时间'],
                ['name' => 'output_voltage', 'type' => 'int', 'desc' => '输出电压(精确到小数点后1位)'],
                ['name' => 'output_current', 'type' => 'int', 'desc' => '输出电流(精确到小数点后1位)'],
                ['name' => 'charging_percentage', 'type' => 'int', 'desc' => '充电度数(精确到小数点后4位)'],
                ['name' => 'is_fault', 'type' => 'int', 'desc' => '故障状态 1:正常 2:故障'],
            ]]
        ]),
        Apidoc\Returned(name: "b", type: "array", desc: "B相充电桩列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电桩编号，14位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'is_online', 'type' => 'int', 'desc' => '在线状态 1:在线 2:离线'],
            ['name' => 'shots', 'type' => 'array', 'desc' => '充电枪', 'children' => [
                ['name' => 'shots_number', 'type' => 'int', 'desc' => '充电枪编号'],
                ['name' => 'is_online', 'type' => 'int', 'desc' => '枪在线状态：1-离线，2-在线'],
                ['name' => 'online_time', 'type' => 'int', 'desc' => '在线状态更新时间'],
                ['name' => 'work_status', 'type' => 'int', 'desc' => '工作状态 1:空闲 2:充电'],
                ['name' => 'is_fault', 'type' => 'int', 'desc' => '故障状态 1:正常 2:故障'],
            ]]
        ]),
        Apidoc\Returned(name: "c", type: "array", desc: "C相充电桩列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电桩编号，14位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'is_online', 'type' => 'int', 'desc' => '在线状态 1:在线 2:离线'],
            ['name' => 'shots', 'type' => 'array', 'desc' => '充电枪', 'children' => [
                ['name' => 'shots_number', 'type' => 'int', 'desc' => '充电枪编号'],
                ['name' => 'is_online', 'type' => 'int', 'desc' => '枪在线状态：1-离线，2-在线'],
                ['name' => 'online_time', 'type' => 'int', 'desc' => '在线状态更新时间'],
                ['name' => 'work_status', 'type' => 'int', 'desc' => '工作状态 1:空闲 2:充电'],
                ['name' => 'is_fault', 'type' => 'int', 'desc' => '故障状态 1:正常 2:故障'],
            ]]
        ]),
    ]
    public function monitor(): Json
    {
        $data = $this->request->post();
        $data['corp_id'] = $this->loginUser->corp_id;
        // ========== 从设备服务获取充电桩监控数据 ========== //
        $response = Api::send('/device/piles/monitor','GET', $data);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }
}