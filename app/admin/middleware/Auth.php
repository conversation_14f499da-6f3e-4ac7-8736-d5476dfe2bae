<?php
/** @noinspection PhpUndefinedFieldInspection */

namespace app\admin\middleware;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\log\LogCollector;
use app\common\model\AdminAuthGroup;
use app\common\model\AdminUsers as AdminUsersModel;
use app\common\model\Menu as MenuModel;
use Chenbingji\Tool\Log;
use Closure;
use think\Container;
use think\Request;
use think\Response;
use app\common\cache\redis\AdminUserLogin;
use think\response\Html;
use Throwable;

class Auth
{
    /**
     * @var array|string[] 存在白名单中的url，能够不进行验证。
     */
    protected array $whitelist = [
        '/admin/login/get_img_code',
        '/admin/login/login',
        '/admin/login/login_test',
        '/admin/common/get_host'
    ];

    /**
     * 验证权限中间件
     * lwj 2023.7.17 新增
     * lwj 2023.10.18 修改
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            if ($request->method() === "OPTIONS") return Container::getInstance()->invokeClass(Html::class, ['', 204]);

            // 白名单验证
            if ($this->whitelistValidator($request)) {
                LogCollector::collectorRunLog('当前接口不需要登录权限');
                return $next($request);
            }

            // 身份验证
            [$verifyResult, $tokenUserData, $userData] = $this->identityValidator($request);
            if ($verifyResult === false) {
                return res_error_login();
            }

            // 如果是超级管理员
            if ($tokenUserData['id'] === 1) {
                LogCollector::collectorRunLog('本次操作用户：超级管理员');
                return $next($request);
            }
            LogCollector::collectorRunLog(sprintf('本次操作用户：%s', $userData['name']));

            // 验证接口权限是否开放
            if ($this->verifyApiIsOpen($request->baseUrl()) === false) {
                // 功能权限验证
                if ($this->functionalPermissionsValidator($request, $userData) === false) {
                    return res_error_auth();
                }
            }

            return $next($request);
        } catch (Throwable $e) {
            LogCollector::collectorRunLog(
                sprintf('用户鉴权中间件异常：异常文件=%s, 异常代码行数=%d, 异常描述=%s, 异常状态码=%s',
                    $e->getFile(), $e->getLine(), $e->getMessage(), $e->getCode()
                ), LogCollector::LevelError);
            return res_error_login();
        }
    }

    protected function verifyApiIsOpen(string $base_url): bool
    {
        return (new MenuModel)->verifyApiIsPublic($base_url);
    }

    /**
     * 功能权限验证器
     *
     * @param Request $request
     * @param array $userData
     * @return bool 是否通过
     */
    protected function functionalPermissionsValidator(Request $request, array $userData): bool
    {
        return (new AdminAuthGroup())->verifyAuthority($userData['perm_group_id'], $request->baseUrl());
    }

    /**
     * 身份验证器
     *
     * @param Request $request
     * @return array [bool:是否通过, null|array:令牌数据, null|array:用户数据]
     */
    protected function identityValidator(Request $request): array
    {
        // 验证令牌是否传递
        $clientToken = $request->header('authorization');
        if (!$clientToken) return [false, null, null];

        // 验证令牌是否有效
        $AdminUserLogin = app(AdminUserLogin::class);
        $token_user = $AdminUserLogin->getUserLoginData($clientToken);
        if (!$token_user) return [false, null, null];
        $token_user['token'] = $clientToken;

        // 验证用户是否存在
        $user = (new AdminUsersModel)->getUserData($token_user['id']);
        if (!$user) {
            LogCollector::collectorRunLog('无效令牌');
            return [false, null, null];
        }

        // 验证密码是否正确
        $serverToken = sha1($token_user['last_time'] . $user['name'] . '-' . $user['password']);
        if ($clientToken !== $serverToken) return [false, null, null];
//        $request->token_user = $token_user;
        $request->adminLoginUser = new AdminLoginUser($token_user);

        // 延续令牌有效时长
        $AdminUserLogin->extendLoginValidityTime($clientToken);


        return [true, $token_user, $user];
    }

    /**
     * 白名单过滤器
     *
     * @param Request $request
     * @return bool 是否通过
     */
    protected function whitelistValidator(Request $request): bool
    {
        $url = strtolower($request->baseUrl());
        if (in_array($url, $this->whitelist)) {
            LogCollector::collectorRunLog('当前接口不需要登录权限');
            return true;
        }
        return false;
    }

}