<?php
declare(strict_types=1);

namespace app\ms;

use think\facade\Log;
use think\queue\Job;

class SendJob
{
    public function fire(Job $job, array $data): void
    {
        // 从传入数据中获取请求信息
        $url     = $data['url'] ?? '';
        $method  = $data['method'] ?? 'POST';
        $params  = $data['data'] ?? [];
        $key     = $data['key'] ?? '';
        $headers = $data['headers'] ?? [];
        $timestamp = $data['timestamp'] ?? 0;
        // 检查缓存中的最新时间戳
        $latestTimestamp = cache("sync_api:{$key}");

        // 如果缓存中的时间戳不存在或比当前任务的时间戳早，跳过该任务
        if ($latestTimestamp === null || $timestamp < $latestTimestamp) {
            Log::warning("cache:{$key} is null or timestamp < latestTimestamp");
            // 打印被拒绝的请求信息
            Log::warning("url:{$url},method:{$method},params:".json_encode_cn($params)."timestamp:{$timestamp},latestTimestamp:{$latestTimestamp}");
            $job->delete();
            return;
        }

        // 执行请求逻辑
        try {
            $response = Api::executeRequest($url, $method, $params,$headers);
            // 请求成功，删除队列任务
            $job->delete();

            // 记录成功日志
            Log::info('Request succeeded: ' . json_encode($response));
        } catch (\Exception $e) {
            // 重新放入队列，稍后重试
            $job->release(10); // 10秒后重试
            // 请求失败，记录失败信息
            Log::error('Request failed: ' . $e->getMessage());
        }
    }

}