<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\queue;

use app\common\cache\redis\StationsMonthRankingListDispatch;
use app\common\log\SocketLogCollector;
use app\event\ChargeAbnormalEndEvent;
use app\event\ChargeNormalEndEvent;
use Redis;

class RankingListQueue extends BaseQueue
{
    public const TypeChargeNormalEnd = 0; // 充电订单正常结束
    public const TypeChargeAbnormalEnd = 1; // 充电订单异常结束

    /**
     * 获取Redis实例
     *
     * @return Redis
     * @throws \RedisException
     */
    protected function getRedisInstance(): Redis
    {
        return $this->connectRedis(config('my.redis2'));
    }

    protected function getKey(): string
    {
        return 'admin:queue:ranking_list';
    }

    protected function consumerHandler(string $data, SocketLogCollector $socketLogCollector): int
    {
        $event = json_decode($data, true);
        if (isset($event['type']) === false) {
            $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), 'error');
            return parent::ResultCodeDiscard;
        }

        switch ($event['type']) {
            case self::TypeChargeNormalEnd:
                $result = $this->chargeNormalEndHandler(new ChargeNormalEndEvent($event));
                break;
            case self::TypeChargeAbnormalEnd:
                $result = $this->chargeAbnormalEndHandler(new ChargeAbnormalEndEvent($event));
                break;
            default:
                $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), 'error');
                $result = parent::ResultCodeFailed;
                break;
        }


        return $result;
    }

    protected function chargeAbnormalEndHandler(ChargeAbnormalEndEvent $event): int
    {
        $StationsMonthRankingListDispatch = app(StationsMonthRankingListDispatch::class);
        $StationsMonthRankingListDispatch->ChargeOrderAbnormalEndCallback(
            $event->corpId,
            $event->stationId,
            $event->payMoney,
            $event->electricityTotal
        );

        return parent::ResultCodeSuccess;
    }

    protected function chargeNormalEndHandler(ChargeNormalEndEvent $event): int
    {
        // 排行榜变动
        $StationsMonthRankingListDispatch = app(StationsMonthRankingListDispatch::class);
        $StationsMonthRankingListDispatch->ChargeOrderNormalEndCallback(
            $event->corpId,
            $event->stationId,
            $event->payMoney,
            $event->electricityTotal
        );

        return parent::ResultCodeSuccess;
    }
}