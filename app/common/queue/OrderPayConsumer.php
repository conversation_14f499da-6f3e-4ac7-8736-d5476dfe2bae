<?php
/** @noinspection PhpUnused */
declare(strict_types=1);

namespace app\common\queue;

use app\common\lib\exception\RuntimeException;
use app\common\model\UserBalanceLog as UserBalanceLogModel;
use app\common\model\Users;
use app\common\lib\SendApplet;
use app\common\model\Users as UsersModel;
use think\facade\Db;
use think\facade\Log;
use Throwable;

/**
 * 微信支付通知 RabbitMQ 消费者
 *
 * 用于处理来自 order 交换机的微信支付成功通知
 * 业务逻辑：只给用户增加余额，不处理订单状态
 */
class OrderPayConsumer extends BaseConsumer
{
    private const EXCHANGE_NAME = 'order';
    private const EXCHANGE_TYPE = 'direct';
    private const QUEUE_NAME = 'order_pay';
    private const ROUTING_KEY = 'order.pay';

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 调用父类构造函数，设置交换机和队列信息
        parent::__construct(
            self::EXCHANGE_NAME,    // 交换机名称
            self::QUEUE_NAME,       // 队列名称
            self::EXCHANGE_TYPE,    // 交换机类型
            self::ROUTING_KEY       // 路由键
        );
    }

    /**
     * 处理消息的业务逻辑（实现基类的抽象方法）
     *
     * @param string $messageBody 消息内容
     * @return string 处理结果常量
     */
    protected function processMessage(string $messageBody): string
    {
        // 解析 JSON 消息
        $msg = json_decode($messageBody, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error('JSON 解析失败: ' . json_last_error_msg());
            return parent::RESULT_SKIP; // JSON 格式错误，跳过不重试
        }

        switch ($msg['action']) {
            case 'pay_success':
                return $this->handlePaySuccess($msg['data']);
            case 'refund_reject':
                return $this->handleRefundReject($msg['data']);
            default:
                Log::error('无效的消息类型: ' . $msg['type']);
                return parent::RESULT_SKIP; // 无效的消息类型，跳过不重试
        }
    }

    /**
     * 处理支付通知业务逻辑
     *
     * @param string $messageBody
     * @return string
     */
    private function handlePaySuccess(array $payData): string
    {
        try {
            $userId = (int)$payData['user_id'];
            $amount = (int)$payData['price'];
            $orderId = $payData['id'] ?? '';

            Log::info(sprintf('处理用户[%d]余额增加[%d分]，订单号[%s]', $userId, $amount, $orderId));

            // 添加用户余额日志
            $addResult = app(UserBalanceLogModel::class)->add(
                $userId,
                $amount,
                '支付成功，加用户余额：' . $amount . '分，订单号：' . $orderId,
                '充值'
            );

            if (empty($addResult)) {
                Log::error('添加余额日志失败');
                return parent::RESULT_FAILED;
            }

            // 增加用户余额
            $increaseBalanceResult = app(Users::class)->increaseBalance($userId, $amount);
            if ($increaseBalanceResult === 0) {
                Log::error('增加用户余额失败');
                return parent::RESULT_FAILED;
            }

        } catch (Throwable $e) {
            Log::error(sprintf('处理支付通知异常: %s', $e->getMessage()));
            return parent::RESULT_FAILED;
        }

        try {
            // 发送小程序通知
            SendApplet::send_uid($userId, [
                'type' => 'pay_notice',
                'result' => 'success',
                'msg' => '支付成功，加余额成功'
            ]);
        } catch (Throwable $e) {
            Log::error(sprintf('发送小程序通知失败: %s', $e->getMessage()));
        }

        return parent::RESULT_SUCCESS;
    }

    /**
     * 处理退款拒绝业务逻辑
     *
     * @param array $refundOrder
     * @return string
     */
    private function handleRefundReject(array $refundOrder): string
    {
        Log::info(sprintf('处理用户[%d]拒绝退款[%d分]，退款单号[%s]', $refundOrder['user_id'], $refundOrder['refund_price'], $refundOrder['id']));

        $add_balance_res = (new UsersModel)
            ->where('id', $refundOrder['user_id'])
            ->limit(1)
            ->update([
                'balance' => Db::raw('balance+' . $refundOrder['refund_price']),
            ]);
        if (!$add_balance_res){
            Log::error('拒绝退款失败，加余额失败');
            return parent::RESULT_FAILED;
        }
        return parent::RESULT_SUCCESS;
    }

}
