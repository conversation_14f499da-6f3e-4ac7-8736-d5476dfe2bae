<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\queue;

use app\common\lib\charging\sdk\RemoteAccountBalanceUpdateCommand;
use app\common\lib\charging\sdk\request\RemoteAccountBalanceUpdateCommandRequest;
use app\common\log\SocketLogCollector;
use app\common\model\Order;
use app\common\model\Users;
use app\common\queue\package\user_balance_change\BasePackage;
use app\common\queue\package\user_balance_change\UpdateChargingStationBalancePackage;
use app\common\queue\package\user_balance_change\UpdateChargingStationBalanceRetryPackage;
use Redis;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class UserBalanceChangeQueue extends BaseQueue
{

    /**
     * 获取Redis实例
     *
     * @return Redis
     * @throws \RedisException
     */
    protected function getRedisInstance(): Redis
    {
        return $this->connectRedis(config('my.redis2'));
    }

    protected function getKey(): string
    {
        return 'admin:queue:user_balance_change';
    }

    /**
     * 消费者处理器
     *
     * @param string $data 数据包
     * @param SocketLogCollector $socketLogCollector
     * @return int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function consumerHandler(string $data, SocketLogCollector $socketLogCollector): int
    {
        $package = BasePackage::decode($data);
        if (property_exists($package, 'type') === false) {
            $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), 'error');
            return parent::ResultCodeDiscard;
        }

        switch ($package->getType()) {
            case BasePackage::TypeUpdateChargingStationBalance:
                /**
                 * @var UpdateChargingStationBalancePackage $package
                 */
                $result = $this->balanceRechargeHandler($package);
                break;
            case BasePackage::TypeUpdateChargingStationBalanceRetry:
                /**
                 * @var UpdateChargingStationBalanceRetryPackage $package
                 */
                $result = $this->balanceRechargeRetryHandler($package);
                break;
            default:
                $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), 'error');
                $result = parent::ResultCodeFailed;
                break;
        }


        return $result;
    }

    /**
     * 余额充值重试梳理
     *
     * @param UpdateChargingStationBalanceRetryPackage $package
     * @return int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function balanceRechargeRetryHandler(UpdateChargingStationBalanceRetryPackage $package): int
    {
        // 检测该用户是否存在进行中的订单
        $chargingOrders = app(Order::class)->userChargingOrders(
            $package->user_id
        );

        if (count($chargingOrders) > 0) {

            $account_balance = app(Users::class)->getUserAvailableBalance($package->user_id);

            foreach ($chargingOrders as $chargingOrder) {
                $request = new RemoteAccountBalanceUpdateCommandRequest([
                    'user_id' => $package->user_id
                    , 'trigger_time' => $package->trigger_time
                    , 'transaction_serial_number' => $chargingOrder['id']
                    , 'physical_card_number' => hex_auto_fill_0($package->user_id, 16)
                    , 'shots_id' => (string)$chargingOrder['sequence']
                    , 'piles_id' => (string)$chargingOrder['piles_id']
                    , 'account_balance' => $account_balance
                    , 'retry_count' => $package->retry_count
                    , 'retried_count' => $package->retried_count
                ]);

                (new RemoteAccountBalanceUpdateCommand($request))->send();
            }
        }


        return parent::ResultCodeSuccess;
    }

    /**
     * 用户余额更新
     *
     * @param UpdateChargingStationBalancePackage $package
     * @return int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function balanceRechargeHandler(UpdateChargingStationBalancePackage $package): int
    {
        // 检测该用户是否存在进行中的订单
        $chargingOrders = app(Order::class)->userChargingOrders(
            $package->user_id
        );

        if (count($chargingOrders) > 0) {

            $account_balance = app(Users::class)->getUserAvailableBalance($package->user_id);

            foreach ($chargingOrders as $chargingOrder) {
                $request = new RemoteAccountBalanceUpdateCommandRequest([
                    'user_id' => $package->user_id
                    , 'trigger_time' => $package->trigger_time
                    , 'physical_card_number' => hex_auto_fill_0($package->user_id, 16)
                    , 'transaction_serial_number' => $chargingOrder['id']
                    , 'shots_id' => (string)$chargingOrder['sequence']
                    , 'piles_id' => (string)$chargingOrder['piles_id']
                    , 'account_balance' => $account_balance
                    , 'retry_count' => RemoteAccountBalanceUpdateCommandRequest::RetryCount
                    , 'retried_count' => 0
                ]);

                (new RemoteAccountBalanceUpdateCommand($request))->send();
            }
        }

        return parent::ResultCodeSuccess;
    }
}