<?php

namespace app\common\queue;

use app\common\log\SocketLogCollector;
use app\event\DeviceAbnormalityEvent;
use Redis;

class AlarmMonitoringQueue extends BaseQueue
{
    // 类型
    public const TypeAutomaticDetection = 1; // 自动检测

    // 失败原因
    public const FailureReason = [
        '',
        '设备编号不匹配',
        '枪已在充电',
        '设备故障',
        '设备离线',
        '未插枪',
    ];

    // 硬件故障
    public const HardwareFailure = [
        '急停按钮动作故障',
        '无可用整流模块',
        '出风口温度过高',
        '交流防雷故障',
        '交直流模块 DC20 通信中断',
        '绝缘检测模块 FC08 通信中断',
        '电度表通信中断',
        '读卡器通信中断',
        'RC10 通信中断',
        '风扇调速板故障',
        '直流熔断器故障',
        '高压接触器故障',
        '门打开'
    ];

    /**
     * 获取Redis实例
     *
     * @return Redis
     * @throws \RedisException
     */
    protected function getRedisInstance(): Redis
    {
        return $this->connectRedis(config('my.redis2'));
    }

    protected function getKey(): string
    {
        return 'admin:queue:alarm_monitoring';
    }

    protected function consumerHandler(string $data, SocketLogCollector $socketLogCollector): int
    {
        $event = json_decode($data, true);

        if (isset($event['type']) === false) {
            $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), 'error');
            return parent::ResultCodeDiscard;
        }

        switch ($event['type']) {
            case self::TypeAutomaticDetection:
                $result = $this->automaticDetectionHandler(new DeviceAbnormalityEvent($event), $socketLogCollector);
                break;
            default:
                $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), 'error');
                $result = parent::ResultCodeFailed;
                break;
        }

        return $result;
    }

    protected function automaticDetectionHandler(DeviceAbnormalityEvent $event, SocketLogCollector $socketLogCollector): int
    {
        if ($event->abnormality_type === DeviceAbnormalityEvent::AbnormalityTypeTransactionRecord) {
            return $this->transactionRecordHandler($event, $socketLogCollector);
        } else if ($event->abnormality_type === DeviceAbnormalityEvent::AbnormalityTypeRemoteStartChargingCommandResponse) {
            return $this->remoteStartChargingCommandResponseHandler($event, $socketLogCollector);
        } else if ($event->abnormality_type === DeviceAbnormalityEvent::AbnormalityTypeUploadRealtimeMonitoringData) {
            return $this->uploadRealtimeMonitoringDataHandler($event, $socketLogCollector);
        } else {
            $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $event->encode()), 'error');
            return parent::ResultCodeFailed;
        }
    }

    protected function uploadRealtimeMonitoringDataHandler(DeviceAbnormalityEvent $event, SocketLogCollector $socketLogCollector): int
    {
        $text = [];
        $length = strlen($event->abnormal_cause);
        for ($i = 0; $i < $length; $i++) {
            if ($event->abnormal_cause[$i] == 1) {
                $text[] = self::HardwareFailure[$i];
            }
        }
        $text = implode(', ', $text);

//        $AlarmRecord = app(AlarmRecord::class);
//        $AlarmRecord->record(
//            $event->corp_id,
//            $event->station_id,
//            AlarmRecord::DeviceTypeShot,
//            $event->shot_id,
//            AlarmRecord::TypeUploadRealtimeMonitoringData,
//            $event->abnormal_cause,
//            AlarmRecord::LevelOne,
//            $text,
//            $text,
//            $event->start_time
//        );

        return self::ResultCodeSuccess;
    }

    protected function remoteStartChargingCommandResponseHandler(DeviceAbnormalityEvent $event, SocketLogCollector $socketLogCollector): int
    {
        $text = self::FailureReason[(int)$event->abnormal_cause] ?? '未知';

//        $AlarmRecord = app(AlarmRecord::class);
//        $AlarmRecord->record(
//            $event->corp_id,
//            $event->station_id,
//            AlarmRecord::DeviceTypeShot,
//            $event->shot_id,
//            AlarmRecord::TypeRemoteStartChargingCommandResponse,
//            $event->abnormal_cause,
//            AlarmRecord::LevelOne,
//            $text,
//            $text,
//            $event->start_time
//        );

        return self::ResultCodeSuccess;
    }

    protected function transactionRecordHandler(DeviceAbnormalityEvent $event, SocketLogCollector $socketLogCollector): int
    {
        $text = get_reason_for_charging_stop((int)$event->abnormal_cause);

//        $AlarmRecord = app(AlarmRecord::class);
//        $AlarmRecord->record(
//            $event->corp_id,
//            $event->station_id,
//            AlarmRecord::DeviceTypeShot,
//            $event->shot_id,
//            AlarmRecord::TypeTransactionRecord,
//            $event->abnormal_cause,
//            AlarmRecord::LevelOne,
//            $text,
//            $text,
//            $event->start_time
//        );

        return self::ResultCodeSuccess;
    }
}