<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\queue;

use app\common\lib\enterprise_wechat\PushChargeNormalEndMessage;
use app\common\lib\enterprise_wechat\PushChargeNormalStartMessage;
use app\common\lib\enterprise_wechat\PushShotsOfflineMessage;
use app\common\log\SocketLogCollector;
use app\event\ChargeNormalEndEvent;
use app\event\ChargeNormalStartEvent;
use app\event\ShotsOfflineEvent;
use Redis;
use RedisException;

class MessagePushQueue extends BaseQueue
{
    public const TypeChargeNormalEnd = 0; // 充电正常结束
    public const TypeChargeNormalStart = 1; // 充电正常开启
    public const TypeShotsOffline = 2; // 充点枪离线

    /**
     * 获取Redis实例
     *
     * @return Redis
     * @throws RedisException
     */
    protected function getRedisInstance(): Redis
    {
        return $this->connectRedis(config('my.redis2'));
    }

    protected function getKey(): string
    {
        return 'admin:queue:message_push';
    }

    protected function consumerHandler(string $data, SocketLogCollector $socketLogCollector): int
    {
        $event = json_decode($data, true);
        if (isset($event['type']) === false) {
            $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), 'error');
            return parent::ResultCodeDiscard;
        }

        switch ($event['type']) {
            case self::TypeChargeNormalEnd:
                $result = $this->chargeNormalEndHandler(new ChargeNormalEndEvent($event), $socketLogCollector);
                break;
            case self::TypeChargeNormalStart:
                $result = $this->chargeNormalStartHandler(new ChargeNormalStartEvent($event), $socketLogCollector);
                break;
            case self::TypeShotsOffline:
                $result = $this->shotsOffline(new ShotsOfflineEvent($event), $socketLogCollector);
                break;
            default:
                $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), 'error');
                $result = parent::ResultCodeFailed;
                break;
        }


        return $result;
    }

    protected function shotsOffline(ShotsOfflineEvent $event, SocketLogCollector $socketLogCollector): int
    {
        // ... 充电正常开启 ...
        $pushShotsOfflineMessage = new PushShotsOfflineMessage($event);
        $response = $pushShotsOfflineMessage->send();

        $body = $response->analysisJsonBody();

        if ($response->isFailed === true || $body['errcode'] !== 0) {
            $socketLogCollector->collectorRunLog('推送到企业微信群里 - 失败 - ' . json_encode($body), 'error');
            return parent::ResultCodeFailed;
        }

        return parent::ResultCodeSuccess;
    }

    protected function chargeNormalStartHandler(ChargeNormalStartEvent $event, SocketLogCollector $socketLogCollector): int
    {
        // ... 充电正常开启 ...
        $pushChargeNormalStartMessage = new PushChargeNormalStartMessage($event);
        $response = $pushChargeNormalStartMessage->send();

        $body = $response->analysisJsonBody();

        if ($response->isFailed === true || $body['errcode'] !== 0) {
            $socketLogCollector->collectorRunLog('推送到企业微信群里 - 失败 - ' . json_encode($body), 'error');
            return parent::ResultCodeFailed;
        }

        return parent::ResultCodeSuccess;
    }

    protected function chargeNormalEndHandler(ChargeNormalEndEvent $event, SocketLogCollector $socketLogCollector): int
    {
        $PushStartNormalEndMessage = new PushChargeNormalEndMessage($event);
        $response = $PushStartNormalEndMessage->send();

        $body = $response->analysisJsonBody();

        if ($response->isFailed === true || $body['errcode'] !== 0) {
            $socketLogCollector->collectorRunLog('推送到企业微信群里 - 失败 - ' . json_encode($body), 'error');
            return parent::ResultCodeFailed;
        }

        return parent::ResultCodeSuccess;
    }
}