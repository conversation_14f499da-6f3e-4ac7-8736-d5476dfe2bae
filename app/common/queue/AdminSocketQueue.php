<?php
/** @noinspection PhpUnused */

namespace app\common\queue;

use app\common\lib\SendAdmin;
use app\common\log\SocketLogCollector;
use app\event\ChargeAbnormalEndEvent;
use app\event\ChargeNormalEndEvent;
use app\event\ShotsCountChangeEvent;
use app\event\ShotsStatusChangeEvent;
use Redis;

class AdminSocketQueue extends BaseQueue
{
    public const TypeChargeNormalEnd = 0; // 正常结束
    public const TypeChargeAbnormalEnd = 1; // 异常结束
    public const TypeShotsCountChange = 2; // 充电枪数量变更
    public const TypeShotsStatusChange = 3; // 充电枪状态变更

    /**
     * 获取Redis实例
     *
     * @return Redis
     * @throws \RedisException
     */
    protected function getRedisInstance(): Redis
    {
        return $this->connectRedis(config('my.redis2'));
    }

    protected function getKey(): string
    {
        return 'admin:queue:admin_socket';
    }

    protected function consumerHandler(string $data, SocketLogCollector $socketLogCollector): int
    {
        $event = json_decode($data, true);
        if (isset($event['type']) === false) {
            $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), 'error');
            return parent::ResultCodeDiscard;
        }

        switch ($event['type']) {
            case self::TypeChargeNormalEnd:
                $result = $this->chargeNormalEndHandler(new ChargeNormalEndEvent($event));
                break;
            case self::TypeChargeAbnormalEnd:
                $result = $this->chargeAbnormalEndHandler(new ChargeAbnormalEndEvent($event));
                break;
            case self::TypeShotsCountChange:
                $result = $this->shotsCountChangeHandler(new ShotsCountChangeEvent($event));
                break;
            case self::TypeShotsStatusChange:
                $result = $this->shotsStatusChangeHandler(new ShotsStatusChangeEvent($event));
                break;
            default:
                $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), 'error');
                $result = parent::ResultCodeFailed;
                break;
        }


        return $result;
    }

    protected function shotsStatusChangeHandler(ShotsStatusChangeEvent $event): int
    {
        SendAdmin::send_group('后台', [
            'type' => 'shots_status_change',
            'msg' => '充电枪状态变更',
            'data' => [
                'corp_id' => $event->corpId,
                'station_id' => $event->stationId,
                'pile_id' => $event->pileId,
                'shot_id' => $event->shotId,
                'before_status' => $event->beforeStatus,
                'after_status' => $event->afterStatus
            ]
        ]);

        return parent::ResultCodeSuccess;
    }

    protected function shotsCountChangeHandler(ShotsCountChangeEvent $event): int
    {

        SendAdmin::send_group('后台', [
            'type' => 'shots_count_change',
            'msg' => '充电枪数量变更',
            'data' => [
                'corp_id' => $event->corpId,
                'station_id' => $event->stationId,
                'pile_id' => $event->pileId,
                'shot_id' => $event->shotId,
                'change_type' => $event->changeType,
                'shot_status' => $event->shotStatus
            ]
        ]);

        return parent::ResultCodeSuccess;
    }

    protected function chargeAbnormalEndHandler(ChargeAbnormalEndEvent $event): int
    {
        SendAdmin::send_group('后台', [
            'type' => 'charge_normal_end',
            'msg' => '充电订单异常结束',
            'data' => [
                'corp_id' => $event->corpId,
                'station_id' => $event->stationId,
                'money' => $event->payMoney,
                'electricity_total' => $event->electricityTotal,
                'charge_duration' => $event->chargeDuration
            ]
        ]);

        return parent::ResultCodeSuccess;
    }

    protected function chargeNormalEndHandler(ChargeNormalEndEvent $event): int
    {
        SendAdmin::send_group('后台', [
            'type' => 'charge_normal_end',
            'msg' => '订单正常结束',
            'data' => [
                'corp_id' => $event->corpId,
                'station_id' => $event->stationId,
                'money' => $event->payMoney,
                'electricity_total' => $event->electricityTotal,
                'valley_electricity' => $event->valleyElectricity,
                'flat_electricity' => $event->flatElectricity,
                'peak_electricity' => $event->peakElectricity,
                'sharp_electricity' => $event->sharpElectricity,
                'service_money' => $event->serviceMoney,
                'charge_duration' => $event->chargeDuration,
                'end_time' => $event->endTime
            ]
        ]);

        return parent::ResultCodeSuccess;
    }
}