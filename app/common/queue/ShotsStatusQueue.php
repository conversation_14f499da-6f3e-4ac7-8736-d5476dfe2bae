<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\queue;

use app\common\log\SocketLogCollector;
use app\common\model\Shots;
use app\event\ShotsStatusChangeEvent;
use Redis;

class ShotsStatusQueue extends BaseQueue
{
    public const TypeStatusChange = 0; // 状态变更
    public const TypeCountChange = 1; // 数量变更

    /**
     * 获取Redis实例
     *
     * @return Redis
     * @throws \RedisException
     */
    protected function getRedisInstance(): Redis
    {
        return $this->connectRedis(config('my.redis2'));
    }

    protected function getKey(): string
    {
        return 'admin:queue:shots_status';
    }

    protected function consumerHandler(string $data, SocketLogCollector $socketLogCollector): int
    {
        $event = json_decode($data, true);
        if (isset($event['type']) === false) {
            $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), 'error');
            return parent::ResultCodeDiscard;
        }

        switch ($event['type']) {
            case self::TypeStatusChange:
                $result = $this->statusChangeHandler(new ShotsStatusChangeEvent($event));
                break;
            default:
                $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), 'error');
                $result = parent::ResultCodeFailed;
                break;
        }


        return $result;
    }

    protected function statusChangeHandler(ShotsStatusChangeEvent $event): int
    {


        $Shots = app(Shots::class);
        $Shots->where('id', $event->shotId)->update([
            'status' => $event->afterStatus,
        ]);

        return parent::ResultCodeSuccess;
    }
}