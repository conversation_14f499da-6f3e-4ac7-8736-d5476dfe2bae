<?php

namespace app\common\queue\package\user_balance_change;


use app\common\lib\exception\RuntimeException;

class BasePackage implements UserBalanceChangeContract
{
    public int $type;

    public const TypeUpdateChargingStationBalance = 0; // 远程更新充电桩用户余额
    public const TypeUpdateChargingStationBalanceRetry = 1; // 远程更新充电桩用户余额-重试

    public function __construct(array $fields)
    {
        foreach ($fields as $key => $value) {
            if (property_exists($this, $key)) {
                $this->{$key} = $value;
            }
        }
    }

    public function encode(): string
    {
        return json_encode($this->toArray());
    }

    public function toArray(): array
    {
        return (array)$this;
    }

    public function getType(): int
    {
        return $this->type;
    }

    public static function decode(string $data): UserBalanceChangeContract
    {
        $data = json_decode($data, true);
        return match ($data['type']) {
            self::TypeUpdateChargingStationBalance => new UpdateChargingStationBalancePackage($data),
            self::TypeUpdateChargingStationBalanceRetry => new UpdateChargingStationBalanceRetryPackage($data),
            default => throw new RuntimeException('无效消息类型'),
        };
    }
}