<?php
/** @noinspection PhpUnusedLocalVariableInspection */
/** @noinspection PhpUnusedParameterInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpUnused */

declare (strict_types=1);

namespace app\common\queue;

use app\common\lib\active_alarm\logic\charging_gun_fault\MonitorFailureCheck;
use app\common\lib\active_alarm\logic\charging_gun_fault\TransactionFailureCheck;
use app\common\lib\active_alarm\logic\piles_online_status\OfflineFailureCheck;
use app\common\lib\active_alarm\logic\piles_online_status\OnlineFailureCheck;
use app\common\lib\charging\request\StartChargeAnswerRequest;
use app\common\lib\charging\request\StopChargeAnswerRequest;
use app\common\lib\charging\request\TransactionRecordRequest;
use app\common\lib\charging\request\UploadRealtimeMonitoringDataRequest;
use app\common\lib\charging\Settlement;
use app\common\lib\charging\StartChargeAndSetTariffAnswer;
use app\common\lib\charging\StartChargeAnswer;
use app\common\lib\charging\StopChargeAnswer;
use app\common\lib\charging\UploadRealtimeMonitoringData;
use app\common\lib\exception\RuntimeException;
use app\common\lib\transfer_service\logic\ShotsChange;
use app\common\lib\WeChat;
use app\common\log\SocketLogCollector;
use app\common\model\Activity;
use app\common\model\CentralizedController;
use app\common\model\ElectricityMeterPowerRecord;
use app\common\model\Order as OrderModel;
use app\common\model\Piles as PilesModel;
use app\common\model\PilesStatus;
use app\common\model\Shots;
use app\common\model\ShotsStatus;
//use app\common\model\StationExtraInfo;
use app\common\model\Stations;
use app\common\model\TariffGroup as TariffGroupModel;
use app\common\model\Users;
use app\ms\Api;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\BasePackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\ActiveUploadRealtimeChargingData;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\CcMonitorSystem;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\CcServiceOfflineNotice;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\CcServiceOnlineNotice;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\CentralizedControllerChange as CentralizedControllerChangeAlias;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\CentralizedControllerChangeAnswer;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\EndOrderAnswer;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\GetChargingStationBillingModel;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\PilesChange as PilesChangeProtocol;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\PilesHeartbeatNotice;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\PilesOfflineNotice;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\PilesOnlineNotice;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\ShotsChange as ShotsChangeProtocol;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\StartNotice;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\StartOrderAnswer;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\StationAllSyncCCAnswer;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\StationAllSyncPilesAnswer;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\StationAllSyncShotsAnswer;
//use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\ReservationStartNotice;
//use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\ReservationStartNotice;
//use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\StationsChange;
//use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\StationsChangeAnswer;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\SyncElectricityMeterPower;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\SyncElectricityMeterPowerAnswer;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\SyncPiles;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\SyncPilesAnswer;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\TransactionRecord;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\TransactionRecordConfirm;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\UploadRealtimeMonitoringDataPackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\json\package\operate\CentralizedControllerChange;
use LieHuoHuYu\ChargeTransferServiceProtocol\json\package\operate\StartOrderAndSetTariffAnswer;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\StationAllSyncShots;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\StationAllSyncCC;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\StationAllSyncPiles;
//use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\ToChargeNowAnswer;
//use LieHuoHuYu\ChargeTransferServiceProtocol\json\package\operate\InsufficientPowerNotice;
//use LieHuoHuYu\ChargeTransferServiceProtocol\json\package\operate\StartNotice;
use LieHuoHuYu\ChargeTransferServiceProtocol\Kernel;
use Redis;
use RedisException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use Throwable;
use app\common\lib\transfer_service\logic\PilesChange as PilesChangeLogic;
use app\common\lib\active_alarm\logic\electricity_meter\FailureCheck as ElectricityMeterFailureCheck;
use app\common\lib\active_alarm\logic\system_resource\FailureCheck as SystemResourceFailureCheck;
use Closure;

class TransferServiceReadQueue extends BaseQueue
{

    /**
     * 获取Redis实例
     *
     * @return Redis
     * @throws RedisException
     */
    protected function getRedisInstance(): Redis
    {
        return $this->connectRedis(config('my.redis2'));
    }

    protected function getKey(): string
    {
        return 'admin:queue:transfer_service_read';
    }

    protected function consumerHandler(string $data, SocketLogCollector $socketLogCollector): int
    {
        try {

            $package = Kernel::decode($data);
            if (empty($package->getIndex())) {
                $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), SocketLogCollector::LevelError);
                return parent::ResultCodeDiscard;
            }


            switch ($package->getIndex()) {
                case BasePackage::TypePong:
                    $socketLogCollector->collectorRunLog($package->encode());
                    $result = self::ResultCodeSuccess;
                    break;
                case BasePackage::TypeStartOrderAndSetTariffAnswer:
                    $result = $this->startOrderAndSetTariffAnswerHandler($socketLogCollector, $package);
                    break;
                case BasePackage::TypeStartOrderAnswer:
                    $result = $this->startOrderAnswerHandler($socketLogCollector, $package);
                    break;
                case BasePackage::TypeEndOrderAnswer:
                    $result = $this->endOrderAnswerHandler($socketLogCollector, $package);
                    break;
                case BasePackage::TypeUploadRealtimeMonitoringData:
                    $result = $this->uploadRealtimeMonitoringData($socketLogCollector, $package);
                    break;
                case BasePackage::TypeGetChargingStationBillingModel:
                    $result = $this->getChargingStationBillingModel($socketLogCollector, $package);
                    break;
                case BasePackage::TypeTransactionRecord:
                    $result = $this->transactionRecord($socketLogCollector, $package);
                    break;
                case BasePackage::TypePilesOnlineNotice:
                    $result = $this->pilesOnlineNotice($socketLogCollector, $package);
                    break;
                case BasePackage::TypePilesOfflineNotice:
                    $result = $this->pilesOfflineNotice($socketLogCollector, $package);
                    break;
                case BasePackage::TypePilesHeartbeatNotice:
                    $result = $this->pilesHeartbeatNotice($socketLogCollector, $package);
                    break;
//                case BasePackage::TypeCentralizedControllerChange:
//                    $result = $this->centralizedControllerChange($socketLogCollector, $package);
//                    break;
//                case BasePackage::TypePilesChange:
//                    $result = $this->pilesChange($socketLogCollector, $package);
//                    break;
//                case BasePackage::TypeShotsChange:
//                    $result = $this->shotsChange($socketLogCollector, $package);
//                    break;
//                case BasePackage::TypeStationsChange:
//                    $result = $this->stationsChange($socketLogCollector, $package);
//                    break;
                case BasePackage::TypeSyncElectricityMeterPower:
                    $result = $this->syncElectricityMeterPower($socketLogCollector, $package);
                    break;
                case BasePackage::TypeActiveUploadRealtimeChargingData:
                    $result = $this->activeUploadRealtimeChargingData($socketLogCollector, $package);
                    break;
//                case BasePackage::TypeCorpChange:
//                    $result = $this->corpChange($socketLogCollector, $package);
//                    break;
                case BasePackage::TypeCcMonitorSystem:
                    $result = $this->ccMonitorSystem($socketLogCollector, $package);
                    break;

                // 预约充电启机通知
//                case BasePackage::TypeReservationStartNotice:
//                    $result = $this->reservationStartNotice($socketLogCollector, $package);
//                    break;
                // 能源路由器服务上线通知
                case BasePackage::TypeCcServiceOnlineNotice:
                    $result = $this->ccServiceOnlineNotice($socketLogCollector, $package);
                    break;
                // 能源路由器服务下线通知
                case BasePackage::TypeCcServiceOfflineNotice:
                    $result = $this->ccServiceOfflineNotice($socketLogCollector, $package);
                    break;
                // 开启充电通知,发送微信通知
                case BasePackage::TypeChargingStartNotice:
                    $result = $this->sendWechatStartNotification($socketLogCollector, $package);
                    break;
                // 预约充电变更立即充电应答
//                case BasePackage::TypeToChargeNowAnswer:
//                    $result = $this->toChargeNowAnswer($socketLogCollector, $package);
//                    break;
//                // 启机功率不足通知
//                case BasePackage::TypeInsufficientPowerNotice:
//                    $result = $this->insufficientPowerNotice($socketLogCollector, $package);
//                    break;
//                // 立即充电启机通知
//                case BasePackage::TypeStartNotice:
//                    $result = $this->startNotice($socketLogCollector, $package);
//                    break;
                // 同步场站所有的充电桩数据
//                case BasePackage::TypeStationAllSyncPiles:
//                    $result = $this->stationAllSyncPiles($socketLogCollector, $package);
//                    break;
                // 同步场站所有充电枪数据
//                case BasePackage::TypeStationAllSyncShots:
//                    $result = $this->stationAllSyncShots($socketLogCollector, $package);
//                    break;
                // 同步场站所有能源路由器数据
//                case BasePackage::TypeStationAllSyncCC:
//                    $result = $this->stationAllSyncCC($socketLogCollector, $package);
//                    break;
                default:
                    $socketLogCollector->collectorRunLog(sprintf('%s | 无效队列消息:%s', $this->getKey(), $data), SocketLogCollector::LevelError);
                    $result = parent::ResultCodeDiscard;
                    break;
            }
        } catch (Throwable $e) {
            $socketLogCollector->collect($e);
            $result = parent::ResultCodeDiscard;

            sleep(1);
        }

        return $result;
    }

//    protected function startNotice(SocketLogCollector $socketLogCollector, BasePackage $package): int
//    {
//        /**
//         * @var StartNotice $body
//         */
//        $body = $package->getBody();
//
//        $message = [
//            'type' => 'start_notice',
//            'msg' => '立即充电启机通知',
//            'data' => [
//                'transaction_serial_number' => $body->getTransactionSerialNumber(),
//                'piles_id' => $body->getPilesId(),
//                'shots_number' => $body->getShotsNumber(),
//            ]
//        ];
//
//        $user_id = Order::getOrderUserId($body->getTransactionSerialNumber());
//
//        if (is_null($user_id)) $socketLogCollector->recordErrorLog(sprintf('订单:%s 关联的用户ID为空', $body->getTransactionSerialNumber()));
//        if (!is_null($user_id)) SendApplet::send_uid($user_id, $message);
//
//
//        return self::ResultCodeSuccess;
//    }

//    protected function insufficientPowerNotice(SocketLogCollector $socketLogCollector, BasePackage $package): int
//    {
//        /**
//         * @var InsufficientPowerNotice $body
//         */
//        $body = $package->getBody();
//
//        $message = [
//            'type' => 'insufficient_power_notice',
//            'msg' => '启机功率不足通知',
//            'data' => [
//                'transaction_serial_number' => $body->getTransactionSerialNumber(),
//                'piles_id' => $body->getPilesId(),
//                'shots_number' => $body->getShotsNumber(),
//                'is_replace' => $body->getIsReplace(),
//                'replace_piles_id' => $body->getReplacePilesId(),
//                'replace_shots_number' => $body->getReplaceShotsNumber()
//            ]
//        ];
//
//        $user_id = Order::getOrderUserId($body->getTransactionSerialNumber());
//
//        if (is_null($user_id)) $socketLogCollector->recordErrorLog(sprintf('订单:%s 关联的用户ID为空', $body->getTransactionSerialNumber()));
//        if (!is_null($user_id)) SendApplet::send_uid($user_id, $message);
//
//        return self::ResultCodeSuccess;
//    }
//
//    protected function toChargeNowAnswer(SocketLogCollector $socketLogCollector, BasePackage $package): int
//    {
//        /**
//         * @var ToChargeNowAnswer $body
//         */
//        $body = $package->getBody();
//
//        if ($body->getResult() === ToChargeNowAnswer::ResultSuccess) {
//            Order::updateOrderStatus($body->getTransactionSerialNumber(), Order::StatusCharging);
//        }
//
//        $message = [
//            'type' => 'to_charge_now_notice',
//            'msg' => '预约充电订单变更为立即充电的结果通知',
//            'data' => [
//                'transaction_serial_number' => $body->getTransactionSerialNumber(),
//                'result' => $body->getResult(),
//                'failure_reason_code' => $body->getFailureReasonCode()
//            ]
//        ];
//        SendApplet::send_uid($body->getAppletUserId(), $message);
//
//        return self::ResultCodeSuccess;
//    }
    protected function stationAllSyncCC(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        try {
            /**
             * @var StationAllSyncCC $body
             */
            $body = $package->getBody();

            $centralizedControllerModel = (new CentralizedController());
            $centralizedControllerModel->deleteStationAllCentralizedController($body->getStationId());

            $inserts = [];
            foreach ($body->getCentralizedControllers() as $centralizedController) {
                $inserts[] = [
                    'id' => $centralizedController['id'],
                    'corp_id' => $centralizedController['corp_id'],
                    'station_id' => $centralizedController['station_id'],
                    'name' => $centralizedController['name']
                ];
            }
            $centralizedControllerModel->insertAll($inserts);

            $result = StationAllSyncPilesAnswer::ResultSuccess;
            $failure_reason_code = StationAllSyncCCAnswer::FailureReasonCodeNone;
        } catch (Throwable $e) {
            $socketLogCollector->collect($e);
            $result = StationAllSyncPilesAnswer::ResultFailed;
            $failure_reason_code = StationAllSyncCCAnswer::FailureReasonCodeUnknown;
        }

        $answer = Kernel::create($package->getSerialNumber(), BasePackage::TypeStationAllSyncCCAnswer, [
            'result' => $result,
            'failure_reason_code' => $failure_reason_code
        ]);

        (new TransferServiceWriteQueue())->push($answer->encode());

        return self::ResultCodeSuccess;
    }

    protected function stationAllSyncShots(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        try {
            /**
             * @var StationAllSyncShots $body
             */
            $body = $package->getBody();

            $shotsModel = new Shots();
            // 删除场站所有桩
            $shotsModel->deleteStationAllShots($body->getStationId());


            $inserts = [];
            $online_shots_ids = $charging_shots_ids = $idle_shots_ids = $fault_shots_ids = $normal_shots_ids = [];

            foreach ($body->getShots() as $shot) {
                $inserts[] = [
                    'id' => $shot['id'],
                    'name' => $shot['name'],
                    'corp_id' => $shot['corp_id'],
                    'station_id' => $shot['station_id'],
                    'piles_id' => $shot['piles_id'],
                    'sequence' => $shot['sequence'],
                    'port_num' => $shot['port_num'],
                ];


                // 充电中的充电枪
                if (isset($shot['work_status']) && (int)$shot['work_status'] === ShotsChangeProtocol::WorkStatusCharging) {
                    $charging_shots_ids[] = $shot['id'];
                    // 空闲的充电枪
                } else if (isset($shot['work_status']) && (int)$shot['work_status'] === ShotsChangeProtocol::WorkStatusIdle) {
                    $idle_shots_ids[] = $shot['id'];
                }

                // 故障的充电枪
                if (isset($shot['is_fault']) && (int)$shot['is_fault'] === ShotsChangeProtocol::IsFaultYes) {
                    $fault_shots_ids[] = $shot['id'];
                    // 正常地充电枪
                } else if (isset($shot['is_fault']) && (int)$shot['is_fault'] === ShotsChangeProtocol::IsFaultNot) {
                    $normal_shots_ids[] = $shot['id'];
                }
            }
            $shotsModel->batchAddShots($inserts);

            $ShotsStatusModel = new ShotsStatus();

            if (count($charging_shots_ids)) {
                $ShotsStatusModel->updateWorkStatus($charging_shots_ids, ShotsStatus::WorkStatusCharging);
            }
            if (count($idle_shots_ids)) {
                $ShotsStatusModel->updateWorkStatus($idle_shots_ids, ShotsStatus::WorkStatusIdle);
            }
            if (count($fault_shots_ids)) {
                $ShotsStatusModel->updateIsFault($fault_shots_ids, ShotsStatus::IsFaultYes);
            }
            if (count($normal_shots_ids)) {
                $ShotsStatusModel->updateIsFault($normal_shots_ids, ShotsStatus::IsFaultNot);
            }

            $result = StationAllSyncShotsAnswer::ResultSuccess;
            $failure_reason_code = StationAllSyncShotsAnswer::FailureReasonCodeNone;
        } catch (Throwable $e) {
            $socketLogCollector->collect($e);
            $result = StationAllSyncShotsAnswer::ResultFailed;
            $failure_reason_code = StationAllSyncShotsAnswer::FailureReasonCodeUnknown;
        }

        $answer = Kernel::create($package->getSerialNumber(), BasePackage::TypeStationAllSyncShotsAnswer, [
            'result' => $result,
            'failure_reason_code' => $failure_reason_code
        ]);

        (new TransferServiceWriteQueue())->push($answer->encode());


        return self::ResultCodeSuccess;
    }

    protected function stationAllSyncPiles(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        try {
            /**
             * @var StationAllSyncPiles $body
             */
            $body = $package->getBody();

            $pilesModel = new PilesModel();
            // 删除场站所有桩
            $pilesModel->deleteStationAllPiles($body->getStationId());

            // 添加同步过来的充电桩
            $inserts = [];
            $online_piles_ids = [];
            foreach ($body->getPiles() as $piles) {
                $inserts[] = [
                    'id' => $piles['id'],
                    'name' => $piles['name'],
                    'corp_id' => $piles['corp_id'],
                    'station_id' => $piles['station_id'],
                    'ac_dc' => $piles['ac_dc'],
                    'type' => $piles['type'],
                    'model' => $piles['model'],
                    'power' => $piles['power'],
                    'comm_id' => $piles['comm_id'],
                    'phase_name' => $piles['phase_name']
                ];
                if (isset($piles['is_online']) && (int)$piles['is_online'] === PilesChangeProtocol::IsOnlineYes) {
                    $online_piles_ids[] = $piles['id'];
                }
            }
            $pilesModel->batchAddPiles($inserts);

            // 更新充电桩在线状态
            (new PilesStatus())->updateIsOnline($online_piles_ids, PilesStatus::IsOnlineYes);

            $result = StationAllSyncPilesAnswer::ResultSuccess;
            $failure_reason_code = StationAllSyncPilesAnswer::FailureReasonCodeNone;
        } catch (Throwable $e) {
            $socketLogCollector->collect($e);
            $result = StationAllSyncPilesAnswer::ResultFailed;
            $failure_reason_code = StationAllSyncPilesAnswer::FailureReasonCodeUnknown;
        }

        $answer = Kernel::create($package->getSerialNumber(), BasePackage::TypeStationAllSyncPilesAnswer, [
            'result' => $result,
            'failure_reason_code' => $failure_reason_code
        ]);

        (new TransferServiceWriteQueue())->push($answer->encode());

        return self::ResultCodeSuccess;
    }

//    protected function startNotice(SocketLogCollector $socketLogCollector, BasePackage $package): int
//    {
//        /**
//         * @var StartNotice $body
//         */
//        $body = $package->getBody();
//
//        $message = [
//            'type' => 'start_notice',
//            'msg' => '立即充电启机通知',
//            'data' => [
//                'transaction_serial_number' => $body->getTransactionSerialNumber(),
//                'piles_id' => $body->getPilesId(),
//                'shots_number' => $body->getShotsNumber(),
//            ]
//        ];
//
//        $user_id = Order::getOrderUserId($body->getTransactionSerialNumber());
//
//        if (is_null($user_id)) $socketLogCollector->recordErrorLog(sprintf('订单:%s 关联的用户ID为空', $body->getTransactionSerialNumber()));
//        if (!is_null($user_id)) SendApplet::send_uid($user_id, $message);
//
//
//        return self::ResultCodeSuccess;
//    }

//    protected function insufficientPowerNotice(SocketLogCollector $socketLogCollector, BasePackage $package): int
//    {
//        /**
//         * @var InsufficientPowerNotice $body
//         */
//        $body = $package->getBody();
//
//        $message = [
//            'type' => 'insufficient_power_notice',
//            'msg' => '启机功率不足通知',
//            'data' => [
//                'transaction_serial_number' => $body->getTransactionSerialNumber(),
//                'piles_id' => $body->getPilesId(),
//                'shots_number' => $body->getShotsNumber(),
//                'is_replace' => $body->getIsReplace(),
//                'replace_piles_id' => $body->getReplacePilesId(),
//                'replace_shots_number' => $body->getReplaceShotsNumber()
//            ]
//        ];
//
//        $user_id = Order::getOrderUserId($body->getTransactionSerialNumber());
//
//        if (is_null($user_id)) $socketLogCollector->recordErrorLog(sprintf('订单:%s 关联的用户ID为空', $body->getTransactionSerialNumber()));
//        if (!is_null($user_id)) SendApplet::send_uid($user_id, $message);
//
//        return self::ResultCodeSuccess;
//    }

//    protected function toChargeNowAnswer(SocketLogCollector $socketLogCollector, BasePackage $package): int
//    {
//        /**
//         * @var ToChargeNowAnswer $body
//         */
//        $body = $package->getBody();
//
//        if ($body->getResult() === ToChargeNowAnswer::ResultSuccess) {
//            Order::updateOrderStatus($body->getTransactionSerialNumber(), Order::StatusCharging);
//        }
//
//        $message = [
//            'type' => 'to_charge_now_notice',
//            'msg' => '预约充电订单变更为立即充电的结果通知',
//            'data' => [
//                'transaction_serial_number' => $body->getTransactionSerialNumber(),
//                'result' => $body->getResult(),
//                'failure_reason_code' => $body->getFailureReasonCode()
//            ]
//        ];
//        SendApplet::send_uid($body->getAppletUserId(), $message);
//
//        return self::ResultCodeSuccess;
//    }

    /**
     * @param SocketLogCollector $socketLogCollector
     * @param BasePackage $package
     * @return int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function ccServiceOfflineNotice(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        /**
         * @var CcServiceOfflineNotice $body
         */
        $body = $package->getBody();

        (new \app\common\lib\active_alarm\logic\charging_services\OfflineFailureCheck(
            $socketLogCollector,
            $package->getBody(),
            $body->getCorpId(),
            $body->getStationId(),
            $body->getCentralizedControllerId()
        ))->failureCheck();

        return self::ResultCodeSuccess;
    }

    /**
     * @param SocketLogCollector $socketLogCollector
     * @param BasePackage $package
     * @return int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function ccServiceOnlineNotice(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        /**
         * @var CcServiceOnlineNotice $body
         */
        $body = $package->getBody();

        (new \app\common\lib\active_alarm\logic\charging_services\OnlineFailureCheck(
            $socketLogCollector,
            $package->getBody(),
            $body->getCorpId(),
            $body->getStationId(),
            $body->getCentralizedControllerId()
        ))->failureCheck();

        return self::ResultCodeSuccess;
    }

//    protected function reservationStartNotice(SocketLogCollector $socketLogCollector, BasePackage $package): int
//    {
//        /**
//         * @var ReservationStartNotice $body
//         */
//        $body = $package->getBody();
//
//        $body->getTransactionSerialNumber();
//        $status = Order::getOrderStatus($body->getTransactionSerialNumber());
//        if ($status === Order::StatusReservation) {
//            Order::updateOrderStatus($body->getTransactionSerialNumber(), Order::StatusCharging);
//        }
//
//        return self::ResultCodeSuccess;
//    }

    protected function activeUploadRealtimeChargingData(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
//        $this->openExceptionCatch(function () use ($package) {
        /**
         * @var ActiveUploadRealtimeChargingData $body
         */
//            $body = $package->getBody();
//            if ($body->getTransactionSerialNumber() !== '00000000000000000000000000000000') {
//                $status = Order::getOrderStatus($body->getTransactionSerialNumber());
//                if (
//                    !is_null($status) &&
//                    in_array($status, [
//                        Order::StatusComplete,
//                        Order::StatusCompulsorySettlement,
//                        Order::StatusAbnormal
//                    ])
//                ) {
//                    $sequence = SerialNumber::increaseSequence();
//                    $package = Kernel::create($sequence, BasePackage::TypeEndOrder, [
//                        'piles_id' => $body->getPilesId(),
//                        'shots_number' => $body->getShotsNumber(),
//                        'transaction_serial_number' => $body->getTransactionSerialNumber(),
//                        'operator' => EndOrder::OperatorSystem
//                    ]);
//                    (new TransferServiceWriteQueue())->push($package->encode());
//
//                }
//            }
//        }, $socketLogCollector);

        return self::ResultCodeSuccess;
    }

    protected function ccMonitorSystem(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        $this->openExceptionCatch(function () use ($package, $socketLogCollector) {
            /**
             * @var CcMonitorSystem $body
             */
            $body = $package->getBody();
            (new SystemResourceFailureCheck($socketLogCollector, $body, $body->getCorpId(), $body->getStationId(), $body->getCentralizedControllerId()))->failureCheck();
        }, $socketLogCollector, true);

        return self::ResultCodeSuccess;
    }

    /**
     * @param SocketLogCollector $socketLogCollector
     * @param BasePackage $package
     * @return int
     * @throws RedisException
     */
    protected function syncElectricityMeterPower(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        try {

            /**
             * @var SyncElectricityMeterPower $body
             */
            $body = $package->getBody();

            (new ElectricityMeterPowerRecord())->addRecord(
                $body->getStationId(),
                $body->getCentralizedControllerId(),
                $body->getAActivePower(),
                $body->getBActivePower(),
                $body->getCActivePower(),
                $body->getTotalActivePower(),
                $body->getSyncTime()
            );

            $corp_id = (new Stations())->getCorpId($body->getStationId());

            $answerPackage = Kernel::create($package->getSerialNumber(), BasePackage::TypeSyncElectricityMeterPowerAnswer, [
                'result' => SyncElectricityMeterPowerAnswer::ResultSuccess,
            ]);
            (new TransferServiceWriteQueue())->push($answerPackage->encode());
        } catch (Throwable $e) {

            $answerPackage = Kernel::create($package->getSerialNumber(), BasePackage::TypeSyncElectricityMeterPowerAnswer, [
                'result' => SyncElectricityMeterPowerAnswer::ResultFailed,
            ]);
            (new TransferServiceWriteQueue())->push($answerPackage->encode());
        }

        if (isset($corp_id)) {
            $this->openExceptionCatch(function () use ($body, $corp_id, $socketLogCollector) {
                (new ElectricityMeterFailureCheck($socketLogCollector, $body, $corp_id, $body->getStationId(), $body->getCentralizedControllerId()))->failureCheck();
            }, $socketLogCollector, true);
        }

        return self::ResultCodeSuccess;
    }

    /**
     * @param SocketLogCollector $socketLogCollector
     * @param BasePackage $package
     * @return int
     * @throws RedisException
     */
    protected function shotsChange(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        (new ShotsChange($socketLogCollector, $package))->run();

        return self::ResultCodeSuccess;
    }

//    /**
//     * @param SocketLogCollector $socketLogCollector
//     * @param BasePackage $package
//     * @return int
//     * @throws RedisException
//     */
//    protected function stationsChange(SocketLogCollector $socketLogCollector, BasePackage $package): int
//    {
//        try {
//
//            // {
//            //"serial_number":65171,
//            //"index":"stations_change",
//            //"body":{
//            //"stations":[{
//            //"id":1004,
//            //"name":"\\u573a\\u7ad9\\u540d\\u79f0",
//            //"all_address":"\\u5317\\u4eac\\u5e02\\u5317\\u4eac\\u5e02\\u4e1c\\u57ce\\u533a\\u573a\\u7ad9\\u5730\\u5740",
//            //"address":"\\u573a\\u7ad9\\u5730\\u5740",
//            //"province":"\\u5317\\u4eac\\u5e02",
//            //"city":"\\u5317\\u4eac\\u5e02",
//            //"district":"\\u4e1c\\u57ce\\u533a",
//            //"type":1,"city_id":"[110000,110100,110101]"}],"operation_type":1}}
//            /**
//             * @var StationsChange $body
//             */
//            $body = $package->getBody();
//            switch ($body->getOperationType()) {
//                case StationsChange::OperationTypeAdd:
//                    $inserts = [];
//                    $stations_extra_info_ids = [];
//                    $stations_data = [];
//                    foreach ($body->getStations() as $station) {
//                        // CREATE TABLE `stations` (
//                        //  `id` bigint(24) NOT NULL AUTO_INCREMENT COMMENT '场站id',
//                        //  `corp_id` bigint(24) NOT NULL COMMENT '运营商编号',
//                        //  `name` varchar(128) NOT NULL COMMENT '场站名称',
//                        //  `all_address` varchar(500) NOT NULL COMMENT '完整场站地址，需带省市区',
//                        //  `address` varchar(500) NOT NULL COMMENT '场站地址，不必带省市区',
//                        //  `province` varchar(128) NOT NULL COMMENT '省\r\n',
//                        //  `city` varchar(128) NOT NULL COMMENT '市',
//                        //  `district` varchar(128) NOT NULL COMMENT '区',
//                        //  `pile_num` int(8) NOT NULL DEFAULT '0' COMMENT '桩数',
//                        //  `type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '场站类型：1-公共、2-自营',
//                        //  `lonlat` varchar(100) NOT NULL COMMENT '经纬度，前面纬度，后面经度',
//                        //  `city_id` json DEFAULT NULL COMMENT '省市区id',
//                        //  PRIMARY KEY (`id`) USING BTREE
//                        //) ENGINE=InnoDB AUTO_INCREMENT=20000064 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
//                        $inserts[] = [
//                            'id' => $station['id'],
//                            'name' => $station['name'],
//                            'corp_id' => $station['corp_id'],
//                            'all_address' => $station['all_address'],
//                            'address' => $station['address'],
//                            'province' => $station['province'],
//                            'city' => $station['city'],
//                            'district' => $station['district'],
//                            'city_id' => $station['city_id'],
//                            'type' => $station['type'],
//                            'lonlat' => $station['lonlat'],
//                            'pile_num' => 0,
//                            'create_time' => date('Y-m-d H:i:s')
//                        ];
//                        $stations_extra_info_ids[] = $station['id'];
//                        $stations_data[] = [
//                            'station_id' => $station['id'],
//                            'corp_id' => $station['corp_id']
//                        ];
//                    }
//                    (new Stations())->insertAll($inserts);
//                    (new StationExtraInfo())->insertDefaultDataAll($stations_extra_info_ids);
//                    StationsActiveAlarmConfig::batchGenerateDefaultStationConfig($stations_data);
//                    break;
//                case StationsChange::OperationTypeUpdate:
//                    $stationsModel = new Stations();
//                    foreach ($body->getStations() as $station) {
//                        $stationsModel->where('id', '=', $station['id'])->update([
//                            'name' => $station['name'],
//                            'all_address' => $station['all_address'],
//                            'address' => $station['address'],
//                            'province' => $station['province'],
//                            'city' => $station['city'],
//                            'district' => $station['district'],
//                            'city_id' => $station['city_id'],
//                        ]);
//                    }
//                    break;
//                case StationsChange::OperationTypeDelete:
//                    $stations_ids = [];
//                    foreach ($body->getStations() as $station) {
//                        $stations_ids[] = $station['id'];
//                    }
//                    (new Stations())->whereIn('id', $stations_ids)->delete();
//                    break;
//                case StationsChange::OperationTypeAllSync:
//                    $stationsModel = (new Stations());
//                    $stationsModel->deleteAll();
//                    $inserts = [];
//                    $stations_extra_info_ids = [];
//                    $batchAddActiveAlarmConfigStationsData = [];
//                    foreach ($body->getStations() as $station) {
//                        $inserts[] = [
//                            'id' => $station['id'],
//                            'name' => $station['name'],
//                            'corp_id' => $station['corp_id'],
//                            'all_address' => $station['all_address'],
//                            'address' => $station['address'],
//                            'province' => $station['province'],
//                            'city' => $station['city'],
//                            'district' => $station['district'],
//                            'city_id' => $station['city_id'],
//                            'type' => $station['type'],
//                            'lonlat' => $station['lonlat'],
//                            'pile_num' => 0,
//                            'create_time' => date('Y-m-d H:i:s')
//                        ];
//                        $stations_extra_info_ids[] = $station['id'];
//                        if (StationsActiveAlarmConfig::isExistence($station['id']) === false) {
//                            $batchAddActiveAlarmConfigStationsData[] = [
//                                'station_id' => $station['id'],
//                                'corp_id' => $station['corp_id']
//                            ];
//                        }
//                    }
//                    $stationsModel->insertAll($inserts);
//                    (new StationExtraInfo())->insertDefaultDataAll($stations_extra_info_ids);
//                    if (!empty($batchAddActiveAlarmConfigStationsData)) StationsActiveAlarmConfig::batchGenerateDefaultStationConfig($batchAddActiveAlarmConfigStationsData);
//                    break;
//            }
//
//            $answerPackage = Kernel::create($package->getSerialNumber(), BasePackage::TypeStationsChangeAnswer, [
//                'result' => StationsChangeAnswer::ResultSuccess,
//                'failure_reason_code' => StationsChangeAnswer::FailureReasonCodeNone
//            ]);
//            (new TransferServiceWriteQueue())->push($answerPackage->encode());
//
//
//        } catch (Throwable $e) {
//            $socketLogCollector->collect($e);
//            $answerPackage = Kernel::create($package->getSerialNumber(), BasePackage::TypeStationsChangeAnswer, [
//                'result' => StationsChangeAnswer::ResultFailed,
//                'failure_reason_code' => StationsChangeAnswer::FailureReasonCodeNone
//            ]);
//            (new TransferServiceWriteQueue())->push($answerPackage->encode());
//        }
//
//        return self::ResultCodeSuccess;
//    }

    /**
     *
     * @param SocketLogCollector $socketLogCollector
     * @param BasePackage $package
     * @return int
     * @throws RedisException
     */
    protected function pilesChange(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        (new PilesChangeLogic($socketLogCollector, $package))->run();

        return self::ResultCodeSuccess;
    }

    /**
     * @param SocketLogCollector $socketLogCollector
     * @param BasePackage $package
     * @return int
     * @throws RedisException
     */
    protected function centralizedControllerChange(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        try {
            /**
             * @var CentralizedControllerChange $body
             */
            $body = $package->getBody();

            switch ($body->getOperationType()) {
                case CentralizedControllerChangeAlias::OperationTypeAdd:
                    $inserts = [];
                    foreach ($body->getCentralizedControllers() as $centralizedController) {
                        $inserts[] = [
                            'id' => $centralizedController['id'],
                            'corp_id' => $centralizedController['corp_id'],
                            'station_id' => $centralizedController['station_id'],
                            'name' => $centralizedController['name']
                        ];
                    }
                    (new CentralizedController())->insertAll($inserts);
                    break;
                case CentralizedControllerChangeAlias::OperationTypeUpdate:
                    $centralizedControllerModel = new CentralizedController();
                    foreach ($body->getCentralizedControllers() as $centralizedController) {
                        $centralizedControllerModel->where('id', '=', $centralizedController['id'])->update([
                            'name' => $centralizedController['name'],
                            'corp_id' => $centralizedController['corp_id'],
                            'station_id' => $centralizedController['station_id']
                        ]);
                    }
                    break;
                case CentralizedControllerChangeAlias::OperationTypeDelete:
                    $ids = [];
                    foreach ($body->getCentralizedControllers() as $centralizedController) {
                        $ids[] = $centralizedController['id'];
                    }
                    (new CentralizedController())
                        ->whereIn('id', $ids)
                        ->delete();
                    break;
                case CentralizedControllerChangeAlias::OperationTypeAllSync:
                    $centralizedControllerModel = (new CentralizedController());
                    $centralizedControllerModel->deleteAll();
                    $inserts = [];
                    foreach ($body->getCentralizedControllers() as $centralizedController) {
                        $inserts[] = [
                            'id' => $centralizedController['id'],
                            'corp_id' => $centralizedController['corp_id'],
                            'station_id' => $centralizedController['station_id'],
                            'name' => $centralizedController['name']
                        ];
                    }
                    $centralizedControllerModel->insertAll($inserts);
                    break;
            }


            $answerPackage = Kernel::create($package->getSerialNumber(), BasePackage::TypeCentralizedControllerChangeAnswer, [
                'result' => CentralizedControllerChangeAnswer::ResultSuccess,
                'failure_reason_code' => CentralizedControllerChangeAnswer::FailureReasonCodeNone
            ]);
            (new TransferServiceWriteQueue())->push($answerPackage->encode());
        } catch (Throwable $e) {
            $socketLogCollector->collect($e);
            $answerPackage = Kernel::create($package->getSerialNumber(), BasePackage::TypeCentralizedControllerChangeAnswer, [
                'result' => CentralizedControllerChangeAnswer::ResultFailed,
                'failure_reason_code' => CentralizedControllerChangeAnswer::FailureReasonCodeNone
            ]);
            (new TransferServiceWriteQueue())->push($answerPackage->encode());
        }


        return self::ResultCodeSuccess;
    }

    /**
     * @param BasePackage $package
     * @return int
     * @throws RedisException
     */
    protected function syncPiles(BasePackage $package): int
    {
        /**
         * @var SyncPiles $syncPiles
         */
        $syncPiles = $package->getBody();

        $pilesIds = [];
        foreach ($syncPiles->getOlineList() as $item) {
            $pilesIds[] = $item['piles_id'];
        }
        (new PilesStatus())->updateIsOnline($pilesIds, PilesStatus::IsOnlineYes);


        $answerPackage = Kernel::create($package->getSerialNumber(), BasePackage::TypeSyncPilesAnswer, [
            'result' => SyncPilesAnswer::ResultYes
        ]);

        (new TransferServiceWriteQueue())->push($answerPackage->encode());

        return self::ResultCodeSuccess;
    }

    /**
     * @param SocketLogCollector $socketLogCollector
     * @param BasePackage $package
     * @return int
     * @throws RedisException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getChargingStationBillingModel(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        /**
         * @var GetChargingStationBillingModel $getChargingStationBillingModel
         */
        $getChargingStationBillingModel = $package->getBody();

        $piles_id = $getChargingStationBillingModel->getPilesId();
        $tariff_group = (new OrderModel())->getChargingOrderTariffData($piles_id);
        if (empty($tariff_group)) {
            $pilesModel = new PilesModel();
            $pilesData = $pilesModel->getPilesData($piles_id, [
                'p.station_id',
            ]);
            $discount = (new Activity())->getStationDiscount($pilesData['station_id']);
            // ========== 从微服务查询充电桩是否有单独的费率ID ==========
            $tariff_group_id = Api::send("/device/piles/tariff",'GET',['pile_id' => $piles_id])['data'];
            if ($tariff_group_id == null){
                // 没有则跟随场站费率组
                $tariff_group_id = $pilesModel->getTariffGroupId($piles_id);
            }

            $tariff_group_model = new TariffGroupModel();
            $tariff_group = $tariff_group_model
                ->where('id', $tariff_group_id)
                ->field([
                    'id as billing_mode_id',
                    'sharp_fee', 'sharp_ser_fee',
                    'peak_fee', 'peak_ser_fee',
                    'flat_fee', 'flat_ser_fee',
                    'valley_fee', 'valley_ser_fee',
                    'loss_rate', 'period_codes'
                ])
                ->find();

            if (empty($tariff_group)) {
                // 没有查询到绑定的费率组
                $socketLogCollector->recordErrorLog(sprintf('没有查询到绑定的费率组:%s', $package->encode()));
                return self::ResultCodeDiscard;
            }
            $tariff_group = $tariff_group->toArray();
        } else {
            $discount = $tariff_group['discount'];
        }


        $package = Kernel::create($package->getSerialNumber(), BasePackage::TypeGetChargingStationBillingModelAnswer, [
            "piles_id" => $getChargingStationBillingModel->getPilesId(),
            "billing_mode_id" => $tariff_group['billing_mode_id'],
            "sharp_fee" => $tariff_group['sharp_fee'],
            "sharp_ser_fee" => (int)(ceil($tariff_group['sharp_ser_fee'] * ($discount / 100))),
            "peak_fee" => $tariff_group['peak_fee'],
            "peak_ser_fee" => (int)(ceil($tariff_group['peak_ser_fee'] * ($discount / 100))),
            "flat_fee" => $tariff_group['flat_fee'],
            "flat_ser_fee" => (int)(ceil($tariff_group['flat_ser_fee'] * ($discount / 100))),
            "valley_fee" => $tariff_group['valley_fee'],
            "valley_ser_fee" => (int)(ceil($tariff_group['valley_ser_fee'] * ($discount / 100))),
            "loss_rate" => $tariff_group['loss_rate'],
            "period_codes" => $tariff_group['period_codes']
        ]);
        (new TransferServiceWriteQueue())->push($package->encode());

        return self::ResultCodeSuccess;
    }

    protected function pilesHeartbeatNotice(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        try {
            /**
             * @var PilesHeartbeatNotice $pilesHeartbeatNoticePackage
             */
            $pilesHeartbeatNoticePackage = $package->getBody();

            $pilesData = (new PilesModel())->getPilesData($pilesHeartbeatNoticePackage->getPilesId());
            if (!$pilesData) {
                $socketLogCollector->collectorRunLog('找不到充电桩不允许登录', 'error');
                return self::ResultCodeDiscard;
//                throw new RuntimeException('找不到充电桩不允许登录');
            }

            foreach ($pilesHeartbeatNoticePackage->getShots() as $shot) {
                $shots_id = (int)($pilesHeartbeatNoticePackage->getPilesId() . '0' . $shot['shots_number']);

                if ($shot['shots_status'] === 0) {
                    (new ShotsStatus())->updateIsFault($shots_id, ShotsStatus::IsFaultNot);
                } else {
                    (new ShotsStatus())->updateIsFault($shots_id, ShotsStatus::IsFaultYes);
                }
            }
        } catch (Throwable $e) {
            $socketLogCollector->collect($e);
            return parent::ResultCodeFailed;
        }

        return parent::ResultCodeSuccess;
    }

    protected function pilesOfflineNotice(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        /**
         * @var PilesOfflineNotice $pilesOfflineNoticePackage
         */
        $pilesOfflineNoticePackage = $package->getBody();


        try {
            $pilesData = (new PilesModel())->getPilesData($pilesOfflineNoticePackage->getPilesId());
            if (!empty($pilesData)) {

                (new PilesStatus())->updateIsOnline($pilesOfflineNoticePackage->getPilesId(), PilesStatus::IsOnlineNot);

            }
        } catch (Throwable $e) {
            $socketLogCollector->collect($e);
            return parent::ResultCodeFailed;
        }
        if (!empty($pilesData)) {
            $this->openExceptionCatch(function () use ($socketLogCollector, $pilesOfflineNoticePackage, $pilesData) {
                (new OfflineFailureCheck($socketLogCollector, $pilesOfflineNoticePackage, $pilesData['corp_id'], $pilesData['station_id']))->failureCheck();
            }, $socketLogCollector, true);
        }

        return parent::ResultCodeSuccess;
    }

    protected function pilesOnlineNotice(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        try {
            /**
             * @var PilesOnlineNotice $pilesOnlineNoticePackage
             */
            $pilesOnlineNoticePackage = $package->getBody();

            $ShotsStatusChangeList = [];

            $pilesData = (new PilesModel())->getPilesData($pilesOnlineNoticePackage->getPilesId());
            if (!empty($pilesData)) {

                (new PilesStatus())->updateIsOnline($pilesOnlineNoticePackage->getPilesId(), PilesStatus::IsOnlineYes);

            }
        } catch (Throwable $e) {
            $socketLogCollector->collect($e, true);
            return parent::ResultCodeFailed;
        }

        if (!empty($pilesData)) {
            $this->openExceptionCatch(function () use ($socketLogCollector, $pilesOnlineNoticePackage, $pilesData) {
                if (isset($pilesData['corp_id']) && isset($pilesData['station_id'])) {
                    (new OnlineFailureCheck($socketLogCollector, $pilesOnlineNoticePackage, $pilesData['corp_id'], $pilesData['station_id']))->failureCheck();
                }
            }, $socketLogCollector, true);
        }


        return parent::ResultCodeSuccess;
    }

    protected function sendTransactionRecordConfirm(TransactionRecordRequest $request, SocketLogCollector $socketLogCollector): void
    {
        try {
            // 回复应答
            $answer = Kernel::create($request->sequence_number, BasePackage::TypeTransactionRecordConfirm, [
                'transaction_serial_number' => $request->transaction_serial_number,
                'confirm_result' => TransactionRecordConfirm::ConfirmResultYes
            ]);
            app(TransferServiceWriteQueue::class)->push($answer->encode());
        } catch (Throwable $e) {
            $socketLogCollector->collect($e);
        }
    }

    protected function transactionRecord(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {

        /**
         * @var TransactionRecord $transactionRecordPackage
         */
        $transactionRecordPackage = $package->getBody();

        $request = new TransactionRecordRequest([
            'data' => [
                'transaction_serial_number' => $transactionRecordPackage->getTransactionSerialNumber(),
                'piles_id' => $transactionRecordPackage->getPilesId(),
                'shots_id' => $transactionRecordPackage->getShotsNumber(),
                'start_time' => $transactionRecordPackage->getStartTime(),
                'end_time' => $transactionRecordPackage->getEndTime(),
                'sharp_price' => $transactionRecordPackage->getSharpPrice(),
                'sharp_charge' => $transactionRecordPackage->getSharpCharge(),
                'loss_sharp_charge' => $transactionRecordPackage->getLossSharpCharge(),
                'sharp_amount' => $transactionRecordPackage->getSharpAmount(),
                'peak_price' => $transactionRecordPackage->getPeakPrice(),
                'peak_charge' => $transactionRecordPackage->getPeakCharge(),
                'loss_peak_charge' => $transactionRecordPackage->getLossPeakCharge(),
                'peak_amount' => $transactionRecordPackage->getPeakAmount(),
                'flat_price' => $transactionRecordPackage->getFlatPrice(),
                'flat_charge' => $transactionRecordPackage->getFlatCharge(),
                'loss_flat_charge' => $transactionRecordPackage->getLossFlatCharge(),
                'flat_amount' => $transactionRecordPackage->getFlatAmount(),
                'valley_price' => $transactionRecordPackage->getValleyPrice(),
                'valley_charge' => $transactionRecordPackage->getValleyCharge(),
                'loss_valley_charge' => $transactionRecordPackage->getLossValleyCharge(),
                'valley_amount' => $transactionRecordPackage->getValleyAmount(),
                'electricity_meter_total_start' => $transactionRecordPackage->getElectricityMeterTotalStart(),
                'electricity_meter_total_end' => $transactionRecordPackage->getElectricityMeterTotalEnd(),
                'total_charge' => $transactionRecordPackage->getTotalCharge(),
                'loss_total_charge' => $transactionRecordPackage->getLossTotalCharge(),
                'consumption_amount' => $transactionRecordPackage->getConsumptionAmount(),
                'unique_identifier_of_electric_vehicle' => $transactionRecordPackage->getUniqueIdentifierOfElectricVehicle(),
                'transaction_identifier' => $transactionRecordPackage->getTransactionIdentifier(),
                'transaction_datetime' => $transactionRecordPackage->getTransactionDatetime(),
                'reason_for_stop' => $transactionRecordPackage->getReasonForStop(),
                'physical_card_number' => $transactionRecordPackage->getPhysicalCardNumber(),
                'sequence_number' => $package->getSerialNumber(),
                'abnormal_alarm_node' => $transactionRecordPackage->getAbnormalAlarmNode()
            ]
        ]);

        try {
            $data = (new Settlement($request, $socketLogCollector))->run();
        } catch (Throwable $e) {
            $socketLogCollector->collect($e);
            if ($e->getCode() === RuntimeException::CodeBusinessException) {
                $this->sendTransactionRecordConfirm($request, $socketLogCollector);
                return parent::ResultCodeDiscard;
            } else {
                return parent::ResultCodeFailed;
            }
        }

        $this->sendTransactionRecordConfirm($request, $socketLogCollector);

        $this->openExceptionCatch(function () use ($socketLogCollector, $transactionRecordPackage, $data) {
            if (isset($data['corp_id']) && isset($data['station_id'])) {
                (new TransactionFailureCheck($socketLogCollector, $transactionRecordPackage, $data['corp_id'], $data['station_id']))->failureCheck();
            }
        }, $socketLogCollector, true);

        return parent::ResultCodeSuccess;
    }

    protected function uploadRealtimeMonitoringData(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        /**
         * @var UploadRealtimeMonitoringDataPackage $uploadRealtimeMonitoringDataPackage
         */
        $uploadRealtimeMonitoringDataPackage = $package->getBody();

        // 获取shot_id
        $shotId = $uploadRealtimeMonitoringDataPackage->getPilesId() . $uploadRealtimeMonitoringDataPackage->getShotsNumber();

        // 获取当前序列号
        $currentSerialNumber = $package->getSerialNumber();

        try {
            // 使用Redis缓存存储序列号
            $redis = $this->getRedisInstance();
            $cacheKey = 'shot_serial_number:' . $shotId;

            // 检查是否存在缓存的序列号
            $cachedSerialNumber = $redis->get($cacheKey);
            if ($cachedSerialNumber !== false) {
                $cachedSerialNumber = (int)$cachedSerialNumber;
                // 差值
                $diff = $cachedSerialNumber - $currentSerialNumber;

                // 如果缓存的序列号和当前序列号大于0，并且不是因为序列号重置（序列号超过65530后会从1重新开始）
                if ($diff > 0 && $diff < 60000) {
                    $socketLogCollector->collectorRunLog(sprintf('丢弃序列号较小的数据包: shot_id=%s, 当前序列号=%d, 缓存序列号=%d', $shotId, $currentSerialNumber, $cachedSerialNumber));
                    return parent::ResultCodeSuccess; // 直接返回成功，不处理这个包
                }
            }

            // 更新缓存的序列号，设置过期时间为1小时
            $redis->setex($cacheKey, 3600, $currentSerialNumber);
        } catch (\RedisException $e) {
            // Redis异常时记录日志，但继续处理数据包
            $socketLogCollector->collect($e);
        }

        $request = new UploadRealtimeMonitoringDataRequest([
            'order_id' => $uploadRealtimeMonitoringDataPackage->getTransactionSerialNumber(),
            'transaction_serial_number' => $uploadRealtimeMonitoringDataPackage->getTransactionSerialNumber(),
            'piles_id' => $uploadRealtimeMonitoringDataPackage->getPilesId(),
            'shots_id' => $uploadRealtimeMonitoringDataPackage->getShotsNumber(),
            'status' => $uploadRealtimeMonitoringDataPackage->getStatus(),
            'is_rest' => $uploadRealtimeMonitoringDataPackage->getIsReset(),
            'is_plugged_int' => $uploadRealtimeMonitoringDataPackage->getIsPluggedIn(),
            'output_voltage' => $uploadRealtimeMonitoringDataPackage->getOutputVoltage(),
            'output_current' => $uploadRealtimeMonitoringDataPackage->getOutputCurrent(),
            'gun_wire_temperature' => $uploadRealtimeMonitoringDataPackage->getGunWireTemperature(),
            'gun_wire_code' => $uploadRealtimeMonitoringDataPackage->getGunWireCode(),
            'soc' => $uploadRealtimeMonitoringDataPackage->getSoc(),
            'maximum_battery_temperature' => $uploadRealtimeMonitoringDataPackage->getMaximumBatteryTemperature(),
            'cumulative_charging_time' => $uploadRealtimeMonitoringDataPackage->getCumulativeChargingTime(),
            'remaining_time' => $uploadRealtimeMonitoringDataPackage->getRemainingTime(),
            'charging_percentage' => $uploadRealtimeMonitoringDataPackage->getChargingPercentage(),
            'calculated_loss_charging_percentage' => $uploadRealtimeMonitoringDataPackage->getCalculatedLossChargingPercentage(),
            'amount_charged' => $uploadRealtimeMonitoringDataPackage->getAmountCharged(),
            'hardware_failure' => $uploadRealtimeMonitoringDataPackage->getHardwareFailure(),
            'hardware_failure_arr' => []
        ]);

        Db::startTrans();
        try {
            $data = (new UploadRealtimeMonitoringData($request, $socketLogCollector))->run();
            Db::commit();
        } catch (\RuntimeException|RuntimeException $e) {
            Db::rollback();
            $socketLogCollector->collect($e);
            if ($e->getCode() === RuntimeException::CodeBusinessException) {
                return parent::ResultCodeDiscard;
            }
            return parent::ResultCodeFailed;
        } catch (Throwable $e) {
            Db::rollback();
            $socketLogCollector->collect($e);
            return parent::ResultCodeFailed;
        }

        $this->openExceptionCatch(function () use ($socketLogCollector, $uploadRealtimeMonitoringDataPackage, $data) {
            if (isset($data['corp_id']) && isset($data['station_id'])) {
                (new MonitorFailureCheck(
                    $socketLogCollector,
                    $uploadRealtimeMonitoringDataPackage,
                    $data['corp_id'],
                    $data['station_id']
                ))->failureCheck();
            }
        }, $socketLogCollector, true);

        return parent::ResultCodeSuccess;
    }

    protected function endOrderAnswerHandler(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        /**
         * @var EndOrderAnswer $endOrderAnswer
         */
        $endOrderAnswer = $package->getBody();

        $request = new StopChargeAnswerRequest([
            'request_id' => $package->getSerialNumber(),
            'piles_id' => $endOrderAnswer->getPilesId(),
            'shots_id' => $endOrderAnswer->getShotsNumber(),
            'stop_result' => $endOrderAnswer->getResult(),
            'failure_reason' => $endOrderAnswer->getFailureReasonCode(),
            'operator' => $endOrderAnswer->getOperator()
        ]);

        Db::startTrans();
        try {
            (new StopChargeAnswer($request, $socketLogCollector))->handler();
            Db::commit();
        } catch (Throwable $e) {
            $socketLogCollector->collect($e);
            Db::rollback();
        }

        return parent::ResultCodeSuccess;
    }

    protected function startOrderAndSetTariffAnswerHandler(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        /**
         * @var StartOrderAndSetTariffAnswer $answer
         */
        $answer = $package->getBody();
        $request = new StartChargeAnswerRequest([
            'request_id' => $package->getSerialNumber(),
            'transaction_serial_number' => $answer->getTransactionSerialNumber(),
            'piles_id' => $answer->getPilesId(),
            'shots_id' => $answer->getShotsNumber(),
            'start_result' => $answer->getResult(),
            'failure_reason' => $answer->getFailureReasonCode(),
            'abnormal_alarm_node' => $answer->getAbnormalAlarmNode()
        ]);

        (new StartChargeAndSetTariffAnswer($request, $socketLogCollector))->handler();

        return parent::ResultCodeSuccess;
    }

    protected function startOrderAnswerHandler(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        /**
         * @var StartOrderAnswer $startOrderAnswer
         */
        $startOrderAnswer = $package->getBody();
        $request = new StartChargeAnswerRequest([
            'request_id' => $package->getSerialNumber(),
            'transaction_serial_number' => $startOrderAnswer->getTransactionSerialNumber(),
            'piles_id' => $startOrderAnswer->getPilesId(),
            'shots_id' => $startOrderAnswer->getShotsNumber(),
            'start_result' => $startOrderAnswer->getResult(),
            'failure_reason' => $startOrderAnswer->getFailureReasonCode(),
            'abnormal_alarm_node' => $startOrderAnswer->getAbnormalAlarmNode()
        ]);

        (new StartChargeAnswer($request, $socketLogCollector))->handler();

        return parent::ResultCodeSuccess;
    }

    /**
     * 开启异常捕捉
     *
     * @param Closure $business 业务逻辑处理
     * @param SocketLogCollector $socketLogCollector
     * @param bool $isStartTrans 是否开启数据库事务
     * @return int
     */
    protected function openExceptionCatch(Closure $business, SocketLogCollector $socketLogCollector, bool $isStartTrans = false): int
    {
        if ($isStartTrans) Db::startTrans();
        try {
            $business();
            if ($isStartTrans) Db::commit();
        } catch (Throwable $e) {
            if ($isStartTrans) Db::rollback();
            $socketLogCollector->collect($e);
            return parent::ResultCodeFailed;
        }

        return parent::ResultCodeSuccess;
    }

    /**
     * 发送充电开始微信通知
     */
    protected function sendWechatStartNotification(SocketLogCollector $socketLogCollector, BasePackage $package): int
    {
        try {
            /**
             * @var StartNotice $startNotice
             */
            $startNotice = $package->getBody();
            $orderId = $startNotice->getTransactionSerialNumber();
            $orderData = app(OrderModel::class)->getOrder($orderId,['user_id', 'shot_id', 'station_id']);
            if (empty($orderData)) {
                $this->socketLogCollector->collectorRunLog('发送充电开始微信通知失败：找不到订单信息', SocketLogCollector::LevelError);
                return parent::ResultCodeFailed;
            }

            // 获取用户open_id
            $usersModel = app(Users::class);
            $userData = $usersModel->where('id', $orderData['user_id'])->field(['open_id'])->find();

            if (empty($userData) || empty($userData['open_id'])) {
                $this->socketLogCollector->collectorRunLog('发送充电开始微信通知失败：找不到用户open_id', SocketLogCollector::LevelError);
                return parent::ResultCodeFailed;
            }

            // 获取充电站信息
            $stationsModel = app(Stations::class);
            $stationData = $stationsModel->where('id', $orderData['station_id'])->field(['name'])->find();
            $stationName = $stationData ? $stationData['name'] : '未知站点';

            // 构建消息参数
            $params = [
                'character_string1' => ['value' => $orderId], // 订单编号
                'thing4' => ['value' => $orderData['shot_id']], // 充电枪号
                'thing3' => ['value' => $stationName], // 充电站点
                'time2' => ['value' => date('Y-m-d H:i:s')], // 开始时间
                'thing5' => ['value' => '请在充电完成后及时移除充电枪'] // 温馨提示
            ];

            // 发送微信模板消息
            $wechat = app(WeChat::class);
            $result = $wechat->send_template_message($userData['open_id'],$orderId, $params, 'start');

            $this->socketLogCollector->collectorRunLog('发送充电开始微信通知结果：' . json_encode_cn($result));
        } catch (Throwable $e) {
            $this->socketLogCollector->collect($e);
            return parent::ResultCodeFailed;
        }
        return parent::ResultCodeSuccess;
    }

}