<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\queue;

use app\common\log\LogCollector;
use app\common\log\SocketLogCollector;
use Redis;
use RedisException;
use think\DbManager;
use Throwable;
use Workerman\Timer;

abstract class BaseQueue
{
    public const ResultCodeFailed = 0; // 失败
    public const ResultCodeSuccess = 1; // 成功
    public const ResultCodeDiscard = 2; // 丢弃

    protected Redis $redis;
    protected ?SocketLogCollector $socketLogCollector = null;

    public function __construct()
    {
        $this->redis = $this->getRedisInstance();
    }

    /**
     * 连接Redis
     *
     * @param array $config Redis配置
     * @return Redis
     * @throws RedisException
     */
    protected function connectRedis(array $config): Redis
    {
        // TODO:缺少断线重连机制和异常处理
        $redis = new Redis();
        if (!empty($config['persistent'])) {
            $redis->pconnect($config['host'], (int)$config['port'], (int)$config['timeout']);
        } else {
            $redis->connect($config['host'], (int)$config['port'], (int)$config['timeout']);
        }
        if (!empty($config['password'])) {
            $redis->auth($config['password']);
        }
        if (!empty($config['database'])) {
            $redis->select($config['database']);
        }

        return $redis;
    }

    abstract protected function getRedisInstance(): Redis;

    abstract protected function getKey(): string;

    abstract protected function consumerHandler(string $data, SocketLogCollector $socketLogCollector): int;

    /**
     * 加入队列
     *
     * @param string ...$data 加入队列的数据
     * @return int 加入后队列的长度
     * @throws RedisException
     */
    public function push(string ...$data): int
    {

        return $this->redis->lpush($this->getKey(), ...$data);
    }

    protected function listenerSql(): void
    {
        $DbManager = app(DbManager::class);
        $DbManager->listen(function ($sql, $time, $master) {
            if (str_starts_with($sql, 'CONNECT:')) {
                if (!is_null($this->socketLogCollector)) {
                    $this->socketLogCollector->collectorRunLog($sql, LogCollector::LevelSql);
                }


                return;
            }

            // 记录SQL
            if (is_bool($master)) {
                // 分布式记录当前操作的主从
                $master = $master ? 'master|' : 'slave|';
            } else {
                $master = '';
            }

            if (!is_null($this->socketLogCollector)) {
                $this->socketLogCollector->collectorRunLog($sql . ' [ ' . $master . 'RunTime:' . $time . 's ]', LogCollector::LevelSql);
            }
        });
    }


    /**
     * 消费队列的处理逻辑
     *
     * @return string
     */
    public function consumer(): string
    {
        $this->listenerSql();

        Timer::add(0.0001, function () {
            $this->socketLogCollector = new SocketLogCollector();
            try {
                $result = $this->redis->brpop([$this->getKey()], 10);
                if (count($result) === 2) {
                    if (!empty($result[1])) {
                        $this->socketLogCollector->collectorRequestLog(str_repeat('-', 40), 'request');
                        $this->socketLogCollector->collectorRequestLog(print_r($result, true), 'request');

                        $runResult = $this->consumerHandler($result[1], $this->socketLogCollector);

                        if ($runResult === self::ResultCodeFailed) {
                            $this->push($result[1]);
                        }
                    }
                } else {
                    // 检测连接是否断开
                    if (!$this->redis->ping()) {
                        // 重连
                        $this->redis = $this->getRedisInstance();
                    }
                }
            } catch (Throwable $e) {
                $this->socketLogCollector->collectorRunLog(sprintf('队列处理异常：异常文件=%s, 异常代码行数=%d, 异常描述=%s, 异常状态码=%s',
                    $e->getFile(), $e->getLine(), $e->getMessage(), $e->getCode()
                ), 'error');
                $this->socketLogCollector->collectorRunLog(print_r($e->getTrace(), true), 'error');
            }
            $this->socketLogCollector->write();
        });

        return '';
    }
}