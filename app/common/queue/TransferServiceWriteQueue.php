<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\queue;

use app\common\lib\transfer_service\Context;
use app\common\log\SocketLogCollector;
use Redis;
use RedisException;

class TransferServiceWriteQueue extends BaseQueue
{
    /**
     * 获取Redis实例
     *
     * @return Redis
     * @throws RedisException
     */
    protected function getRedisInstance(): Redis
    {
        return $this->connectRedis(config('my.redis2'));
    }

    protected function getKey(): string
    {
        return 'admin:queue:transfer_service_write';
    }

    protected ?Context $context;

    public function setContext(Context $context): void
    {
        $this->context = $context;
    }

    /**
     * 消费队列的处理逻辑
     *
     * @return string
     */
    public function consumer(): string
    {
        $socketLogCollector = new SocketLogCollector();
        try {
            $result = $this->redis->rpop($this->getKey());
            if (!empty($result)) {
                $socketLogCollector->collectorRequestLog(str_repeat('-', 40), 'request');
                $socketLogCollector->collectorRequestLog(print_r($result, true), 'request');
                $runResult = $this->consumerHandler($result, $socketLogCollector);

                if ($runResult === self::ResultCodeFailed) {
                    $this->push($result);
                }
            }
        } catch (\Throwable $e) {
            $socketLogCollector->collectorRunLog(sprintf('队列处理异常：异常文件=%s, 异常代码行数=%d, 异常描述=%s, 异常状态码=%s',
                $e->getFile(), $e->getLine(), $e->getMessage(), $e->getCode()
            ), 'error');
            $socketLogCollector->collectorRunLog(print_r($e->getTrace(), true), 'error');
        }

        $socketLogCollector->write();

        return '';
    }

    protected function consumerHandler(string $data, SocketLogCollector $socketLogCollector): int
    {
        $this->context->connection->send($data);

        return self::ResultCodeSuccess;
    }

}