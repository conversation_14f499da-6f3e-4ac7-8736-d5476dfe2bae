<?php

namespace app\common\context;

use app\common\log\LogCollector;
use Predis\Client as Predis;

class Order
{
    protected function getOrderStatusKey(string $order_id): string
    {
        return sprintf('运营平台远程控制启机-状态-%s', $order_id);
    }

    public function recordOrderStatus(string $order_id, string $status): bool
    {
        $key = $this->getOrderStatusKey($order_id);

        $redis5 = new Predis(config('my.redis5'));
        $result = $redis5->set($key, $status, 'EX', 300);
        return $result->getPayload() === 'OK';
    }

    public function getOrderStatus(string $order_id): ?string
    {
        $key = $this->getOrderStatusKey($order_id);
        $redis5 = new Predis(config('my.redis5'));
        return $redis5->get($key);
    }

    public function deleteOrderStatus(string $order_id): bool
    {
        $key = $this->getOrderStatusKey($order_id);
        $redis5 = new Predis(config('my.redis5'));
        return $redis5->del($key) > 0;
    }


    public function saveOrderIdToContextMap(string $order_id, array $context): bool
    {
        $cache_key = sprintf("充电桩服务订单_%s", $order_id);
        $result = cache($cache_key, $context, 864000);
        LogCollector::collectorRunLog(sprintf('cache - result - %s', print_r($result, true)));
        return $result;
    }

    public function getOrderIdToContextMap(string $order_id): ?array
    {
        $cache_key = sprintf("充电桩服务订单_%s", $order_id);
        return cache($cache_key);
    }

    public function saveSequenceNumberToContextMap(int $sequenceNumber, array $context): bool
    {
        $cache_key = sprintf('充电桩服务_%s', $sequenceNumber);
        $result = cache($cache_key, $context, 864000);
        LogCollector::collectorRunLog(sprintf('cache - result2 - %s', print_r($result, true)));
        return $result;
    }

    public function getSequenceNumberToContextMap(int $sequenceNumber): ?array
    {
        $cache_key = sprintf('充电桩服务_%s', $sequenceNumber);
        return cache($cache_key);
    }



}