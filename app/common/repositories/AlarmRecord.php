<?php
/** @noinspection PhpUnused */

namespace app\common\repositories;


use app\common\model\AlarmRecord as AlarmRecordModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;

class AlarmRecord
{

    // 设备类型
    public const DeviceTypeShots = 1; // 充电枪
    public const DeviceTypePiles = 2; // 充电桩
    public const DeviceTypeCentralizedController = 3; // 能源路由器

    public const DeviceTypeToText = [
        self::DeviceTypeShots => '充电枪',
        self::DeviceTypePiles => '充电桩',
        self::DeviceTypeCentralizedController => '能源路由器'
    ];

    // 告警类型
    /**
     * @var AlarmRecord::TypeCRS 上传实时监测数据
     */
    public const TypeCRS = 1;
    /**
     * @var AlarmRecord::TypeCTR 交易记录
     */
    public const TypeCTR = 2;
    /**
     * @var AlarmRecord::TypeTCO 充电服务离线告警
     */
    public const TypeTCO = 3;
    /**
     * @var AlarmRecord::TypeEMP 读取电表功率告警
     */
    public const TypeEMP = 4;
    /**
     * @var AlarmRecord::TypePON 充电桩离线告警
     */
    public const TypePON = 5;
    /**
     * @var AlarmRecord::TypeMSR 能源路由器资源告警
     */
    public const TypeMSR = 6;

    public const TypeToText = [
        self::TypeCRS => '实时监测数据告警',
        self::TypeCTR => '交易记录告警',
        self::TypeTCO => '充电服务离线告警',
        self::TypeEMP => '读取电表功率告警',
        self::TypePON => '充电桩离线告警',
        self::TypeMSR => '能源路由器资源告警',
    ];


    // 上传实时监测数据
    public const CodeCRS001 = 'CRS001'; // 急停按钮动作故障
    public const CodeCRS002 = 'CRS002'; // 无可用整流模块
    /**
     * @var AlarmRecord::CodeCRS003 出风口温度过高
     */
    public const CodeCRS003 = 'CRS003'; // 出风口温度过高
    public const CodeCRS004 = 'CRS004'; // 交流防雷故障
    public const CodeCRS005 = 'CRS005'; // 交直流模块 DC20 通信中断
    public const CodeCRS006 = 'CRS006'; // 绝缘检测模块 FC08 通信中断
    public const CodeCRS007 = 'CRS007'; // 电度表通信中断
    public const CodeCRS008 = 'CRS008'; // 读卡器通信中断
    public const CodeCRS009 = 'CRS009'; // RC10 通信中断
    public const CodeCRS00A = 'CRS00A'; // 风扇调速板故障
    public const CodeCRS00B = 'CRS00B'; // 直流熔断器故障
    public const CodeCRS00C = 'CRS00C'; // 高压接触器故障
    public const CodeCRS00D = 'CRS00D'; // 门打开

    // 交易记录
    public const CodeCTR04A = 'CTR04A'; // 充电启动失败
    public const CodeCTR04B = 'CTR04B'; // 充电启动失败，控制导引断开
    public const CodeCTR04C = 'CTR04C'; // 充电启动失败，断路器跳位
    public const CodeCTR04D = 'CTR04D'; // 充电启动失败，电表通信中断
    public const CodeCTR04E = 'CTR04E'; // 充电启动失败，余额不足
    public const CodeCTR04F = 'CTR04F'; // 充电启动失败，充电模块故障
    public const CodeCTR050 = 'CTR050'; // 充电启动失败，急停开入
    public const CodeCTR051 = 'CTR051'; // 充电启动失败，防雷器异常
    public const CodeCTR052 = 'CTR052'; // 充电启动失败，BMS 未就绪
    public const CodeCTR053 = 'CTR053'; // 充电启动失败，温度异常
    public const CodeCTR054 = 'CTR054'; // 充电启动失败，电池反接故障
    public const CodeCTR055 = 'CTR055'; // 充电启动失败，电子锁异常
    public const CodeCTR056 = 'CTR056'; // 充电启动失败，合闸失败
    public const CodeCTR057 = 'CTR057'; // 充电启动失败，绝缘异常
    public const CodeCTR058 = 'CTR058'; // 预留
    public const CodeCTR059 = 'CTR059'; // 充电启动失败，接收 BMS 握手报文 BHM 超时
    public const CodeCTR05A = 'CTR05A'; // 充电启动失败，接收 BMS 和车辆的辨识报文超时 BRM
    public const CodeCTR05B = 'CTR05B'; // 充电启动失败，接收电池充电参数报文超时 BCP
    public const CodeCTR05C = 'CTR05C'; // 充电启动失败，接收 BMS 完成充电准备报文超时 BRO AA
    public const CodeCTR05D = 'CTR05D'; // 充电启动失败，接收电池充电总状态报文超时 BCS
    public const CodeCTR05E = 'CTR05E'; // 充电启动失败，接收电池充电要求报文超时 BCL
    public const CodeCTR05F = 'CTR05F'; // 充电启动失败，接收电池状态信息报文超时 BSM
    public const CodeCTR060 = 'CTR060'; // 充电启动失败，GB2015 电池在 BHM 阶段有电压不允许充电
    public const CodeCTR061 = 'CTR061'; // 充电启动失败，GB2015 辨识阶段在 BRO_AA 时候电池实际电压与 BCP 报文电池电压差距大于 5%
    public const CodeCTR062 = 'CTR062'; // 充电启动失败，B2015 充电机在预充电阶段从 BRO_AA 变成BRO_00 状态
    public const CodeCTR063 = 'CTR063'; // 充电启动失败，接收主机配置报文超时
    public const CodeCTR064 = 'CTR064'; // 充电启动失败，充电机未准备就绪,我们没有回 CRO AA，对应老国标
    public const CodeCTR065 = 'CTR065'; // 充电启动失败其他原因预留
    public const CodeCTR066 = 'CTR066'; // 充电启动失败其他原因预留
    public const CodeCTR067 = 'CTR067'; // 充电启动失败其他原因预留
    public const CodeCTR068 = 'CTR068'; // 充电启动失败其他原因预留
    public const CodeCTR069 = 'CTR069'; // 充电启动失败其他原因预留
    public const CodeCTR06A = 'CTR06A'; // 充电异常中止，系统闭锁
    public const CodeCTR06B = 'CTR06B'; // 充电异常中止，导引断开
    public const CodeCTR06C = 'CTR06C'; // 充电异常中止，断路器跳位
    public const CodeCTR06D = 'CTR06D'; // 充电异常中止，电表通信中断
    public const CodeCTR06E = 'CTR06E'; // 充电异常中止，余额不足
    public const CodeCTR06F = 'CTR06F'; // 充电异常中止，交流保护动作
    public const CodeCTR070 = 'CTR070'; // 充电异常中止，直流保护动作
    public const CodeCTR071 = 'CTR071'; // 充电异常中止，充电模块故障
    public const CodeCTR072 = 'CTR072'; // 充电异常中止，急停开入
    public const CodeCTR073 = 'CTR073'; // 充电异常中止，防雷器异常
    public const CodeCTR074 = 'CTR074'; // 充电异常中止，温度异常

    public const CodeCTR075 = 'CTR075'; // 充电异常中止，输出异常
    public const CodeCTR076 = 'CTR076'; // 充电异常中止，充电无流
    public const CodeCTR077 = 'CTR077'; // 充电异常中止，电子锁异常

    public const CodeCTR078 = 'CTR078'; // 充电异常中止预留
    public const CodeCTR079 = 'CTR079'; // 充电异常中止，总充电电压异常
    public const CodeCTR07A = 'CTR07A'; // 充电异常中止，总充电电流异常
    public const CodeCTR07B = 'CTR07B'; // 充电异常中止，单体充电电压异常
    public const CodeCTR07C = 'CTR07C'; // 充电异常中止，电池组过温
    public const CodeCTR07D = 'CTR07D'; // 充电异常中止，最高单体充电电压异常
    public const CodeCTR07E = 'CTR07E'; // 充电异常中止，最高电池组过温
    public const CodeCTR07F = 'CTR07F'; // 充电异常中止，BMV 单体充电电压异常
    public const CodeCTR080 = 'CTR080'; // 充电异常中止，BMT 电池组过温
    public const CodeCTR081 = 'CTR081'; // 充电异常中止，电池状态异常停止充电
    public const CodeCTR082 = 'CTR082'; // 充电异常中止，车辆发报文禁止充电
    public const CodeCTR083 = 'CTR083'; // 充电异常中止，充电桩断电
    public const CodeCTR084 = 'CTR084'; // 充电异常中止，接收电池充电总状态报文超时
    public const CodeCTR085 = 'CTR085'; // 充电异常中止，接收电池充电要求报文超时
    public const CodeCTR086 = 'CTR086'; // 充电异常中止，接收电池状态信息报文超时
    public const CodeCTR087 = 'CTR087'; // 充电异常中止，接收 BMS 中止充电报文超时
    public const CodeCTR088 = 'CTR088'; // 充电异常中止，接收 BMS 充电统计报文超时
    public const CodeCTR089 = 'CTR089'; // 充电异常中止，接收对侧 CCS 报文超时
    public const CodeCTR08A = 'CTR08A'; // 充电异常中止其他原因预留
    public const CodeCTR08B = 'CTR08B'; // 充电异常中止其他原因预留
    public const CodeCTR08C = 'CTR08C'; // 充电异常中止其他原因预留
    public const CodeCTR08D = 'CTR08D'; // 充电异常中止其他原因预留
    public const CodeCTR08E = 'CTR08E'; // 充电异常中止其他原因预留
    public const CodeCTR08F = 'CTR08F'; // 充电异常中止其他原因预留
    public const CodeCTR090 = 'CTR090'; // 未知原因停止

    public const CodeCTR100 = 'CTR100'; // 调度算法结束充电失败
    public const CodeCTR101 = 'CTR101'; // 下调功率失败

    // 充电服务离线告警
    public const CodeTCO001 = 'TCO001'; // 调度算法离线
    public const CodeTCO002 = 'TCO002'; // 驱动服务离线
    public const CodeTCO003 = 'TCO003';  // 充电管理离线

    // 读取电表功率告警
    public const CodeEMP001 = 'EMP001'; // 电表功率读取异常

    // 充电桩离线告警
    public const CodePON001 = 'PON001'; // 充电桩离线

    // 能源路由器资源告警
    public const CodeMSR001 = 'MSR001'; // 内存资源告警
    public const CodeMSR002 = 'MSR002'; // 磁盘资源告警
    public const CodeMSR003 = 'MSR003'; // CPU资源告警


    // 上传实时监测数据
    public const CRSToCodeMap = [
        0b0000000000000001 => self::CodeCRS001,
        0b0000000000000010 => self::CodeCRS002,
        0b0000000000000100 => self::CodeCRS003,
        0b0000000000001000 => self::CodeCRS004,
        0b0000000000010000 => self::CodeCRS005,
        0b0000000000100000 => self::CodeCRS006,
        0b0000000001000000 => self::CodeCRS007,
        0b0000000010000000 => self::CodeCRS008,
        0b0000000100000000 => self::CodeCRS009,
        0b0000001000000000 => self::CodeCRS00A,
        0b0000010000000000 => self::CodeCRS00B,
        0b0000100000000000 => self::CodeCRS00C,
        0b0001000000000000 => self::CodeCRS00D,
    ];

    // 交易记录
    public const CTRToCodeMap = [
        0x4A => self::CodeCTR04A,
        0x4B => self::CodeCTR04B,
        0x4C => self::CodeCTR04C,
        0x4D => self::CodeCTR04D,
        0x4E => self::CodeCTR04E,
        0x4F => self::CodeCTR04F,
        0x50 => self::CodeCTR050,
        0x51 => self::CodeCTR051,
        0x52 => self::CodeCTR052,
        0x53 => self::CodeCTR053,
        0x54 => self::CodeCTR054,
        0x55 => self::CodeCTR055,
        0x56 => self::CodeCTR056,
        0x57 => self::CodeCTR057,
        0x58 => self::CodeCTR058,
        0x59 => self::CodeCTR059,
        0x5A => self::CodeCTR05A,
        0x5B => self::CodeCTR05B,
        0x5C => self::CodeCTR05C,
        0x5D => self::CodeCTR05D,
        0x5E => self::CodeCTR05E,
        0x5F => self::CodeCTR05F,
        0x60 => self::CodeCTR060,

        0x61 => self::CodeCTR061,
        0x62 => self::CodeCTR062,
        0x63 => self::CodeCTR063,
        0x64 => self::CodeCTR064,
        0x65 => self::CodeCTR065,
        0x66 => self::CodeCTR066,
        0x67 => self::CodeCTR067,
        0x68 => self::CodeCTR068,
        0x69 => self::CodeCTR069,
        0x6A => self::CodeCTR06A,
        0x6B => self::CodeCTR06B,
        0x6C => self::CodeCTR06C,
        0x6D => self::CodeCTR06D,
        0x6E => self::CodeCTR06E,
        0x6F => self::CodeCTR06F,

        0x70 => self::CodeCTR070,
        0x71 => self::CodeCTR071,
        0x72 => self::CodeCTR072,
        0x73 => self::CodeCTR073,
        0x74 => self::CodeCTR074,
        0x75 => self::CodeCTR075,
        0x76 => self::CodeCTR076,
        0x77 => self::CodeCTR077,
        0x78 => self::CodeCTR078,
        0x79 => self::CodeCTR079,
        0x7A => self::CodeCTR07A,
        0x7B => self::CodeCTR07B,
        0x7C => self::CodeCTR07C,
        0x7D => self::CodeCTR07D,
        0x7E => self::CodeCTR07E,
        0x7F => self::CodeCTR07F,

        0x80 => self::CodeCTR080,
        0x81 => self::CodeCTR081,
        0x82 => self::CodeCTR082,
        0x83 => self::CodeCTR083,
        0x84 => self::CodeCTR084,
        0x85 => self::CodeCTR085,
        0x86 => self::CodeCTR086,
        0x87 => self::CodeCTR087,
        0x88 => self::CodeCTR088,
        0x89 => self::CodeCTR089,
        0x8A => self::CodeCTR08A,
        0x8B => self::CodeCTR08B,
        0x8C => self::CodeCTR08C,
        0x8D => self::CodeCTR08D,
        0x8E => self::CodeCTR08E,
        0x8F => self::CodeCTR08F,

        0x90 => self::CodeCTR090,

        0x100 => self::CodeCTR100,
        0x101 => self::CodeCTR101,
    ];

    // 充电服务离线告警
    public const TCOToCodeMap = [
        0x200 => self::CodeTCO001,
        0x201 => self::CodeTCO002,
        0x202 => self::CodeTCO003,
    ];

    // 读取电表功率告警
    public const EMPToCodeMap = [
        0x210 => self::CodeEMP001
    ];

    // 充电桩离线告警
    public const PONToCodeMap = [
        0x220 => self::CodePON001
    ];

    // 能源路由器资源告警
    public const MSRToCodeMap = [
        0x230 => self::CodeMSR001,
        0x231 => self::CodeMSR002,
        0x232 => self::CodeMSR003,
    ];

    public const CodeToMessageMap = [
        // 上传实时监测数据
        self::CodeCRS001 => '急停按钮动作故障',
        self::CodeCRS002 => '无可用整流模块',
        self::CodeCRS003 => '出风口温度过高',
        self::CodeCRS004 => '交流防雷故障',
        self::CodeCRS005 => '交直流模块 DC20 通信中断',
        self::CodeCRS006 => '绝缘检测模块 FC08 通信中断',
        self::CodeCRS007 => '电度表通信中断',
        self::CodeCRS008 => '读卡器通信中断',
        self::CodeCRS009 => 'RC10 通信中断',
        self::CodeCRS00A => '风扇调速板故障',
        self::CodeCRS00B => '直流熔断器故障',
        self::CodeCRS00C => '高压接触器故障',
        self::CodeCRS00D => '门打开',
        // 交易记录
        self::CodeCTR04A => '充电启动失败',
        self::CodeCTR04B => '充电启动失败，控制导引断开',
        self::CodeCTR04C => '充电启动失败，断路器跳位',
        self::CodeCTR04D => '充电启动失败，电表通信中断',
        self::CodeCTR04E => '充电启动失败，余额不足',
        self::CodeCTR04F => '充电启动失败，充电模块故障',
        self::CodeCTR050 => '充电启动失败，急停开入',
        self::CodeCTR051 => '充电启动失败，急停开入',
        self::CodeCTR052 => '充电启动失败，BMS 未就绪',
        self::CodeCTR053 => '充电启动失败，温度异常',
        self::CodeCTR054 => '充电启动失败，电池反接故障',
        self::CodeCTR055 => '充电启动失败，电子锁异常',
        self::CodeCTR056 => '充电启动失败，合闸失败',
        self::CodeCTR057 => '充电启动失败，绝缘异常',
        self::CodeCTR058 => '预留',
        self::CodeCTR059 => '充电启动失败，接收 BMS 握手报文 BHM 超时',
        self::CodeCTR05A => '充电启动失败，接收 BMS 和车辆的辨识报文超时 BRM',
        self::CodeCTR05B => '充电启动失败，接收电池充电参数报文超时 BCP',
        self::CodeCTR05C => '充电启动失败，接收 BMS 完成充电准备报文超时 BRO AA',
        self::CodeCTR05D => '充电启动失败，接收电池充电总状态报文超时 BCS',
        self::CodeCTR05E => '充电启动失败，接收电池充电要求报文超时 BCL',
        self::CodeCTR05F => '充电启动失败，接收电池状态信息报文超时 BSM',
        self::CodeCTR060 => '充电启动失败，GB2015 电池在 BHM 阶段有电压不允许充电',
        self::CodeCTR061 => '充电启动失败，GB2015 辨识阶段在 BRO_AA 时候电池实际电压与 BCP 报文电池电压差距大于 5%',
        self::CodeCTR062 => '充电启动失败，B2015 充电机在预充电阶段从 BRO_AA 变成BRO_00 状态',
        self::CodeCTR063 => '充电启动失败，接收主机配置报文超时',
        self::CodeCTR064 => '充电启动失败，充电机未准备就绪,我们没有回 CRO AA，对应老国标',
        self::CodeCTR065 => '充电启动失败其他原因预留',
        self::CodeCTR066 => '充电启动失败其他原因预留',
        self::CodeCTR067 => '充电启动失败其他原因预留',
        self::CodeCTR068 => '充电启动失败其他原因预留',
        self::CodeCTR069 => '充电启动失败其他原因预留',
        self::CodeCTR06A => '充电异常中止，系统闭锁',
        self::CodeCTR06B => '充电异常中止，导引断开',
        self::CodeCTR06C => '充电异常中止，断路器跳位',
        self::CodeCTR06D => '充电异常中止，电表通信中断',
        self::CodeCTR06E => '充电异常中止，余额不足',
        self::CodeCTR06F => '充电异常中止，交流保护动作',
        self::CodeCTR070 => '充电异常中止，直流保护动作',
        self::CodeCTR071 => '充电异常中止，充电模块故障',
        self::CodeCTR072 => '充电异常中止，急停开入',
        self::CodeCTR073 => '充电异常中止，防雷器异常',
        self::CodeCTR074 => '充电异常中止，温度异常',
        self::CodeCTR075 => '充电异常中止，输出异常',
        self::CodeCTR076 => '充电异常中止，充电无流',
        self::CodeCTR077 => '充电异常中止，电子锁异常',
        self::CodeCTR078 => '充电异常中止预留',
        self::CodeCTR079 => '充电异常中止，总充电电压异常',
        self::CodeCTR07A => '充电异常中止，总充电电流异常',
        self::CodeCTR07B => '充电异常中止，单体充电电压异常',
        self::CodeCTR07C => '充电异常中止，电池组过温',
        self::CodeCTR07D => '充电异常中止，最高单体充电电压异常',
        self::CodeCTR07E => '充电异常中止，最高电池组过温',
        self::CodeCTR07F => '充电异常中止，BMV 单体充电电压异常',


        self::CodeCTR080 => '充电异常中止，BMT 电池组过温',
        self::CodeCTR081 => '充电异常中止，电池状态异常停止充电',
        self::CodeCTR082 => '充电异常中止，车辆发报文禁止充电',
        self::CodeCTR083 => '充电异常中止，充电桩断电',
        self::CodeCTR084 => '充电异常中止，接收电池充电总状态报文超时',
        self::CodeCTR085 => '充电异常中止，接收电池充电要求报文超时',
        self::CodeCTR086 => '充电异常中止，接收电池状态信息报文超时',
        self::CodeCTR087 => '充电异常中止，接收 BMS 中止充电报文超时',
        self::CodeCTR088 => '充电异常中止，接收 BMS 充电统计报文超时',
        self::CodeCTR089 => '充电异常中止，接收对侧 CCS 报文超时',
        self::CodeCTR08A => '充电异常中止其他原因预留',
        self::CodeCTR08B => '充电异常中止其他原因预留',
        self::CodeCTR08C => '充电异常中止其他原因预留',
        self::CodeCTR08D => '充电异常中止其他原因预留',
        self::CodeCTR08E => '充电异常中止其他原因预留',
        self::CodeCTR08F => '充电异常中止其他原因预留',
        self::CodeCTR090 => '未知原因停止',


        self::CodeCTR100 => '调度算法结束充电失败',
        self::CodeCTR101 => '下调功率失败',
        // 充电服务离线告警
        self::CodeTCO001 => '调度算法离线',
        self::CodeTCO002 => '驱动服务离线',
        self::CodeTCO003 => '充电管理离线',
        // 读取电表功率告警
        self::CodeEMP001 => '电表功率读取异常',
        // 充电桩离线告警
        self::CodePON001 => '充电桩离线',
        // 能源路由器资源告警
        self::CodeMSR001 => '内存资源告警',
        self::CodeMSR002 => '磁盘资源告警',
        self::CodeMSR003 => 'CPU资源告警'
    ];


    public const StatusIsRecoveryNot = 1; // 未恢复
    public const StatusIsRecoveryYes = 2; // 已恢复

    public const StatusMessage = [
        self::StatusIsRecoveryNot => '未恢复',
        self::StatusIsRecoveryYes => '已恢复'
    ];


    public static function getCRSCode(int $hardwareFailureIndex): ?string
    {
        $hardwareFailureCode = 1 << ($hardwareFailureIndex - 1);
        if (isset(self::CRSToCodeMap[$hardwareFailureCode]) === false) return null;
        return self::CRSToCodeMap[$hardwareFailureCode];
    }


    /**
     * 验证告警记录是否存在未恢复的
     * @param int $device_type
     * @param string $device_id
     * @param int $type
     * @param string $code
     * @return bool
     */
    public static function verifyAlarmRecordIsExistsNotRecovery(int $device_type, string $device_id, int $type, string $code): bool
    {
        $where = [
            ['device_type', '=', $device_type],
            ['device_id', '=', $device_id],
            ['type', '=', $type],
            ['code', '=', $code],
            ['is_recovery', '=', self::StatusIsRecoveryNot]
        ];
        return (new AlarmRecordModel())->where($where)->count() > 0;
    }


    /**
     * 获取告警记录
     *
     * @param int $id
     * @return array|null
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public static function getAlarmRecord(int $id): ?array
    {
        $data = (new AlarmRecordModel())->where('id', '=', $id)->find();
        if (empty($data)) return null;
        return $data->toArray();
    }

    /**
     * 查询未恢复的告警记录
     *
     * @param int $device_type
     * @param string $device_id
     * @param int $type
     * @param string $code
     * @return array|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function queryNotRecoveryAlarmRecord(int $device_type, string $device_id, int $type, string $code): ?array
    {
        $where = [
            ['device_type', '=', $device_type],
            ['device_id', '=', $device_id],
            ['type', '=', $type],
            ['code', '=', $code],
            ['is_recovery', '=', self::StatusIsRecoveryNot]
        ];
        $data = (new AlarmRecordModel())->where($where)->find();
        if (empty($data)) return null;
        return $data->toArray();
    }

    /**
     * 查询设备未恢复的告警记录数量
     *
     * @param int $device_type
     * @param string $device_id
     * @param int $type
     * @param string|null $exclude_code
     * @return int
     */
    public static function queryDeviceNotRecoveryAlarmRecordCount(int $device_type, string $device_id, int $type, ?string $exclude_code = null): int
    {
        $where = [
            ['device_type', '=', $device_type],
            ['device_id', '=', $device_id],
            ['type', '=', $type],
            ['is_recovery', '=', self::StatusIsRecoveryNot]
        ];
        if (!is_null($exclude_code)) {
            $where[] = ['code', '<>', $exclude_code];
        }
        return (new AlarmRecordModel())->where($where)->count();
    }

    /**
     * 查询设备未恢复的告警记录
     *
     * @param int $device_type
     * @param string $device_id
     * @param int $type
     * @param string|null $exclude_code
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function queryDeviceNotRecoveryAlarmRecords(int $device_type, string $device_id, int $type, ?string $exclude_code = null): array
    {
        $where = [
            ['device_type', '=', $device_type],
            ['device_id', '=', $device_id],
            ['type', '=', $type],
            ['is_recovery', '=', self::StatusIsRecoveryNot]
        ];
        if (!is_null($exclude_code)) {
            $where[] = ['code', '<>', $exclude_code];
        }
        return (new AlarmRecordModel())->where($where)->select()->toArray();
    }

    /**
     * 批量创建告警记录
     *
     * @param int $corp_id
     * @param int $station_id
     * @param int $device_type
     * @param string $device_id
     * @param int $type
     * @param array $codes
     * @return int
     */
    public static function batchCreateAlarmRecord(
        int $corp_id, int $station_id, int $device_type, string $device_id, int $type, array $codes
    ): int
    {
        $inserts = [];
        foreach ($codes as $code) {
            $inserts[] = [
                'corp_id' => $corp_id,
                'station_id' => $station_id,
                'device_type' => $device_type,
                'device_id' => $device_id,
                'type' => $type,
                'code' => $code,
                'alarm_time' => time(),
                'is_recovery' => self::StatusIsRecoveryNot,
                'recovery_time' => 0
            ];
        }


        return (new AlarmRecordModel())->insertAll($inserts);
    }

    /**
     * 创建告警记录
     *
     * @param int $corp_id
     * @param int $station_id
     * @param int $device_type
     * @param string $device_id
     * @param int $type
     * @param string $code
     * @return int
     */
    public static function createAlarmRecord(
        int $corp_id, int $station_id, int $device_type, string $device_id, int $type, string $code
    ): int
    {
        return (new AlarmRecordModel())->insertGetId([
            'corp_id' => $corp_id,
            'station_id' => $station_id,
            'device_type' => $device_type,
            'device_id' => $device_id,
            'type' => $type,
            'code' => $code,
            'alarm_time' => time(),
            'is_recovery' => self::StatusIsRecoveryNot,
            'recovery_time' => 0,
        ]);
    }

    public static function batchUpdateRecoverTime(int $device_type, string $device_id, int $type, ?string $exclude_code = null): int
    {
        $where = [
            ['device_type', '=', $device_type],
            ['device_id', '=', $device_id],
            ['type', '=', $type],
            ['is_recovery', '=', self::StatusIsRecoveryNot]
        ];
        if (!is_null($exclude_code)) {
            $where[] = ['code', '<>', $exclude_code];
        }
        $model = new AlarmRecordModel();
        $model->where($where)->update([
            'recovery_time' => time(),
            'is_recovery' => self::StatusIsRecoveryYes
        ]);
        return $model->getNumRows();
    }

    public static function useAlarmCodeBatchUpdateRecoverTime(int $device_type, string $device_id, int $alarm_type, array $alarm_codes): int
    {
        $where = [
            ['device_type', '=', $device_type],
            ['device_id', '=', $device_id],
            ['type', '=', $alarm_type],
            ['is_recovery', '=', self::StatusIsRecoveryNot],
            ['code', 'in', $alarm_codes],
        ];
        $model = new AlarmRecordModel();
        $model->where($where)->update([
            'recovery_time' => time(),
            'is_recovery' => self::StatusIsRecoveryYes
        ]);
        return $model->getNumRows();
    }

    /**
     * 记录告警恢复时间
     *
     * @param int $id
     * @return bool
     */
    public static function recordAlarmRecoveryTime(int $id): bool
    {
        return (new AlarmRecordModel())->where('id', '=', $id)->update([
                'recovery_time' => time(),
                'is_recovery' => self::StatusIsRecoveryYes
            ]) > 0;
    }

    /**
     * 告警记录列表
     *
     * @param array $params 参数
     * @param int $page [default=1] 页码
     * @param int $limit [default=10] 每页条数
     * @return array
     * @throws DbException
     */
    public static function getListData(array $params, int $page = 1, int $limit = 10): array
    {
        // 整理筛选条件
        $where = [];
        if (!empty($params['is_recovery'])) {
            $where[] = ['ar.is_recovery', '=', $params['is_recovery']];
        }
        if (!empty($params['alarm_start_time'])) {
            $where[] = ['ar.alarm_time', '>=', strtotime($params['alarm_start_time'])];
        }
        if (!empty($params['alarm_end_time'])) {
            $where[] = ['ar.alarm_time', '<=', strtotime($params['alarm_end_time'])];
        }
        if (!empty($params['corp_id'])) {
            $where[] = ['ar.corp_id', '=', $params['corp_id']];
        }
        if (!empty($params['station_id'])) {
            if (is_array($params['station_id'])) {
                $where[] = ['ar.station_id', 'in', $params['station_id']];
            } else {
                $where[] = ['ar.station_id', '=', $params['station_id']];
            }
        }
        if (!empty($params['device_type'])) {
            $where[] = ['ar.device_type', '=', $params['device_type']];
        }
        if (!empty($params['device_id'])) {
            $where[] = ['ar.device_id', 'like', '%' . sprintf('%s', $params['device_id']) . '%'];
        }
        if (!empty($params['type'])) {
            $where[] = ['ar.type', '=', $params['type']];
        }
        if (!empty($params['code'])) {
            $where[] = ['ar.code', 'like', '%' . sprintf('%s', $params['code']) . '%'];
        }

        /**
         * @var Query $query
         */
        $query = new AlarmRecordModel();

        $fields = [
            'ar.id', 'c.name as corp_name', 's.name as station_name',
            'ar.device_type', 'ar.device_id', 'ar.type', 'ar.code',
            'ar.alarm_time', 'ar.recovery_time', 'ar.is_recovery'
        ];

        return $query->alias('ar')
            ->leftJoin('corp c', 'c.id = ar.corp_id')
            ->leftJoin('stations s', 's.id = ar.station_id')
            ->field($fields)
            ->where($where)
            ->order('ar.alarm_time', 'DESC')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ])->toArray();
    }

    /**
     * 告警记录数量
     *
     * @param int $corp_id [default=0] 运营商ID 为0时表示查询所有运营商的
     * @return int
     */
    public static function alarmRecordCount(int $corp_id): int
    {
        $where = [];
        $where[] = ['is_recovery', '=', self::StatusIsRecoveryNot];
        if (!empty($corp_id)) {
            $where[] = ['corp_id', '=', $corp_id];
        }
        $query = new AlarmRecordModel();
        return $query->where($where)->count('id');
    }

    /**
     * 按场站分组查询未恢复的告警数量
     *
     * @return array
     */
    public static function getGroupByStationNotRecoveryAlarmCount(): array
    {
        /**
         * @var Query $model
         */
        $model = new AlarmRecordModel();
        return $model->alias('ar')
            ->leftJoin('corp c', 'c.id = ar.corp_id')
            ->leftJoin('stations s', 's.id = ar.station_id')
            ->group('station_id')
            ->where('is_recovery', '=', self::StatusIsRecoveryNot)
            ->column([
                'count(*) as count',
                'c.name as corp_name',
                's.name as station_name'
            ], 'station_id');
    }

    /**
     * 查询设备未恢复的告警记录
     *
     * @param int $station_id
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function queryStationNotRecoveryAlarmRecords(int $station_id): array
    {
        $where = [
            ['is_recovery', '=', self::StatusIsRecoveryNot],
            ['station_id', '=', $station_id],
        ];
        $field = [
            'id', 'device_type', 'device_id',
            'type', 'code', 'alarm_time'
        ];
        /**
         * @var Query $AlarmRecordModel
         */
        $AlarmRecordModel = new AlarmRecordModel();
        return $AlarmRecordModel
            ->where($where)
            ->field($field)
            ->select()
            ->toArray();
    }
}