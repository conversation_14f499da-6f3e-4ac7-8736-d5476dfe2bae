<?php
/** @noinspection PhpUnused */

namespace app\common\repositories;


use app\common\model\WorkOrder as WorkOrderModel;

class WorkOrder
{
    public static function getActiveAlarmRelationWorkOrderIds(array $alarm_record_ids): array
    {
        $where = [
            ['source', '=', WorkOrderModel::SourceAlarmGeneration],
            ['create_user_id', 'in', $alarm_record_ids]
        ];
        return (new WorkOrderModel())->where($where)->column('id');
    }
}