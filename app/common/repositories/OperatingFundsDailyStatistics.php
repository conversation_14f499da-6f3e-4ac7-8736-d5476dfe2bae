<?php

namespace app\common\repositories;

// 全站运营资金每日统计表
use app\common\model\Order;
use app\common\model\PayOrder;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use app\common\model\OperatingFundsDailyStatistics as DailyStatisticsModel;

class OperatingFundsDailyStatistics
{
    /**
     * @param string $start_time 开始时间
     * @param string $end_time 结束时间
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function get_time_range_data(string $start_time, string $end_time): array
    {
        $start_time = date('Y-m-d 00:00:00', strtotime($start_time));
        $end_time = date('Y-m-d 00:00:00', strtotime($end_time));

        $data = (new DailyStatisticsModel())->get_time_range_data($start_time, $end_time);

        // 如果时间范围包含今天
        $current_time = date('Y-m-d');
        if (strtotime($start_time) <= strtotime($current_time) && strtotime($end_time) >= strtotime($current_time)) {
            $payOrderStatistics = (new PayOrder())->dailyStatistics($current_time);
            $orderStatistics = (new Order())->dailyStatistics($current_time);

            array_unshift($data, [
                'recharge_money' => $payOrderStatistics['money'],
                'recharge_count' => $payOrderStatistics['count'],
                'charging_money' => $orderStatistics['money'],
                'charging_count' => $orderStatistics['count'],
                'date' => $current_time,
            ]);
        }

        return $data;
    }

    /**
     * @param string $start_time 开始时间
     * @param string $end_time 结束时间
     * @param int $page
     * @param int $limit
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function get_list(string $start_time, string $end_time, int $page, int $limit): array
    {
        $start_time = date('Y-m-d 00:00:00', strtotime($start_time));
        $end_time = date('Y-m-d 00:00:00', strtotime($end_time));

        $data = (new DailyStatisticsModel())->get_list($start_time, $end_time, $page, $limit);

        // 如果时间范围包含今天
        $current_time = date('Y-m-d');
        if (strtotime($start_time) <= strtotime($current_time) && strtotime($end_time) >= strtotime($current_time)) {
            $payOrderStatistics = (new PayOrder())->dailyStatistics($current_time);
            $orderStatistics = (new Order())->dailyStatistics($current_time);

            array_unshift($data["data"], [
                'recharge_money' => $payOrderStatistics['money'],
                'recharge_count' => $payOrderStatistics['count'],
                'charging_money' => $orderStatistics['money'],
                'charging_count' => $orderStatistics['count'],
                'date' => $current_time,
            ]);
        }
        return $data;
    }
}