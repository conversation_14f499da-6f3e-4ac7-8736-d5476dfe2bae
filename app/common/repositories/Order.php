<?php
/** @noinspection PhpUnused */

namespace app\common\repositories;

use app\common\model\Order as OrderModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Order
{
    public const StatusPlaceAnOrder = 1; // 下单
    public const StatusCharging = 3; // 充电中
    public const StatusComplete = 5; // 完成
    public const StatusAbnormal = 6; // 异常
    public const StatusCompulsorySettlement = 7; // 自动结算
    public const StatusReservation = 8; // 预约

    public static function getOrderStatus(string $transactionSerialNumber): ?int
    {
        return (new OrderModel())->where('id', '=', $transactionSerialNumber)->value('status');
    }

    public static function getOrderUserId(string $transactionSerialNumber): ?int
    {
        return (new OrderModel())->where('id', '=', $transactionSerialNumber)->value('user_id');
    }

    /**
     * @param string $transactionSerialNumber
     * @param array|null $field
     * @return array|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getOrderData(string $transactionSerialNumber, ?array $field = null): ?array
    {
        if (is_null($field)) $field = '*';
        $data = (new OrderModel())->where('id', '=', $transactionSerialNumber)->field($field)->find();
        if (empty($data)) return null;
        return $data->toArray();
    }

    public static function updateOrderStatus(string $transactionSerialNumber, int $status): bool
    {
        (new OrderModel())->where('id', '=', $transactionSerialNumber)->update([
            'status' => $status,
            'update_time' => date('Y-m-d H:i:s')
        ]);
        return true;
    }
}