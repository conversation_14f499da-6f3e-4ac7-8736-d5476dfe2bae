<?php

namespace app\common\repositories;

// 运营商运营资金每日统计表
use app\common\model\Order;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use app\common\model\CorpOperatingFundsDailyStatistics as CorpDailyStatisticsModel;

class CorpOperatingFundsDailyStatistics
{
    /**
     * @param int $corp_id 运营商ID
     * @param string $start_time 开始时间
     * @param string $end_time 结束时间
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function get_time_range_data(int $corp_id, string $start_time, string $end_time): array
    {
        $start_time = date('Y-m-d 00:00:00', strtotime($start_time));
        $end_time = date('Y-m-d 00:00:00', strtotime($end_time));

        $data = (new CorpDailyStatisticsModel())->get_time_range_data($corp_id, $start_time, $end_time);

        // 如果时间范围包含今天
        $current_time = date('Y-m-d');
        if (strtotime($start_time) <= strtotime($current_time) && strtotime($end_time) >= strtotime($current_time)) {
            $orderStatistics = (new Order())->dailyStatistics($current_time, $corp_id);

            array_unshift($data, [
                'charging_money' => $orderStatistics['money'],
                'charging_count' => $orderStatistics['count'],
                'date' => $current_time,
            ]);
        }

        return $data;
    }

    /**
     * @param int $corp_id
     * @param string $start_time
     * @param string $end_time
     * @param int $page
     * @param int $limit
     * @return array
     * @throws DbException
     */
    public static function get_list(int $corp_id, string $start_time, string $end_time, int $page, int $limit): array
    {
        $start_time = date('Y-m-d 00:00:00', strtotime($start_time));
        $end_time = date('Y-m-d 00:00:00', strtotime($end_time));

        $data = (new CorpDailyStatisticsModel())->get_list($corp_id, $start_time, $end_time, $page, $limit);
        // 如果时间范围包含今天
        $current_time = date('Y-m-d');
        if (strtotime($start_time) <= strtotime($current_time) && strtotime($end_time) >= strtotime($current_time)) {
            $orderStatistics = (new Order())->dailyStatistics($current_time, $corp_id);

            array_unshift($data["data"], [
                'charging_money' => $orderStatistics['money'],
                'charging_count' => $orderStatistics['count'],
                'date' => $current_time,
            ]);
        }
        return $data;
    }
}