<?php
/** @noinspection PhpUnused */

namespace app\common\repositories;


use app\common\model\ActiveAlarmConfig as ActiveAlarmConfigModel;
use app\common\lib\entity\ActiveAlarmConfig as ActiveAlarmConfigEntity;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class ActiveAlarmConfig
{
    public const DefaultID = 1;

    /**
     * @return ActiveAlarmConfigEntity|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function find(): ?ActiveAlarmConfigEntity
    {
        $data = (new ActiveAlarmConfigModel())->where('id', '=', self::DefaultID)->find();
        if (empty($data)) {
            return null;
        }

        return new ActiveAlarmConfigEntity($data->toArray());
    }

    public static function updateConfigData($config): bool
    {
        $config['update_time'] = date('Y-m-d H:i:s');
        (new ActiveAlarmConfigModel())->where('id', '=', self::DefaultID)->update($config);
        return true;
    }
}