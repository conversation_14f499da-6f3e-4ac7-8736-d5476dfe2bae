<?php

namespace app\common\repositories;

use app\common\model\StationExtraInfo as StationExtraInfoModel;

class StationsExtraInfo
{
    public const IsSupportReservationNot = 0; // 不支持预约充电
    public const IsSupportReservationYes = 1; // 支持预约充电

    public static function getIsSupportReservation(int $station_id): ?int
    {
        return app(StationExtraInfoModel::class)->where('id', '=', $station_id)->value('is_support_reservation');
    }
}