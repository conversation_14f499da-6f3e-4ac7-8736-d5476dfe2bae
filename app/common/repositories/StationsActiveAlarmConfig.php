<?php
/** @noinspection PhpUnused */

namespace app\common\repositories;


use app\common\model\StationsActiveAlarmConfig as StationsActiveAlarmConfigModel;
use app\common\lib\entity\StationsActiveAlarmConfig as StationsActiveAlarmConfigEntity;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class StationsActiveAlarmConfig
{
    // 是否开启主动告警
    public const StatusOpen = 1; // 开启
    public const StatusClose = 0; // 关闭

    // 充电枪故障告警 - 是否自动生成工单
    public const SFIsGenerateWorkOrderYes = 1; // 是
    public const SFIsGenerateWorkOrderNot = 2; // 否

    // 充电服务离线告警 - 是否自动生成工单
    public const CSOIsGenerateWorkOrderYes = 1; // 是
    public const CSOIsGenerateWorkOrderNot = 2; // 否

    // 读取电表功率告警 - 是否自动生成工单
    public const REMPIsGenerateWorkOrderYes = 1; // 是
    public const REMPIsGenerateWorkOrderNot = 2; // 否

    // 充电桩离线告警 - 是否自动生成工单
    public const POIsGenerateWorkOrderYes = 1; // 是
    public const POIsGenerateWorkOrderNot = 2; // 否

    // 能源路由器资源告警 - 是否自动生成工单
    public const CCSRIsGenerateWorkOrderYes = 1; // 是
    public const CCSRIsGenerateWorkOrderNot = 2; // 否


    /**
     * @param int $station_id 充电场站ID
     * @return StationsActiveAlarmConfigEntity|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function find(int $station_id): ?StationsActiveAlarmConfigEntity
    {
        // 因为充电桩生命周期改动的原因，场站平台的场站并不会跟运营平台的场站直接挂钩，这样会造成中转服务上报过来的告警信息的场站ID都会为0。
        // 为了不影响到告警功能的正常使用，这里增加一个临时的配置。
        if ($station_id === 0) {
            $data = config('active_alarm_config.temporary');
            if (empty($data)) {
                return null;
            }
        } else {
            $data = (new StationsActiveAlarmConfigModel())
                ->where('station_id', '=', $station_id)
                ->find();
            if (empty($data)) {
                return null;
            }
            $data = $data->toArray();
        }


        return new StationsActiveAlarmConfigEntity($data);
    }


    /**
     * 为场站生成默认配置
     *
     * @param int $station_id 场站ID
     * @param int $corp_id 运营商ID
     * @return bool
     */
    public static function generateDefaultStationConfig(int $corp_id, int $station_id): bool
    {
        return (new StationsActiveAlarmConfigModel())->insert([
                'station_id' => $station_id,
                'corp_id' => $corp_id,
                'enterprise_wechat_key' => '',
                'sf_is_generate_work_order' => self::SFIsGenerateWorkOrderNot,
                'sf_auto_dispatch_user_id' => 0,
                'cso_is_generate_work_order' => self::CSOIsGenerateWorkOrderNot,
                'cso_auto_dispatch_user_id' => 0,

                'remp_is_generate_work_order' => self::REMPIsGenerateWorkOrderNot,
                'remp_auto_dispatch_user_id' => 0,

                'po_is_generate_work_order' => self::POIsGenerateWorkOrderNot,
                'po_auto_dispatch_user_id' => 0,

                'ccsr_is_generate_work_order' => self::CCSRIsGenerateWorkOrderNot,
                'ccsr_auto_dispatch_user_id' => 0,

                'update_time' => date('Y-m-d H:i:s')
            ]) > 0;
    }

    /**
     * 为场站批量生成默认配置
     *
     * @param array $stations_data
     * @return int
     */
    public static function batchGenerateDefaultStationConfig(array $stations_data): int
    {
        $inserts = [];
        foreach ($stations_data as $station_data) {
            $inserts[] = [
                'station_id' => $station_data['station_id'],
                'corp_id' => $station_data['corp_id'],
                'enterprise_wechat_key' => '',
                'email' => '',
                'sf_is_generate_work_order' => self::SFIsGenerateWorkOrderNot,
                'sf_auto_dispatch_user_id' => 0,
                'cso_is_generate_work_order' => self::CSOIsGenerateWorkOrderNot,
                'cso_auto_dispatch_user_id' => 0,

                'remp_is_generate_work_order' => self::REMPIsGenerateWorkOrderNot,
                'remp_auto_dispatch_user_id' => 0,

                'po_is_generate_work_order' => self::POIsGenerateWorkOrderNot,
                'po_auto_dispatch_user_id' => 0,

                'ccsr_is_generate_work_order' => self::CCSRIsGenerateWorkOrderNot,
                'ccsr_auto_dispatch_user_id' => 0,

                'update_time' => date('Y-m-d H:i:s')
            ];
        }

        return (new StationsActiveAlarmConfigModel())->insertAll($inserts);
    }


    /**
     * 验证是否存在
     *
     * @param int $station_id 场站ID
     * @return bool true:存在 false:不存在
     */
    public static function isExistence(int $station_id): bool
    {
        return (new StationsActiveAlarmConfigModel())->where('station_id', '=', $station_id)->count() > 0;
    }

    public static function getEmail(int $station_id): ?string
    {
        return (new StationsActiveAlarmConfigModel())->where('station_id', '=', $station_id)->value('email');
    }

    public static function getEnterpriseWechatKey(int $station_id): ?string
    {
        return (new StationsActiveAlarmConfigModel())->where('station_id', '=', $station_id)->value('enterprise_wechat_key');
    }

    public static function updateConfig(int $station_id, array $config): bool
    {
        (new StationsActiveAlarmConfigModel())->where('station_id', '=', $station_id)->update([
            'enterprise_wechat_key' => $config['enterprise_wechat_key'],
            'email' => $config['email'],
            'sf_is_generate_work_order' => $config['sf_is_generate_work_order'],
            'sf_auto_dispatch_user_id' => $config['sf_auto_dispatch_user_id'],
            'cso_is_generate_work_order' => $config['cso_is_generate_work_order'],
            'cso_auto_dispatch_user_id' => $config['cso_auto_dispatch_user_id'],

            'remp_is_generate_work_order' => $config['remp_is_generate_work_order'],
            'remp_auto_dispatch_user_id' => $config['remp_auto_dispatch_user_id'],

            'po_is_generate_work_order' => $config['po_is_generate_work_order'],
            'po_auto_dispatch_user_id' => $config['po_auto_dispatch_user_id'],

            'ccsr_is_generate_work_order' => $config['ccsr_is_generate_work_order'],
            'ccsr_auto_dispatch_user_id' => $config['ccsr_auto_dispatch_user_id'],

            'update_time' => date('Y-m-d H:i:s')
        ]);
        return true;
    }
}