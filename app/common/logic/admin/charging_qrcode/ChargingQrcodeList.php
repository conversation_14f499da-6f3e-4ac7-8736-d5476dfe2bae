<?php

namespace app\common\logic\admin\charging_qrcode;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\VerifyData;
use app\common\model\ChargingQrcode;
use Respect\Validation\Validator as v;
use think\db\Query;
use think\Request;

class ChargingQrcodeList
{
    protected Request $request;
    protected AdminLoginUser $loginUser;

    public const SortFieldsID = 1; // 活动ID
    public const SortFieldsCreateTime = 2; // 创建时间
    public const SortFieldsOptions = [
        ['value' => self::SortFieldsID, 'label' => 'ID'],
        ['value' => self::SortFieldsCreateTime, 'label' => '创建时间'],
    ];
    public const SortFieldsMap = [
        self::SortFieldsID => 'cq.id',
        self::SortFieldsCreateTime => 'cq.create_time',
    ];

    // 排序方式
    public const SortTypeAsc = 2; // 升序
    public const SortTypeDesc = 1; // 降序
    public const SortTypeMap = [
        self::SortTypeAsc => 'ASC',
        self::SortTypeDesc => 'DESC'
    ];
    public const SortTypeOptions = [
        ['value' => self::SortTypeAsc, 'label' => '升序'],
        ['value' => self::SortTypeDesc, 'label' => '降序']
    ];

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    protected function getSortType(int $sort_type_option_value): string
    {
        return self::SortTypeMap[$sort_type_option_value] ?? self::SortTypeMap[self::SortTypeDesc];
    }

    protected function getSortField($option_value): string
    {
        return self::SortFieldsMap[$option_value] ?? self::SortFieldsMap[self::SortFieldsID];
    }

    public function sort_list_info(): array
    {
        return [
            'order_name' => self::SortFieldsOptions[0]['value'],
            'order_type' => self::SortTypeDesc,
            'sort_list' => self::SortFieldsOptions,
        ];
    }

    protected function transformSortParams(array $params): array
    {
        $params['sort_field'] = $this->getSortField($params['sort_field']);
        $params['sort_type'] = $this->getSortType($params['sort_type']);

        return $params;
    }

    /**
     * 增加数据权限过滤条件
     *
     * @param AdminLoginUser $loginUser
     * @param array $params
     * @return array
     */
    protected function addDataPermissionsFilterConditions(AdminLoginUser $loginUser, array $params): array
    {
        return $params;
    }

    /**
     * 整理过滤条件
     *
     * @param array $filters
     * @return array[]
     */
    protected function arrangeFilters(array $filters): array
    {
        $new_filters = [];

        if (!empty($filters['filter_min_id'])) {
            $new_filters[] = [
                'cq.id', '>=', $filters['filter_min_id']
            ];
        }

        if (!empty($filters['filter_max_id'])) {
            $new_filters[] = [
                'cq.id', '<=', $filters['filter_max_id']
            ];
        }

        return $new_filters;
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::charging_qrcode([
            'page',
            'limit',
            'sort_field',
            'sort_type',
            'filter_min_id',
            'filter_max_id'
        ]));
    }

    protected function queryListData(array $filters, int $page, int $limit, string $sort_field, string $sort_type): array
    {
        $fields = [
            'cq.id', 'cq.path', 'cq.url', 'cq.create_time',
        ];
        $model = (new ChargingQrcode());
        /**
         * @var Query $model
         */
        return $model->alias('cq')
            ->where($filters)
            ->field($fields)
            ->order($sort_field, $sort_type)
            ->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();
    }

    public function get_list_data(): array
    {
        // 验证参数
        $params = $this->verifyParams($this->request->post());

        // 转换排序参数
        $params = $this->transformSortParams($params);

        // 增加数据权限的过滤条件
        $params = $this->addDataPermissionsFilterConditions($this->loginUser, $params);

        // 整理过滤条件
        $filters = $this->arrangeFilters($params);

        return $this->queryListData($filters, $params['page'], $params['limit'], $params['sort_field'], $params['sort_type']);
    }


}