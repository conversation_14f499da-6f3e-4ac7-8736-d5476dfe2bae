<?php

namespace app\common\logic\admin\charging_qrcode;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\model\ChargingQrcode;
use Endroid\QrCode\Color\Color;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\Label\Font\Font;
use Endroid\QrCode\Label\Label;
use Endroid\QrCode\Label\Margin\Margin;
use Endroid\QrCode\Logo\Logo;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\RoundBlockSizeMode;
use Endroid\QrCode\Writer\PngWriter;
use Respect\Validation\Validator as v;
use think\facade\Cache;
use think\file\UploadedFile;
use think\Request;

class GenerateQrcode
{
    protected AdminLoginUser $loginUser;
    protected Request $request;
    protected ?UploadedFile $logo;

    protected array $generate_qrcode = [];

    protected function addGenerateQrcode(string $qrcode_path): void
    {
        $this->generate_qrcode[] = $qrcode_path;
    }

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
        $this->logo = $this->request->file('logo');
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::charging_qrcode([
            'generate_count',
            'url_prefix'
        ]));
    }

    protected function verifyPermission(AdminLoginUser $loginUser): bool
    {
        if ($loginUser->pid !== 0 || $loginUser->corp_id !== 0) {
            throw new RuntimeException('只允许超级管理员生成二维码', [], RuntimeException::CodeBusinessException);
        }

        return true;
    }

    protected function setTimeLimit(int $duration): bool
    {
        // 设置超时时长
        return set_time_limit($duration);
    }

    public function run(): array
    {
        // 权限验证
        $this->verifyPermission($this->loginUser);
        // 验证参数
        $params = $this->verifyParams($this->request->post());
        // 设置超时时长(假设每生成一个二维码需要耗时1秒,额外增加10秒.)
        $this->setTimeLimit($params['generate_count'] + 10);
        // 如果有上传Logo，验证图片尺寸120x120
        $logoTmpPath = $this->verifyLogo($this->logo);
        // 初始化二维码数据
        $initQrcodeData = $this->initQrcodeData($params['generate_count'], $params['url_prefix']);
        // 批量生成二维码
        $bigQrcodeData = $this->batchGenerateQrcode($initQrcodeData, $logoTmpPath);
        // 将大数组拆分成N个小数组
        $chunkQrcodeDataArray = $this->chunkArray($bigQrcodeData);
        // 写入数据库
        foreach ($chunkQrcodeDataArray as $qrcodeDataArray) {
            // 批量保存二维码数据
            $this->batchSaveQrcodeData($qrcodeDataArray);
        }

        // todo: 还需要在异常处理时,回滚事务后将生成的二维码删除掉.

        return [
            'generate_qrcode_list' => $bigQrcodeData
        ];
    }

    protected function batchGenerateQrcode(array $initQrcodeData, ?string $logo_tmp_path = null): array
    {
        foreach ($initQrcodeData as &$qrcode) {
            // 生成二维码
            [$fileRelativePath, $fileAbsolutePath] = $this->generateQrcode($qrcode['url'], $qrcode['id'], $logo_tmp_path);

            // 将生成的二维码的绝对路径收集起来，用于事务回滚的操作
            $this->addGenerateQrcode($fileAbsolutePath);

            $qrcode['path'] = $fileRelativePath;
        }

        return $initQrcodeData;
    }

    protected function getNewId(): int
    {
        $value = Cache::inc('charging:qrcode:auto_increment', 1);
        if ($value === false) throw new RuntimeException('自增ID失败', [], RuntimeException::CodeDatabaseException);
        return $value;
    }

    protected function initQrcodeData(int $generate_count, string $url_prefix): array
    {
        $inserts = [];
        for ($i = 0; $i < $generate_count; $i++) {
            $new_id = $this->getNewId();
            $url = sprintf("%s?id=%d", $url_prefix, $new_id);

            $inserts[] = [
                'id' => $new_id,
                'url' => $url,
                'path' => "",
                'create_time' => date('Y-m-d H:i:s')
            ];
        }

        return $inserts;
    }

    protected function chunkArray(array $big_array, int $chunk_size = 500): array
    {
        // 初始化一个空数组来存储分割后的小数组
        $chunks = [];

        // 计算需要多少个小数组
        $numChunks = ceil(count($big_array) / $chunk_size);

        // 循环分割大数组
        for ($i = 0; $i < $numChunks; $i++) {
            // 使用 array_slice 函数来获取子数组
            $chunks[] = array_slice($big_array, $i * $chunk_size, $chunk_size);
        }

        return $chunks;
    }

    protected function batchSaveQrcodeData(array $inserts): int
    {
        if (empty($inserts)) return 0;
        $model = app(ChargingQrcode::class);
        $model->insertAll($inserts);
        return $model->getNumRows();
    }


    protected function verifyLogo(?UploadedFile $logo): ?string
    {
        if ($logo === null) {
            return null;
        }

        // 验证图片类型
        if (!in_array($logo->getOriginalMime(), [
            'image/jpeg', 'image/png'
        ])) {
            throw new RuntimeException('logo图片需要是png或jpg类型的', [], RuntimeException::CodeBusinessException);
        }

        $tmpPath = $logo->getPath() . DIRECTORY_SEPARATOR . $logo->getFilename();

        [$width, $height] = getimagesize($tmpPath);

        if ($width !== 120 || $height !== 120) {
            throw new RuntimeException('logo图片尺寸需要是120x120的', [], RuntimeException::CodeBusinessException);
        }

        // 验证图片是否含有恶意代码
        $content = file_get_contents($tmpPath);
        if (str_contains($content, "<?php")) {
            LogCollector::collectorRunLog('发现有人上传的图片含有恶意代码', LogCollector::LevelError);
            throw new RuntimeException('图片已损坏无法使用', [], RuntimeException::CodeBusinessException);
        }

        return $tmpPath;
    }

    /**
     * 为字符串中的每个字符之间增加一个空格的间隙
     * @param string $content
     * @return string
     */
    protected function addSpaceGap(string $content): string
    {
        return implode(" ", str_split($content));
    }


    protected function generateQrcode(string $content, int $id, ?string $logo_path): array
    {
        // 二维码知识 - 容错率
        // 二维码的识别能力与其容错率有关，容错率是指二维码被遮挡后仍能被正常扫描的最大面积。根据搜索结果，二维码的容错率分为四个等级：L、M、Q、H，分别对应7%、15%、25%、30%的容错能力。
        // 这意味着在理论上，如果二维码的容错率设置为最高等级H，那么即使有30%的区域被遮挡，二维码仍然可以被识别和扫描。
        // 然而，实际情况可能会更复杂。二维码的黑色和白色方块对应于机器编码的0和1，如果遮挡的部分恰好是不重要的信息位，那么即使超过30%的区域被遮挡，二维码也有可能被成功扫描。
        // 此外，二维码中包含的信息结构，如三个角上的定位点（pattern finder），如果被遮挡，将无法找到二维码，从而无法进行扫描。
        // 在实际应用中，建议不要遮挡超过1/3的二维码区域，尤其是不要挡住三个角上的定位点，以确保二维码能够被顺利扫描。
        // 此外，二维码的边缘定位框和中间定位小块也不能被遮挡，否则即使容错率再高也无法被扫描。
        // 因此，为了确保二维码能够被扫描，应尽量避免遮挡，并确保二维码的清晰度和对比度，以便扫描设备能够准确读取信息。

        // ErrorCorrectionLevel::High = 30%
        // ErrorCorrectionLevel::Quartile = 25%
        // ErrorCorrectionLevel::Medium = 15%
        // ErrorCorrectionLevel::Low = 7%

        // 创建二维码实例
        $qrCode = QrCode::create($content)
            ->setEncoding(new Encoding('UTF-8'))
            ->setErrorCorrectionLevel(new ErrorCorrectionLevel\ErrorCorrectionLevelQuartile())
            ->setSize(560)
            ->setMargin(10)
            ->setRoundBlockSizeMode(new RoundBlockSizeMode\RoundBlockSizeModeMargin())
            ->setForegroundColor(new Color(0, 0, 0))
            ->setBackgroundColor(new Color(255, 255, 255));

        // 加载Logo
        if (!empty($logo_path)) {
            $logo = new Logo($logo_path, 120, 120);
        } else {
            $logo = null;
        }


        // 使用PngWriter写出二维码
        $writer = new PngWriter();
        // 文件名
        $fileName = sprintf('charging_qrcode_%d.png', $id);
        // 目录名
        $dirName = 'charging_qrcode';
        // 文件相对路径
        $fileRelativePath = sprintf("%s" . DIRECTORY_SEPARATOR . "%s", $dirName, $fileName);
        // 文件所在目录绝对路径
        $fileAbsoluteDir = public_path($dirName);
        if (!is_dir($fileAbsoluteDir)) {
            mkdir($fileAbsoluteDir, 0777, true);
        }
        // 文件绝对路径
        $fileAbsolutePath = sprintf("%s%s", $fileAbsoluteDir, $fileName);
        $fontDir = root_path('vendor' . DIRECTORY_SEPARATOR . 'endroid' . DIRECTORY_SEPARATOR . 'qr-code' . DIRECTORY_SEPARATOR . 'assets');
        $font = new Font($fontDir . 'open_sans.ttf', 36);
        $labelMargin = new Margin(0, 10, 8, 10);
        $label = new Label(sprintf("NO. %s", $this->addSpaceGap((string)$id)), $font, null, $labelMargin);

        $writer->write($qrCode, $logo, $label)->saveToFile($fileAbsolutePath);

        return [$fileRelativePath, $fileAbsolutePath];
    }


}