<?php

namespace app\common\logic\admin\charging_qrcode;


use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\ChargingQrcodeToShots;
use think\Request;
use Respect\Validation\Validator as v;

class Unbind
{
    protected Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function run(): array
    {
        $params = $this->verifyParams($this->request->post());


        if (!$this->verifyIfItExists($params['charging_qrcode_id'], $params['shots_id'])) {
            throw new RuntimeException('二维码与充电枪未建立绑定关系', [], RuntimeException::CodeBusinessExceptionChargingQrcodeBeenBound);
        }

        $result = $this->unbind($params['charging_qrcode_id'], $params['shots_id']);

        if (!$result) {
            throw new RuntimeException('解绑失败', [], RuntimeException::CodeServiceException);
        }

        return [];
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::charging_qrcode([
            'charging_qrcode_id',
            'shots_id',
        ]));
    }

    protected function verifyIfItExists(int $charging_qrcode_id, int $shots_id): bool
    {
        return app(ChargingQrcodeToShots::class)->verifyIfItExists($charging_qrcode_id, $shots_id);
    }

    protected function unbind(int $charging_qrcode_id, int $shots_id): bool
    {
        return app(ChargingQrcodeToShots::class)->unbindQrcode($charging_qrcode_id, $shots_id);
    }
}