<?php

namespace app\common\logic\admin\charging_qrcode;


use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\ChargingQrcode;
use app\common\model\ChargingQrcodeToShots;
use app\common\model\Shots;
use think\Request;
use Respect\Validation\Validator as v;

class Bind
{
    protected Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function run(): array
    {
        $params = $this->verifyParams($this->request->post());

        // 验证充电枪是否存在
        if (!$this->verifyShotsIfExists($params['shots_id'])) {
            throw new RuntimeException('无效充电枪ID', [], RuntimeException::CodeBusinessException);
        }
        // 验证二维码是否存在
        if (!$this->verifyChargingQrcodeIfExists($params['charging_qrcode_id'])) {
            throw new RuntimeException('无效二维码ID', [], RuntimeException::CodeBusinessException);
        }


        if ($this->verifyQrcodeHasItBeenBound($params['charging_qrcode_id'])) {
            // 如果是强行绑定的话，解除二维码原有的绑定
            if (!empty($params['is_force_bind'])) {
                $result = $this->unbindChargingQrcode($params['charging_qrcode_id']);
                if (!$result) {
                    throw new RuntimeException('强行绑定失败', [], RuntimeException::CodeServiceException);
                }
            } else {
                throw new RuntimeException('二维码已经被绑定', [], RuntimeException::CodeBusinessExceptionChargingQrcodeBeenBound);
            }
        }

        if ($this->verifyShotsHasItBeenBound($params['shots_id'])) {
            // 如果是强行绑定的话，解除充电枪原有的绑定
            if (!empty($params['is_force_bind'])) {
                $result = $this->unbindShots($params['shots_id']);
                if (!$result) {
                    throw new RuntimeException('强行绑定失败', [], RuntimeException::CodeServiceException);
                }
            } else {
                throw new RuntimeException('充电枪已经被绑定', [], RuntimeException::CodeBusinessExceptionShotsBeenBound);
            }
        }

        $result = $this->bind($params['charging_qrcode_id'], $params['shots_id']);

        if (!$result) {
            throw new RuntimeException('绑定失败', [], RuntimeException::CodeServiceException);
        }

        return [];
    }

    protected function verifyChargingQrcodeIfExists(int $charging_qrcode_id): bool
    {
        return app(ChargingQrcode::class)->verifyIfExists($charging_qrcode_id);
    }

    protected function verifyShotsIfExists(int $shots_id): bool
    {
        return app(Shots::class)->verifyIfExists($shots_id);
    }

    protected function unbindShots(int $shots_id): bool
    {
        return app(ChargingQrcodeToShots::class)->unbindShots($shots_id);
    }


    protected function verifyShotsHasItBeenBound(int $shots_id): bool
    {
        return app(ChargingQrcodeToShots::class)->verifyShotsHasItBeenBound($shots_id);
    }


    protected function unbindChargingQrcode(int $charging_qrcode_id): bool
    {
        return app(ChargingQrcodeToShots::class)->unbindChargingQrcode($charging_qrcode_id);
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::charging_qrcode([
            'charging_qrcode_id',
            'shots_id',
            'is_force_bind'
        ]));
    }

    protected function verifyQrcodeHasItBeenBound(int $charging_qrcode_id): bool
    {
        return app(ChargingQrcodeToShots::class)->verifyQrcodeHasItBeenBound($charging_qrcode_id);
    }

    protected function bind(int $charging_qrcode_id, int $shots_id): bool
    {
        return app(ChargingQrcodeToShots::class)->bindQrcode($charging_qrcode_id, $shots_id);
    }
}