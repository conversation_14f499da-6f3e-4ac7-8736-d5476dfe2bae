<?php

namespace app\common\logic\admin\piles;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\model\Piles;
use app\common\model\PilesStatus;
use app\common\model\Stations;
use Respect\Validation\Validator as v;
use think\Request;

class Add
{
    protected AdminLoginUser $loginUser;
    protected Request $request;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::piles([
            'station_id',
            'id',
            'name',
            'ac_dc',
            'type',
            'model',
            'power',
            'comm_id',
            'phase_name'
        ]));
    }

    public function run(): array
    {
        $params = $this->verifyParams($this->request->post());

        $this->verifyDataPermissions($this->loginUser, $params);

        $this->verifyParamsNumberIsExistent($params['id']);

        $insertRow = $this->insertPiles($this->loginUser, $params);
        LogCollector::collectorRunLog(sprintf('$insertRow = %d', $insertRow));

        $insertRow = $this->insertPilesStatus($params['id']);
        LogCollector::collectorRunLog(sprintf('$insertRow = %d', $insertRow));

        $this->queryPilesOnlineStatus($params['id']);

        return [];
    }

    protected function queryPilesOnlineStatus(int $piles_id): void
    {
        $pilesApi = new \app\common\device_client\Piles();
        $res = $pilesApi->getPilesOnlineStatus($piles_id);
        if ($res->httpCode === 200) {
            $body = $res->analysisJsonBody();
            if ($body['code'] === 200) {
                if (isset($body['data']['online_status']) && $body['data']['online_status'] === 2) {
                    (new PilesStatus())->updateIsOnline($piles_id, PilesStatus::IsOnlineYes);
                }
            }
        }

        LogCollector::collectorRunLog(json_encode((array)$res));
    }

    protected function queryStationExistent(int $corp_id, int $station_id): bool
    {
        $model = app(Stations::class);
        return $model->corpStationIsExistent($corp_id, $station_id);
    }

    protected function verifyDataPermissions(AdminLoginUser $loginUser, array $params): void
    {
        if ($loginUser->corp_id === 0) {
            throw new RuntimeException('只允许运营商账号操作', [], RuntimeException::CodeBusinessException);
        }

        // 验证场站是否存在
        if (!$this->queryStationExistent($loginUser->corp_id, $params['station_id'])) {
            throw new RuntimeException('无效场站ID', [], RuntimeException::CodeBusinessException);
        }
    }

    /**
     * @param int $piles_id
     * @return void
     */
    protected function verifyParamsNumberIsExistent(int $piles_id): void
    {
        $model = app(Piles::class);
        if ($model->isExistent($piles_id)) {
            throw new RuntimeException('充电桩已存在', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function insertPilesStatus(int $piles_id): int
    {
        $insert = [
            'id' => $piles_id,
            'is_online' => PilesStatus::IsOnlineNot,
            'is_online_update_time' => date('Y-m-d H:i:s'),
        ];
        $model = app(PilesStatus::class);
        $model->insert($insert);
        return $model->getNumRows();
    }

    protected function insertPiles(AdminLoginUser $loginUser, array $params): int
    {
        $model = app(Piles::class);
        $model->insert([
            'id' => $params['id'],
            'name' => $params['name'],
            'corp_id' => $loginUser->corp_id,
            'station_id' => $params['station_id'],
            'ac_dc' => $params['ac_dc'],
            'type' => $params['type'],
            'model' => $params['model'],
            'power' => $params['power'],
            'comm_id' => $params['comm_id'],
            'opt_id' => $loginUser->id,
            'phase_name' => $params['phase_name'] ?? 'a',
            'create_time' => date('Y-m-d H:i:s'),
        ]);
        return $model->getNumRows();
    }

}