<?php

namespace app\common\logic\admin\piles;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\model\Piles;
use app\common\model\Stations;
use Respect\Validation\Validator as v;
use think\Request;

class Update
{
    protected AdminLoginUser $loginUser;
    protected Request $request;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    public function run(): array
    {
        // 验证参数
        $params = $this->verifyParams($this->request->post());
        // 验证数据权限
        $this->verifyDataPermissions($this->loginUser);
        // 验证场站是否存在
        $this->verifyStation($this->loginUser->corp_id, $params['station_id']);
        // 验证桩是否存在
        $this->verifyPilesIsExistent($this->loginUser->corp_id, $params['id']);
        // 更新数据
        $updateRow = $this->updatePiles($params['id'], $params);
        LogCollector::collectorRunLog(sprintf('$updateRow = %d', $updateRow));

        return [];
    }

    protected function updatePiles(int $piles_id, array $params): int
    {
        $update = [
            'name' => $params['name'],
            'station_id' => $params['station_id'],
            'ac_dc' => $params['ac_dc'],
            'type' => $params['type'],
            'model' => $params['model'],
            'power' => $params['power'],
            'comm_id' => $params['comm_id'],
        ];
        if (!empty($params['phase_name'])) {
            $update['phase_name'] = $params['phase_name'];
        }

        $model = app(Piles::class);
        $model->where('id', '=', $piles_id)->update($update);
        return $model->getNumRows();
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::piles([
            'station_id',
            'id',
            'name',
            'ac_dc',
            'type',
            'model',
            'power',
            'comm_id',
            'phase_name'
        ]));
    }

    protected function verifyStation(int $corp_id, int $station_id): void
    {
        // 验证场站是否存在
        if (!$this->queryStationExistent($corp_id, $station_id)) {
            throw new RuntimeException('无效场站ID', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyDataPermissions(AdminLoginUser $loginUser): void
    {
        if ($loginUser->corp_id === 0) {
            throw new RuntimeException('只允许运营商账号操作', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function queryStationExistent(int $corp_id, int $station_id): bool
    {
        $model = app(Stations::class);
        return $model->corpStationIsExistent($corp_id, $station_id);
    }

    protected function verifyPilesIsExistent(int $corp_id, int $piles_id): void
    {
        $model = app(Piles::class);
        if (!$model->corpPilesIsExistent($corp_id, $piles_id)) {
            throw new RuntimeException('无效充电桩ID', [], RuntimeException::CodeBusinessException);
        }
    }
}