<?php

namespace app\common\logic\admin\piles;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\model\Piles;
use app\common\model\PilesStatus;
use app\common\model\Shots;
use app\common\model\Stations;
use Respect\Validation\Validator as v;
use think\Request;

class Delete
{
    protected AdminLoginUser $loginUser;
    protected Request $request;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    public function run(): array
    {
        // 验证参数
        $params = $this->verifyParams($this->request->post());
        // 验证数据权限
        $this->verifyDataPermissions($this->loginUser);
        // 验证桩是否存在
        $this->verifyPilesIsExistent($this->loginUser->corp_id, $params['id']);
        // 验证是否有绑定充电枪
        $this->verifyBindShots($params['id']);
        // 更新数据
        $deleteRow = $this->deletePiles($params['id']);
        LogCollector::collectorRunLog(sprintf('$deleteRow = %d', $deleteRow));

        $deleteRow = $this->deletePilesStatus($params['id']);
        LogCollector::collectorRunLog(sprintf('$deleteRow = %d', $deleteRow));

        return [];
    }

    protected function queryBindShotsId(int $piles_id): ?int
    {
        $model = app(Shots::class);
        $where = [
            ['piles_id', '=', $piles_id],
            ['is_del', '=', Shots::IsDelNot]
        ];
        return $model->where($where)->value('id');
    }

    protected function verifyBindShots(int $piles_id): void
    {
        $shots_id = $this->queryBindShotsId($piles_id);
        if (!empty($shots_id)) {
            throw new RuntimeException(sprintf('请先删除已绑定的充电枪: %s', $shots_id), [], RuntimeException::CodeBusinessException);
        }
    }

    protected function deletePiles(int $piles_id): int
    {
        $model = app(Piles::class);
        $model->where('id', '=', $piles_id)->update([
            'is_del' => Piles::IsDelYes
        ]);
        return $model->getNumRows();
    }

    protected function deletePilesStatus(int $piles_id): int
    {
        $model = app(PilesStatus::class);
        $model->where('id', '=', $piles_id)->delete();
        return $model->getNumRows();
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::piles([
            'id',
        ]));
    }

    protected function verifyDataPermissions(AdminLoginUser $loginUser): void
    {
        if ($loginUser->corp_id === 0) {
            throw new RuntimeException('只允许运营商账号操作', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyPilesIsExistent(int $corp_id, int $piles_id): bool
    {
        $model = app(Piles::class);
        return $model->corpPilesIsExistent($corp_id, $piles_id);
    }
}