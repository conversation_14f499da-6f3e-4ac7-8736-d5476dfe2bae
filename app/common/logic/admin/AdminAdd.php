<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\logic\admin;

use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\entity\AdminAddRequest;
use app\common\model\AdminAuthGroup;
use app\common\model\AdminUsers;
use app\common\model\CorpAdminGroup;
use app\common\model\StationDataAuthority;
use app\ms\Api;
use think\db\exception\DbException;

class AdminAdd
{
    protected AdminAddRequest $request;

    public function __construct(AdminAddRequest $request)
    {
        $this->request = $request;
    }

    /**
     * 查询列表数据
     *
     * @return array
     * @throws DbException
     */
    public function run(): array
    {
        // 如果不是超级管理员 或者 运营商管理员 就不能添加账户
        if ($this->request->admin_user_pid > 0) {
            throw new RuntimeException('无权访问', [], RuntimeException::CodeBusinessException);
        }

        // 验证运营商管理员是否有添加该分组账号的权限
        if ($this->request->corp_id > 0) {
            $adminGroupIds = (new CorpAdminGroup())->getCorpAdminGroupIds($this->request->corp_id);
            if (!in_array($this->request->perm_group_id, $adminGroupIds)) {
                throw new RuntimeException('管理组不存在', [], RuntimeException::CodeBusinessException);
            }
        }

        $AdminUsersModel = new AdminUsers();
        if ($AdminUsersModel->isUsernameAlreadyExists($this->request->name)) {
            throw new RuntimeException('管理员用户名已存在', [], RuntimeException::CodeBusinessException);
        }

        // todo: 不加锁的话，存在重复的风险。
        $is = $AdminUsersModel->isPhoneAlreadyExists($this->request->phone);
        if ($is) {
            throw new RuntimeException('手机号已被绑定', [], RuntimeException::CodeBusinessException);
        }

        // todo: 不加锁的话，存在重复的风险。
        $is = $AdminUsersModel->isEmailAlreadyExists($this->request->email);
        if ($is) {
            throw new RuntimeException('电子邮箱已被绑定', [], RuntimeException::CodeBusinessException);
        }


        if ((new AdminAuthGroup())->isAlreadyExists($this->request->perm_group_id) === false) {
            throw new RuntimeException('管理组不存在', [], RuntimeException::CodeBusinessException);
        }

        $res = $AdminUsersModel->createAdminUser(
            $this->request->name,
            $this->request->phone,
            $this->request->email,
            $this->request->perm_group_id,
            $this->request->password,
            $this->request->corp_id,
            $this->request->admin_user_id
        );
        if ($res === false) throw new RuntimeException('新增失败', [], RuntimeException::CodeBusinessException);

        // 如果是运营商管理员添加账号，需要给这个子账号记录场站权限
        if (!empty($this->request->station_ids)) {
            (new StationDataAuthority())->addDataAuthority($res['id'], $this->request->station_ids);
        }

        // ========== 将数据同步到微服务 ==========
        $res['service_id'] = 1;
        // 将数组使用逗号分隔，然后拼接成字符串
        $res['station_ids'] = implode(',', $this->request->station_ids);
        Api::send("/auth/admin", "POST", $res,[],'id,service_id');

        return [];
    }


}