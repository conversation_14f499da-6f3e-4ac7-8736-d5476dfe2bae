<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\common\logic\admin\refund_order;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\SendApplet;
use app\common\lib\VerifyData;
use app\common\model\File;
use app\common\model\PayOrder as PayOrderModel;
use app\common\model\RefundOrder as RefundOrderModel;
use Respect\Validation\Validator as v;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Request;

class RefundSuccess
{
    protected Request $request;
    protected AdminLoginUser $loginUser;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->request = $request;
        $this->loginUser = $loginUser;
    }

    /**
     * @throws ModelNotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     */
    public function run(): array
    {
        // 验证操作权限
        $this->verifyOperationPermissions($this->loginUser);
        // 验证参数
        $params = $this->verifyParams($this->request->post());
        // 验证退款截图
        $this->verifyRefundScreenshot($params['refund_screenshot_id'], $this->loginUser->id);
        // 加载退款订单
        $refundOrder = $this->loadRefundOrder($params['refund_id']);
        // 验证退款订单
        $this->verifyRefundOrder($refundOrder);
        // 更新退款订单状态
        $this->updateRefundOrderStatus($params['refund_id'], $params['refund_screenshot_id']);
        // 更新重置订单的退款状态
        $this->updatePayOrderRefundStatus($refundOrder['order_id']);
        // 发送通知给小程序
        $this->sendNoticeToApplet($refundOrder['user_id']);

        return [];
    }

    protected function verifyRefundScreenshot(int $refund_screenshot_id, int $user_id): void
    {
        // 查询退款截图是否存在
        $is = $this->refundScreenshotIsExistence($refund_screenshot_id, $user_id);
        if (!$is) {
            throw new RuntimeException('无效退款截图ID', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function refundScreenshotIsExistence(int $refund_screenshot_id, int $user_id): bool
    {
        $model = app(File::class);
        return $model->queryUserIsUpload($refund_screenshot_id, $user_id);
    }

    protected function verifyOperationPermissions(AdminLoginUser $loginUser): void
    {
        if ($loginUser->corp_id > 0) {
            throw new RuntimeException('没有权限', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::pay_order([
            'refund_id',
            'refund_screenshot_id'
        ]));
    }

    /**
     * @throws ModelNotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     */
    protected function loadRefundOrder(string $refund_id): ?array
    {
        $model = app(RefundOrderModel::class);
        $refund_order = $model
            ->where('id', $refund_id)
            ->field('id,order_id,user_id,refund_price,order_price,currency,state')
            ->find();

        if (!$refund_order) return null;
        return $refund_order->toArray();
    }

    protected function updateRefundOrderStatus(string $refund_id, int $refund_screenshot_id): bool
    {
        $model = app(RefundOrderModel::class);

        $model->where('id', $refund_id)
            ->limit(1)
            ->update([
                'state' => RefundOrderModel::StateRefundSuccess,
                'refund_screenshot_id' => $refund_screenshot_id,
                'msg' => '退款成功',
                'success_time' => date('Y-m-d H:i:s'),
            ]);

        return $model->getNumRows() > 0;
    }

    protected function updatePayOrderRefundStatus(string $order_id): bool
    {
        $payOrderModel = app(PayOrderModel::class);
        $payOrderModel->where('id', $order_id)
            ->limit(1)
            ->update([
                'refund_state' => PayOrderModel::StatePayComplete,
            ]);
        return $payOrderModel->getNumRows() > 0;
    }


    protected function sendNoticeToApplet(int $user_id): bool
    {
        return SendApplet::send_uid($user_id, [
            'type' => 'refund_notice',
            'result' => 'success',
            'msg' => '退款成功',
            'refund_status' => 'SUCCESS',
        ]);
    }

    protected function verifyRefundOrder(?array $refundOrder): void
    {
        if (is_null($refundOrder)) {
            throw new RuntimeException('退款订单不存在', [], RuntimeException::CodeBusinessException);
        }

        switch ($refundOrder['state']) {
            case RefundOrderModel::StateRefunding:
                throw new RuntimeException('退款订单正在退款中', [], RuntimeException::CodeBusinessException);
            case RefundOrderModel::StateRefuseRefund:
                throw new RuntimeException('退款订单已被拒绝退款', [], RuntimeException::CodeBusinessException);
            case RefundOrderModel::StateCancelRefund:
                throw new RuntimeException('退款订单已被取消', [], RuntimeException::CodeBusinessException);
            case RefundOrderModel::StateRefundSuccess:
                throw new RuntimeException('退款订单已退款成功', [], RuntimeException::CodeBusinessException);
            case RefundOrderModel::StateRefundFail:
                throw new RuntimeException('退款订单已退款失败', [], RuntimeException::CodeBusinessException);
            case RefundOrderModel::StateOther:
                throw new RuntimeException('退款订单处于其他状态', [], RuntimeException::CodeBusinessException);
        }
    }
}