<?php

namespace app\common\logic\admin\stations;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\Piles;
use app\common\model\Stations;
use app\common\model\Stations as StationsModel;
use Respect\Validation\Validator as v;
use think\Request;

class Delete
{
    protected AdminLoginUser $loginUser;
    protected Request $request;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::stations([
            'id',
        ]));
    }

    protected function verifyDataPermissions(AdminLoginUser $loginUser, int $station_id): void
    {
        // 如果是运营商账号
        if ($loginUser->corp_id > 0) {
            $dbCorpId = (new StationsModel())->getCorpId($station_id);
            if ($dbCorpId !== $loginUser->corp_id) {
                throw new RuntimeException('无效场站ID', [], RuntimeException::CodeBusinessException);
            }
        } else {
            $dbCorpId = (new StationsModel())->getCorpId($station_id);
            if (empty($dbCorpId)) {
                throw new RuntimeException('无效场站ID', [], RuntimeException::CodeBusinessException);
            }
        }
    }

    protected function deleteStation(int $station_id): int
    {
        $model = app(Stations::class);
        $model->where('id', '=', $station_id)->update([
            'is_del' => Stations::IsDelYes,
        ]);
        return $model->getNumRows();
    }

    protected function queryStationPilesIsExistent(int $station_id): int
    {
        $model = app(Piles::class);
        $where = [
            ['station_id', '=', $station_id],
            ['is_del', '=', Piles::IsDelNot]
        ];
        return $model->where($where)->count();
    }

    protected function verifyStationPiles(int $station_id): void
    {
        $count = $this->queryStationPilesIsExistent($station_id);

        if ($count > 0) {
            throw new RuntimeException('请先删除已绑定的充电桩', [], RuntimeException::CodeBusinessException);
        }
    }

    public function run(): array
    {
        $params = $this->verifyParams($this->request->post());

        $this->verifyDataPermissions($this->loginUser, $params['id']);

        $this->verifyStationPiles($params['id']);

        $this->deleteStation($params['id']);

        return [];
    }
}