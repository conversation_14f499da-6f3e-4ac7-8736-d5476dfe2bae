<?php

namespace app\common\logic\admin\stations;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\StationExtraInfo;
use app\common\model\Stations;
use app\common\repositories\StationsActiveAlarmConfig;
use app\ms\Api;
use Respect\Validation\Validator as v;
use think\Request;

class Add
{
    protected AdminLoginUser $loginUser;
    protected Request $request;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::stations([
            'name',
            'address',
            'city_id',
            'lonlat',
            'tariff_group_id',
            'type',
            'status',
            'pictures',
            'work_time',
            'tag_pos',
            'tag_park',
            'place_rate',
            'charge',
            'charge_phone',
            'tag_toilet',
            'tag_canopy',
            'tag_rest',
            'tag_pnp',
            'tag_insure',
            'tag_protect',
            'tag_ultrafast',
            'tag_fast',
            'tag_slow',
            'is_support_reservation',
        ]));
    }

    protected function verifyDataPermissions(AdminLoginUser $loginUser): void
    {
        if ($loginUser->corp_id === 0) {
            throw new RuntimeException('只允许运营商账号操作', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function get_city_for_city_id(array $city_id): array|bool
    {
        return get_city_for_city_id($city_id);
    }

    protected function addStation(AdminLoginUser $loginUser, array $params): int
    {
        $insert = [
            'corp_id' => $loginUser->corp_id,
            'name' => $params['name'],
            'all_address' => $params['all_address'],
            'address' => $params['address'],
            'province' => $params['province'],
            'city' => $params['city'],
            'district' => $params['district'],
            'type' => $params['type'],
            'lonlat' => $params['lonlat'],
            'city_id' => $params['city_id'],
            'create_time' => date('Y-m-d H:i:s')
        ];
        $model = app(Stations::class);
        return $model->insertGetId($insert);
    }

    protected function addStationExtraInfo(int $station_id, array $params): int
    {
        $model = app(StationExtraInfo::class);
        $model->insert([
            'id' => $station_id,
            'tariff_group_id' => $params['tariff_group_id'],
            'status' => $params['status'],
            'pictures' => $params['pictures'],
            'work_time' => $params['work_time'],
            'tag_pos' => $params['tag_pos'],
            'tag_park' => $params['tag_park'],
            'place_rate' => $params['place_rate'],
            'create_time' => date('Y-m-d H:i:s'),
            'charge' => $params['charge'],
            'charge_phone' => $params['charge_phone'],
            'tag_toilet' => $params['tag_toilet'],
            'tag_canopy' => $params['tag_canopy'],
            'tag_rest' => $params['tag_rest'],
            'tag_pnp' => $params['tag_pnp'],
            'tag_insure' => $params['tag_insure'],
            'tag_protect' => $params['tag_protect'],
            'tag_ultrafast' => $params['tag_ultrafast'],
            'tag_fast' => $params['tag_fast'],
            'tag_slow' => $params['tag_slow'],
            'update_time' => date('Y-m-d H:i:s'),
            'is_support_reservation' => $params['is_support_reservation'],
        ]);
        return $model->getNumRows();
    }

    protected function generateDefaultActiveAlarmConfig(int $corp_id, int $station_id): bool
    {
        return StationsActiveAlarmConfig::generateDefaultStationConfig($corp_id, $station_id);
    }

    public function run(): array
    {
        $this->verifyDataPermissions($this->loginUser);

        $params = $this->verifyParams($this->request->post());

        $city = $this->get_city_for_city_id($params['city_id']);
        if (!$city) throw new RuntimeException("获取省市区失败", [], RuntimeException::CodeBusinessException);

        $params['province'] = $city['province'];
        $params['city'] = $city['city'];
        $params['district'] = $city['district'];
        $params['all_address'] = $city['province'] . $city['city'] . $city['district'] . $params['address'];
        $params['opt_id'] = $this->loginUser->id;

        $station_id = $this->addStation($this->loginUser, $params);

        $this->addStationExtraInfo($station_id, $params);

        $this->generateDefaultActiveAlarmConfig($this->loginUser->corp_id, $station_id);

        // 同步场站信息到微服务(只更新运营商ID)
        $req['id'] = $station_id;
        $req['corp_id'] = $this->loginUser->corp_id;
        Api::send("/device/stations","PUT",$req);

        return [
            'new_id' => $station_id
        ];
    }
}