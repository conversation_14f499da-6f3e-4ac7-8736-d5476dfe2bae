<?php

namespace app\common\logic\admin\stations;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\model\StationExtraInfo;
use app\common\model\Stations;
use app\common\model\Stations as StationsModel;
use Exception;
use Respect\Validation\Validator as v;
use think\Request;

class Update
{
    protected AdminLoginUser $loginUser;
    protected Request $request;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::stations([
            'id',
            'name',
            'address',
            'city_id',
            'lonlat',
            'tariff_group_id',
            'type',
            'status',
            'pictures',
            'work_time',
            'tag_pos',
            'tag_park',
            'place_rate',
            'charge',
            'charge_phone',
            'tag_toilet',
            'tag_canopy',
            'tag_rest',
            'tag_pnp',
            'tag_insure',
            'tag_protect',
            'tag_ultrafast',
            'tag_fast',
            'tag_slow',
            'is_support_reservation'
        ]));
    }


    protected function verifyStation(AdminLoginUser $loginUser, int $station_id): void
    {
        // 如果是运营商账号
        if ($loginUser->corp_id > 0) {
            $dbCorpId = (new StationsModel())->getCorpId($station_id);
            if ($dbCorpId !== $loginUser->corp_id) {
                throw new RuntimeException('无效场站ID', [], RuntimeException::CodeBusinessException);
            }
        }
    }

    protected function get_city_for_city_id(array $city_id): array|bool
    {
        return get_city_for_city_id($city_id);
    }

    protected function updateStation(int $station_id, array $params): int
    {
        $city = $this->get_city_for_city_id($params['city_id']);
        if (!$city) throw new RuntimeException("无效省市区ID", [], RuntimeException::CodeBusinessException);

        $params['province'] = $city['province'];
        $params['city'] = $city['city'];
        $params['district'] = $city['district'];
        $params['all_address'] = $city['province'] . $city['city'] . $city['district'] . $params['address'];

        $update = [
            'name' => $params['name'],
            'all_address' => $params['all_address'],
            'address' => $params['address'],
            'province' => $params['province'],
            'city' => $params['city'],
            'district' => $params['district'],
            'type' => $params['type'],
            'lonlat' => $params['lonlat'],
            'city_id' => $params['city_id'],
        ];
        $model = app(Stations::class);
        $model->where('id', '=', $station_id)->update($update);
        return $model->getNumRows();
    }

    protected function updateStationExtraInfo(int $station_id, array $params): int
    {
        $stationExtraInfoModel = new StationExtraInfo();
        $stationExtraInfoModel->where('id', '=', $station_id)->update([
            'tariff_group_id' => $params['tariff_group_id'],
            'status' => $params['status'],
            'pictures' => $params['pictures'],
            'work_time' => $params['work_time'],
            'tag_pos' => $params['tag_pos'],
            'tag_park' => $params['tag_park'],
            'place_rate' => $params['place_rate'],
            'charge' => $params['charge'],
            'charge_phone' => $params['charge_phone'],
            'tag_toilet' => $params['tag_toilet'],
            'tag_canopy' => $params['tag_canopy'],
            'tag_rest' => $params['tag_rest'],
            'tag_pnp' => $params['tag_pnp'],
            'tag_insure' => $params['tag_insure'],
            'tag_protect' => $params['tag_protect'],
            'tag_ultrafast' => $params['tag_ultrafast'],
            'tag_fast' => $params['tag_fast'],
            'tag_slow' => $params['tag_slow'],
            'is_support_reservation' => $params['is_support_reservation'],
            'update_time' => date('Y-m-d H:i:s'),
        ]);

        return $stationExtraInfoModel->getNumRows();
    }

    /**
     * @return array
     * @throws Exception
     */
    public function run(): array
    {
        $params = $this->verifyParams($this->request->post());

        // 验证场站
        $this->verifyStation($this->loginUser, $params['id']);

        $updateRow = $this->updateStation($params['id'], $params);
        LogCollector::collectorRunLog(sprintf('$updateRow = %d', $updateRow));

        $updateRow = $this->updateStationExtraInfo($params['id'], $params);
        LogCollector::collectorRunLog(sprintf('$updateRow = %d', $updateRow));

        return [];
    }
}