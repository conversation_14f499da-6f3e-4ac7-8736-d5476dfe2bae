<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\logic\admin;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\entity\AdminEditRequest;
use app\common\model\AdminAuthGroup;
use app\common\model\AdminUsers;
use app\common\model\AdminUsersRelationApplet;
use app\common\model\CorpAdminGroup;
use app\common\model\StationDataAuthority;
use app\ms\Api;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class AdminEdit
{
    protected AdminLoginUser $loginUser;
    protected AdminEditRequest $request;

    public function __construct(AdminLoginUser $loginUser, AdminEditRequest $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    /**
     * 查询列表数据
     *
     * @return array
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function run(): array
    {

        $AdminUsersModel = new AdminUsers();
        $userData = $AdminUsersModel->getUserData($this->request->id);

        if ($this->loginUser->pid > 0) {
            throw new RuntimeException('没有权限', [], RuntimeException::CodeBusinessException);
        }


        if ($this->loginUser->corp_id > 0 && $userData['corp_id'] !== $this->loginUser->corp_id) {
            throw new RuntimeException('管理组不存在', [], RuntimeException::CodeBusinessException);
        }

        // todo: 不加锁的话，存在重名的风险。
        $is = $AdminUsersModel->isUsernameAlreadyExists($this->request->name, $this->request->id);
        if ($is) {
            throw new RuntimeException('管理员用户名已存在', [], RuntimeException::CodeBusinessException);
        }

        // todo: 不加锁的话，存在重复的风险。
        $is = $AdminUsersModel->isPhoneAlreadyExists($this->request->phone, $this->request->id);
        if ($is) {
            throw new RuntimeException('手机号已被绑定', [], RuntimeException::CodeBusinessException);
        }

        // todo: 不加锁的话，存在重复的风险。
        $is = $AdminUsersModel->isEmailAlreadyExists($this->request->email, $this->request->id);
        if ($is) {
            throw new RuntimeException('电子邮箱已被绑定', [], RuntimeException::CodeBusinessException);
        }

        // 如果被编辑的账号属于运营商账号，那么需要验证一下这个运营商是否具有编辑后的管理组的权限。
        if ($userData['corp_id'] > 0) {
            $admin_group_ids = (new CorpAdminGroup())->getCorpAdminGroupIds($userData['corp_id']);
            if (!in_array($this->request->perm_group_id, $admin_group_ids)) {
                throw new RuntimeException('管理组不存在', [], RuntimeException::CodeBusinessException);
            }
            // 如果不是属于运营商账号，那么只需要验证管理员组是否存在就好。
        } else {
            $is = (new AdminAuthGroup)->isAlreadyExists($this->request->perm_group_id);
            if (!$is) throw new RuntimeException('管理组不存在', [], RuntimeException::CodeBusinessException);
        }

        // 当手机号发生变更时，需要将当前账号与原先绑定的小程序账号解除绑定。
        (new AdminUsersRelationApplet())->unbindApplet($this->request->id);


        $AdminUsersModel->where('id', '=', $this->request->id)
            ->update([
                'avatar' => '',
                'name' => $this->request->name,
                'phone' => $this->request->phone,
                'email' => $this->request->email,
                'perm_group_id' => $this->request->perm_group_id,
                'update_time' => date('Y-m-d H:i:s')
            ]);
        if ($AdminUsersModel->getNumRows() === 0) {
            throw new RuntimeException('编辑失败', [], RuntimeException::CodeDatabaseException);
        }

        if ($this->loginUser->pid === 0) {
            $StationDataAuthorityModel = new StationDataAuthority();
            $StationDataAuthorityModel->removeUserAllDataAuthority($this->request->id);
            if (count($this->request->station_ids)) {
                $StationDataAuthorityModel->addDataAuthority($this->request->id, $this->request->station_ids);
            }
        }

        // ========== 将数据同步到微服务 ==========
        $res['id'] = $this->request->id;
        $res['service_id'] = 1;
        $res['name'] = $this->request->name;
        $res['phone'] = $this->request->phone;
        $res['email'] = $this->request->email;
        $res['perm_group_id'] = $this->request->perm_group_id;
        // 将数组使用逗号分隔，然后拼接成字符串
        $res['station_ids'] = implode(',', $this->request->station_ids);
        Api::send("/auth/admin", "PUT", $res,[],'id,service_id');

        return [];
    }
}