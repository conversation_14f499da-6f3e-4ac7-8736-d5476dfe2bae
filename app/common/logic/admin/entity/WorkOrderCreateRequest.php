<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\logic\admin\entity;

use app\common\logic\base\entity\BaseEntity;

class WorkOrderCreateRequest extends BaseEntity
{
    // 'title', 'priority', 'template_id', 'extra_field_data',
    //                'corp_id', 'station_id', 'device_type', 'device_id'
    public string $title;
    public int $priority;
    public int $source;
    public int $template_id;
    public int $create_user_type;
    public int $create_user_id;
    public ?string $create_user_name = null;
    public array $extra_field_data;
    public int $corp_id;
    public int $station_id;
    public int $device_type;
    public string $device_id;
}