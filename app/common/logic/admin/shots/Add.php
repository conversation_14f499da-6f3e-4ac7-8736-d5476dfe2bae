<?php

namespace app\common\logic\admin\shots;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\QrCode;
use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\model\Piles;
use app\common\model\Shots;
use app\common\model\ShotsQrcode;
use app\common\model\ShotsStatus;
use Respect\Validation\Validator as v;
use think\Request;

class Add
{
    protected AdminLoginUser $loginUser;
    protected Request $request;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::shots([
            'name',
            'piles_id',
            'sequence',
        ]));
    }

    public function run(): array
    {
        $params = $this->verifyParams($this->request->post());

        $this->verifyDataPermissions($this->loginUser);

        // 查询充电桩数据
        $pilesData = $this->queryPilesData($this->loginUser->corp_id, $params['piles_id']);

        $this->verifyPilesData($pilesData);

        $shots_id = (int)sprintf('%d0%d', $params['piles_id'], $params['sequence']);

        // 验证充电枪ID是否已存在
        $this->verifyShotsIDIsExistent($shots_id);

        $insertRow = $this->insertShots($this->loginUser, $pilesData['station_id'], $shots_id, $params);
        LogCollector::collectorRunLog(sprintf('$insertRow = %d', $insertRow));

        $insertRow = $this->insertShotsStatus($shots_id);
        LogCollector::collectorRunLog(sprintf('$insertRow = %d', $insertRow));

        // 生成二维码
        $path = $this->generateShotsQrcode($shots_id);

        $insertRow = $this->insertShotsQrcode($shots_id, $path);
        LogCollector::collectorRunLog(sprintf('$insertRow = %d', $insertRow));

        return [
            'new_id' => $shots_id
        ];
    }

    protected function insertShotsQrcode(int $shots_id, string $path): int
    {
        $shotsQrcodeModel = new ShotsQrcode();
        $shotsQrcodeModel->insertShotsQrcode($shots_id, $path);
        return $shotsQrcodeModel->getNumRows();
    }

    protected function generateShotsQrcode(int $shots_id): string
    {
        return QrCode::create_wechat_qrcode((string)$shots_id);
    }

    protected function verifyPilesData(?array $pilesData): void
    {
        if (empty($pilesData)) {
            throw new RuntimeException('无效充电桩ID', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function queryPilesData(int $corp_id, int $piles_id): ?array
    {
        $model = app(Piles::class);
        return $model->getCorpPiles($corp_id, $piles_id, [
            'corp_id', 'station_id'
        ]);
    }

    protected function insertShots(AdminLoginUser $loginUser, int $station_id, int $shots_id, array $params): int
    {
        $model = app(Shots::class);
        $model->insert([
            'id' => $shots_id,
            'name' => $params['name'],
            'corp_id' => $loginUser->corp_id,
            'station_id' => $station_id,
            'piles_id' => $params['piles_id'],
            'sequence' => $params['sequence'],
            'port_num' => $params['sequence'],
            'qrcode' => '',
            'opt_id' => $loginUser->id,
            'create_time' => date('Y-m-d H:i:s'),
            'is_fault' => 1,
            'status_update_time' => date('Y-m-d H:i:s'),
        ]);
        return $model->getNumRows();
    }

    protected function verifyDataPermissions(AdminLoginUser $loginUser): void
    {
        if ($loginUser->corp_id === 0) {
            throw new RuntimeException('只允许运营商账号操作', [], RuntimeException::CodeBusinessException);
        }
    }

    /**
     * @param int $shots_id
     * @return void
     */
    protected function verifyShotsIDIsExistent(int $shots_id): void
    {
        $model = app(Shots::class);
        if ($model->isExistent($shots_id)) {
            throw new RuntimeException('充电枪已经存在', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function insertShotsStatus(int $shots_id): int
    {
        $insert = [
            'id' => $shots_id,
            'is_online' => 0,
            'is_online_update_time' => date('Y-m-d H:i:s'),
            'work_status' => ShotsStatus::WorkStatusIdle,
            'work_status_update_time' => date('Y-m-d H:i:s'),
            'is_fault' => ShotsStatus::IsFaultNot,
            'is_fault_update_time' => date('Y-m-d H:i:s'),
        ];
        $model = app(ShotsStatus::class);
        $model->insert($insert);
        return $model->getNumRows();
    }


}