<?php

namespace app\common\logic\admin\shots;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\model\Piles;
use app\common\model\Shots;
use Respect\Validation\Validator as v;
use think\Request;

class Update
{
    protected AdminLoginUser $loginUser;
    protected Request $request;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    public function run(): array
    {
        // 验证参数
        $params = $this->verifyParams($this->request->post());
        // 验证数据权限
        $this->verifyDataPermissions($this->loginUser);
        // 验证桩是否存在
        $this->verifyShotsIsExistent($this->loginUser->corp_id, $params['id']);
        // 更新数据
        $updateRow = $this->updateShots($params['id'], $params);
        LogCollector::collectorRunLog(sprintf('$updateRow = %d', $updateRow));

        return [];
    }

    protected function updateShots(int $piles_id, array $params): int
    {
        $model = app(Shots::class);
        $model->where('id', '=', $piles_id)->update([
            'name' => $params['name'],
        ]);
        return $model->getNumRows();
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::shots([
            'id',
            'name',
        ]));
    }

    protected function verifyDataPermissions(AdminLoginUser $loginUser): void
    {
        if ($loginUser->corp_id === 0) {
            throw new RuntimeException('只允许运营商账号操作', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyShotsIsExistent(int $corp_id, int $shots_id): void
    {
        $model = app(Shots::class);
        if (!$model->corpStationIsExistent($corp_id, $shots_id)) {
            throw new RuntimeException('无效充电枪ID', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyPilesIsExistent(int $corp_id, int $piles_id): bool
    {
        $model = app(Piles::class);
        return $model->corpPilesIsExistent($corp_id, $piles_id);
    }
}