<?php

namespace app\common\logic\admin\shots;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\model\Shots;
use app\common\model\ShotsQrcode;
use app\common\model\ShotsStatus;
use Respect\Validation\Validator as v;
use think\Request;

class Delete
{
    protected AdminLoginUser $loginUser;
    protected Request $request;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    public function run(): array
    {
        // 验证参数
        $params = $this->verifyParams($this->request->post());
        // 验证数据权限
        $this->verifyDataPermissions($this->loginUser);
        // 验证枪是否存在
        $this->verifyShotsIsExistent($this->loginUser->corp_id, $params['id']);
        // 更新数据
        $deleteRow = $this->deleteShots($params['id']);
        LogCollector::collectorRunLog(sprintf('$deleteRow = %d', $deleteRow));

        $deleteRow = $this->deleteShotsStatus($params['id']);
        LogCollector::collectorRunLog(sprintf('$deleteRow = %d', $deleteRow));

        $deleteRow = $this->deleteShotsQrcode($params['id']);
        LogCollector::collectorRunLog(sprintf('$deleteRow = %d', $deleteRow));

        return [];
    }

    protected function deleteShotsQrcode(int $piles_id): int
    {
        $model = app(ShotsQrcode::class);
        $model->where('id', '=', $piles_id)->delete();
        return $model->getNumRows();
    }

    protected function deleteShotsStatus(int $shots_id): int
    {
        $model = app(ShotsStatus::class);
        $model->where('id', '=', $shots_id)->delete();
        return $model->getNumRows();
    }

    protected function deleteShots(int $shots_id): int
    {
        $model = app(Shots::class);
        $model->where('id', '=', $shots_id)->update([
            'is_del' => Shots::IsDelYes,
        ]);
        return $model->getNumRows();
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::shots([
            'id',
        ]));
    }

    protected function verifyDataPermissions(AdminLoginUser $loginUser): void
    {
        if ($loginUser->corp_id === 0) {
            throw new RuntimeException('只允许运营商账号操作', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyShotsIsExistent(int $corp_id, int $shots_id): void
    {
        $model = app(Shots::class);
        if (!$model->corpStationIsExistent($corp_id, $shots_id)) {
            throw new RuntimeException('无效充电枪ID', [], RuntimeException::CodeBusinessException);
        }
    }
}