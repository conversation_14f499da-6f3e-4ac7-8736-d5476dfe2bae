<?php
/** @noinspection PhpUnused */

namespace app\common\logic\admin;

use app\common\lib\charging\applet\request\StartChargeRequest;
use app\common\lib\charging\applet\StartCharge;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\Order;
use app\common\model\RestartOrderLog;
use Respect\Validation\Validator as v;

class DebugToolRestartOrder
{
    public function run(int $user_id, array $params): array
    {
        $verifyData = $this->verifyParams($params);

        $orderData = app(Order::class)
            ->getOrder($verifyData['transaction_serial_number'], ['user_id', 'shot_id']);
        if (empty($orderData)) {
            throw new RuntimeException('无效订单', [], RuntimeException::CodeBusinessException);
        }

        app(RestartOrderLog::class)->addLog(
            $verifyData['transaction_serial_number'],
            $orderData['user_id'],
            $user_id,
            get_ip(),
            $verifyData['power_limit']
        );

        $request = new StartChargeRequest([
            'user_id' => $orderData['user_id'],
            'user_token' => "",
            'shots_id' => $orderData['shot_id'],
            'power_limit' => $verifyData['power_limit']
        ]);

        return (new StartCharge($request))->run();
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::debug_tool([
            'transaction_serial_number',
            'power_limit',
        ]));
    }


}