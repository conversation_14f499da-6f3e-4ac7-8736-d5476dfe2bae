<?php
/** @noinspection PhpUnused */

namespace app\common\logic\admin;

use app\common\lib\exception\RuntimeException;
use app\common\model\AdminUsers;
use app\common\model\WorkOrder as WorkOrderModel;
use app\common\model\WorkOrderProcessingRecords;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class WorkOrderAutoAssignHandler extends WorkOrderAssignHandler
{

    /**
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function run(): array
    {
        // 检查指派的处理人员
        $AdminUsersModel = new AdminUsers();
        $adminUserData = $AdminUsersModel->where('id', '=', $this->request->admin_user_id)
            ->field(['name'])
            ->find();
        if (!empty($adminUserData)) {
            $this->newAdminUserData = $adminUserData->toArray();
        }
        if (empty($this->newAdminUserData)) {
            throw new RuntimeException('无效的被指派的处理人员ID', [], RuntimeException::CodeBusinessException);
        }

        // 记录工单历史
        $this->addProcessingRecord();

        // 绑定工单与处理人员关系
        $this->bindWordOrderAndHandlerPeopleRelation();

        // 更新工单状态
        $WorkOrderModel = new WorkOrderModel();
        $WorkOrderModel->updateStatus(
            $this->request->work_order_id,
            WorkOrderModel::StatusSolving
        );

        return [];
    }

    /**
     *
     * @return void
     */
    protected function addProcessingRecord(): void
    {
        $WorkOrderProcessingRecordsModel = new WorkOrderProcessingRecords();

        $message = sprintf('【系统】自动将工单分配给 %s 处理', $this->newAdminUserData['name']);

        $WorkOrderProcessingRecordsModel->addRecord(
            $this->request->work_order_id,
            WorkOrderProcessingRecords::UserTypeSystem,
            0,
            $message
        );
    }
}