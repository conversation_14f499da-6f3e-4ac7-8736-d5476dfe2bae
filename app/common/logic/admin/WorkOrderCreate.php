<?php
/** @noinspection PhpUnused */

namespace app\common\logic\admin;

use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\entity\WorkOrderCreateRequest;
use app\common\model\WorkOrder;
use app\common\model\WorkOrder as WorkOrderModel;
use app\common\model\WorkOrderExtraData;
use app\common\model\WorkOrderField as WorkOrderFieldModel;
use app\common\model\WorkOrderProcessingRecords;
use app\common\model\WorkOrderRelation;
use app\common\model\WorkOrderTemplateField;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class WorkOrderCreate
{
    protected WorkOrderCreateRequest $request;
    protected bool $isVerifyExtraFieldData = true;

    public function __construct(WorkOrderCreateRequest $request)
    {
        $this->request = $request;
    }

    public function setIsVerifyExtraFieldData($isVerify): static
    {
        $this->isVerifyExtraFieldData = $isVerify;
        return $this;
    }

    /**
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function run(): array
    {
        // 验证额外的字段
        if ($this->isVerifyExtraFieldData) $this->verifyExtraFieldData();

        // 创建工单
        $newId = $this->createWorkOrder();

        // 记录额外的字段数据
        $this->recordExtraFieldData($newId);

        // 记录所属关系
        $this->recordAffiliation($newId);

        // 记录处理记录
        $this->recordProcessingRecord($newId);

        // 通知
//            $notice_admin_user_id = (new WorkOrderTemplateRelation())->getDefaultNoticeAdminUserID(
//                $verifyData['template_id'],
//                $verifyData['corp_id'],
//                $verifyData['station_id']
//            );
//            if (!is_null($notice_admin_user_id)) {
        // todo: 添加通知 和 推送第三方通知
//            }


        return [
            'new_id' => $newId
        ];
    }

    /**
     * 记录处理记录
     *
     * @param string $work_order_id
     * @return void
     */
    protected function recordProcessingRecord(string $work_order_id): void
    {
        // 添加工单历史记录

        if ($this->request->source === WorkOrder::SourceAdminCreate) {
            $message = WorkOrderProcessingRecords::createWorkOrderMessage($this->request->create_user_name);
        } else if ($this->request->source === WorkOrder::SourceAlarmGeneration) {
            $message = WorkOrderProcessingRecords::alarmAutoCreateWorkOrderMessage();
        } else {
            $message = WorkOrderProcessingRecords::commonUserCreateWorkOrderMessage($this->request->create_user_id);
        }

        $WorkOrderProcessingRecordsModel = new WorkOrderProcessingRecords();
        $WorkOrderProcessingRecordsModel->addRecord(
            $work_order_id,
            WorkOrderProcessingRecords::UserTypeSystem,
            0,
            $message
        );
    }


    /**
     * 记录所属关系
     *
     * @param string $work_order_id
     * @return void
     */
    protected function recordAffiliation(string $work_order_id): void
    {
        $result = (new WorkOrderRelation())->addRelation(
            $work_order_id,
            $this->request->corp_id,
            $this->request->station_id,
            $this->request->device_type,
            $this->request->device_id
        );
        if (empty($result)) {
            throw new RuntimeException('创建失败', [], RuntimeException::CodeDatabaseException);
        }
    }

    /**
     * 记录额外的字段数据
     *
     * @param string $work_order_id
     * @return void
     */
    protected function recordExtraFieldData(string $work_order_id): void
    {
        $WorkOrderExtraData = new WorkOrderExtraData();
        $result = $WorkOrderExtraData->addExtraData($work_order_id, $this->request->extra_field_data);
        if (empty($result)) {
            throw new RuntimeException('创建失败', [], RuntimeException::CodeDatabaseException);
        }
    }

    protected function createWorkOrder(): string
    {
        $WorkOrderModel = new WorkOrderModel();
        $newId = $WorkOrderModel->addWorkOrder(
            $this->request->title,
            $this->request->priority,
            $this->request->source,
            $this->request->template_id,
            $this->request->create_user_type,
            $this->request->create_user_id,
        );
        if (empty($newId)) {
            throw new RuntimeException('创建失败', [], RuntimeException::CodeDatabaseException);
        }
        return $newId;
    }

    /**
     * 验证自定义字段的传递
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function verifyExtraFieldData(): void
    {
        $fields = (new WorkOrderTemplateField())->getFields($this->request->template_id);
        foreach ($fields as $field) {
            if ($field['is_require'] === WorkOrderFieldModel::IsRequireYes) {
                if (empty($this->request->extra_field_data[$field['key']])) {
                    throw new RuntimeException(sprintf('字段"%s"是必填项', $field['name']), [], RuntimeException::CodeBusinessException);
                }
                if (in_array($field['type'], [
                    WorkOrderFieldModel::TypeSingleLineText,
                    WorkOrderFieldModel::TypeMultilineText,
                    WorkOrderFieldModel::TypeDate,
                    WorkOrderFieldModel::TypeImage
                ])
                ) {
                    // 验证是否是字符串
                    if (is_string($this->request->extra_field_data[$field['key']]) === false) {
                        throw new RuntimeException(sprintf('字段 %s 必须传递字符串类型的数据', $field['name']), [], RuntimeException::CodeBusinessException);
                    }
                } else if ($field['type'] === WorkOrderFieldModel::TypeNumber) {
                    // 验证是否是数字
                    if (!is_numeric($this->request->extra_field_data[$field['key']])) {
                        throw new RuntimeException(sprintf('字段 %s 必须传递数字类型的数据', $field['name']), [], RuntimeException::CodeBusinessException);
                    }
                } else if ($field['type'] === WorkOrderFieldModel::TypeSelect) {
                    // 验证传递的值是否包含在选项列表中
                    $options = json_decode($field['options'], true);
                    $values = array_column($options, 'value');
                    if (in_array($this->request->extra_field_data[$field['key']], $values) === false) {
                        throw new RuntimeException(sprintf('字段 %s 的值无效', $field['name']), [], RuntimeException::CodeBusinessException);
                    }
                } else if (WorkOrderFieldModel::TypeMultipleSelect === $field['type']) {
                    $options = json_decode($field['options'], true);
                    $values = array_column($options, 'value');
                    foreach ($this->request->extra_field_data[$field['key']] as $value) {
                        if (in_array($value, $values) === false) {
                            throw new RuntimeException(sprintf('字段 %s 的存在无效值: %s', $field['name'], $value), [], RuntimeException::CodeBusinessException);
                        }
                    }
                }
            }
        }
    }
}