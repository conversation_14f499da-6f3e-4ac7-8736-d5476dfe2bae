<?php
/** @noinspection PhpUnused */

namespace app\common\logic\admin;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\entity\WorkOrderAssignHandlerRequest;
use app\common\model\AdminUsers;
use app\common\model\WorkOrder as WorkOrderModel;
use app\common\model\WorkOrderProcessingRecords;
use app\common\model\WorkOrderResponsible;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class WorkOrderAssignHandler
{
    protected WorkOrderAssignHandlerRequest $request;
    protected ?AdminLoginUser $loginUser;
    protected ?array $workOrderData;
    protected array $newAdminUserData;

    protected ?int $oldAdminUserId = null;

    protected bool $isVerify = true;

    public function __construct(WorkOrderAssignHandlerRequest $request, ?AdminLoginUser $loginUser = null)
    {
        $this->request = $request;
        $this->loginUser = $loginUser;
    }

    public function setIsVerify(bool $isVerify): static
    {
        $this->isVerify = $isVerify;

        return $this;
    }


    /**
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function run(): array
    {
        // 检查工单状态
        $WorkOrderModel = new WorkOrderModel();
        $this->workOrderData = $WorkOrderModel->getInfo(
            $this->request->work_order_id,
            ['wo.status', 'wor.corp_id']
        );
        if ($this->isVerify) $this->verifyWorkOrderStatus();

        // 检查指派的处理人员
        $AdminUsersModel = new AdminUsers();
        $adminUserData = $AdminUsersModel->where('id', '=', $this->request->admin_user_id)
            ->field(['corp_id', 'name'])
            ->find();
        if (!empty($adminUserData)) {
            $this->newAdminUserData = $adminUserData->toArray();
        }
        if (empty($this->newAdminUserData)) {
            throw new RuntimeException('无效的被指派的处理人员ID', [], RuntimeException::CodeBusinessException);
        }
        if ($this->isVerify) $this->verifyHandlerPeople();

        // todo: 通知
        // todo: 添加通知 和 推送第三方通知

        // 记录工单历史
        $this->addProcessingRecord();

        // 绑定工单与处理人员关系
        $this->bindWordOrderAndHandlerPeopleRelation();

        // 更新工单状态
        if ($this->workOrderData['status'] === WorkOrderModel::StatusOpen) {
            $WorkOrderModel = new WorkOrderModel();
            $WorkOrderModel->updateStatus(
                $this->request->work_order_id,
                WorkOrderModel::StatusSolving
            );
        }

        return [];
    }

    /**
     * 验证工单状态
     *
     * @return void
     */
    protected function verifyWorkOrderStatus(): void
    {
        if (empty($this->workOrderData)) {
            throw new RuntimeException('无效的工单ID', [], RuntimeException::CodeBusinessException);
        } else if ($this->workOrderData['status'] === WorkOrderModel::StatusResolved) {
            throw new RuntimeException('订单已解决', [], RuntimeException::CodeBusinessException);
        } else if ($this->workOrderData['status'] === WorkOrderModel::StatusClosed) {
            throw new RuntimeException('订单已关闭', [], RuntimeException::CodeBusinessException);
        }
    }

    /**
     * 验证处理人
     *
     * @return void
     */
    protected function verifyHandlerPeople(): void
    {
        if ($this->loginUser->corp_id > 0 && $this->loginUser->pid === 0) {
            if ($this->workOrderData['corp_id'] !== $this->loginUser->corp_id) {
                throw new RuntimeException('无效的工单ID', [], RuntimeException::CodeBusinessException);
            }

            if ($this->loginUser->id !== $this->request->admin_user_id) {
                if ($this->newAdminUserData['corp_id'] !== $this->loginUser->corp_id) {
                    throw new RuntimeException('无效的被指派的处理人员ID', [], RuntimeException::CodeBusinessException);
                }
            }
        } else if ($this->loginUser->corp_id > 0) {
            if ($this->workOrderData['corp_id'] !== $this->loginUser->corp_id) {
                throw new RuntimeException('无效的工单ID', [], RuntimeException::CodeBusinessException);
            }

            if ($this->loginUser->id !== $this->request->admin_user_id) {
                throw new RuntimeException('无效的被指派的处理人员ID', [], RuntimeException::CodeBusinessException);
            }
        } else {
            if ($this->newAdminUserData['corp_id'] !== $this->workOrderData['corp_id']) {
                throw new RuntimeException('指派的人员与工单不属于同一个运营商', [], RuntimeException::CodeBusinessException);
            }
        }

        $WorkOrderResponsibleModel = new WorkOrderResponsible();
        $this->oldAdminUserId = $WorkOrderResponsibleModel->getWorkOrderActivateResponsibleId($this->request->work_order_id);

        if ($this->oldAdminUserId === $this->request->admin_user_id) {
            throw new RuntimeException('指派的工作人员已经是当前工单的责任人');
        }
    }

    /**
     *
     * @return void
     */
    protected function addProcessingRecord(): void
    {
        $WorkOrderProcessingRecordsModel = new WorkOrderProcessingRecords();
        $WorkOrderResponsibleModel = new WorkOrderResponsible();
        $AdminUsersModel = new AdminUsers();


        if (!is_null($this->oldAdminUserId)) {
            $WorkOrderResponsibleModel->updateWorkOrderActivateStatus(
                $this->request->work_order_id,
                $this->oldAdminUserId,
                WorkOrderResponsible::IsActivateNot
            );

            $admin_user_name = $AdminUsersModel->getAdminName($this->oldAdminUserId);

            $message = sprintf('【系统】%s 将工单的处理人从 %s 更换为 %s',
                    $this->loginUser->name, $admin_user_name, $this->newAdminUserData['name']);

        } else {

            $message = sprintf('【系统】%s 将工单分配给 %s 处理',
                $this->loginUser->name, $this->newAdminUserData['name']
            );
        }

        $WorkOrderProcessingRecordsModel->addRecord(
            $this->request->work_order_id,
            $WorkOrderProcessingRecordsModel::UserTypeAdminUser,
            $this->loginUser->id,
            $message
        );
    }

    /**
     * 绑定工单与处理人员关系
     *
     * @return void
     */
    protected function bindWordOrderAndHandlerPeopleRelation(): void
    {
        $WorkOrderResponsibleModel = new WorkOrderResponsible();
        $is = $WorkOrderResponsibleModel->where([
                ['work_order_id', '=', $this->request->work_order_id],
                ['admin_user_id', '=', $this->request->admin_user_id]
            ])->count() > 0;
        if ($is === true) {
            $WorkOrderResponsibleModel->updateWorkOrderActivateStatus(
                $this->request->work_order_id,
                $this->request->admin_user_id,
                WorkOrderResponsible::IsActivateYes
            );
        } else {
            $WorkOrderResponsibleModel->setResponsible(
                $this->request->admin_user_id,
                $this->request->work_order_id
            );
        }
    }
}