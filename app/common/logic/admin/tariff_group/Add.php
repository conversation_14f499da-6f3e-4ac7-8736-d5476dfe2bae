<?php

namespace app\common\logic\admin\tariff_group;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\TariffGroup;
use Respect\Validation\Validator as v;
use think\Request;

class Add
{
    protected AdminLoginUser $loginUser;
    protected Request $request;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    protected function verifyDataPermissions(AdminLoginUser $loginUser): void
    {
        if ($loginUser->corp_id === 0) {
            throw new RuntimeException('只允许运营商账号操作', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::tariff_group([
            'name',
            'sharp_fee',
            'peak_fee',
            'flat_fee',
            'valley_fee',
            'sharp_ser_fee',
            'peak_ser_fee',
            'flat_ser_fee',
            'valley_ser_fee',
            'loss_rate',
            'period_codes',
            'surcharge'
        ]));
    }


    protected function formatParams(array $params): array
    {
        $params['sharp_fee'] = decimal_to_int($params['sharp_fee']);
        $params['peak_fee'] = decimal_to_int($params['peak_fee']);
        $params['flat_fee'] = decimal_to_int($params['flat_fee']);
        $params['valley_fee'] = decimal_to_int($params['valley_fee']);
        $params['sharp_ser_fee'] = decimal_to_int($params['sharp_ser_fee']);
        $params['peak_ser_fee'] = decimal_to_int($params['peak_ser_fee']);
        $params['flat_ser_fee'] = decimal_to_int($params['flat_ser_fee']);
        $params['valley_ser_fee'] = decimal_to_int($params['valley_ser_fee']);
        $params['surcharge'] = decimal_to_int($params['surcharge']);

        $params['period_json_sum'] = period_json_add_fee($params, period_codes_to_period_rate_list($params['period_codes']));
        $params['period_json'] = period_json_add_fee($params, period_codes_to_period_rate_list_all($params['period_codes']));

        return $params;
    }

    protected function insertTariffGroup(AdminLoginUser $loginUser, array $formatParams): int
    {
        $insert = [
            'corp_id' => $loginUser->corp_id,
            'opt_id' => $loginUser->id,
            'create_time' => date('Y-m-d H:i:s'),
        ];

        $insert = array_merge($insert, $formatParams);

        $model = app(TariffGroup::class);
        return $model->insertGetId($insert);
    }

    public function run(): array
    {
        $this->verifyDataPermissions($this->loginUser);

        $post = $this->request->post();

        $post['period_codes'] = period_rate_list_to_period_codes($post['period_codes']);

        $params = $this->verifyParams($post);

        // 格式化参数
        $formatParams = $this->formatParams($params);

        $new_id = $this->insertTariffGroup($this->loginUser, $formatParams);

        if (empty($new_id)) {
            throw new RuntimeException('添加失败', [], RuntimeException::CodeBusinessException);
        }

        return [
            'new_id' => $new_id
        ];
    }
}