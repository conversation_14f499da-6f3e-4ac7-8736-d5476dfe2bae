<?php

namespace app\common\logic\admin\tariff_group;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\VerifyData;
use app\common\model\TariffGroup;
use Respect\Validation\Validator as v;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Request;

class TariffGroupList
{
    protected Request $request;
    protected AdminLoginUser $loginUser;

    public const SortFieldsID = 1; // 场站ID
    public const SortFieldsCreateTime = 2; // 创建时间
    public const SortFieldsOptions = [
        ['value' => self::SortFieldsID, 'label' => 'ID'],
        ['value' => self::SortFieldsCreateTime, 'label' => '创建时间'],
    ];
    public const SortFieldsMap = [
        self::SortFieldsID => 'tg.id',
        self::SortFieldsCreateTime => 'tg.create_time',
    ];

    // 排序方式
    public const SortTypeAsc = 2; // 升序
    public const SortTypeDesc = 1; // 降序
    public const SortTypeMap = [
        self::SortTypeAsc => 'ASC',
        self::SortTypeDesc => 'DESC'
    ];
    public const SortTypeOptions = [
        ['value' => self::SortTypeAsc, 'label' => '升序'],
        ['value' => self::SortTypeDesc, 'label' => '降序']
    ];

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    protected function getSortType(int $sort_type_option_value): string
    {
        return self::SortTypeMap[$sort_type_option_value] ?? self::SortTypeMap[self::SortTypeDesc];
    }

    protected function getSortField($option_value): string
    {
        return self::SortFieldsMap[$option_value] ?? self::SortFieldsMap[self::SortFieldsID];
    }

    public function sort_list_info(): array
    {
        return [
            'sort_field' => self::SortFieldsOptions[0]['value'],
            'sort_type' => self::SortTypeDesc,
            'sort_field_options' => self::SortFieldsOptions,
            'sort_type_options' => self::SortTypeOptions
        ];
    }

    protected function transformSortParams(array $params): array
    {
        $params['sort_field'] = $this->getSortField($params['sort_field']);
        $params['sort_type'] = $this->getSortType($params['sort_type']);

        return $params;
    }

    /**
     * 增加数据权限过滤条件
     *
     * @param AdminLoginUser $loginUser
     * @param array $params
     * @return array
     */
    protected function addDataPermissionsFilterConditions(AdminLoginUser $loginUser, array $params): array
    {
        if ($loginUser->corp_id > 0) {
            $params['filter_corp_id'] = $loginUser->corp_id;
        }

        return $params;
    }

    /**
     * 整理过滤条件
     *
     * @param array $filters
     * @return array[]
     */
    protected function arrangeFilters(array $filters): array
    {
        $new_filters = [];

        if (!empty($filters['filter_corp_id'])) {
            $new_filters[] = [
                'tg.corp_id', '=', $filters['filter_corp_id']
            ];
        }

        if (!empty($filters['filter_name'])) {
            $new_filters[] = [
                'tg.name', 'like', '%' . $filters['filter_name'] . '%'
            ];
        }


        return $new_filters;
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::tariff_group([
            'page',
            'limit',
            'sort_field',
            'sort_type',
            'filter_name',
        ]));
    }

    /**
     * @param array $filters
     * @param int $page
     * @param int $limit
     * @param string $sort_field
     * @param string $sort_type
     * @return array
     * @throws DbException
     */
    protected function queryListData(array $filters, int $page, int $limit, string $sort_field, string $sort_type): array
    {
        $fields = [
            'tg.id', 'tg.name', 'tg.sharp_fee', 'tg.peak_fee', 'tg.flat_fee',
            'tg.valley_fee', 'tg.sharp_ser_fee', 'tg.peak_ser_fee',
            'tg.flat_ser_fee', 'tg.valley_ser_fee', 'tg.loss_rate', 'tg.period_codes',
        ];
        $model = app(TariffGroup::class);
        /**
         * @var Query $model
         */
        return $model->alias('tg')
            ->where($filters)
            ->field($fields)
            ->order($sort_field, $sort_type)
            ->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();
    }

    /**
     * @return array
     * @throws DbException
     */
    public function get_list_data(): array
    {
        // 验证参数
        $params = $this->verifyParams($this->request->post());

        // 转换排序参数
        $params = $this->transformSortParams($params);

        // 增加数据权限的过滤条件
        $params = $this->addDataPermissionsFilterConditions($this->loginUser, $params);

        // 整理过滤条件
        $filters = $this->arrangeFilters($params);

        return $this->queryListData($filters, $params['page'], $params['limit'], $params['sort_field'], $params['sort_type']);
    }

    protected function verifyOptionsParams(array $params): array
    {
        return v::input($params, VerifyData::tariff_group([
            'filter_phase_name'
        ]));
    }

    /**
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function get_options(): array
    {
        $params = $this->verifyOptionsParams($this->request->post());

        // 增加数据权限的过滤条件
        $params = $this->addDataPermissionsFilterConditions($this->loginUser, $params);

        // 整理过滤条件
        $filters = $this->arrangeFilters($params);

        return $this->queryOptionsData($filters);
    }

    /**
     * @param array $filters
     * @return array
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    protected function queryOptionsData(array $filters): array
    {
        $fields = [
            'c.id', 'c.name',
        ];
        $model = app(TariffGroup::class);
        /**
         * @var Query $model
         */
        return $model->alias('c')
            ->where($filters)
            ->field($fields)
            ->select()
            ->toArray();
    }


}