<?php

namespace app\common\logic\admin\tariff_group;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\model\Stations;
use app\common\model\TariffGroup;
use Respect\Validation\Validator as v;
use think\Request;

class Delete
{
    protected AdminLoginUser $loginUser;
    protected Request $request;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    protected function verifyOperationPermissions(AdminLoginUser $loginUser): void
    {
        if ($loginUser->corp_id === 0) {
            throw new RuntimeException('只允许运营商账号操作', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyDataPermissions(AdminLoginUser $loginUser, array $params): void
    {
        $model = app(TariffGroup::class);
        $where = [
            ['id', '=', $params['id']],
            ['corp_id', '=', $loginUser->corp_id]
        ];
        $count = $model->where($where)->count();
        if ($count === 0) {
            throw new RuntimeException('无效费率组ID', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::tariff_group([
            'id',
        ]));
    }


    protected function deleteTariffGroup(int $id): int
    {
        $model = app(TariffGroup::class);
        $model->where('id', '=', $id)->delete();
        return $model->getNumRows();
    }


    protected function queryBindStationName(int $id): ?string
    {
        $model = app(Stations::class);
        return $model->queryBindStationName($id);
    }

    protected function verifyBindStation(int $id): void
    {
        $station_name = $this->queryBindStationName($id);
        if (!empty($station_name)) {
            throw new RuntimeException(
                sprintf("已绑定充电场站：%s，请解除绑定后再删除。", $station_name),
                [],
                RuntimeException::CodeBusinessException
            );
        }
    }

    public function run(): array
    {
        $this->verifyOperationPermissions($this->loginUser);

        $params = $this->verifyParams($this->request->post());

        $this->verifyDataPermissions($this->loginUser, $params);

        // 验证费率是否已绑定场站
        $this->verifyBindStation($params['id']);

        $deleteRow = $this->deleteTariffGroup($params['id']);
        LogCollector::collectorRunLog(sprintf('$deleteRow = %d', $deleteRow));

        if (empty($deleteRow)) {
            throw new RuntimeException('删除失败', [], RuntimeException::CodeBusinessException);
        }

        return [];
    }
}