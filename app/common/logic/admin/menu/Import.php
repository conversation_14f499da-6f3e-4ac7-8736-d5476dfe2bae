<?php

namespace app\common\logic\admin\menu;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\log\LogCollector;
use app\common\model\AdminAuthGroupRules;
use app\common\model\AdminUsers;
use app\common\model\Menu;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use think\facade\Db;
use think\Request;

class Import
{
    protected Request $request;

    protected AdminLoginUser $loginUser;
    protected int $autoIncrement = 1;

    // 字段索引
    public const FieldIndexTitle = 0; // 标题
    public const FieldIndexPid = 1; // 父节点
    public const FieldIndexType = 2; // 类型 菜单 接口
    public const FieldIndexUrl = 3; // 链接
    public const FieldIndexSort = 4; // 序号
    public const FieldIndexIsShow = 5; // 是否显示
    public const FieldIndexIsOpen = 6; // 权限是否开放
    public const FieldIndexIcon = 7; // 图标
    public const FieldIndexAdminAuth = 8; // 管理员权限
    public const FieldIndexCorpAuth = 9; // 销售权限


    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    public function run(): array
    {
        $file = $this->request->file('file');
        $tmpFile = $file->getPath() . DIRECTORY_SEPARATOR . $file->getFilename();

        // 读取表格数据
        $table_data = $this->readTableData($tmpFile);

        $this->verifySameName($table_data);

        // 获取原始权限规则映射
        $originalAuthRulesMap = $this->getOriginalAuthRulesMap();

        // 解析表格数据
        [$newMenusData, $newAuthGroupRulesData] = $this->analyzeTableData($table_data, $originalAuthRulesMap);


        $delRow = $this->clearOldMenuData();
        LogCollector::collectorRunLog(sprintf('$delRow = %d', $delRow));

        $insertRow = $this->insertNewMenuData($newMenusData);
        LogCollector::collectorRunLog(sprintf('$insertRow = %d', $insertRow));

        $delRow = $this->clearOldAuthGroupRulesData();
        LogCollector::collectorRunLog(sprintf('$delRow = %d', $delRow));

        $insertRow = $this->insertNewAuthGroupRulesData($newAuthGroupRulesData);
        LogCollector::collectorRunLog(sprintf('$insertRow = %d', $insertRow));


        return [];
    }

    protected function getOriginalAuthRulesMap(): array
    {
        $model = app(AdminAuthGroupRules::class);
        $data = $model->field(['rule_name', 'group_id'])->select()->toArray();
        $map = [];
        // 映射每个接口下的权限组
        foreach ($data as $value) {
            if (!isset($map[$value['rule_name']])) {
                $map[$value['rule_name']] = [];
            }
            $map[$value['rule_name']][] = $value['group_id'];
        }
        return $map;
    }

    protected function clearOldAuthGroupRulesData(): int
    {
        $model = app(AdminAuthGroupRules::class);
        return Db::table($model->getTable())->delete(true);
    }

    protected function insertNewAuthGroupRulesData(array $newAuthGroupRulesData): int
    {
        $model = app(AdminAuthGroupRules::class);
        $model->insertAll($newAuthGroupRulesData);
        return $model->getNumRows();
    }

    protected function insertNewMenuData(array $newMenus): int
    {
        $model = app(Menu::class);
        $model->insertAll($newMenus);
        return $model->getNumRows();
    }

    protected function clearOldMenuData(): int
    {
        $model = app(Menu::class);
        return Db::table($model->getTable())->delete(true);
    }

    public function readTableData(string $filePath): array
    {
        $tmpFile = $filePath;
        $reader = new Xlsx();
        $reader->setReadDataOnly(true);
        $spreadsheet = $reader->load($tmpFile);

        $worksheet = $spreadsheet->getActiveSheet();
        return $worksheet->toArray();
    }

    public function verifySameName(array $table_data): void
    {
        $map = [];
        foreach ($table_data as $value) {
            if (!isset($map[$value[self::FieldIndexTitle]])) {
                $map[$value[self::FieldIndexTitle]] = 1;
            } else {
                throw new RuntimeException(sprintf('重复名称: %s', $value[self::FieldIndexTitle]), [], RuntimeException::CodeBusinessException);
            }
        }
    }

    protected function getAutoIncrementID(): int
    {
        return $this->autoIncrement++;
    }


    public function buildMenuIDMap(array $table_data): array
    {
        $map = [];
        foreach ($table_data as $row => $value) {
            if ($row === 0) continue;
            $map[$value[self::FieldIndexTitle]] = $this->getAutoIncrementID();
        }

        return $map;
    }

    public function getPid(array $titleToIDMap, ?string $pidTitle): int
    {
        if (is_null($pidTitle)) return 0;
        if (!isset($titleToIDMap[$pidTitle])) {
            throw new RuntimeException(sprintf('没有找到上级菜单: %s', $pidTitle), [], RuntimeException::CodeBusinessException);
        }
        return $titleToIDMap[$pidTitle];
    }

    public function buildMenuLevelMap(array $tableData): array
    {
        $map = [];
        $level = 1;
        foreach ($tableData as $row => $value) {
            if ($row === 0) continue;
            if (empty($value[self::FieldIndexPid])) {
                $map[$value[self::FieldIndexTitle]] = $level;
            }
        }
        $level++;

        while ($level <= 10) {
            foreach ($tableData as $row => $value) {
                if ($row === 0) continue;
                if (!isset($map[$value[self::FieldIndexTitle]]) && !empty($map[$value[self::FieldIndexPid]])) {
                    $map[$value[self::FieldIndexTitle]] = $map[$value[self::FieldIndexPid]] + 1;
                }
            }
            $level++;
        }

        if (count($map) < count($tableData) - 1) {
            throw new RuntimeException('菜单层级超过10', [], RuntimeException::CodeBusinessException);
        } else if (count($map) > count($tableData) - 1) {
            throw new RuntimeException('程序异常', [], RuntimeException::CodeBusinessException);
        }

        return $map;
    }

    protected function getLevel(array $levelMap, ?string $title): int
    {
        if (isset($levelMap[$title])) {
            return $levelMap[$title];
        }

        throw new RuntimeException(sprintf('菜单 %s 没有找到等级', $title), [], RuntimeException::CodeBusinessException);
    }


    public function analyzeTableData(array $table_data, array $originalAuthRulesMap): array
    {
        $levelMap = $this->buildMenuLevelMap($table_data);
        $idMap = $this->buildMenuIDMap($table_data);

        $menusData = [];
        $authGroupRules = [];


        foreach ($table_data as $row => $value) {
            if ($row === 0) continue;
            $menuData = [
                'id' => $idMap[$value[self::FieldIndexTitle]],
                'title' => str_replace("：",":",$value[self::FieldIndexTitle]),// 将中文冒号替换为英文冒号
                'pid' => $this->getPid($idMap, $value[self::FieldIndexPid]),
                'level' => $this->getLevel($levelMap, $value[self::FieldIndexTitle]),
                'icon_url' => $value[self::FieldIndexIcon] ?? '',
                'url_value' => '',
                'api_url' => '',
                'sort' => $value[self::FieldIndexSort],
                'menu_state' => $value[self::FieldIndexIsShow] == '显示' ? 1 : 2,
                'state' => 1,
                'auth_open' => $value[self::FieldIndexIsOpen] == '私有' ? 1 : 2,
                'opt_id' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];

            $type = $value[self::FieldIndexType];
            if ($type === '菜单') {
                $menuData['url_value'] = $value[self::FieldIndexUrl] ?? '';
                $rule_name = sprintf('menu:%s', $menuData['id']);
            } else {
                $menuData['api_url'] = $value[self::FieldIndexUrl] ?? '';
                $rule_name = sprintf('api:%s', $menuData['id']);
            }
            $menuData['rule_name'] = $rule_name;

            // 检查原始权限映射中是否存在此规则
            if (isset($originalAuthRulesMap[$rule_name])) {
                // 为原本拥有该权限的 group_id 添加规则
                foreach ($originalAuthRulesMap[$rule_name] as $groupId) {
                        $authGroupRules[] = [
                        'group_id' => $groupId,
                        'rule_name' => $rule_name
                    ];
                }
            }

            $menusData[] = $menuData;
        }

        return [$menusData, $authGroupRules];
    }
}