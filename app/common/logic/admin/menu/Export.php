<?php

namespace app\common\logic\admin\menu;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\model\AdminAuthGroupRules;
use app\common\model\AdminUsers;
use app\common\model\Menu;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use think\Request;

class Export
{
    protected Request $request;

    protected AdminLoginUser $loginUser;

    // 字段索引
    public const FieldIndexTitle = 'A'; // 标题
    public const FieldIndexPid = 'B'; // 父节点
    public const FieldIndexType = 'C'; // 类型 菜单 接口
    public const FieldIndexUrl = 'D'; // 链接
    public const FieldIndexSort = 'E'; // 序号
    public const FieldIndexIsShow = 'F'; // 是否显示
    public const FieldIndexIsOpen = 'G'; // 权限是否开放
    public const FieldIndexIcon = 'H'; // 图标

    // 类型
    public const TypeMenu = '菜单';
    public const TypeApi = '接口';

    // 是否显示
    public const IsShowTrue = '显示';
    public const IsShowFalse = '隐藏';

    // 权限是否开放
    public const IsOpenTrue = '开放';
    public const IsOpenFalse = '私有';

    // 权限
    public const AuthGroupAuthOpen = '开放';
    public const AuthGroupAuthClose = '关闭';

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    protected function queryMenuMap(): array
    {
        $model = app(Menu::class);
        return $model->column('*', 'id');
    }

    protected function getType(string $title): string
    {
        if (str_contains($title, '接口:')) {
            return '接口';
        } else {
            return '菜单';
        }
    }

    protected function getIsShowMsg(int $menu_state): string
    {
        return $menu_state === 1 ? '显示' : '隐藏';
    }

    protected function getIsOpenMsg(int $auth_open): string
    {
        return $auth_open === 1 ? '私有' : '开放';
    }

    protected function organizeData(array $menuMap, array $authGroupRuleMap): array
    {
        $new = [];

        foreach ($menuMap as $id => $value) {
            $type = $this->getType($value['title']);
            if ($type === '接口') {
                $url = $value['api_url'];
            } else {
                $url = $value['url_value'];
            }
            if (!empty($value['pid']) && !isset($menuMap[$value['pid']]['title'])) {
                continue;
            }
            $new[] = [
                'title' => $value['title'],
                'pid_title' => empty($value['pid']) ? '' : $menuMap[$value['pid']]['title'],
                'type' => $type,
                'url' => $url,
                'sort' => $value['sort'],
                'is_show' => $this->getIsShowMsg($value['menu_state']),
                'is_open' => $this->getIsOpenMsg($value['auth_open']),
                'icon' => $value['icon_url'],
            ];
        }

        return $new;
    }

    public function run(): array
    {
        $menuMap = $this->queryMenuMap();
        $ruleMap = $this->queryAuthGroupRulesMap();
        $tableData = $this->organizeData($menuMap, $ruleMap);

        $fileUrl = $this->export($tableData);

        return [
            'url' => $fileUrl
        ];
    }

    public function queryAuthGroupRulesMap(): array
    {
        // admin_auth_group_rules
        $model = app(AdminAuthGroupRules::class);
        $data = $model->field(['rule_name', 'group_id'])->select()->toArray();

        $map = [];
        foreach ($data as $value) {
            $map[$value['group_id']][] = $value['rule_name'];
        }

        return $map;
    }

    protected function getAuthMsg(array $rulesMap, int $groupID, string $ruleName): string
    {
        if (isset($rulesMap[$groupID]) && in_array($ruleName, $rulesMap[$groupID])) {
            return '开放';
        }

        return '关闭';
    }


    protected function export(array $tableData): string
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $this->setHeader($sheet);
        $this->setContent($sheet, $tableData);
        return $this->exportFile($spreadsheet);
    }

    protected function setHeader(Worksheet $sheet): void
    {
        $header = ['名称', '父节点', '类型', '链接', '序号', '是否显示', '开放权限', '图标'];

        // 设置列宽度
        $sheet->getColumnDimension('A')->setAutoSize(true);
        $sheet->getColumnDimension('B')->setAutoSize(true);
        $sheet->getColumnDimension('C')->setAutoSize(true);
        $sheet->getColumnDimension('D')->setAutoSize(true);
        $sheet->getColumnDimension('E')->setAutoSize(true);
        $sheet->getColumnDimension('F')->setAutoSize(true);
        $sheet->getColumnDimension('G')->setAutoSize(true);
        $sheet->getColumnDimension('H')->setAutoSize(true);

        foreach ($header as $k => $h) {
            $column = Coordinate::stringFromColumnIndex($k + 1); // 将索引转换为列字母
            $coordinate = $column . '1'; // 列字母和行号的组合
            $sheet->setCellValue($coordinate, $h);
        }
    }

    protected function setContent(Worksheet $sheet, array $table_data): void
    {
        $row = 2;
        foreach ($table_data as $v) {
            $sheet->getRowDimension($row);
            $sheet->setCellValueExplicit('A' . $row, $v['title'], DataType::TYPE_STRING)
                ->setCellValue('B' . $row, $v['pid_title'])
                ->setCellValue('C' . $row, $v['type'])
                ->setCellValue('D' . $row, $v['url'])
                ->setCellValue('E' . $row, $v['sort'])
                ->setCellValue('F' . $row, $v['is_show'])
                ->setCellValue('G' . $row, $v['is_open'])
                ->setCellValue('H' . $row, $v['icon']);

            $row++;
        }

        // 设置所有单元格的垂直居中和水平居中
        $sheet->getStyle('A1:H' . ($row - 1))->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle('A1:H' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    }

    protected function exportFile(Spreadsheet $spreadsheet): string
    {
        $Xlsx = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);

        $dir = 'excel/xlsx/';
        $dir2 = public_path() . '/excel/xlsx/';
        $file_name = '菜单数据' . date('Ymd_His') . '.xlsx';
        $file = public_path() . $dir . $file_name;
        if (!is_dir($dir2)) mkdir($dir2, 0777, true);
        $url = config('my.host') . $dir . $file_name;
        $Xlsx->save($file);

        return $url;
    }
}