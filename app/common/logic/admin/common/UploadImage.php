<?php

namespace app\common\logic\admin\common;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\log\LogCollector;
use app\common\model\File as FileModel;
use think\facade\Filesystem;
use think\file\UploadedFile;
use think\Request;

class UploadImage
{
    protected Request $request;
    protected AdminLoginUser $loginUser;

    public function __construct(Request $request, AdminLoginUser $loginUser)
    {
        $this->request = $request;
        $this->loginUser = $loginUser;
    }

    public function run(): array
    {
        $image = $this->request->file('file');

        // 验证图片
        $this->verifyImage($image);

        // 保存图片
        $imageInfo = $this->saveImage($image);

        // 记录数据库
        $image_id = $this->recordDatabase($imageInfo, $this->loginUser->id);

        // 返回应答
        return $this->getResponse($imageInfo, $this->loginUser->id, $image_id);
    }

    protected function getResponse(array $imageInfo, int $user_id, int $image_id): array
    {
        return array_merge($imageInfo, [
            'id' => $image_id,
            'user_id' => $user_id
        ]);
    }

    protected function recordDatabase(array $image_info, int $user_id): int
    {
        $insert = [
            'name' => $image_info['name'],
            'size' => $image_info['size'],
            'md5' => $image_info['md5'],
            'sha1' => $image_info['sha1'],
            'type' => $image_info['type'],
            'format' => $image_info['format'],
            'url' => $image_info['url'],
            'user_id' => $user_id,
        ];

        return (new FileModel)->insertGetId($insert);
    }

    protected function saveImage(UploadedFile $image): array
    {
        $file_size = $image->getSize();
        $md5 = $image->md5();
        $sha1 = $image->sha1();
        $type = $image->getMime();
        $name = $image->getOriginalName();
        $format = $image->extension();

        $save_name = Filesystem::disk('public')->putFile('img', $image);

        if ($save_name) {
            $file_url = 'storage/' . str_replace('\\', '/', $save_name);
            return [
                'name' => $name,
                'size' => $file_size,
                'md5' => $md5,
                'sha1' => $sha1,
                'type' => $type,
                'format' => $format,
                'url' => $file_url,
            ];
        }
        throw new RuntimeException('保存失败', [], RuntimeException::CodeServiceException);
    }

    protected function verifyImage(UploadedFile $image): ?string
    {
        // 验证图片类型
        if (!in_array($image->getOriginalMime(), [
            'image/jpeg', 'image/png'
        ])) {
            throw new RuntimeException('图片需要是png或jpg类型的', [], RuntimeException::CodeBusinessException);
        }

        // 文件大小不超过6MB
        if ($image->getSize() > 6291456) {
            throw new RuntimeException('文件大小不超过6MB', [], RuntimeException::CodeBusinessException);
        }

        $tmpPath = $image->getPath() . DIRECTORY_SEPARATOR . $image->getFilename();

        // 验证图片是否含有恶意代码
        $content = file_get_contents($tmpPath);
        if (str_contains($content, "<?php")) {
            LogCollector::collectorRunLog('发现有人上传的图片含有恶意代码', LogCollector::LevelError);
            throw new RuntimeException('图片已损坏无法使用', [], RuntimeException::CodeBusinessException);
        }

        return $tmpPath;
    }


}