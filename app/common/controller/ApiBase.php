<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\common\controller;

use app\BaseController;
use app\common\controller\entity\Response;
use app\common\lib\exception\RuntimeException;
use app\common\lib\ExceptionLogCollector;
use app\common\log\LogCollector;
use Closure;
use Predis\Client;
use Respect\Validation\Exceptions\ValidationException;
use think\facade\Db;
use think\response\Json;
use Throwable;

class ApiBase extends BaseController
{

    /**
     * 失败返回
     * @param array $data
     * @param ?string $msg
     * @param ?int $code
     * @param bool $is_open_exception_catch
     * @return Json|Response
     */
    protected function res_error($data = [], ?string $msg = '失败', ?int $code = 100, bool $is_open_exception_catch = false): Json|Response
    {
        if (is_null($code)) $code = 100;
        if (is_null($msg)) $msg = '失败';
        if (is_null($data)) $data = [];
        if ($is_open_exception_catch === true) return new Response($code, $msg, $data);
        return json(['code' => $code, 'msg' => $msg, 'data' => $data]);
    }

    /**
     * 成功返回
     * @param array $data
     * @param ?string $msg
     * @param ?int $code
     * @param bool $is_open_exception_catch
     * @return Json|Response
     */
    protected function res_success($data = [], ?string $msg = '成功', ?int $code = 200, bool $is_open_exception_catch = false): Json|Response
    {
        if (is_null($code)) $code = 200;
        if (is_null($msg)) $msg = '成功';
        if (is_null($data)) $data = [];
        if ($is_open_exception_catch === true) return new Response($code, $msg, $data);
        return json(['code' => $code, 'msg' => $msg, 'data' => $data]);
    }

    /**
     * 警告返回
     * @param array $data
     * @param ?string $msg
     * @param ?int $code
     * @param bool $is_open_exception_catch
     * @return Json|Response
     */
    protected function res_warning($data = [], ?string $msg = '警告', ?int $code = 300, bool $is_open_exception_catch = false): Json|Response
    {
        if (is_null($code)) $code = 300;
        if (is_null($msg)) $msg = '警告';
        if (is_null($data)) $data = [];
        if ($is_open_exception_catch === true) return new Response($code, $msg, $data);
        return json(['code' => $code, 'msg' => $msg, 'data' => $data]);
    }


    public const SetSuccess = 1;
    public const SetFailed = 0;

    protected function lock(string $lockName): bool
    {
        $redisConfig = config('lock.redis');
        $lockFailCount = config('lock.lock_fail_count');
        $failedCount = 0;
        $redis = new Client($redisConfig);
        while (true) {
            $result = $redis->setnx($lockName, 1);
            if ($result === self::SetFailed) {
                $failedCount++;
                if ($failedCount >= $lockFailCount) {
                    LogCollector::collectorRunLog(sprintf('%s - 连续%d次获取锁失败，结束当前请求。', $lockName, $failedCount));
                    return false;
                }
                LogCollector::collectorRunLog(sprintf('%s - 获取锁失败，等待100毫秒。', $lockName));
                usleep(100 * 1000);
            } else {
                LogCollector::collectorRunLog(sprintf('%s - 获取锁成功。', $lockName));
                break;
            }
        }
        // 添加有效时长
        $redis->expire($lockName, config('lock.expire', 10));
        return true;
    }

    protected function unlock(string $lockName): void
    {
        $redisConfig = config('lock.redis');
        $redis = new Client($redisConfig);
        $redis->del($lockName);
        LogCollector::collectorRunLog(sprintf('%s - 释放锁成功。', $lockName));
    }


    /**
     * 开启异常捕捉
     *
     * @param Closure $business 业务逻辑处理
     * @param bool $isStartTrans 是否开启数据库事务
     * @param ?string $lockName 锁的键名
     * @return Json
     */
    protected function openExceptionCatch(Closure $business, bool $isStartTrans = false, ?string $lockName = null): Json
    {
        try {
            if (!empty($lockName)) {
                $lockResult = $this->lock($lockName);
                if ($lockResult === false) {
                    return $this->res_error([], '服务异常请稍后重试');
                }
            }

            if ($isStartTrans === true) Db::startTrans();

            $response = $business();

            if ($isStartTrans === true) Db::commit();

            if (!empty($lockName)) $this->unlock($lockName);

            if (is_null($response)) $response = [];
            if ($response instanceof Response) {
                return $this->res_success($response->data, $response->msg, $response->code);
            }
            return $this->res_success($response);
        } catch (ValidationException $e) {

            if ($isStartTrans === true) Db::rollback();

            if (!empty($lockName)) $this->unlock($lockName);

            // 参数传递异常
            return $this->res_error([], $e->getMessage());
        } catch (RuntimeException $e) {

            if ($isStartTrans === true) Db::rollback();

            if (!empty($lockName)) $this->unlock($lockName);

            // 业务异常
            if ($e->getCode() === RuntimeException::CodeBusinessException) {
                return $this->res_error($e->getContext(), $e->getMessage());
                // 业务警告
            } else if ($e->getCode() === RuntimeException::CodeBusinessWarning) {
                return $this->res_warning($e->getContext(), $e->getMessage());
                // 返回具体的异常码
            } elseif ($e->getCode() > 4000) {
                return $this->res_error($e->getContext(), $e->getMessage(), $e->getCode());
                // 服务异常
            } else {
                ExceptionLogCollector::collect($e);
                return $this->res_error($e->getContext(), '处理异常：' . $e->getMessage());
            }
        } catch (Throwable $e) {

            if ($isStartTrans === true) Db::rollback();

            if (!empty($lockName)) $this->unlock($lockName);

            // 其他服务异常
            ExceptionLogCollector::collect($e);
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

}