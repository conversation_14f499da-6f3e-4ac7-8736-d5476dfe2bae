<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\cache\base;

// 充电过程中的上下文
class ChargingContextData
{
    public string $token;
    public int $user_id;
    public string $type;
    public string $order_id;
    public int $corp_id;
    public int $station_id;
    public int $shot_id;

    public function __construct(string|array $data)
    {
        if (is_string($data)) {
            $data = json_decode($data, true);
        }
        foreach ($data as $key => $value) {
            if (property_exists($this, $key)) {
                $this->{$key} = $value;
            }
        }
    }

    public function toArray(): array
    {
        $result = [];
        foreach ($this as $key => $value) {
            $result[$key] = $value;
        }
        return $result;
    }

    public function toJsonString(): string
    {
        return json_encode_cn($this->toArray());
    }
}