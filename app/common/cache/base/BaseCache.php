<?php
/** @noinspection PhpUnused */
/** @noinspection PhpMultipleClassDeclarationsInspection */

namespace app\common\cache\base;

use app\common\new_queue\entity\RedisConfig;
use RedisClient\ClientFactory;
use RedisClient\RedisClient;

class BaseCache
{
    protected ?RedisClient $redisClient = null;

    protected function getRedisConfig():RedisConfig
    {
        $config = config('my.redis');
        $redisConfig = new RedisConfig();
        $redisConfig->server = sprintf("%s:%s", $config['host'], $config['port']);
        $redisConfig->timeout = $config['timeout'];
        $redisConfig->database = $config['database'];
        if (!empty($config['password'])) {
            $redisConfig->password = $config['password'];
        }
        $redisConfig->version = '6.0.0';

        return $redisConfig;
    }

    protected function getRedis(): RedisClient
    {
        if ($this->redisClient === null) {
            $this->redisClient = ClientFactory::create($this->getRedisConfig()->toArray());
        }
        return $this->redisClient;
    }
}