<?php
/** @noinspection PhpUnused */

namespace app\common\cache\redis;

use app\common\cache\base\BaseCache;

// 图形验证码
class ImageVerificationCodeCache extends BaseCache
{
    protected function getKey(string $id): string
    {
        return sprintf('img_code:%s', $id);
    }

    public function save(string $id, string $verification_code): bool
    {
        $key = $this->getKey($id);
        return $this->getRedis()->setex($key, 600, $verification_code);
    }

    public function get(string $id): ?string
    {
        return $this->getRedis()->get($this->getKey($id));
    }
}
