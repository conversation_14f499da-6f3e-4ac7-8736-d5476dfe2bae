<?php

namespace app\common\cache\redis;

use app\common\cache\base\ChargingContextData;
use Predis\Client as Predis;
use Predis\Response\Status;

class ChargingContext
{
    private static ?Predis $redisInstance = null;
    private static ?ChargingContext $instance = null;

    private function __construct()
    {
        self::$redisInstance = new Predis(config('my.redis'));
    }

    public static function __make(): static
    {
        if (is_null(static::$instance)) {
            static::$instance = new static();
        }

        return static::$instance;
    }

    /**
     * 获取Key
     *
     * @param string $order_id 订单ID
     * @return string Key
     */
    protected static function getKey(string $order_id): string
    {
        return sprintf('charging:context:%s', $order_id);
    }

    public function saveData(string $order_id, ChargingContextData $context): Status
    {
        return self::$redisInstance->setex(
            self::getKey($order_id),
            864000,
            $context->toJsonString()
        );
    }

    public function getData(string $order_id): ?ChargingContextData
    {
        $data = self::$redisInstance->get(
            self::getKey($order_id)
        );
        if (empty($data)) {
            return null;
        }

        return new ChargingContextData($data);
    }


}