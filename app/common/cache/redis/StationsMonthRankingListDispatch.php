<?php
/** @noinspection PhpUnused */

namespace app\common\cache\redis;

// 充电站的排行榜的调度器
use app\common\model\Stations;

class StationsMonthRankingListDispatch
{
    // 类型
    public const TYPE_CHARGE_INCOME = 'charge_income'; // 营业额(充电收入)
    public const TYPE_ELECTRICITY = 'electricity'; // 充电量
    public const TYPE_ORDER_COUNT = 'order_count'; // 订单量
    public const TYPE_UTILIZATION_RATE = 'utilization_rate'; // 利用率

    /**
     * 订单正常结束回调
     *
     * @param int $corp_id 运营商ID
     * @param int $station_id 充电桩ID
     * @param int $consumption_amount 消费金额
     * @param int $total_charge 本次充电消耗的总电量
     * @return bool
     */
    public function ChargeOrderNormalEndCallback(int $corp_id, int $station_id, int $consumption_amount, int $total_charge): bool
    {
        // 排行榜变动
        $this->increaseScore($corp_id, $station_id, self::TYPE_CHARGE_INCOME, $consumption_amount);
        $this->increaseScore($corp_id, $station_id, self::TYPE_ORDER_COUNT, 1);
        $this->increaseScore($corp_id, $station_id, self::TYPE_ELECTRICITY, $total_charge);
        // TODO: 缺少利用率的排行榜变动

        return true;
    }

    /**
     * 订单异常结束回调
     *
     * @param int $corp_id 运营商ID
     * @param int $station_id 充电桩ID
     * @param int $consumption_amount 消费金额
     * @param int $total_charge 本次充电消耗的总电量
     * @return bool
     */
    public function ChargeOrderAbnormalEndCallback(int $corp_id, int $station_id, int $consumption_amount, int $total_charge): bool
    {
        $this->increaseScore($corp_id, $station_id, self::TYPE_ORDER_COUNT, 1);
        if ($consumption_amount > 0) {
            $this->increaseScore($corp_id, $station_id, self::TYPE_CHARGE_INCOME, $consumption_amount);
        }

        if ($total_charge > 0) {
            $this->increaseScore($corp_id, $station_id, self::TYPE_ELECTRICITY, $total_charge);
        }

        return true;
    }


    /**
     * 增加分数
     *
     * @param int $crop_id 运营商ID
     * @param int $station_id 充电桩ID
     * @param string $type 排行榜类型
     * @param int $score 增加的分数
     * @return bool
     */
    public function increaseScore(int $crop_id, int $station_id, string $type, int $score): bool
    {
        if ($score <= 0) return 0;

        $CorpStationsRankingList = app(CorpStationsRankingList::class);
        $CorpStationsRankingList->increaseScore($crop_id, $station_id, $type, $score);
        $StationsRankingList = app(StationsRankingList::class);
        $StationsRankingList->increaseScore($station_id, $type, $score);

        return true;
    }

    /**
     * 减少分数
     *
     * @param int $crop_id 运营商ID
     * @param int $station_id 充电桩ID
     * @param int $score 增加的分数
     * @param string $type 排行榜类型
     * @return bool
     */
    public function reduceScore(int $crop_id, int $station_id, int $score, string $type): bool
    {
        if ($score <= 0) return 0;

        $CorpStationsRankingList = app(CorpStationsRankingList::class);
        $CorpStationsRankingList->reduceScore($crop_id, $station_id, $type, $score);
        $StationsRankingList = app(StationsRankingList::class);
        $StationsRankingList->reduceScore($station_id, $type, $score);

        return true;
    }

    /**
     * 榜单前十名
     *
     * @param string $type 类型
     * @param int $corp_id [default=0] 运营商ID 如果为0表示查询所有运营商的充电站排行榜
     * @return array
     */
    public function rankingListTop10(string $type, int $corp_id = 0): array
    {
        // 查询Redis的有序队列
        if ($corp_id === 0) {
            $StationsRankingList = app(StationsRankingList::class);
            $rankingList = $StationsRankingList->RankingListTop10($type);
        } else {
            $CorpStationsRankingList = app(CorpStationsRankingList::class);
            $rankingList = $CorpStationsRankingList->RankingListTop10($type, $corp_id);
        }

        // 获取前10名的充电站ID
        $station_ids = array_keys($rankingList);
        // 查询前10名的充电站名称
        $StationsModel = app(Stations::class);
        $stationIdToNames = $StationsModel->getStationNames($station_ids);

        // 组装结果数据
        $result = [];
        foreach ($rankingList as $station_id => $value) {
            if (isset($stationIdToNames[$station_id]) === true) {
                $result[] = [
                    'corp_id' => $corp_id,
                    'station_id' => $station_id,
                    'station_name' => $stationIdToNames[$station_id],
                    'value' => $value
                ];
            }
        }
        return $result;
    }
}