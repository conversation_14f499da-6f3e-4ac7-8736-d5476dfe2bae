<?php

namespace app\common\cache\redis;

use Predis\Client as Predis;

// 所有运营商的充电站月排行榜
class StationsRankingList
{
    private static ?Predis $redisInstance = null;
    private static ?StationsRankingList $instance = null;

    private function __construct()
    {
        self::$redisInstance = new Predis(config('my.redis'));
    }

    public static function __make(): static
    {
        if (is_null(static::$instance)) {
            static::$instance = new static();
        }

        return static::$instance;
    }

    /**
     * 增加分数
     *
     * @param int $station_id 充电桩ID
     * @param string $type 排行榜类型
     * @param int $score 增加的分数
     * @return int 返回成员增加分数后的实际分数
     */
    public function increaseScore(int $station_id, string $type, int $score): int
    {
        if ($score <= 0) return 0;

        $key = self::getKey($type);

        $result = (int)self::$redisInstance->zincrby(
            $key,
            $score,
            $station_id
        );

        self::$redisInstance->expire($key, 2 * 30 * 24 * 60 * 60);

        return $result;
    }

    /**
     * 减少分数
     *
     * @param int $station_id 充电桩ID
     * @param int $score 增加的分数
     * @param string $type 排行榜类型
     * @return int 返回成员增加分数后的实际分数
     */
    public function reduceScore(int $station_id, int $score, string $type): int
    {
        if ($score <= 0) return 0;

        $key = self::getKey($type);

        $result = (int)self::$redisInstance->zincrby(
            self::getKey($type),
            $score,
            $station_id
        );

        self::$redisInstance->expire($key, 2 * 30 * 24 * 60 * 60);

        return $result;
    }


    /**
     * 获取用户登录数据Key
     *
     * @param string $type 类型
     * @return string Key
     */
    protected static function getKey(string $type): string
    {
        return sprintf('stations:%s:ranking_list:%s', $type, date('m'));
    }

    /**
     * 榜单前十名
     *
     * @param string $type 类型
     * @return array
     */
    public function RankingListTop10(string $type): array
    {
        return self::$redisInstance->zRevRange(
            self::getKey($type),
            0,
            10,
            [
                'WITHSCORES' => true,
            ]
        );
    }
}