<?php
/** @noinspection PhpUnused */

namespace app\common\cache\redis;

use Predis\Client as Predis;

// 小程序用户登录的数据缓存
class AppletUserLogin implements UserLoginInterface
{
    private static ?Predis $redisInstance = null;
    private static ?AppletUserLogin $instance = null;

    private function __construct()
    {
        self::$redisInstance = new Predis(config('my.redis'));
    }

    public static function __make(): static
    {
        if (is_null(static::$instance)) {
            static::$instance = new static();
        }

        return static::$instance;
    }

    /**
     * 保存用户登录数据
     *
     * @param string $token 用户令牌
     * @param array $userData 用户数据
     * @return ?int
     */
    public function saveUserLoginData(string $token, array $userData): ?int
    {
        if (empty($token) || empty($userData)) return null;

        $expireDuration = 86400 * 30;

        $expireTime = time() + $expireDuration;
        static::$redisInstance->set(
            static::getUserLoginDataKey($token),
            json_encode_cn($userData),
            'EX',
            $expireDuration
        );
        return $expireTime;
    }

    /**
     * 删除用户登录数据
     *
     * @param string $token 用户令牌
     * @return bool
     */
    public function removeUserLoginData(string $token): bool
    {
        if (empty($token)) return false;

        static::$redisInstance->del(
            static::getUserLoginDataKey($token),
        );
        return true;
    }

    /**
     * 读取用户登录数据
     *
     * @param string $token 用户令牌
     * @return array
     */
    public function getUserLoginData(string $token): array
    {
        if (empty($token)) return [];

        $value = static::$redisInstance->get(static::getUserLoginDataKey($token));
        if (empty($value)) return [];

        return json_decode($value, true);
    }

    public function extendLoginValidityTime(string $token): ?int
    {
        if (empty($token)) return null;

        $expireDuration = 86400 * 30;

        $expireTime = time() + $expireDuration;

        static::$redisInstance->expire(
            static::getUserLoginDataKey($token),
            $expireDuration
        );

        return $expireTime;
    }

    /**
     * 获取用户登录数据Key
     *
     * @param string $token 用户令牌
     * @return string Key
     */
    protected static function getUserLoginDataKey(string $token): string
    {
        return sprintf('applet:%s', $token);
    }
}
