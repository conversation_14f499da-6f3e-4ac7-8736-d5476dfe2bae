<?php
/** @noinspection PhpUnused */
/** @noinspection PhpUnusedLocalVariableInspection */

namespace app\common\cache\redis;

use Predis\Client as Predis;

// 充电枪状态
class ShotsStatusCache
{
    private static ?Predis $redisInstance = null;
    private static ?ShotsStatusCache $instance = null;

    /**
     * @var int 缓存过期时间
     */
    protected int $expiration = 60;

    private function __construct()
    {
        self::$redisInstance = new Predis(config('my.redis5'));
    }

    public static function __make(): static
    {
        if (is_null(static::$instance)) {
            static::$instance = new static();
        }

        return static::$instance;
    }

    /**
     * 获取Key
     *
     * @param int $shotId 充电枪ID
     * @return string Key
     */
    protected static function getKey(int $shotId): string
    {
        return sprintf('admin:shot_status:%d', $shotId);
    }

    /**
     * 获取分布式锁Key
     *
     * @param int $shotId 充电枪ID
     * @return string Key
     */
    protected static function getLockKey(int $shotId): string
    {
        return sprintf('admin:shot_status:lock:%d', $shotId);
    }

    protected function getLock(int $shotId): bool
    {
        $result = self::$redisInstance->setnx(
            self::getLockKey($shotId),
            ''
        );
        if ($result === 1) {
            self::$redisInstance->expire(self::getLockKey($shotId), $this->expiration);
            return true;
        }

        return false;
    }

    protected function releaseLock(int $shotId): bool
    {
        self::$redisInstance->del(self::getLockKey($shotId));
        return true;
    }

    /**
     * 获取充电枪状态
     *
     * @param int $shotId 充电枪ID
     * @return ?int
     */
//    public function getStatus(int $shotId): ?int
//    {
////        $cacheStatus = $this->getCacheStatus($shotId);
////        if (!is_null($cacheStatus)) return $cacheStatus;
////
////        // 没有缓存
////
////        // 获取锁
////        if ($this->getLock($shotId) === true) {
////            try {
//        // 查数据库中的状态
//        $Shots = app(Shots::class);
////                $status = $Shots->getStatus($shotId);
//        return $Shots->getStatus($shotId);
//
////                // 更新到缓存中
////                $this->setCacheStatus($shotId, $status);
////
////                // 释放锁
////                $this->releaseLock($shotId);
////
////                return $status;
////            } catch (\Throwable $e) {
////                // 释放锁
////                $this->releaseLock($shotId);
////
////                return null;
////            }
////        } else {
////            // 休眠一小会
////            trace('休眠一小会:10ms', 'debug');
////            usleep(10 * 1000);
////            return $this->getStatus($shotId);
////        }
//    }

    /**
     * 设置充电枪状态
     *
     * @param int $shotId 充电枪ID
     * @param int $status 充电枪状态
     * @return bool
     */
//    public function setStatus(int $shotId, int $status): bool
//    {
////        // 获取锁
////        if ($this->getLock($shotId) === true) {
////            try {
////                // 删除缓存数据
////                $this->delCacheStatus($shotId);
//
//        // 查数据库中的状态
//        $Shots = app(Shots::class);
//        return $Shots->updateStatus($shotId, $status) > 0;
//
////                // 释放锁
////                $this->releaseLock($shotId);
////
////                return true;
////            } catch (\Throwable $e) {
////                // 释放锁
////                $this->releaseLock($shotId);
////
////                return false;
////            }
////        } else {
////            // 休眠一小会
////            trace('休眠一小会:10ms', 'debug');
////            usleep(10);
////            return $this->setStatus($shotId, $status);
////        }
//    }

    /**
     * 获取充电枪缓存状态
     *
     * @param int $shotId 充电枪ID
     * @return ?int
     */
    protected function getCacheStatus(int $shotId): ?int
    {
        $value = self::$redisInstance->get(self::getKey($shotId));
        if (is_null($value)) return null;

        return (int)$value;
    }

    /**
     * 设置充电枪缓存状态
     *
     * @param int $shotId 充电枪ID
     * @param int $status 状态
     * @return bool
     */
    protected function setCacheStatus(int $shotId, int $status): bool
    {
        $result = self::$redisInstance->setex(self::getKey($shotId), $this->expiration, $status);
        if ($result->getPayload() === 'OK') {
            return true;
        }
        return false;
    }

    /**
     * 删除充电枪缓存状态
     *
     * @param int $shotId 充电枪ID
     * @return bool
     */
    protected function delCacheStatus(int $shotId): bool
    {
        self::$redisInstance->del(self::getKey($shotId));
        return true;
    }
}