<?php

namespace app\common\cache\redis;

use Predis\Client as Predis;

// 运营商下的充电站月排行榜
class CorpStationsRankingList
{
    private static ?Predis $redisInstance = null;
    private static ?CorpStationsRankingList $instance = null;

    private function __construct()
    {
        self::$redisInstance = new Predis(config('my.redis'));
    }

    public static function __make(): static
    {
        if (is_null(static::$instance)) {
            static::$instance = new static();
        }

        return static::$instance;
    }

    /**
     * 增加分数
     *
     * @param int $corp_id 运营商ID
     * @param int $station_id 充电桩ID
     * @param string $type 排行榜类型
     * @param int $score 增加的分数
     * @return int 返回成员增加分数后的实际分数
     */
    public function increaseScore(int $corp_id, int $station_id, string $type, int $score): int
    {
        if ($score <= 0) return 0;

        $key = self::getKey($corp_id, $type);

        $result = (int)self::$redisInstance-><PERSON><PERSON>($key, $score, $station_id);

        // 有效期两个月
        self::$redisInstance->expire($key, 2 * 30 * 24 * 3600);

        return $result;
    }

    /**
     * 减少分数
     *
     * @param int $corp_id 运营商ID
     * @param int $station_id 充电桩ID
     * @param int $score 增加的分数
     * @param string $type 排行榜类型
     * @return int 返回成员增加分数后的实际分数
     */
    public function reduceScore(int $corp_id, int $station_id, int $score, string $type): int
    {
        if ($score <= 0) return 0;

        $key = self::getKey($corp_id, $type);

        $result = (int)self::$redisInstance->zincrby($key, $score, $station_id);

        // 有效期两个月
        self::$redisInstance->expire($key, 2 * 30 * 24 * 3600);

        return $result;
    }


    /**
     * 获取Key
     *
     * @param int $corp_id 运营商ID
     * @param string $type 类型
     * @return string Key
     */
    protected static function getKey(int $corp_id, string $type): string
    {
        return sprintf('corp:%d:stations:%s:ranking_list:%s', $corp_id, $type, date('m'));
    }

    /**
     * 榜单前十名
     *
     * @param string $type 类型
     * @param int $corp_id
     * @return array
     */
    public function RankingListTop10(string $type, int $corp_id): array
    {
        return self::$redisInstance->zRevRange(
            self::getKey($corp_id, $type),
            0,
            10,
            [
                'WITHSCORES' => true,
                'REV' => true
            ]
        );
    }
}