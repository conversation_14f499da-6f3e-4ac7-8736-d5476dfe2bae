<?php

namespace app\common\cache\redis\entity;

class BaseEntity
{
    public function __construct(array|string $data)
    {
        if (is_string($data)) $data = json_decode($data, true);
        foreach ($data as $key => $value) {
            if (property_exists($this, $key)) {
                $this->{$key} = $value;
            }
        }
    }

    public function toArray(): array
    {
        $result = [];
        foreach ($this as $key => $value) {
            $result[$key] = $value;
        }
        return $result;
    }

    public function toJsonString(): string
    {
        return json_encode_cn($this->toArray());
    }
}