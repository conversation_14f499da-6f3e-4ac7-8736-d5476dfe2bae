<?php

namespace app\common\email;

use app\common\lib\ExceptionLogCollector;
use Throwable;


class ActiveAlarmSummaryEmail
{
    public function send(string $email, string $corp_name, string $station_name, string $body): bool
    {
        try {
            $BaseEmail = new BaseEmail();
            $BaseEmail->addRecipients($email);
            $BaseEmail->setContent(sprintf('主动告警每日汇总 - %s - %s', $corp_name, $station_name), $body);
            return $BaseEmail->send();
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            return false;
        }
    }
}