<?php
/** @noinspection PhpUnused */

namespace app\common\email;

use PHPMailer\PHPMailer\Exception;
use P<PERSON>Mailer\PHPMailer\PHPMailer;

class BaseEmail
{

    protected PHPMailer $mailer;

    /**
     * @throws Exception
     */
    public function __construct()
    {
        $this->mailer = new PHPMailer(true);
        $this->setting();
        $this->setFrom(config('email.from_address'), config('email.from_name'));
    }

    /**
     * 服务器设置
     * @return static
     */
    protected function setting(): static
    {
        // 启用详细调试输出
        $this->mailer->SMTPDebug = config('email.debug');
        // 使用SMTP发送
        $this->mailer->isSMTP();
        // 设置要通过的SMTP服务器
        $this->mailer->Host = config('email.smtp_host');
        // 启用SMTP身份验证
        $this->mailer->SMTPAuth = config('email.smtp_auth');
        // SMTP用户名
        $this->mailer->Username = config('email.username');
        // SMTP密码
        $this->mailer->Password = config('email.password');
        // 启用隐式TLS加密
        $this->mailer->SMTPSecure = config('email.smtp_secure');
        // 要连接到的TCP端口；如果已设置`SMTPSecure=PHPMailer:：ENCRYPTION_STARTTLS，请使用587`
        $this->mailer->Port = config('email.smtp_port');
        return $this;
    }

    /**
     * 设置发件人
     *
     * @param string $address
     * @param string $name
     * @return bool
     * @throws Exception
     */
    protected function setFrom(string $address, string $name): bool
    {
        return $this->mailer->setFrom($address, $name);
    }

    /**
     * 添加收件人
     * @param string $address 收件人邮箱地址
     * @param string $name 收件人名称
     * @return bool
     * @throws Exception
     */
    public function addRecipients(string $address, string $name = ''): bool
    {
        return $this->mailer->addAddress($address, $name);
    }

    /**
     * 添加附件
     *
     * @param string $path
     * @param string $name
     * @param string $encoding
     * @param string $type
     * @param string $disposition
     * @return bool
     * @throws Exception
     */
    public function addAttachments(
        string $path,
        string $name = '',
        string $encoding = PHPMailer::ENCODING_BASE64,
        string $type = '',
        string $disposition = 'attachment'
    ): bool
    {
        return $this->mailer->addAttachment($path, $name, $encoding, $type, $disposition);
    }

    public function setContent(string $subject, string $body, bool $is_html = true): static
    {
        $this->mailer->isHTML($is_html);                                  //Set email format to HTML
        $this->mailer->Subject = $subject;
        if ($is_html) {
            $this->mailer->Body = $body;
        } else {
            $this->mailer->AltBody = $body;
        }

        return $this;
    }

    /**
     * @return bool
     * @throws Exception
     */
    public function send(): bool
    {
        return $this->mailer->send();
    }
}