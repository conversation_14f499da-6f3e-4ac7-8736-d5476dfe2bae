<?php

namespace app\common\new_queue\trait;

use app\common\new_queue\entity\PilesOfflineRecoveryBody;
use app\common\repositories\Corp;
use app\common\repositories\Stations;
use Chenbingji\Tool\format\Time;

trait PilesOfflineRecoveryTrait
{
    public function pilesOfflineRecoveryTraitProduction(PilesOfflineRecoveryBody $body): int
    {
        return $this->pushToQueue(self::TypePilesOfflineRecovery, $body->toArray());
    }

    public function pilesOfflineRecoveryTraitConsumption(PilesOfflineRecoveryBody $body): array
    {
        $contentVars = [
            Corp::getName($body->corp_id) ?? '未知',
            Stations::getName($body->station_id) ?? '未知',
            $body->piles_id,
            date('Y-m-d H:i:s', $body->alarm_time),
            date('Y-m-d H:i:s', $body->recovery_time),
            Time::formatDuration($body->recovery_time - $body->alarm_time)
        ];
        $content = sprintf("充电桩离线告警恢复\n
         >运营商名称:<font color=\"comment\">%s</font>
         >场站名称:<font color=\"comment\">%s</font>
         >充电桩ID:<font color=\"comment\">%s</font>
         >离线时间:<font color=\"comment\">%s</font>
         >恢复时间:<font color=\"comment\">%s</font>
         >离线时长:<font color=\"comment\">%s</font>
         ", ...$contentVars);

        return [
            json_encode([
                'msgtype' => 'markdown',
                'markdown' => [
                    'content' => $content,
                ]
            ]),
            $body->enterprise_wechat_key
        ];
    }
}
