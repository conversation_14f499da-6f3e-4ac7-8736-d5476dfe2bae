<?php

namespace app\common\new_queue\trait;

use app\common\new_queue\entity\CcServiceOfflineAlarmBody;
use app\common\repositories\Corp;
use app\common\repositories\Stations;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\CcServiceOfflineNotice;

trait CcServiceOfflineAlarmTrait
{
    public function ccServiceOfflineAlarmTraitProduction(CcServiceOfflineAlarmBody $body): int
    {
        return $this->pushToQueue(self::TypeCcServiceOfflineAlarm, $body->toArray());
    }

    public function ccServiceOfflineAlarmTraitConsumption(CcServiceOfflineAlarmBody $body): array
    {
        $service = [
            CcServiceOfflineNotice::ServiceTypeChargeManage => '充电管理',
            CcServiceOfflineNotice::ServiceTypeAlgorithm => '调度算法',
            CcServiceOfflineNotice::ServiceTypeDriver => '驱动服务',
        ];

        $contentVars = [
            Corp::getName($body->corp_id) ?? '未知',
            Stations::getName($body->station_id) ?? '未知',
            $body->centralized_controller_id,
            $service[$body->service_type] ?? '未知',
            date('Y-m-d H:i:s', $body->alarm_time),
        ];
        $content = sprintf("能源路由器服务离线告警\n
         >运营商名称:<font color=\"comment\">%s</font>
         >场站名称:<font color=\"comment\">%s</font>
         >能源路由器ID:<font color=\"comment\">%s</font>
         >服务类型:<font color=\"comment\">%s</font>
         >告警时间:<font color=\"comment\">%s</font>
         ", ...$contentVars);

        return [
            json_encode([
                'msgtype' => 'markdown',
                'markdown' => [
                    'content' => $content,
                ]
            ]),
            $body->enterprise_wechat_key
        ];
    }
}