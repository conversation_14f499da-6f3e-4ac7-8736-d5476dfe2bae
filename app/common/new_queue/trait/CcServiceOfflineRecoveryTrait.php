<?php

namespace app\common\new_queue\trait;

use app\common\new_queue\entity\CcServiceOfflineRecoveryBody;
use app\common\repositories\Corp;
use app\common\repositories\Stations;
use Chenbingji\Tool\format\Time;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\CcServiceOnlineNotice;

trait CcServiceOfflineRecoveryTrait
{
    public function ccServiceOfflineRecoveryTraitProduction(CcServiceOfflineRecoveryBody $body): int
    {
        return $this->pushToQueue(self::TypeCcServiceOfflineRecovery, $body->toArray());
    }

    public function ccServiceOfflineRecoveryTraitConsumption(CcServiceOfflineRecoveryBody $body): array
    {
        $service = [
            CcServiceOnlineNotice::ServiceTypeChargeManage => '充电管理',
            CcServiceOnlineNotice::ServiceTypeAlgorithm => '调度算法',
            CcServiceOnlineNotice::ServiceTypeDriver => '驱动服务',
        ];

        $contentVars = [
            Corp::getName($body->corp_id) ?? '未知',
            Stations::getName($body->station_id) ?? '未知',
            $body->centralized_controller_id,
            $service[$body->service_type] ?? '未知',
            date('Y-m-d H:i:s', $body->alarm_time),
            date('Y-m-d H:i:s', $body->recovery_time),
            Time::formatDuration($body->recovery_time - $body->alarm_time)
        ];
        $content = sprintf("能源路由器服务离线告警恢复\n
         >运营商名称:<font color=\"comment\">%s</font>
         >场站名称:<font color=\"comment\">%s</font>
         >能源路由器ID:<font color=\"comment\">%s</font>
         >服务类型:<font color=\"comment\">%s</font>
         >离线时间:<font color=\"comment\">%s</font>
         >恢复时间:<font color=\"comment\">%s</font>
         >离线时长:<font color=\"comment\">%s</font>
         ", ...$contentVars);

        return [
            json_encode([
                'msgtype' => 'markdown',
                'markdown' => [
                    'content' => $content,
                ]
            ]),
            $body->enterprise_wechat_key
        ];
    }
}
