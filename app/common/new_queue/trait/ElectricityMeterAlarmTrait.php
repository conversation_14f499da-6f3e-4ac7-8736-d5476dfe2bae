<?php

namespace app\common\new_queue\trait;

use app\common\new_queue\entity\ElectricityMeterAlarmBody;
use app\common\repositories\AlarmRecord;
use app\common\repositories\Corp;
use app\common\repositories\Stations;

trait ElectricityMeterAlarmTrait
{
    public function electricityMeterAlarmTraitProduction(ElectricityMeterAlarmBody $body): int
    {
        return $this->pushToQueue(self::TypeElectricityMeterAlarm, $body->toArray());
    }

    public function electricityMeterAlarmTraitConsumption(ElectricityMeterAlarmBody $body): array
    {
        $contentVars = [
            Corp::getName($body->corp_id) ?? '未知',
            Stations::getName($body->station_id) ?? '未知',
            $body->device_id,
            $body->code,
            AlarmRecord::CodeToMessageMap[$body->code] ?? '未知',
            date('Y-m-d H:i:s', $body->alarm_time)
        ];

        $content = sprintf("读取电表功率告警\n
         >运营商名称:<font color=\"comment\">%s</font>
         >场站名称:<font color=\"comment\">%s</font>
         >能源路由器ID:<font color=\"comment\">%s</font>
         >告警代码:<font color=\"comment\">%s</font>
         >告警代码描述:<font color=\"comment\">%s</font>
         >告警时间:<font color=\"comment\">%s</font>
         ", ...$contentVars);

        return [
            json_encode([
                'msgtype' => 'markdown',
                'markdown' => [
                    'content' => $content,
                ]
            ]),
            $body->enterprise_wechat_key
        ];
    }
}