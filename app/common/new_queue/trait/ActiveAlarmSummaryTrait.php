<?php
/** @noinspection XmlDeprecatedElement */
/** @noinspection HtmlDeprecatedAttribute */
/** @noinspection HtmlDeprecatedTag */

namespace app\common\new_queue\trait;

use app\common\new_queue\entity\ActiveAlarmSummaryBody;
use app\common\repositories\AlarmRecord;
use app\common\repositories\Stations;

trait ActiveAlarmSummaryTrait
{

    public function activeAlarmSummaryTraitProduction(ActiveAlarmSummaryBody $body): int
    {
        return $this->pushToQueue(self::TypeActiveAlarmSummary, $body->toArray());
    }

    public function activeAlarmSummaryTraitConsumption(ActiveAlarmSummaryBody $body): array
    {
        $station_name = Stations::getName($body->station_id) ?? '未知';


        $content = sprintf("未恢复告警汇总\n
                    >场站名称:<font color=\"comment\">%s</font>\n", $station_name);

        foreach ($body->alarm_records as $record) {
            $device_type = AlarmRecord::DeviceTypeToText[$record['device_type']] ?? '未知';
            $type = AlarmRecord::TypeToText[$record['type']] ?? '未知';
            $code_text = AlarmRecord::CodeToMessageMap[$record['code']] ?? '未知';
            $content .= sprintf("> 
                        >设备类型:<font color=\"comment\">%s</font>
                        >设备ID:<font color=\"comment\">%s</font>
                        >告警类型:<font color=\"comment\">%s</font>
                        >告警代码:<font color=\"comment\">%s(%s)</font>
                        >告警时间:<font color=\"comment\">%s</font>
                        ", $device_type, $record['device_id'], $type, $record['code'], $code_text,
                date('Y-m-d H:i:s', $record['alarm_time']));
        }

        return [
            json_encode([
                'msgtype' => 'markdown',
                'markdown' => [
                    'content' => $content,
                ]
            ]),
            $body->enterprise_wechat_key
        ];
    }
}