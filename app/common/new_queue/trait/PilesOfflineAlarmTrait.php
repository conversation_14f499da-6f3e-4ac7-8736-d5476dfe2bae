<?php

namespace app\common\new_queue\trait;

use app\common\new_queue\entity\PilesOfflineAlarmBody;
use app\common\repositories\Corp;
use app\common\repositories\Stations;

trait PilesOfflineAlarmTrait
{
    public function pilesOfflineAlarmTraitProduction(PilesOfflineAlarmBody $body): int
    {
        return $this->pushToQueue(self::TypePilesOfflineAlarm, $body->toArray());
    }

    public function pilesOfflineAlarmTraitConsumption(PilesOfflineAlarmBody $body): array
    {
        $contentVars = [
            Corp::getName($body->corp_id) ?? '未知',
            Stations::getName($body->station_id) ?? '未知',
            $body->piles_id,
            date('Y-m-d H:i:s', $body->alarm_time)
        ];
        $content = sprintf("充电桩离线告警\n
         >运营商名称:<font color=\"comment\">%s</font>
         >场站名称:<font color=\"comment\">%s</font>
         >充电桩ID:<font color=\"comment\">%s</font>
         >告警时间:<font color=\"comment\">%s</font>
         ", ...$contentVars);

        return [
            json_encode([
                'msgtype' => 'markdown',
                'markdown' => [
                    'content' => $content,
                ]
            ]),
            $body->enterprise_wechat_key
        ];
    }
}