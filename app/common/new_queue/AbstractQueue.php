<?php
/** @noinspection PhpUnused */

/** @noinspection PhpMultipleClassDeclarationsInspection */

namespace app\common\new_queue;

use app\common\log\SocketLogCollector;
use app\common\new_queue\entity\RedisConfig;
use RedisClient\ClientFactory;
use RedisClient\RedisClient;
use Workerman\Timer;

abstract class AbstractQueue
{
    protected string $queue_key = '';

    protected RedisConfig $config;

    protected ?RedisClient $redisClient = null;

    abstract protected function getRedisConfig(): RedisConfig;


    public function getRedis(): RedisClient
    {
        if ($this->redisClient === null) {
            $this->redisClient = ClientFactory::create($this->getRedisConfig()->toArray());
        }
        return $this->redisClient;
    }

    public function pushToQueue(int $type, array $body): int
    {
        $redis = $this->getRedis();
        return $redis->lpush($this->queue_key, self::encode([
            'type' => $type,
            'body' => $body
        ]));
    }

    public function popFromQueue(): ?array
    {
        $redis = $this->getRedis();
        $data = $redis->rpop($this->queue_key);
        if (is_null($data)) return null;
        return self::decode($data);
    }

    /**
     * 从队列中弹出最后一个元素，如果列表没有元素会阻塞列表直到等待超时或发现可弹出元素为止。
     *
     * @param int $timeout 等待超时时间(单位:秒)
     * @return array|null
     */
    public function blockPopFromQueue(int $timeout = 1): array|null
    {
        $redis = $this->getRedis();
        $data = $redis->brpop([$this->queue_key], $timeout);
        if (is_null($data)) {
            return null;
        }
        return self::decode($data[$this->queue_key]);
    }

    public static function encode(array $data): string
    {
        return json_encode($data);
    }

    public static function decode(string $data): array
    {
        return json_decode($data, true);
    }


    protected function beforeStartConsumer(): void
    {

    }


    /**
     * 启动消费者
     *
     * @return bool
     */
    public function startConsumer(): bool
    {
        $this->beforeStartConsumer();

        Timer::add(0.0001, function () {
            $this->socketLogCollector = new SocketLogCollector();

            $result = $this->blockPopFromQueue(10);

            if (!empty($result)) {
                $this->consumerHandler($result['type'], $result['body']);
            }
        });

        return true;
    }

    abstract protected function consumerHandler(int $type, array $body);


    // 设置 Redis 配置
    // 获取 Redis 实例
    // 开启 MySQL 监听
    // 获取 Redis 键
    // 推入队列
    // 弹出队列
    // 异常捕捉

}