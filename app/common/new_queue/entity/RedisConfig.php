<?php

namespace app\common\new_queue\entity;

class RedisConfig
{
    /**
     * @var string 服务IP和端口
     */
    public string $server = '127.0.0.1:6379';
    /**
     * @var int 通过套接字读取/写入数据的超时
     */
    public int $timeout = 1;

    /**
     * @var string 指定版本以避免一些意外错误。
     */
    public string $version = '4.0.10';

    public ?string $password = null;

    public int $database = 0;

    public function toArray(): array
    {
        $data = [];
        foreach ((array)$this as $key => $value) {
            if (!is_null($value)) {
                $data[$key] = $value;
            }
        }

        return $data;
    }

//    public array $config = [
//        // 可选。默认 = '127.0.0.1:6379'。您可以使用 'unix:///tmp/redis.sock'
//        'server' => '127.0.0.1:6379',
//
//        // 选修的。默认 = 1
//        // 通过套接字读取/写入数据的超时
//        'timeout' => 2,
//
//        // 选修的。默认 = null
//        // 在此处查看更多信息：http://php.net/manual/en/function.stream-socket-client.php
//        'connection' => [
//            // 可选。 Default = ini_get("default_socket_timeout")
//            // 超时仅在连接套接字时适用
//            'timeout' => 2,
//
//            // 选修的。默认 = STREAM_CLIENT_CONNECT
//            // 位掩码字段，可以设置为连接标志的任意组合。
//            // 当前连接标志的选择仅限于 STREAM_CLIENT_CONNECT （默认）、
//            // STREAM_CLIENT_ASYNC_CONNECT 和 STREAM_CLIENT_PERSISTENT。
//            'flags' => STREAM_CLIENT_CONNECT
//        ],
//
//        // 选修的。指定版本以避免一些意外错误。
//        'version' => '4.0.10',
//
//        // 选修的。仅当 Redis 服务器需要密码 (AUTH)
//        'password' => 'some-password', // 时才使用它，
//
//        // 选修的。如果您想在连接
//        'database' => 1, //时选择非默认数据库 (db != 0) ，请使用它，
//
//        // 选修的。具有 RedisCluster 支持配置的数组
//        'cluster' => [
//            'enabled' => false,
//
//            // 选修的。默认 = []。集群插槽和服务器的映射
//            // array(max_slot => server [, ...])
//            // 具有 3 个节点的集群示例:
//            'clusters' => [
//                5460 => '127.0.0.1:7001', // 插槽从 0 到 5460
//                10922 => '127.0.0.1:7002', // 从 5461 到 10922 的槽
//                16383 => '127.0.0.1:7003', // 从 10923 到 16383 的槽
//            ],
//
//            // 选修的。默认值=假。
//            // 使用参数在 init RedisClient 上更新下面的集群槽映射。
//            // RedisClient 将执行命令 CLUSTER SLOTS 来获取映射。
//            'init_on_start' => false,
//
//            // 选修的。默认值=假。
//            // 如果 Redis 返回错误 -MOVED 那么 RedisClient 将执行
//            // 命令 CLUSTER SLOTS 来更新集群槽映射
//            'init_on_error_moved' => true,
//
//            // 选修的。默认 = 0.05 秒。下次尝试尝试 TRYAGAIN 错误之前已超时。
//            'timeout_on_error_tryagain' => 0.25, // 秒
//        ]
//    ];
}