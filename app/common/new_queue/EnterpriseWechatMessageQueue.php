<?php

namespace app\common\new_queue;

use app\common\lib\http\HttpClient;
use app\common\lib\http\Response;
use app\common\new_queue\entity\ActiveAlarmSummaryBody;
use app\common\new_queue\entity\CcServiceOfflineAlarmBody;
use app\common\new_queue\entity\CcServiceOfflineRecoveryBody;
use app\common\new_queue\entity\CCSystemResourceAlarmBody;
use app\common\new_queue\entity\CCSystemResourceRecoveryBody;
use app\common\new_queue\entity\ElectricityMeterAlarmBody;
use app\common\new_queue\entity\ElectricityMeterRecoveryBody;
use app\common\new_queue\entity\MonitorFailureAlarmBody;
use app\common\new_queue\entity\MonitorFailureRecoveryBody;
use app\common\new_queue\entity\PilesOfflineAlarmBody;
use app\common\new_queue\entity\PilesOfflineRecoveryBody;
use app\common\new_queue\entity\TransactionFailureAlarmBody;
use app\common\new_queue\entity\TransactionFailureRecoveryBody;
use app\common\new_queue\trait\ActiveAlarmSummaryTrait;
use app\common\new_queue\trait\CcServiceOfflineAlarmTrait;
use app\common\new_queue\trait\CcServiceOfflineRecoveryTrait;
use app\common\new_queue\trait\CCSystemResourceAlarmTrait;
use app\common\new_queue\trait\CCSystemResourceRecoveryTrait;
use app\common\new_queue\trait\ElectricityMeterAlarmTrait;
use app\common\new_queue\trait\ElectricityMeterRecoveryTrait;
use app\common\new_queue\trait\MonitorFailureAlarmTrait;
use app\common\new_queue\trait\MonitorFailureRecoveryTrait;
use app\common\new_queue\trait\PilesOfflineAlarmTrait;
use app\common\new_queue\trait\PilesOfflineRecoveryTrait;
use app\common\new_queue\trait\TransactionFailureAlarmTrait;
use app\common\new_queue\trait\TransactionFailureRecoveryTrait;

//
class EnterpriseWechatMessageQueue extends BaseQueue
{
    use PilesOfflineAlarmTrait, PilesOfflineRecoveryTrait
        , CCSystemResourceAlarmTrait, CCSystemResourceRecoveryTrait
        , ElectricityMeterAlarmTrait, ElectricityMeterRecoveryTrait
        , MonitorFailureAlarmTrait, MonitorFailureRecoveryTrait
        , TransactionFailureAlarmTrait, TransactionFailureRecoveryTrait
        , CcServiceOfflineRecoveryTrait, CcServiceOfflineAlarmTrait
        , ActiveAlarmSummaryTrait;

    protected string $queue_key = 'queue:enterprise_wechat_message';

    public const TypePilesOfflineAlarm = 7; // 充电桩离线告警
    public const TypePilesOfflineRecovery = 8; // 充电桩离线恢复
    public const TypeCCSystemResourceAlarm = 9; // 能源路由器资源告警
    public const TypeCCSystemResourceRecovery = 10; // 能源路由器资源恢复
    public const TypeElectricityMeterAlarm = 11; // 读取电表功率告警
    public const TypeElectricityMeterRecovery = 12; // 读取电表功率恢复
    public const TypeMonitorFailureAlarm = 13; // 实时监测数据告警
    public const TypeMonitorFailureRecovery = 14; // 实时监测数据恢复
    public const TypeTransactionFailureAlarm = 15; // 交易记录告警
    public const TypeTransactionFailureRecovery = 16; // 交易记录恢复
    public const TypeCcServiceOfflineAlarm = 17; // 能源路由器服务离线告警
    public const TypeCcServiceOfflineRecovery = 18; // 能源路由器服务离线恢复

    // 告警记录汇总
    public const TypeActiveAlarmSummary = 100; // 主动告警汇总


    protected function handler(int $type, array $body): int
    {
        switch ($type) {
            case self::TypePilesOfflineAlarm:
                $bodyEntity = new PilesOfflineAlarmBody($body);
                [$requestBody, $enterpriseWechatKey] = $this->pilesOfflineAlarmTraitConsumption($bodyEntity);
                break;
            case self::TypePilesOfflineRecovery:
                $bodyEntity = new PilesOfflineRecoveryBody($body);
                [$requestBody, $enterpriseWechatKey] = $this->pilesOfflineRecoveryTraitConsumption($bodyEntity);
                break;
            case self::TypeCCSystemResourceAlarm:
                $bodyEntity = new CCSystemResourceAlarmBody($body);
                [$requestBody, $enterpriseWechatKey] = $this->ccSystemResourceAlarmTraitConsumption($bodyEntity);
                break;
            case self::TypeCCSystemResourceRecovery:
                $bodyEntity = new CCSystemResourceRecoveryBody($body);
                [$requestBody, $enterpriseWechatKey] = $this->ccSystemResourceRecoveryTraitConsumption($bodyEntity);
                break;
            case self::TypeElectricityMeterAlarm:
                $bodyEntity = new ElectricityMeterAlarmBody($body);
                [$requestBody, $enterpriseWechatKey] = $this->electricityMeterAlarmTraitConsumption($bodyEntity);
                break;
            case self::TypeElectricityMeterRecovery:
                $bodyEntity = new ElectricityMeterRecoveryBody($body);
                [$requestBody, $enterpriseWechatKey] = $this->electricityMeterRecoveryTraitConsumption($bodyEntity);
                break;
            case self::TypeTransactionFailureAlarm:
                $bodyEntity = new TransactionFailureAlarmBody($body);
                [$requestBody, $enterpriseWechatKey] = $this->transactionFailureAlarmTraitConsumption($bodyEntity);
                break;
            case self::TypeTransactionFailureRecovery:
                $bodyEntity = new TransactionFailureRecoveryBody($body);
                [$requestBody, $enterpriseWechatKey] = $this->transactionFailureRecoveryTraitConsumption($bodyEntity);
                break;
            case self::TypeMonitorFailureAlarm:
                $bodyEntity = new MonitorFailureAlarmBody($body);
                [$requestBody, $enterpriseWechatKey] = $this->monitorFailureAlarmTraitConsumption($bodyEntity);
                break;
            case self::TypeMonitorFailureRecovery:
                $bodyEntity = new MonitorFailureRecoveryBody($body);
                [$requestBody, $enterpriseWechatKey] = $this->monitorFailureRecoveryTraitConsumption($bodyEntity);
                break;
            case self::TypeActiveAlarmSummary:
                $bodyEntity = new ActiveAlarmSummaryBody($body);
                [$requestBody, $enterpriseWechatKey] = $this->activeAlarmSummaryTraitConsumption($bodyEntity);
                break;
            // 能源路由器服务离线告警
            case self::TypeCcServiceOfflineAlarm:
                $bodyEntity = new CcServiceOfflineAlarmBody($body);
                [$requestBody, $enterpriseWechatKey] = $this->ccServiceOfflineAlarmTraitConsumption($bodyEntity);
                break;
            // 能源路由器服务离线恢复
            case self::TypeCcServiceOfflineRecovery:
                $bodyEntity = new CcServiceOfflineRecoveryBody($body);
                [$requestBody, $enterpriseWechatKey] = $this->ccServiceOfflineRecoveryTraitConsumption($bodyEntity);
                break;
            default:
                $this->socketLogCollector->collectorRequestLog(
                    sprintf('无效类型: %s | body: %s', $type, print_r($body, true)),
                    $this->socketLogCollector::LevelWarning
                );
                return parent::ResultCodeDiscard;
        }

        // 发送企业微信消息
        $response = $this->sendRequest($requestBody, $enterpriseWechatKey);


        // 验证是否发送成功
        if ($this->verifyResponse($response)) return parent::ResultCodeSuccess;

        // 记录日志
        $this->socketLogCollector->collectorRunLog(
            print_r($response->analysisJsonBody(), true),
            $this->socketLogCollector::LevelError
        );
        sleep(1);

        return parent::ResultCodeFailed;
    }

    protected function verifyResponse(Response $response): bool
    {
        $responseBody = $response->analysisJsonBody();
        if ($response->isFailed === false) {
            if ($responseBody['errcode'] === 0) {
                return true;
            }
        }
        return false;
    }

    protected function sendRequest(string $body, string $enterpriseWechatKey): Response
    {
        $httpClient = new HttpClient();
        return $httpClient->setProtocol('https')
            ->setDomain('qyapi.weixin.qq.com')
            ->setMethod('POST')
            ->setUri('cgi-bin/webhook/send')->setQuery([
                'key' => $enterpriseWechatKey
            ])
            ->setBody($body)->sendRequest();
    }

}