## EnterpriseWechatMessageQueue - 企业微信消息队列

### 队列的键：

### 数据结构

| 字段   | 类型     | 是否必填 | 说明                                    |
|------|--------|------|---------------------------------------|
| type | int    | 是    | 协议类型                                  |
| body | object | 是    | 协议的主体内容，内容根据类型的不同而不同，具体根据对应类型查看下方的定义。 |

### 队列数据类型大全

| 类型 | 说明                           |
|----|------------------------------|
| 7  | 充电桩离线告警。在充电桩被检测出已离线时，自动发出。   | 
| 8  | 充电桩离线恢复。充电桩在离线之后，再次上线时，自动发出。 |
| 9  | 能源路由器资源告警。                   |
| 10 | 能源路由器资源恢复。                   |

### 协议定义

#### 1. 充电桩离线告警

##### body 定义

| 字段         | 类型  | 是否必填 | 说明    |
|------------|-----|------|-------|
| corp_id    | int | 是    | 运营商ID | 
| station_id | int | 是    | 场站ID  |
| piles_id   | int | 是    | 充电桩ID |
| alarm_time | int | 是    | 告警时间  |

##### 示例

```json
{
  "type": 7,
  "body": {
    "corp_id": 100001,
    "station_id": 1013,
    "piles_id": 20230105003018,
    "alarm_time": 1715686379
  }
}
```

#### 2. 充电桩离线恢复

##### body 定义

| 字段            | 类型  | 是否必填 | 说明    |
|---------------|-----|------|-------|
| corp_id       | int | 是    | 运营商ID | 
| station_id    | int | 是    | 场站ID  |
| piles_id      | int | 是    | 充电桩ID |
| alarm_time    | int | 是    | 告警时间  |
| recovery_time | int | 是    | 恢复时间  |

##### 示例

```json
{
  "type": 8,
  "body": {
    "corp_id": 100001,
    "station_id": 1013,
    "piles_id": 20230105003018,
    "alarm_time": 1715686379,
    "recovery_time": 1715686399
  }
}
```

#### 3. 能源路由器资源告警

##### body 定义

| 字段         | 类型     | 是否必填 | 说明      |
|------------|--------|------|---------|
| corp_id    | int    | 是    | 运营商ID   | 
| station_id | int    | 是    | 场站ID    |
| device_id  | string | 是    | 能源路由器ID |
| code       | string | 是    | 告警代码    |
| alarm_time | int    | 是    | 告警时间    |

##### 示例

```json
{
  "type": 9,
  "body": {
    "corp_id": 100001,
    "station_id": 1013,
    "device_id": "70B5E8298982",
    "alarm_time": 1715686379
  }
}
```

#### 4. 能源路由器资源恢复

##### body 定义

| 字段            | 类型     | 是否必填 | 说明      |
|---------------|--------|------|---------|
| corp_id       | int    | 是    | 运营商ID   | 
| station_id    | int    | 是    | 场站ID    |
| device_id     | string | 是    | 能源路由器ID |
| code          | string | 是    | 告警代码    |
| alarm_time    | int    | 是    | 告警时间    |
| recovery_time | int    | 是    | 恢复时间    |

##### 示例

```json
{
  "type": 10,
  "body": {
    "corp_id": 100001,
    "station_id": 1013,
    "device_id": "70B5E8298982",
    "alarm_time": 1715686379,
    "recovery_time": 1715686399
  }
}
```