<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\common\new_queue;

use app\common\log\LogCollector;
use app\common\log\SocketLogCollector;
use app\common\new_queue\entity\RedisConfig;
use Closure;
use think\DbManager;
use think\facade\Db;
use Throwable;

class BaseQueue extends AbstractQueue
{
    public const ResultCodeFailed = 0; // 失败
    public const ResultCodeSuccess = 1; // 成功
    public const ResultCodeDiscard = 2; // 丢弃

    protected ?SocketLogCollector $socketLogCollector = null;
    // 开启 MySQL 监听
    // 异常捕捉


    protected function beforeStartConsumer(): void
    {
        $this->startDatabaseListener();
    }


    protected function getRedisConfig(): RedisConfig
    {
        $config = config('my.redis');
        $redisConfig = new RedisConfig();
        $redisConfig->server = sprintf("%s:%s", $config['host'], $config['port']);
        $redisConfig->timeout = $config['timeout'];
        $redisConfig->database = $config['database'];
        if (!empty($config['password'])) {
            $redisConfig->password = $config['password'];
        }
        $redisConfig->version = '6.0.0';

        return $redisConfig;
    }

    protected function consumerHandler(int $type, array $body): void
    {
        $this->socketLogCollector = new SocketLogCollector();
        $this->socketLogCollector->collectorRequestLog(str_repeat('-', 40), 'request');
        $this->socketLogCollector->collectorRequestLog(print_r([
            'type' => $type,
            'body' => $body
        ], true), 'request');
        $handlerResult = $this->openExceptionCatch(function () use ($type, $body) {

            return $this->handler($type, $body);

        });

        $this->socketLogCollector->write();
        // 如果处理失败了，推送回队列，等待下次处理。
        if ($handlerResult === self::ResultCodeFailed) {
            $this->pushToQueue($type, $body);
        }
    }

    protected function handler(int $type, array $body): int
    {
        // to do ...
        return self::ResultCodeSuccess;
    }

    protected function startDatabaseListener(): void
    {
        $DbManager = app(DbManager::class);
        $DbManager->listen(function ($sql, $time, $master) {
            if (str_starts_with($sql, 'CONNECT:')) {
                if (!is_null($this->socketLogCollector)) {
                    $this->socketLogCollector->collectorRunLog($sql, LogCollector::LevelSql);
                }


                return;
            }

            // 记录SQL
            if (is_bool($master)) {
                // 分布式记录当前操作的主从
                $master = $master ? 'master|' : 'slave|';
            } else {
                $master = '';
            }

            if (!is_null($this->socketLogCollector)) {
                $this->socketLogCollector->collectorRunLog($sql . ' [ ' . $master . 'RunTime:' . $time . 's ]', LogCollector::LevelSql);
            }
        });
    }

    /**
     * 开启异常捕捉
     *
     * @param Closure $business 业务逻辑处理
     * @param bool $isStartTrans 是否开启数据库事务
     * @return int
     */
    protected function openExceptionCatch(Closure $business, bool $isStartTrans = false): int
    {
        if ($isStartTrans) Db::startTrans();
        try {
            $result = $business();
            if ($isStartTrans) Db::commit();
        } catch (Throwable $e) {
            $this->socketLogCollector->collectorRunLog(sprintf('队列处理异常：异常文件=%s, 异常代码行数=%d, 异常描述=%s, 异常状态码=%s',
                $e->getFile(), $e->getLine(), $e->getMessage(), $e->getCode()
            ), 'error');
            $this->socketLogCollector->collectorRunLog(print_r($e->getTrace(), true), 'error');
            if ($isStartTrans) Db::rollback();
            return self::ResultCodeFailed;
        }

        return $result;
    }
}