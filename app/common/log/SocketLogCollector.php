<?php
/** @noinspection PhpUnused */

namespace app\common\log;

use Ramsey\Uuid\Uuid;
use think\facade\Log;
use Throwable;

class SocketLogCollector
{
    public array $requestLog = [];
    public array $runLog = [];
    public array $responseLog = [];

    // 等级
    public const LevelRequest = 'request';
    public const LevelResponse = 'response';
    public const LevelInfo = 'info';
    public const LevelDebug = 'debug';
    public const LevelError = 'error';
    public const LevelWarning = 'warning';
    public const LevelSql = 'sql';

    protected function getExceptionInfo(Throwable $e): string
    {
        return sprintf('File: %s, Line: %d, Code: %d, Message: %s',
            $e->getFile(), $e->getLine(), $e->getCode(), $e->getMessage());
    }

    public function collect(Throwable $e, bool $isCommand = false): void
    {
        if ($isCommand === false) {
            $this->collectorRunLog($this->getExceptionInfo($e), self::LevelError);
            $this->collectorRunLog(json_encode_cn($e->getTrace(), true), self::LevelError);
        } else {
            trace(self::getExceptionInfo($e), self::LevelError);
            trace(json_encode_cn($e->getTrace(), true), self::LevelError);
        }
    }

    public function recordErrorLog(string $message, array $debugBacktrace = []): void
    {
        $string = json_encode($debugBacktrace);
        $this->collectorRunLog($message . ' => ' . $string, self::LevelError);
    }


    public function collectorRunLog(string $message, string $level = self::LevelDebug): void
    {
        if (!in_array($level, config('log.level'))) return;
        $this->runLog[] = ['time' => date('c'), 'level' => $level, 'message' => $message];
    }

    public function collectorRequestLog(string $message, string $level): void
    {
        if (!in_array($level, config('log.level'))) return;
        $this->requestLog[] = ['time' => date('c'), 'level' => $level, 'message' => $message];
    }

    public function collectorResponseLog(string $message, string $level): void
    {
        if (!in_array($level, config('log.level'))) return;
        $this->responseLog[] = ['time' => date('c'), 'level' => $level, 'message' => $message];
    }

    public function write(): bool
    {
        $uuid = Uuid::uuid4(); // 随机字符串
        $log = array_merge($this->requestLog, $this->runLog, $this->responseLog);
        foreach ($log as $value) {
            Log::channel('cli_log')->record(sprintf('[%s][%s] %s', $value['time'], $uuid, $value['message']), $value['level']);
        }
        $this->requestLog = $this->runLog = $this->responseLog = [];

        return true;
    }
}