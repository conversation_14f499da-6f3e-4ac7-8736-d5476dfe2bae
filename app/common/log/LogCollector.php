<?php
/** @noinspection PhpUnused */

namespace app\common\log;


class LogCollector
{
    public static array $requestLog = [];
    public static array $runLog = [];
    public static array $responseLog = [];

    // 等级
    public const LevelRequest = 'request';
    public const LevelResponse = 'response';
    public const LevelInfo = 'info';
    public const LevelDebug = 'debug';
    public const LevelError = 'error';
    public const LevelSql = 'sql';

    public static function collectorRunLog(string $message, string $level = self::LevelDebug): void
    {
        if (!in_array($level, config('log.level'))) return;
        self::$runLog[] = ['time' => date('c'), 'level' => $level, 'message' => $message];
    }

    public static function collectorRequestLog(string $message, string $level): void
    {
        if (!in_array($level, config('log.level'))) return;
        self::$requestLog[] = ['time' => date('c'), 'level' => $level, 'message' => $message];
    }

    public static function collectorResponseLog(string $message, string $level): void
    {
        if (!in_array($level, config('log.level'))) return;
        self::$responseLog[] = ['time' => date('c'), 'level' => $level, 'message' => $message];
    }

    public static function write(): bool
    {
        $log = array_merge(self::$requestLog, self::$runLog, self::$responseLog);
        foreach ($log as $value) {
            trace(sprintf('[%s] %s', $value['time'], $value['message']), $value['level']);
        }

        return true;
    }
}