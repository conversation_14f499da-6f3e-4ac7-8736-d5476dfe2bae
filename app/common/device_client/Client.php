<?php
/** @noinspection PhpUnused */

namespace app\common\device_client;

use app\common\lib\exception\RuntimeException;
use app\common\log\LogCollector;
use <PERSON>bingji\Tool\http\HttpClient;
use <PERSON>bingji\Tool\http\Response;
use <PERSON><PERSON>ji\Tool\common\Encryption;

class Client extends HttpClient
{
    public const UriPilesGetOnlineStatus = 'business_platform/piles/get_online_status';


    public function sendRequest(): Response
    {
        $starTime = microtime(true);
        $response = parent::sendRequest();

        LogCollector::collectorRunLog(sprintf('url: %s | method: %s | runtime: %s', $this->getUrl(), $this->getMethod(), microtime(true) - $starTime), 'send');
        LogCollector::collectorRunLog(sprintf('body: %s', $this->getBody()), 'send');
        LogCollector::collectorRunLog(sprintf('header: %s', print_r($this->header, true)), 'send');
        LogCollector::collectorRunLog(sprintf('code= %s | failedMsg: %s', $response->httpCode, $response->failedMsg), LogCollector::LevelResponse);
        LogCollector::collectorRunLog(sprintf('response: header= %s', print_r($response->header, true)), LogCollector::LevelResponse);
        LogCollector::collectorRunLog(sprintf('response: body= %s', $response->body), LogCollector::LevelResponse);

        return $response;
    }


    /**
     * 加密
     *
     * @param string $plaintext 加密前的明文
     * @param string $app_secret 密钥
     * @return array 包含加密后的密文、加密算法、初始化向量
     */
    protected function encryption(string $plaintext, string $app_secret): array
    {
        return Encryption::encryption($plaintext, $app_secret);
    }

    /**
     * 解密
     *
     * @param string $ciphertext 密文
     * @param string $app_secret 密钥
     * @return false|string 解密后的明文
     */
    protected function decryption(string $ciphertext, string $iv, string $algorithm, string $app_secret): false|string
    {
        return Encryption::decryption($ciphertext, $iv, $app_secret, $app_secret);
    }

    public function sendPostRequest(string $uri, array $params): Response
    {
        $config = config('my.transfer_service');
        if (empty($config['app_id']) || empty($config['app_secret']) || empty($config['domain'])) {
            throw new RuntimeException('未配置好APP_ID、APP_SECRET、DOMAIN的任意一项', [], RuntimeException::CodeServiceException);
        }

        $plaintext = json_encode($params);
        $resource = $this->encryption($plaintext, $config['app_secret']);
        $resource = array_merge($resource, ['app_id' => $config['app_id']]);


        return $this->setMethod(parent::MethodPost)
            ->setProtocol(parent::ProtocolHttp)
            ->setHeader([
                'Content-type' => 'application/json',
            ])
            ->setDomain($config['domain'])
            ->setUri($uri)
            ->setBody(json_encode($resource))
            ->sendRequest();
    }
}