<?php
declare (strict_types=1);

namespace app\common\lib;

/**
 * 时间工具
 * cbj 2023.10.19 新增
 */
class TimeTool
{
    /**
     * 获取历史时间
     *
     * @param int $day [default=0] 几天前
     * @return int 历史时间戳
     */
    public static function getHistoryTimestamp(int $day = 0): int
    {
        return strtotime(date('Y-m-d 00:00:00', strtotime('-' . --$day . ' day')));
    }

    /**
     * 获取今年年初的时间戳
     *
     * @return int 时间戳
     */
    public static function getThisYearTimestamp(): int
    {
        return strtotime(date('Y-01-01 00:00:00'));
    }

    /**
     * 获取这个月月初的时间戳
     *
     * @return int 时间戳
     */
    public static function getThisMonthTimestamp(): int
    {
        return strtotime(date('Y-m-01 00:00:00'));
    }

    /**
     * 获取今天凌晨的时间戳
     *
     * @return int 时间戳
     */
    public static function getTodayTimestamp(): int
    {
        return strtotime(date('Y-m-d 00:00:00'));
    }

    /**
     * 获取指定年份的时间范围
     *
     * @param int $year 年份
     * @return array [起始时间戳, 结尾时间戳]
     */
    public static function yearTimeFrame(int $year): array
    {
        return [
            strtotime(date($year . '-01-01 00:00:00')),
            strtotime(date(++$year . '-01-01 00:00:00'))
        ];
    }

    /**
     * 获取指定年月的时间范围
     *
     * @param int $year 年份
     * @param int $month 月份
     * @return array [起始时间戳, 结尾时间戳]
     */
    public static function yearMonthTimeFrame(int $year, int $month): array
    {
        $startTime = strtotime(date($year . '-' . $month . '-01 00:00:00'));
        $endTime = strtotime(date($year . '-' . ++$month . '-01 00:00:00'));
        if ($month == 13) {
            $endTime = strtotime(date(++$year . '-' . 1 . '-01 00:00:00'));
        }
        return [
            $startTime,
            $endTime,
        ];
    }

    /**
     * 获取指定年月日的时间范围
     *
     * @param int $year 年份
     * @param int $month 月份
     * @param int $day 日
     * @return array [起始时间戳, 结尾时间戳]
     */
    public static function yearMonthDayTimeFrame(int $year, int $month, int $day): array
    {
        return [
            strtotime(date($year . '-' . $month . '-' . $day . ' 00:00:00')),
            strtotime(date($year . '-' . $month . '-' . $day . ' 23:59:59'))
        ];
    }


}
