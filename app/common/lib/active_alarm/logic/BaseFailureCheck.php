<?php
/** @noinspection PhpUnused */

/** @noinspection PhpStatementHasEmptyBodyInspection */

namespace app\common\lib\active_alarm\logic;

use app\common\lib\entity\StationsActiveAlarmConfig as StationActiveAlarmConfigEntity;
use app\common\log\SocketLogCollector;
use app\common\logic\admin\entity\WorkOrderAssignHandlerRequest;
use app\common\logic\admin\entity\WorkOrderCreateRequest;
use app\common\logic\admin\WorkOrderAutoAssignHandler;
use app\common\logic\admin\WorkOrderCreate;
use app\common\model\WorkOrder;
use app\common\model\WorkOrderProcessingRecords;
use app\common\repositories\AlarmRecord;
use app\common\repositories\StationsActiveAlarmConfig;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\PackageInterface;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use app\common\repositories\WorkOrder as WorkOrderRepositories;
use app\common\lib\active_alarm\entity\AlarmRecord as AlarmRecordEntity;

abstract class BaseFailureCheck
{

    protected PackageInterface $businessPackage;
    protected int $device_type = AlarmRecord::DeviceTypeShots;
    protected int $alarm_type = AlarmRecord::TypeCTR;
    protected int $corp_id;
    protected int $station_id;
    protected ?string $centralized_controller_id = null;
    protected ?StationActiveAlarmConfigEntity $stationActiveAlarmConfig = null;
    protected SocketLogCollector $socketLogCollector;
    /**
     * @var bool 当设备上报的异常与上次的不同时，是否需要自动将之前的异常标记为已回复
     */
    protected bool $is_auto_previous_anomalies = true;

    /**
     * @param SocketLogCollector $socketLogCollector
     * @param PackageInterface $businessPackage
     * @param int $corp_id
     * @param int $station_id
     * @param string|null $centralized_controller_id
     */
    public function __construct(SocketLogCollector $socketLogCollector, PackageInterface $businessPackage, int $corp_id, int $station_id, ?string $centralized_controller_id = null)
    {
        $this->socketLogCollector = $socketLogCollector;
        $this->businessPackage = $businessPackage;
        $this->corp_id = $corp_id;
        $this->station_id = $station_id;
        $this->centralized_controller_id = $centralized_controller_id;
    }


    /**
     * 获取场站主动告警配置
     *
     * @return ?StationActiveAlarmConfigEntity
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getStationActiveAlarmConfig(): ?StationActiveAlarmConfigEntity
    {
        if (is_null($this->stationActiveAlarmConfig)) {
            $this->stationActiveAlarmConfig = StationsActiveAlarmConfig::find($this->station_id);
            if (empty($this->stationActiveAlarmConfig)) {
                return null;
            }
        }
        return $this->stationActiveAlarmConfig;
    }

    abstract protected function verifyReportProtocolIsFailure(): bool;

    abstract protected function getAlarmCode(): array;

    abstract protected function getDeviceId(): string;

//    abstract protected function getRecoveryStateChange(): string;
//
//    abstract protected function getAbnormalStateChanges(): string;

    abstract protected function isAutoGenerateWorkOrder(): bool;

    /**
     * 是否自动分配工单给用户
     *
     * @return bool
     */
    abstract protected function isAutoDispatchWorkOrderToUser(): bool;

    /**
     * 获取指派的用户ID
     *
     * @return ?int
     */
    abstract protected function getDispatchUserId(): ?int;

    abstract protected function generateWorkOrderTitle(): string;

    /**
     * 推送故障恢复的企业微信消息
     *
     * @param AlarmRecordEntity $record
     * @param string $enterprise_wechat_key
     * @return bool
     */
    abstract protected function productionRecoverNotice(AlarmRecordEntity $record, string $enterprise_wechat_key): bool;

    /**
     * 推送故障告警的企业微信消息
     *
     * @param AlarmRecordEntity $record
     * @param string $enterprise_wechat_key
     * @return bool
     */
    abstract protected function productionAlarmNotice(AlarmRecordEntity $record, string $enterprise_wechat_key): bool;

    /**
     * @param array $records
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function batchRecover(array $records): void
    {
        $alarm_codes = array_column($records, 'code');
        $alarm_record_ids = array_column($records, 'id');
        AlarmRecord::useAlarmCodeBatchUpdateRecoverTime($this->device_type, $this->getDeviceId(), $this->alarm_type, $alarm_codes);

        // 将对应的工单标记为已处理
        $workOrderIds = WorkOrderRepositories::getActiveAlarmRelationWorkOrderIds($alarm_record_ids);
        $WorkOrderProcessingRecordsModel = new WorkOrderProcessingRecords();
        $WorkOrderModel = new WorkOrder();

        foreach ($workOrderIds as $workOrderId) {
            $WorkOrderProcessingRecordsModel->addRecord(
                $workOrderId,
                WorkOrderProcessingRecords::UserTypeSystem,
                0,
                WorkOrderProcessingRecords::systemCheckFaultRecovery()
            );

            $WorkOrderModel->updateStatus($workOrderId, WorkOrder::StatusClosed);
        }


        $config = $this->getStationActiveAlarmConfig();

        if (!empty($config) && !empty($config->enterprise_wechat_key)) {
            foreach ($records as $record) {
                $record['recovery_time'] = time();
                $this->productionRecoverNotice(new AlarmRecordEntity($record), $config->enterprise_wechat_key);
            }
        }
    }

    /**
     * 批量告警
     *
     * @param array $alarm_codes
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function batchAlarm(array $alarm_codes): void
    {
        $createWorkOrderRequest = new WorkOrderCreateRequest([
            'title' => $this->generateWorkOrderTitle(),
            'priority' => WorkOrder::PriorityStandard,
            'source' => WorkOrder::SourceAlarmGeneration,
            'template_id' => 0,
            'create_user_type' => WorkOrder::CreateUserTypeAuto,
            // 这个值需要在下边替换为告警记录ID
            'create_user_id' => 0,
            'extra_field_data' => [],
            'corp_id' => $this->corp_id,
            'station_id' => $this->station_id,
            'device_type' => $this->device_type,
            'device_id' => $this->getDeviceId(),
        ]);


        // 生成告警记录
        foreach ($alarm_codes as $alarm_code) {

            $new_alarm_record_id = AlarmRecord::createAlarmRecord(
                $this->corp_id,
                $this->station_id,
                $this->device_type,
                $this->getDeviceId(),
                $this->alarm_type,
                $alarm_code,
            );

            $createWorkOrderRequest->create_user_id = $new_alarm_record_id;

            // 是否自动生成工单
            if ($this->isAutoGenerateWorkOrder()) {

                $data = (new WorkOrderCreate($createWorkOrderRequest))
                    ->setIsVerifyExtraFieldData(false)
                    ->run();

                if ($this->isAutoDispatchWorkOrderToUser()) {
                    $request = new WorkOrderAssignHandlerRequest([
                        'work_order_id' => $data['new_id'],
                        'admin_user_id' => $this->getDispatchUserId()
                    ]);
                    (new WorkOrderAutoAssignHandler($request))->run();
                }
            }


            // 推送告警通知
            $this->pushAlarmNotice($new_alarm_record_id, $alarm_code);
        }
    }

    /**
     * 推送告警通知
     *
     * @param int $new_alarm_record_id
     * @param string $alarm_code
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function pushAlarmNotice(int $new_alarm_record_id, string $alarm_code): void
    {
        $config = $this->getStationActiveAlarmConfig();

        if (!empty($config) && !empty($config->enterprise_wechat_key)) {
            $record = [
                'id' => $new_alarm_record_id,
                'corp_id' => $this->corp_id,
                'station_id' => $this->station_id,
                'device_type' => $this->device_type,
                'device_id' => $this->getDeviceId(),
                'type' => $this->alarm_type,
                'code' => $alarm_code,
                'alarm_time' => time(),
                'recovery_time' => 0
            ];
            $this->productionAlarmNotice(new AlarmRecordEntity($record), $config->enterprise_wechat_key);
        }
    }


    /**
     * @return void
     * @throws DbException
     */
    protected function notFailureHandler(): void
    {
        // todo: 当充电管理、调度算法都离线了的情况下，充电管理重新上线了之后，调度算法的告警也会被自动恢复，这是不对的。
        // 之前这个设备有记录(同一类型的)故障告警
//        $count = AlarmRecord::queryDeviceNotRecoveryAlarmRecordCount($this->device_type, $this->device_id, $this->alarm_type);
        $records = AlarmRecord::queryDeviceNotRecoveryAlarmRecords(
            $this->device_type,
            $this->getDeviceId(),
            $this->alarm_type
        );
        if (count($records) > 0) {
            // 将之前的故障记录标记为已恢复。
//            AlarmRecord::batchUpdateRecoverTime($this->device_type, $this->device_id, $this->alarm_type);
            $this->batchRecover($records);
        } else {
            // 之前这个设备没有记录(同一类型的)故障告警 - 不做处理；
        }
    }

    /**
     * @return bool
     * @throws DbException
     */
    protected function isExistHistoricalFaults(): bool
    {
        $count = AlarmRecord::queryDeviceNotRecoveryAlarmRecordCount(
            $this->device_type,
            $this->getDeviceId(),
            $this->alarm_type,
        );
        return $count > 0;
    }

    /**
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    protected function failureHandler(): void
    {
        // 故障码
        $alarm_codes = $this->getAlarmCode();


        // 之前这个设备有记录(同一类型的)故障告警
        if ($this->isExistHistoricalFaults()) {

            // 验证当前故障是否已被记录
            $records = AlarmRecord::queryDeviceNotRecoveryAlarmRecords(
                $this->device_type,
                $this->getDeviceId(),
                $this->alarm_type,
            );

            $is_equal = false;
            $recover_alarm_records = [];
            foreach ($records as $record) {
                // 之前记录的故障码与当前上报的故障码不同
                if (in_array($record['code'], $alarm_codes)) {
                    $is_equal = true;
                } else {
                    $recover_alarm_records[] = $record;
                }
            }

            // 之前记录的故障码与当前上报的故障码相同
            if ($is_equal === true) {
                if (count($recover_alarm_records) > 0) {

                    // 多个恢复
                    $this->batchRecover($recover_alarm_records);
//                    AlarmRecord::useAlarmCodeBatchUpdateRecoverTime($this->device_type, $this->device_id, $not_equal_codes);

                }

                // 之前记录的故障码与当前上报的故障码不同
            } else {
                if ($this->is_auto_previous_anomalies) {
                    // 将之前的故障记录标记为已恢复
                    $this->batchRecover($recover_alarm_records);
                }

//                AlarmRecord::batchUpdateRecoverTime($this->device_type, $this->device_id, $this->alarm_type);
                // 多个恢复

                // 为当前上报的故障新增多条故障记录
                $this->batchAlarm($alarm_codes);
            }

        } else {
            // 之前这个设备没有记录(同一类型的)故障告警

            // 为当前上报的故障新增多条故障记录
            $this->batchAlarm($alarm_codes);
        }
    }

    /**
     * 故障检测
     *
     * 可能的场景:
     * 1. 上报的协议表示没有故障，分下边两种情况：
     * 1.1. 之前这个设备有记录(同一类型的)故障告警 - 将之前的故障记录标记为已恢复；
     * 1.2. 之前这个设备没有记录(同一类型的)故障告警 - 不做处理；
     * 2. 上报的协议表示已经故障，分下边两种情况：
     * 2.1. 之前这个设备有记录(同一类型的)故障告警，分下边两种情况：
     * 2.1.1. 之前记录的故障码与当前上报的故障码相同 - 不做处理；
     * 2.1.2. 之前记录的故障码与当前上报的故障码不同 - 将之前的故障记录标记为已恢复，为当前上报的故障新增一条故障记录；
     * 2.2. 之前这个设备没有记录(同一类型的)故障告警 - 为当前上报的故障新增一条故障记录；
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function failureCheck(): void
    {
        // 上报的协议表示没有故障
        if ($this->verifyReportProtocolIsFailure() === false) {

            $this->notFailureHandler();

            // 上报的协议表示已经故障
        } else {

            $this->failureHandler();

        }
    }
}