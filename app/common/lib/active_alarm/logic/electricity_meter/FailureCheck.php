<?php
/** @noinspection PhpUnused */


namespace app\common\lib\active_alarm\logic\electricity_meter;

use app\common\lib\active_alarm\entity\AlarmRecord as AlarmRecordEntity;
use app\common\new_queue\EnterpriseWechatMessageQueue;
use app\common\new_queue\entity\ElectricityMeterAlarmBody;
use app\common\new_queue\entity\ElectricityMeterRecoveryBody;
use app\common\repositories\StationsActiveAlarmConfig;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\SyncElectricityMeterPower;
use app\common\lib\active_alarm\logic\BaseFailureCheck;
use app\common\repositories\AlarmRecord;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class FailureCheck extends BaseFailureCheck
{

    protected int $device_type = AlarmRecord::DeviceTypeCentralizedController;
    protected int $alarm_type = AlarmRecord::TypeEMP;

    /**
     * 推送故障恢复的企业微信消息
     *
     * @param AlarmRecordEntity $record
     * @param string $enterprise_wechat_key
     * @return bool
     */
    protected function productionRecoverNotice(AlarmRecordEntity $record, string $enterprise_wechat_key): bool
    {

        $body = new ElectricityMeterRecoveryBody([
            'corp_id' => $record->corp_id,
            'station_id' => $record->station_id,
            'device_id' => $record->device_id,
            'code' => $record->code,
            'alarm_time' => $record->alarm_time,
            'recovery_time' => $record->recovery_time,
            'enterprise_wechat_key' => $enterprise_wechat_key
        ]);

        (new EnterpriseWechatMessageQueue())->electricityMeterRecoveryTraitProduction($body);

        return true;
    }

    /**
     * 推送故障告警的企业微信消息
     *
     * @param AlarmRecordEntity $record
     * @param string $enterprise_wechat_key
     * @return bool
     */
    protected function productionAlarmNotice(AlarmRecordEntity $record, string $enterprise_wechat_key): bool
    {

        $body = new ElectricityMeterAlarmBody([
            'corp_id' => $record->corp_id,
            'station_id' => $record->station_id,
            'device_id' => $record->device_id,
            'code' => $record->code,
            'alarm_time' => $record->alarm_time,
            'enterprise_wechat_key' => $enterprise_wechat_key
        ]);

        (new EnterpriseWechatMessageQueue())->electricityMeterAlarmTraitProduction($body);

        return true;
    }

    /**
     * 是否自动分配工单给用户
     *
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function isAutoDispatchWorkOrderToUser(): bool
    {
        $config = $this->getStationActiveAlarmConfig();
        if (empty($config)) {
            return false;
        }
        return $config->remp_auto_dispatch_user_id > 0;
    }

    /**
     * 获取指派的用户ID
     *
     * @return ?int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getDispatchUserId(): ?int
    {
        $config = $this->getStationActiveAlarmConfig();
        if (is_null($config)) {
            return null;
        }
        return $config->sf_auto_dispatch_user_id;
    }


    /**
     * 是否自动生成工单
     *
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function isAutoGenerateWorkOrder(): bool
    {
        $stationConfig = StationsActiveAlarmConfig::find($this->station_id);
        if (empty($stationConfig)) return false;
        return $stationConfig->remp_is_generate_work_order === $stationConfig::RempIsGenerateWorkOrderYes;
    }

    /**
     * 生成工单标题
     *
     * @return string
     */
    protected function generateWorkOrderTitle(): string
    {
        $codes = $this->getAlarmCode();
        return sprintf('读取电表功率告警 | 故障码: %s', implode(',', $codes));
    }

    protected function verifyReportProtocolIsFailure(): bool
    {
        /**
         * @var SyncElectricityMeterPower $businessPackage
         */
        $businessPackage = $this->businessPackage;
        return $businessPackage->getStatus() !== SyncElectricityMeterPower::StatusReadSuccess;
    }


    protected function getAlarmCode(): array
    {
        return [AlarmRecord::CodeEMP001];
    }


    protected function getDeviceId(): string
    {
        return $this->centralized_controller_id;
    }
}