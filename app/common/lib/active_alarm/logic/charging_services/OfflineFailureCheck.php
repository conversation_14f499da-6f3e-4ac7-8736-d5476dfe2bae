<?php
/** @noinspection PhpUnused */


namespace app\common\lib\active_alarm\logic\charging_services;

use app\common\lib\active_alarm\entity\AlarmRecord as AlarmRecordEntity;
use app\common\lib\active_alarm\logic\BaseFailureCheck;
use app\common\new_queue\EnterpriseWechatMessageQueue;
use app\common\new_queue\entity\CcServiceOfflineAlarmBody;
use app\common\repositories\AlarmRecord;
use app\common\repositories\StationsActiveAlarmConfig;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\CcServiceOfflineNotice;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\CcServiceOnlineNotice;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class OfflineFailureCheck extends BaseFailureCheck
{
    protected int $device_type = AlarmRecord::DeviceTypeCentralizedController;
    protected int $alarm_type = AlarmRecord::TypeTCO;

    /**
     * @var bool 当设备上报的异常与上次的不同时，是否需要自动将之前的异常标记为已回复
     */
    protected bool $is_auto_previous_anomalies = false;

    /**
     * 推送故障恢复的企业微信消息
     *
     * @param AlarmRecordEntity $record
     * @param string $enterprise_wechat_key
     * @return bool
     */
    protected function productionRecoverNotice(AlarmRecordEntity $record, string $enterprise_wechat_key): bool
    {

        return true;
    }

    /**
     * 推送故障告警的企业微信消息
     *
     * @param AlarmRecordEntity $record
     * @param string $enterprise_wechat_key
     * @return bool
     */
    protected function productionAlarmNotice(AlarmRecordEntity $record, string $enterprise_wechat_key): bool
    {
        if ($record->code === AlarmRecord::CodeTCO001) {
            $service_type = CcServiceOnlineNotice::ServiceTypeAlgorithm;
        } else if ($record->code === AlarmRecord::CodeTCO002) {
            $service_type = CcServiceOnlineNotice::ServiceTypeDriver;
        } else {
            $service_type = CcServiceOnlineNotice::ServiceTypeChargeManage;
        }

        $body = new CcServiceOfflineAlarmBody([
            'corp_id' => $record->corp_id,
            'station_id' => $record->station_id,
            'centralized_controller_id' => $record->device_id,
            'service_type' => $service_type,
            'alarm_time' => $record->alarm_time,
            'enterprise_wechat_key' => $enterprise_wechat_key
        ]);

        (new EnterpriseWechatMessageQueue())->ccServiceOfflineAlarmTraitProduction($body);
        return true;
    }

    /**
     * 是否自动分配工单给用户
     *
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function isAutoDispatchWorkOrderToUser(): bool
    {
        $config = $this->getStationActiveAlarmConfig();
        if (empty($config)) {
            return false;
        }

        return $config->cso_auto_dispatch_user_id > 0;
    }

    /**
     * 获取指派的用户ID
     *
     * @return ?int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getDispatchUserId(): ?int
    {
        $config = $this->getStationActiveAlarmConfig();
        if (is_null($config)) {
            return null;
        }
        return $config->sf_auto_dispatch_user_id;
    }

    /**
     * 是否自动生成工单
     *
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function isAutoGenerateWorkOrder(): bool
    {
        $stationConfig = StationsActiveAlarmConfig::find($this->station_id);
        if (empty($stationConfig)) return false;
        return $stationConfig->cso_is_generate_work_order === $stationConfig::PoIsGenerateWorkOrderYes;
    }

    /**
     * 生成工单标题
     *
     * @return string
     */
    protected function generateWorkOrderTitle(): string
    {
        return '能源路由器服务离线告警';
    }

    protected function verifyReportProtocolIsFailure(): bool
    {
        return true;
    }


    protected function getAlarmCode(): array
    {
        /**
         * @var CcServiceOfflineNotice $businessPackage
         */
        $businessPackage = $this->businessPackage;
        if ($businessPackage->getServiceType() === CcServiceOfflineNotice::ServiceTypeChargeManage) {
            return [AlarmRecord::CodeTCO003];
        } else if ($businessPackage->getServiceType() === CcServiceOfflineNotice::ServiceTypeAlgorithm) {
            return [AlarmRecord::CodeTCO001];
        } else {
            return [AlarmRecord::CodeTCO002];
        }
    }


    protected function getDeviceId(): string
    {
        /**
         * @var CcServiceOfflineNotice $businessPackage
         */
        $businessPackage = $this->businessPackage;

        return $businessPackage->getCentralizedControllerId();
    }
}