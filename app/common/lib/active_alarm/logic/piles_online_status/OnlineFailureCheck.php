<?php
/** @noinspection PhpUnused */


namespace app\common\lib\active_alarm\logic\piles_online_status;

use app\common\lib\active_alarm\entity\AlarmRecord as AlarmRecordEntity;
use app\common\lib\active_alarm\logic\BaseFailureCheck;
use app\common\new_queue\EnterpriseWechatMessageQueue;
use app\common\new_queue\entity\PilesOfflineRecoveryBody;
use app\common\repositories\AlarmRecord;
use app\common\repositories\StationsActiveAlarmConfig;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\PilesOnlineNotice;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class OnlineFailureCheck extends BaseFailureCheck
{
    protected int $device_type = AlarmRecord::DeviceTypePiles;
    protected int $alarm_type = AlarmRecord::TypePON;

    /**
     * 推送故障恢复的企业微信消息
     *
     * @param AlarmRecordEntity $record
     * @param string $enterprise_wechat_key
     * @return bool
     */
    protected function productionRecoverNotice(AlarmRecordEntity $record, string $enterprise_wechat_key): bool
    {

        $body = new PilesOfflineRecoveryBody([
            'corp_id' => $record->corp_id,
            'station_id' => $record->station_id,
            'piles_id' => $record->device_id,
            'alarm_time' => $record->alarm_time,
            'recovery_time' => $record->recovery_time,
            'enterprise_wechat_key' => $enterprise_wechat_key
        ]);

        (new EnterpriseWechatMessageQueue())->pilesOfflineRecoveryTraitProduction($body);

        return true;
    }

    /**
     * 推送故障告警的企业微信消息
     *
     * @param AlarmRecordEntity $record
     * @param string $enterprise_wechat_key
     * @return bool
     */
    protected function productionAlarmNotice(AlarmRecordEntity $record, string $enterprise_wechat_key): bool
    {
        return true;
    }

    /**
     * 是否自动分配工单给用户
     *
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function isAutoDispatchWorkOrderToUser(): bool
    {
        $config = $this->getStationActiveAlarmConfig();
        if (is_null($config)) {
            return false;
        }
        return $config->po_auto_dispatch_user_id > 0;
    }

    /**
     * 获取指派的用户ID
     *
     * @return ?int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getDispatchUserId(): ?int
    {
        $config = $this->getStationActiveAlarmConfig();
        if (is_null($config)) {
            return null;
        }
        return $config->sf_auto_dispatch_user_id;
    }

    /**
     * 是否自动生成工单
     *
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function isAutoGenerateWorkOrder(): bool
    {
        $stationConfig = StationsActiveAlarmConfig::find($this->station_id);
        if (empty($stationConfig)) return false;
        return $stationConfig->po_is_generate_work_order === $stationConfig::PoIsGenerateWorkOrderYes;
    }

    /**
     * 生成工单标题
     *
     * @return string
     */
    protected function generateWorkOrderTitle(): string
    {
        return '充电桩离线告警';
    }

    protected function verifyReportProtocolIsFailure(): bool
    {
        return false;
    }


    protected function getAlarmCode(): array
    {
        return [];
    }


    protected function getDeviceId(): string
    {
        /**
         * @var PilesOnlineNotice $businessPackage
         */
        $businessPackage = $this->businessPackage;

        return $businessPackage->getPilesId();
    }
}