<?php
/** @noinspection PhpUnused */


namespace app\common\lib\active_alarm\logic\system_resource;

use app\common\lib\active_alarm\entity\AlarmRecord as AlarmRecordEntity;
use app\common\lib\entity\ActiveAlarmConfig as ActiveAlarmConfigEntity;
use app\common\log\SocketLogCollector;
use app\common\new_queue\EnterpriseWechatMessageQueue;
use app\common\new_queue\entity\CCSystemResourceAlarmBody;
use app\common\new_queue\entity\CCSystemResourceRecoveryBody;
use app\common\repositories\ActiveAlarmConfig;
use app\common\repositories\StationsActiveAlarmConfig;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\CcMonitorSystem;
use app\common\lib\active_alarm\logic\BaseFailureCheck;
use app\common\repositories\AlarmRecord;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\PackageInterface;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class FailureCheck extends BaseFailureCheck
{

    protected int $device_type = AlarmRecord::DeviceTypeCentralizedController;
    protected int $alarm_type = AlarmRecord::TypeMSR;
    protected ActiveAlarmConfigEntity $config;

    /**
     * 推送故障恢复的企业微信消息
     *
     * @param AlarmRecordEntity $record
     * @param string $enterprise_wechat_key
     * @return bool
     */
    protected function productionRecoverNotice(AlarmRecordEntity $record, string $enterprise_wechat_key): bool
    {
        $body = new CCSystemResourceRecoveryBody([
            'corp_id' => $record->corp_id,
            'station_id' => $record->station_id,
            'device_id' => $record->device_id,
            'code' => $record->code,
            'alarm_time' => $record->alarm_time,
            'recovery_time' => $record->recovery_time,
            'enterprise_wechat_key' => $enterprise_wechat_key
        ]);


        (new EnterpriseWechatMessageQueue())->ccSystemResourceRecoveryTraitProduction($body);

        return true;
    }

    /**
     * 推送故障告警的企业微信消息
     *
     * @param AlarmRecordEntity $record
     * @param string $enterprise_wechat_key
     * @return bool
     */
    protected function productionAlarmNotice(AlarmRecordEntity $record, string $enterprise_wechat_key): bool
    {
        $body = new CCSystemResourceAlarmBody([
            'corp_id' => $record->corp_id,
            'station_id' => $record->station_id,
            'device_id' => $record->device_id,
            'code' => $record->code,
            'alarm_time' => $record->alarm_time,
            'enterprise_wechat_key' => $enterprise_wechat_key
        ]);

        (new EnterpriseWechatMessageQueue())->ccSystemResourceAlarmTraitProduction($body);

        return true;
    }

    /**
     * @param SocketLogCollector $socketLogCollector
     * @param PackageInterface $businessPackage
     * @param int $corp_id
     * @param int $station_id
     * @param string|null $centralized_controller_id
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function __construct(SocketLogCollector $socketLogCollector, PackageInterface $businessPackage, int $corp_id, int $station_id, ?string $centralized_controller_id = null)
    {
        parent::__construct($socketLogCollector, $businessPackage, $corp_id, $station_id, $centralized_controller_id);
        $this->config = ActiveAlarmConfig::find();
    }

    /**
     * 是否自动分配工单给用户
     *
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function isAutoDispatchWorkOrderToUser(): bool
    {
        $config = $this->getStationActiveAlarmConfig();
        if (is_null($config)) {
            return false;
        }
        return $config->ccsr_auto_dispatch_user_id > 0;
    }

    /**
     * 获取指派的用户ID
     *
     * @return ?int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getDispatchUserId(): ?int
    {
        $config = $this->getStationActiveAlarmConfig();
        if (is_null($config)) {
            return null;
        }
        return $config->sf_auto_dispatch_user_id;
    }


    /**
     * 是否自动生成工单
     *
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function isAutoGenerateWorkOrder(): bool
    {
        $stationConfig = StationsActiveAlarmConfig::find($this->station_id);
        if (empty($stationConfig)) return false;
        return $stationConfig->ccsr_is_generate_work_order === $stationConfig::CcsrIsGenerateWorkOrderYes;
    }

    /**
     * 生成工单标题
     *
     * @return string
     */
    protected function generateWorkOrderTitle(): string
    {
        $codes = $this->getAlarmCode();
        return sprintf('能源路由器资源告警 | 故障码: %s', implode(',', $codes));
    }

    protected function verifyReportProtocolIsFailure(): bool
    {
        return count($this->getAlarmCode()) > 0;
    }


    protected function getAlarmCode(): array
    {
        $codes = [];

        /**
         * @var CcMonitorSystem $businessPackage
         */
        $businessPackage = $this->businessPackage;

        if ($businessPackage->getCpuUsage() > $this->config->ccsr_cpu_alarm_value) {
            $codes[] = AlarmRecord::CodeMSR003;
        }
        if ($businessPackage->getMemoryAvailable() < $this->config->ccsr_memory_alarm_value) {
            $codes[] = AlarmRecord::CodeMSR001;
        }
        if ($businessPackage->getDiskFree() < $this->config->ccsr_disk_alarm_value) {
            $codes[] = AlarmRecord::CodeMSR002;
        }

        return $codes;
    }


    protected function getDeviceId(): string
    {
        return $this->centralized_controller_id;
    }
}