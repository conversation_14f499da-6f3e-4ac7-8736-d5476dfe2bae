<?php
/** @noinspection PhpUnused */


namespace app\common\lib\active_alarm\logic\charging_gun_fault;

use app\common\lib\active_alarm\entity\AlarmRecord as AlarmRecordEntity;
use app\common\lib\active_alarm\logic\BaseFailureCheck;
use app\common\lib\entity\ActiveAlarmConfig as ActiveAlarmConfigEntity;
use app\common\log\SocketLogCollector;
use app\common\new_queue\EnterpriseWechatMessageQueue;
use app\common\new_queue\entity\MonitorFailureAlarmBody;
use app\common\new_queue\entity\MonitorFailureRecoveryBody;
use app\common\repositories\AlarmRecord;
use app\common\repositories\StationsActiveAlarmConfig;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\UploadRealtimeMonitoringDataPackage;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class MonitorFailureCheck extends BaseFailureCheck
{

    protected int $device_type = AlarmRecord::DeviceTypeShots;
    protected int $alarm_type = AlarmRecord::TypeCRS;
    protected ActiveAlarmConfigEntity $config;


    /**
     * 推送故障恢复的企业微信消息
     *
     * @param AlarmRecordEntity $record
     * @param string $enterprise_wechat_key
     * @return bool
     */
    protected function productionRecoverNotice(AlarmRecordEntity $record, string $enterprise_wechat_key): bool
    {
        $body = new MonitorFailureRecoveryBody([
            'corp_id' => $record->corp_id,
            'station_id' => $record->station_id,
            'device_id' => $record->device_id,
            'code' => $record->code,
            'alarm_time' => $record->alarm_time,
            'recovery_time' => $record->recovery_time,
            'enterprise_wechat_key' => $enterprise_wechat_key
        ]);

        (new EnterpriseWechatMessageQueue())->monitorFailureRecoveryTraitProduction($body);

        return true;
    }

    /**
     * 推送故障告警的企业微信消息
     *
     * @param AlarmRecordEntity $record
     * @param string $enterprise_wechat_key
     * @return bool
     */
    protected function productionAlarmNotice(AlarmRecordEntity $record, string $enterprise_wechat_key): bool
    {
        $body = new MonitorFailureAlarmBody([
            'corp_id' => $record->corp_id,
            'station_id' => $record->station_id,
            'device_id' => $record->device_id,
            'code' => $record->code,
            'alarm_time' => $record->alarm_time,
            'enterprise_wechat_key' => $enterprise_wechat_key
        ]);

        (new EnterpriseWechatMessageQueue())->monitorFailureAlarmTraitProduction($body);

        return true;
    }

    /**
     * 是否自动生成工单
     *
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function isAutoGenerateWorkOrder(): bool
    {
        $stationConfig = StationsActiveAlarmConfig::find($this->station_id);
        if (empty($stationConfig)) return false;
        return $stationConfig->sf_is_generate_work_order === $stationConfig::SfIsGenerateWorkOrderYes;
    }

    /**
     * 生成工单标题
     *
     * @return string
     */
    protected function generateWorkOrderTitle(): string
    {
        $codes = $this->getAlarmCode();
        return sprintf('充电枪故障告警 | 故障码: %s', implode(',', $codes));
    }

    protected function verifyReportProtocolIsFailure(): bool
    {
        /**
         * @var UploadRealtimeMonitoringDataPackage $businessPackage
         */
        $businessPackage = $this->businessPackage;
        return $businessPackage->getStatus() === UploadRealtimeMonitoringDataPackage::StatusFault;
    }

    /**
     * 是否自动分配工单给用户
     *
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function isAutoDispatchWorkOrderToUser(): bool
    {
        $config = $this->getStationActiveAlarmConfig();
        if (is_null($config)) {
            return false;
        }

        return $config->sf_auto_dispatch_user_id > 0;
    }

    /**
     * 获取指派的用户ID
     *
     * @return ?int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getDispatchUserId(): ?int
    {
        $config = $this->getStationActiveAlarmConfig();
        if (is_null($config)) {
            return null;
        }
        return $config->sf_auto_dispatch_user_id;
    }


    protected function getAlarmCode(): array
    {
        $codes = [];
        /**
         * @var UploadRealtimeMonitoringDataPackage $businessPackage
         */
        $businessPackage = $this->businessPackage;
        $hardwareFailure = $businessPackage->getHardwareFailure();
        for ($i = 0; $i < 16; $i++) {
            if (isset($hardwareFailure[$i]) && $hardwareFailure[$i] === 1) {
                $code = AlarmRecord::getCRSCode($i);
                if (!is_null($code)) {
                    $codes[] = $code;
                } else {
                    $this->socketLogCollector->collectorRunLog(
                        sprintf('没有查询到对应的告警代码:%s - %s', 'UploadRealtimeMonitoringDataPackage', $i),
                        SocketLogCollector::LevelWarning
                    );
                }
            }
        }

        if (count($codes) === 0) {
            $this->socketLogCollector->collectorRunLog(
                '没有查询到具体故障原因',
                SocketLogCollector::LevelWarning
            );
        }
        return $codes;
    }


    protected function getDeviceId(): string
    {
        /**
         * @var UploadRealtimeMonitoringDataPackage $businessPackage
         */
        $businessPackage = $this->businessPackage;
        return $businessPackage->getPilesId() * 100 + $businessPackage->getShotsNumber();
    }
}