<?php
/** @noinspection PhpUnused */

namespace app\common\lib\active_alarm\entity;

class AlarmRecord
{
    public int $id;
    public int $corp_id;
    public int $station_id;
    public int $device_type;
    public string $device_id;
    public int $type;
    public string $code;
    public int $alarm_time;
    public int $recovery_time;

    public function __construct(array $options)
    {
        foreach ($options as $key => $value) {
            if (property_exists($this, $key)) $this->{$key} = $value;
        }
    }
}