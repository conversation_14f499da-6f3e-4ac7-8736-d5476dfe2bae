<?php

namespace app\common\lib\send_handle_timer;

use app\common\lib\send_handle_timer\request\RemoveRemoteAccountBalanceUpdateRequest;

class RemoveRemoteAccountBalanceUpdate extends BaseHandler
{
    protected RemoveRemoteAccountBalanceUpdateRequest $request;

    public function __construct(RemoveRemoteAccountBalanceUpdateRequest $request)
    {
        $this->request = $request;
    }

    public function handler(): void
    {
        trace('删除-远程账户余额更新', '下发指令定时器');
        $timer_key = '远程账户余额更新' . $this->request->request_id;
        TimerManager::getInstance()->delTimer($timer_key);
        trace('删除-远程账户余额更新==>完成：' . $this->request->request_id, '下发指令定时器');
    }
}