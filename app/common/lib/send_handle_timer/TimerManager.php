<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\send_handle_timer;

use Predis\Client as Predis;
use Workerman\Connection\AsyncTcpConnection;
use Workerman\Lib\Timer;
use Throwable;

class TimerManager
{
    protected Predis $redis;

    protected static ?TimerManager $instance = null;

    private array $timer = [];

    protected function __construct()
    {
        $this->redis = new Predis(config('my.redis2'));
    }

    public static function getInstance(): static
    {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 加载下发指令处理定时器
     * lwj 2023.9.12 新增
     * lwj 2023.9.27 修改
     * @return void
     */
    public function loadTimer(): void
    {
        $timer_list = $this->redis->hgetall('下发指令处理定时器-列表');
        if (!$timer_list) {
            echo '定时器列表不存在' . PHP_EOL;
            return;
        }

        foreach ($timer_list as $k => $v) {
            $vv = json_decode($v, true);
            $timer_time = $vv['timer_time'] ?? config('my.send_handle_timer_timeout');
            $remainder = time() - $vv['time'] - $timer_time;
            if ($remainder < 0) {
                $this->AddTimer($k, $vv, abs($remainder));
            } else {
                $this->redis->hdel('充电桩定时器-列表', [$k]);
            }
        }
    }

    /**
     * 发送到异步执行超时处理任务进程
     * lwj 2023.9.12 新增
     * @param array $task_data
     * @return void
     */
    private function SendTimeoutHandleJob(array $task_data): void
    {
        try {
            // 与远程task服务建立异步连接，ip为远程task服务的ip，如果是本机就是127.0.0.1，如果是集群就是lvs的ip
            $task_connection = new AsyncTcpConnection(config('my.TimeoutHandleJobApi'));
            // 发送数据
            trace('发送到异步执行超时处理任务进程：发送==>' . json_encode_cn($task_data), '下发指令定时器');
            $task_connection->send(json_encode_cn($task_data));
            // 异步获得结果
            $task_connection->onMessage = function (AsyncTcpConnection $task_connection, $task_result) {
                trace('发送到异步执行超时处理任务进程：结果=》' . $task_result, '下发指令定时器');
                // 获得结果后记得关闭异步连接
                $task_connection->close();
            };
            // 执行异步连接
            $task_connection->connect();
        } catch (Throwable $e) {
            trace('发送到异步执行超时处理任务进程：异常=》' . $e->getMessage(), '下发指令定时器');
        }

    }

    /**
     * 添加下发指令处理定时器
     * lwj 2023.9.12 新增
     * lwj 2023.9.27 修改
     * @param string|int $timer_key
     * @param array $data
     * @param int|null $timeout
     * @return void
     */
    public function addTimer(string|int $timer_key, array $data, int|null $timeout = null): void
    {
        trace('添加定时器：开始=》' . $timer_key, '下发指令定时器');
        if (isset($this->timer[$timer_key])) {
            Timer::del($this->timer[$timer_key]);
            unset($this->timer[$timer_key]);
            trace('存在定时器，先删除=》' . $timer_key, '下发指令定时器');
        }

        if (!$timeout) $timeout = config('my.send_handle_timer_timeout');
        trace('定时器时间=》' . $timeout, '下发指令定时器');
        $this->timer[$timer_key] = Timer::add($timeout, function () use ($data, $timer_key) {
            trace('执行定时器:数据=》' . json_encode_cn($data), '下发指令定时器');
            $this->SendTimeoutHandleJob($data);
            $this->redis->hdel('下发指令处理定时器-列表', [$timer_key]);
            unset($this->timer[$timer_key]);
            trace('定时器执行完', '下发指令定时器');
        }, '', false);

        $data['timeout'] = $timeout;
        $this->redis->hset('下发指令处理定时器-列表', $timer_key, json_encode_cn($data));
        trace('添加定时器：结束=》' . $timer_key, '下发指令定时器');
    }

    /**
     * 删除下发指令处理定时器
     * lwj 2023.9.12 新增
     * lwj 2023.9.27 修改
     * @param string|int $timer_key
     * @return void
     */
    public function delTimer(string|int $timer_key): void
    {
        if (isset($this->timer[$timer_key])) {
            Timer::del($this->timer[$timer_key]);
            unset($this->timer[$timer_key]);
            trace('删除定时器=》' . $timer_key, '下发指令定时器');
        }
        $this->redis->hdel('下发指令处理定时器-列表', [$timer_key]);
    }


}