<?php
/** @noinspection PhpUnusedParameterInspection */
declare (strict_types=1);

namespace app\common\lib;

use app\common\log\LogCollector;
use Throwable;

/**
 * 异常日志收集器
 * cbj 2023.10.19 新增
 */
class ExceptionLogCollector
{
    protected static function getExceptionInfo(Throwable $e): string
    {
        return sprintf('File: %s, Line: %d, Code: %d, Message: %s',
            $e->getFile(), $e->getLine(), $e->getCode(), $e->getMessage());
    }

    public static function collect(Throwable $e, bool $isCommand = false): void
    {
        if ($isCommand === false) {
            LogCollector::collectorRunLog(self::getExceptionInfo($e), LogCollector::LevelError);
//            LogCollector::collectorRunLog(print_r($e->getTrace(), true), LogCollector::LevelError);
        } else {
            trace(self::getExceptionInfo($e), LogCollector::LevelError);
            trace(print_r($e->getTrace(), true), LogCollector::LevelError);
        }
    }

    public static function recordErrorLog(string $message, array $debugBacktrace = []): void
    {
        $string = json_encode($debugBacktrace);
        LogCollector::collectorRunLog($message . ' => ' . $string, LogCollector::LevelError);
    }
}