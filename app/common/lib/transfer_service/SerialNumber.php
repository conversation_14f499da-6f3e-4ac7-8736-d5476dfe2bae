<?php
/** @noinspection PhpUnused */

namespace app\common\lib\transfer_service;

use RedisException;
use Redis;

class SerialNumber
{
    protected static ?Redis $redis = null;

    /**
     * 获取Redis实例
     *
     * @return Redis
     * @throws RedisException
     */
    protected static function getRedisInstance(): Redis
    {
        if (is_null(self::$redis)) {
            self::$redis = self::connectRedis(config('my.redis2'));
        }
        return self::$redis;
    }

    /**
     * 连接Redis
     *
     * @param array $config Redis配置
     * @return Redis
     * @throws RedisException
     */
    protected static function connectRedis(array $config): Redis
    {
        // TODO:缺少断线重连机制和异常处理
        $redis = new Redis();
        if (!empty($config['persistent'])) {
            $redis->pconnect($config['host'], (int)$config['port'], (int)$config['timeout']);
        } else {
            $redis->connect($config['host'], (int)$config['port'], (int)$config['timeout']);
        }
        if (!empty($config['password'])) {
            $redis->auth($config['password']);
        }
        if (!empty($config['database'])) {
            $redis->select($config['database']);
        }

        return $redis;
    }

    /**
     * 自增序列号
     *
     * @param string $type
     * @return int
     * @throws RedisException
     */
    public static function increaseSequence(string $type = 'transfer'): int
    {
        $redis = self::getRedisInstance();

        $key = sprintf('sequence_%s', $type);

        return $redis->incr($key) % 65530;
    }

    public static function sequenceSetTo0(string $type = 'transfer'): bool
    {
        $redis = self::getRedisInstance();

        $key = sprintf('sequence_%s', $type);

        return $redis->set($key, 0);
    }
}