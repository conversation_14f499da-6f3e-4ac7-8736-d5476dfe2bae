<?php
/** @noinspection PhpUnused */

namespace app\common\lib\transfer_service;

use app\common\queue\TransferServiceReadQueue;
use think\facade\Log;
use Throwable;

class TransferServiceClientRead extends Client
{

    public function run(): void
    {
        $this->start();
    }

    protected function log(string $message): void
    {
        Log::channel('charge_client_read')->record(sprintf("[%s] %s", date('c'), $message));
    }

    protected function onMessageHandler(string $message): void
    {
        // 推入队列中
        try {
            app(TransferServiceReadQueue::class)->push($message);
        } catch (Throwable $e) {
            $this->log(print_r($e->getTrace(), true));
        }
    }
}