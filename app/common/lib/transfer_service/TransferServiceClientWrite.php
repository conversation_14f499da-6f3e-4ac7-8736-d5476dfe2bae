<?php
/** @noinspection PhpUnused */

namespace app\common\lib\transfer_service;

use app\common\lib\exception\RuntimeException;
use app\common\queue\TransferServiceWriteQueue;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\BasePackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\LoginAnswerPackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\LoginPackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\Kernel;
use Chenbingji\Tool\common\Encryption;
use think\facade\Log;
use Throwable;
use Workerman\Connection\AsyncTcpConnection;
use Workerman\Timer;


class TransferServiceClientWrite
{
    protected int $serial_number = 0;

    protected Context $context;
    protected TransferServiceWriteQueue $transferServiceWriteQueue;
    protected ?int $consumer_timer_id;

    protected string $log_prefix = '中转服务客户端 - 只写: ';

    protected string $host = '';

    protected string $app_id = '';
    protected string $app_secret = '';

    public function __construct()
    {
        $this->context = new Context();
        $this->transferServiceWriteQueue = new TransferServiceWriteQueue();
        $this->transferServiceWriteQueue->setContext($this->context);
        $this->host = config('my.transfer_service.host', '');
        $this->app_id = config('my.transfer_service.app_id', '');
        $this->app_secret = config('my.transfer_service.app_secret', '');

    }

    protected function log(string $message, $level = 'debug'): void
    {
        Log::channel('charge_client_write')->record(sprintf("[%s] %s", date('c'), $message), $level);
    }

    protected function openConsumer(): void
    {
        $timer_id = Timer::add(0.01, function () {
            if ($this->context->status === Context::StatusLoginSuccess) {
                $this->transferServiceWriteQueue->consumer();
            }
        });

        if (is_bool($timer_id)) throw new RuntimeException('开启失败');

        $this->consumer_timer_id = $timer_id;
    }

    protected function closeConsumer(): void
    {
        if (isset($this->consumer_timer_id) && $this->consumer_timer_id > 0) {
            Timer::del($this->consumer_timer_id);
        }
    }

    public function run(): void
    {
        try {
            $this->context->connection = $connection = new AsyncTcpConnection(config('my.transfer_service.host'));
            // 当连接建立成功时
            $connection->onConnect = function (AsyncTcpConnection $connection) {
                $this->log(sprintf('[%s] 建立连接', $connection->id));

                $this->login($connection);
            };

            $connection->onMessage = function (AsyncTcpConnection $connection, $data) {

                $this->log(sprintf('接收到请求: %s', $data));
                $package = Kernel::decode($data);

                // 未登录状态下，等待登陆应答
                if ($this->context->status !== Context::StatusLoginSuccess) {

                    $this->log(sprintf('解析结果: %s', $package->encode()));
                    if ($package->getIndex() === BasePackage::TypeLoginAnswer) {
                        /**
                         * @var LoginAnswerPackage $loginAnswer
                         */
                        $loginAnswer = $package->getBody();
                        if ($loginAnswer->getResult() === LoginAnswerPackage::StatusSuccess) {
                            $this->context->status = Context::StatusLoginSuccess;
                            $this->log(sprintf('[%s] 登陆成功', $connection->id));

                            // 开启心跳循环
                            $this->createHeartbeatLoop($connection);

                            // 开启消费者
                            $this->openConsumer();
                        } else {
                            $this->context->status = Context::StatusLoginFailed;
                            $this->log(sprintf('[%s] 登陆失败', $connection->id));
                        }
                    }
                }
            };
            $connection->onClose = function (AsyncTcpConnection $connection) {
                $this->context->status = Context::StatusOffline;
                $this->closeConsumer();
                $connection->reconnect(3);
                $this->log(sprintf('[%s] 断开连接', $connection->id));
            };

            $connection->onError = function (AsyncTcpConnection $connection, $code, $msg) {
                $this->log(sprintf('client_id: %s, code: %s, msg: %s, ', $connection->id, $code, $msg), 'error');
            };

            $connection->connect();
        } catch (Throwable $e) {
            $this->log(print_r([
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'code' => $e->getCode()
            ], true));
            $this->log(print_r($e->getTrace(), true));
        }
    }

    protected function getSerialNumber(): int
    {
        return SerialNumber::increaseSequence();
    }

    protected function login(AsyncTcpConnection $connection): void
    {
        $this->context->status = Context::StatusLoggingIn;


        $data = Encryption::encryption(json_encode([
            'type' => LoginPackage::TypeWriteOnly
        ]), $this->app_secret);

        $data = array_merge($data, [
            'app_id' => $this->app_id
        ]);

        $package = Kernel::create($this->getSerialNumber(), BasePackage::TypeLogin, $data);

        $this->log(sprintf('发送登陆: %s', $package->encode()));

        $connection->send($package->encode());
    }

    public function createHeartbeatLoop(AsyncTcpConnection $connection): void
    {
        Timer::add(20, function (AsyncTcpConnection $connection) {

            $package = Kernel::create($this->getSerialNumber(), BasePackage::TypePing);

            $this->log(sprintf('发送心跳: %s', $package->encode()));

            $connection->send($package->encode());

        }, [$connection]);
    }
}
