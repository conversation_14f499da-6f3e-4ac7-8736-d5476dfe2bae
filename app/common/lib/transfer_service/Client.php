<?php
/** @noinspection PhpUnusedLocalVariableInspection */
/** @noinspection PhpUnusedParameterInspection */
/** @noinspection PhpUnused */

namespace app\common\lib\transfer_service;

use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\BasePackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\LoginAnswerPackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\LoginPackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\Kernel;
use <PERSON>bingji\Tool\common\Encryption;
use Ramsey\Uuid\Uuid;
use RedisException;
use Throwable;
use Workerman\Connection\AsyncTcpConnection;
use Workerman\Timer;

abstract class Client
{
    public const DisconnectErrorBrokenPipe = 32;
    public const DisconnectErrorConnectionResetByPeer = 104;

    public const IsCloseNot = 1; // 未主动关闭
    public const IsCloseYes = 2; // 已主动关闭

    public const IsCloseName = [
        self::IsCloseNot => '未主动关闭',
        self::IsCloseYes => '已主动关闭'
    ];

    public const ConnectStatusNot = 1; // 未连接
    public const ConnectStatusProgress = 2; // 进行中
    public const ConnectStatusYes = 3; // 已连接

    public const ConnectStatusName = [
        self::ConnectStatusNot => '未连接',
        self::ConnectStatusProgress => '进行中',
        self::ConnectStatusYes => '已连接'
    ];

    public const LoginStatusNot = 1; // 未登陆
    public const LoginStatusProgress = 2; // 登陆中
    public const LoginStatusYes = 3; // 已登陆
    public const LoginStatusName = [
        self::LoginStatusNot => '未登录',
        self::LoginStatusProgress => '登陆中',
        self::LoginStatusYes => '已登陆'
    ];
    protected int $serial_number = 0;

    protected int $is_close = self::IsCloseNot;
    protected int $connect_status = self::ConnectStatusNot;
    protected int $login_status = self::LoginStatusNot;

    protected string $heartbeat_loop_random = '';
    protected int $heartbeat_loop_timer = 0;

    protected string $host = '';

    protected string $app_id = '';
    protected string $app_secret = '';

    protected ?AsyncTcpConnection $connect = null;

    public function __construct()
    {
        $this->host = config('my.transfer_service.host', '');
        $this->app_id = config('my.transfer_service.app_id', '');
        $this->app_secret = config('my.transfer_service.app_secret', '');
    }

    public function start(): void
    {
        $this->connect();
    }

    public function connect(): bool
    {
        if ($this->connect_status >= self::ConnectStatusProgress) return true;
        $this->updateConnectStatus(self::ConnectStatusProgress);

        try {
            // 建立socket连接到内部推送端口
            $this->connect = new AsyncTcpConnection($this->host);

            $this->connect->onConnect = [$this, 'onConnectSuccess'];
            $this->connect->onMessage = [$this, 'onMessage'];
            $this->connect->onClose = [$this, 'onClose'];
            $this->connect->onError = [$this, 'onError'];

            $this->connect->connect();

        } catch (Throwable $e) {
            $this->onConnectFailed();
            return false;
        }

        return true;
    }

    public function onError(AsyncTcpConnection $connection, $code, $msg): void
    {
        $this->log(sprintf('client_id: %s, code: %s, msg: %s, ', $connection->id, $code, $msg));
    }

    protected function onConnectFailed(): void
    {
        $this->updateConnectStatus(self::ConnectStatusNot);

        $this->reconnect();
    }

    protected function reconnect(): void
    {
        sleep(3);
        $this->connect();
    }

    /**
     * 发送登陆协议包
     *
     * @return void
     * @throws RedisException
     */
    protected function sendLoginPackage(): void
    {
        $this->updateLoginStatus(self::LoginStatusProgress);

        $data = Encryption::encryption(json_encode([
            'type' => LoginPackage::TypeReadOnly
        ]), $this->app_secret);

        $data = array_merge($data, [
            'app_id' => $this->app_id
        ]);

        $package = Kernel::create($this->getSerialNumber(), BasePackage::TypeLogin, $data);

        $this->sendMessage($package->encode());
    }

    protected function sendMessage(string $message): bool|null
    {
        $result = $this->connect->send(sprintf("%s\n", $message));
        $this->log(sprintf('[send] %s -> %s | 发送结果: %s', $message, $this->host, $result ? '成功' : '失败'));
        return $result;
    }


    public function onConnectSuccess(AsyncTcpConnection $connection): void
    {
        $this->updateConnectStatus(self::ConnectStatusYes);

        try {

            $this->sendLoginPackage();

        } catch (Throwable $e) {

            $this->collectAbnormalLog($e);

            if (in_array($e->getCode(), [self::DisconnectErrorBrokenPipe, self::DisconnectErrorConnectionResetByPeer])) {
                $this->reconnect();
            }
        }
    }

    abstract protected function log(string $message);

    protected function updateConnectStatus(int $connect_status): void
    {
        if ($this->connect_status === $connect_status) return;

        $this->log(sprintf('connect status change: old = %s , new = %s'
                , self::ConnectStatusName[$this->connect_status]
                , self::ConnectStatusName[$connect_status])
        );

        $this->connect_status = $connect_status;
    }


    protected function updateIsClose(int $is_close): void
    {
        if ($this->is_close === $is_close) return;

        $this->log(sprintf('is close change: old = %s , new = %s'
                , self::IsCloseName[$this->is_close]
                , self::IsCloseName[$is_close])
        );

        $this->is_close = $is_close;
    }

    protected function updateLoginStatus(int $login_status): void
    {
        if ($this->login_status === $login_status) return;

        $this->log(sprintf('login status change: old = %s , new = %s'
                , self::LoginStatusName[$this->login_status]
                , self::LoginStatusName[$login_status])
        );

        $this->login_status = $login_status;
    }

    abstract protected function onMessageHandler(string $message): void;


    public function onMessage(AsyncTcpConnection $connection, string $message): void
    {
        $this->log(sprintf('[receive] %s <- %s', $message, $this->host));

        if ($this->login_status === self::LoginStatusYes) {

            $this->onMessageHandler($message);

        } else {
            $package = Kernel::decode($message);

            if ($package->getIndex() === BasePackage::TypeLoginAnswer) {
                /**
                 * @var LoginAnswerPackage $body
                 */
                $body = $package->getBody();
                if ($body->getResult() === LoginAnswerPackage::StatusSuccess) {
                    $this->loginSuccessHandler();
                } else {
                    $this->loginFailedHandler();
                }
            }
        }
    }

    protected function collectAbnormalLog(Throwable $e): void
    {
        $this->log(print_r([
            'message' => $e->getMessage(),
            'line' => $e->getLine(),
            'file' => $e->getFile(),
            'code' => $e->getCode()
        ], true));
        $this->log(print_r($e->getTrace(), true));
    }

    protected function loginSuccessHandler(): void
    {
        if ($this->login_status === self::LoginStatusYes) return;
        $this->updateLoginStatus(self::LoginStatusYes);

        // 心跳循环
        $this->openHeartbeatLoop();
    }

    protected function loginFailedHandler(): void
    {
        $this->updateLoginStatus(self::LoginStatusNot);
    }

    public function onClose(AsyncTcpConnection $connection): void
    {
        $this->heartbeat_loop_random = '';
        $this->onDisconnect();
    }

    protected function onDisconnect(): void
    {
        $this->updateLoginStatus(self::LoginStatusNot);
        $this->updateConnectStatus(self::ConnectStatusNot);
        // 重连
        $this->reconnect();
    }

    /**
     * 获取序列号
     *
     * @return int
     * @throws RedisException
     */
    protected function getSerialNumber(): int
    {
        return SerialNumber::increaseSequence();
    }

    protected function openHeartbeatLoop(): void
    {
        $this->heartbeat_loop_random = Uuid::uuid4();
        $this->log(sprintf('心跳循环开启 : %s', $this->heartbeat_loop_random));
        $this->heartbeat_loop_timer = $this->heartbeatLoop($this->heartbeat_loop_random);
    }

    protected function heartbeatLoopClose(string $random): void
    {
        $this->log(sprintf('心跳循环关闭 : %s', $random));
        Timer::del($this->heartbeat_loop_timer);
    }

    protected function heartbeatLoop(string $random): int
    {
        return Timer::add(20, function (string $random) {

            if ($random !== $this->heartbeat_loop_random) {
                $this->heartbeatLoopClose($random);
            }

            try {
                $package = Kernel::create($this->getSerialNumber(), BasePackage::TypePing);

                $this->sendMessage($package->encode());
            } catch (Throwable $e) {
                $this->collectAbnormalLog($e);
                if (in_array($e->getCode(), [32, 104])) {
                    $this->heartbeatLoopClose($random);
                }
            }
        }, [$random]);
    }
}