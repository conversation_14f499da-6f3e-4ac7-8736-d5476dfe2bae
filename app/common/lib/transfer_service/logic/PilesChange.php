<?php
/** @noinspection PhpUnused */

namespace app\common\lib\transfer_service\logic;

use app\common\log\SocketLogCollector;
use app\common\model\Piles as PilesModel;
use app\common\model\PilesStatus;
use app\common\queue\TransferServiceWriteQueue;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\BasePackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\PilesChangeAnswer;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\PilesChange as PilesChangeProtocol;
use LieHuoHuYu\ChargeTransferServiceProtocol\Kernel;
use RedisException;
use think\db\exception\DbException;

class PilesChange extends Base
{

    protected SocketLogCollector $socketLogCollector;
    protected BasePackage $package;

    public function __construct(SocketLogCollector $socketLogCollector, BasePackage $package)
    {
        $this->socketLogCollector = $socketLogCollector;
        $this->package = $package;
    }

    /**
     * 回复应答
     *
     * @param string $protocol_index
     * @param int $result
     * @param int $failure_reason_code
     * @return void
     * @throws RedisException
     */
    protected function replyAnswer(string $protocol_index, int $result, int $failure_reason_code = PilesChangeAnswer::FailureReasonCodeNone): void
    {
        $answerPackage = Kernel::create($this->package->getSerialNumber(), $protocol_index, [
            'result' => $result,
            'failure_reason_code' => $failure_reason_code
        ]);
        (new TransferServiceWriteQueue())->push($answerPackage->encode());
    }


    /**
     * @return void
     * @throws RedisException
     */
    public function run(): void
    {
        $this->openExceptionCapture(function () {
            /**
             * @var PilesChangeProtocol $body
             */
            $body = $this->package->getBody();

            switch ($body->getOperationType()) {
                case PilesChangeProtocol::OperationTypeAdd:
                    $this->addHandler($body->getPiles());
                    break;
                case PilesChangeProtocol::OperationTypeUpdate:
                    $this->updateHandler($body->getPiles());
                    break;
                case PilesChangeProtocol::OperationTypeDelete:
                    $this->deleteHandler($body->getPiles());
                    break;
                case PilesChangeProtocol::OperationTypeAllSync:
                    $this->allSyncHandler($body->getPiles());
                    break;
            }

            $this->replyAnswer(BasePackage::TypePilesChangeAnswer, PilesChangeAnswer::ResultSuccess);
        }, true);
    }

    /**
     * 全部同步操作
     *
     * @param array $piles_list
     * @return void
     * @throws DbException
     */
    protected function allSyncHandler(array $piles_list): void
    {
        $pilesModel = new PilesModel();
        // 删除现有的所有充电桩信息
        $pilesModel->deleteAll();

        $inserts = [];
        $online_piles_ids = [];
        foreach ($piles_list as $piles) {
            $inserts[] = [
                'id' => $piles['id'],
                'name' => $piles['name'],
                'corp_id' => $piles['corp_id'],
                'station_id' => $piles['station_id'],
                'ac_dc' => $piles['ac_dc'],
                'type' => $piles['type'],
                'model' => $piles['model'],
                'power' => $piles['power'],
                'comm_id' => $piles['comm_id'],
                'phase_name' => $piles['phase_name']
            ];
            if (isset($piles['is_online']) && (int)$piles['is_online'] === PilesChangeProtocol::IsOnlineYes) {
                $online_piles_ids[] = $piles['id'];
            }
        }
        $pilesModel->batchAddPiles($inserts);

        // 更新充电桩在线状态
        (new PilesStatus())->updateIsOnline($online_piles_ids, PilesStatus::IsOnlineYes);
    }

    /**
     * 删除充电桩
     *
     * @param array $piles_list
     * @return void
     */
    protected function deleteHandler(array $piles_list): void
    {
        $pilesModel = new PilesModel();
        $piles_ids = [];
        foreach ($piles_list as $piles) {
            $piles_ids[] = $piles['id'];
        }
        if (!empty($piles_ids)) {
            $pilesModel->whereIn('id', $piles_ids)->delete();
        }
    }

    /**
     * 更新充电桩
     *
     * @param array $piles_list
     * @return void
     */
    protected function updateHandler(array $piles_list): void
    {
        $pilesModel = new PilesModel();
        foreach ($piles_list as $piles) {
            $pilesModel->where('id', '=', $piles['id'])->update([
                'name' => $piles['name'],
                'station_id' => $piles['station_id'],
                'corp_id' => $piles['corp_id'],
                'ac_dc' => $piles['ac_dc'],
                'type' => $piles['type'],
                'model' => $piles['model'],
                'power' => $piles['power'],
                'comm_id' => $piles['comm_id'],
                'status' => 0,
                'phase_name' => $piles['phase_name']
            ]);
        }
    }

    /**
     * 添加充电桩
     *
     * @param array $piles_list
     * @return void
     */
    protected function addHandler(array $piles_list): void
    {
        $inserts = [];
        foreach ($piles_list as $piles) {
            $inserts[] = [
                'id' => $piles['id'],
                'name' => $piles['name'],
                'corp_id' => $piles['corp_id'],
                'station_id' => $piles['station_id'],
                'ac_dc' => $piles['ac_dc'],
                'type' => $piles['type'],
                'model' => $piles['model'],
                'power' => $piles['power'],
                'comm_id' => $piles['comm_id'],
                'phase_name' => $piles['phase_name']
            ];
        }
        (new PilesModel())->batchAddPiles($inserts);
    }
}