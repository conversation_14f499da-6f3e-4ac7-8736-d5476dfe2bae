<?php
/** @noinspection PhpUnused */

namespace app\common\lib\transfer_service\logic;

use app\common\log\SocketLogCollector;
use app\common\model\Shots;
use app\common\model\ShotsStatus;
use app\common\queue\TransferServiceWriteQueue;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\BasePackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\ShotsChangeAnswer;
use LieHuoHuYu\ChargeTransferServiceProtocol\Kernel;
use RedisException;
use think\db\exception\DbException;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\ShotsChange as ShotsChangeProtocol;

class ShotsChange extends Base
{

    protected SocketLogCollector $socketLogCollector;
    protected BasePackage $package;

    public function __construct(SocketLogCollector $socketLogCollector, BasePackage $package)
    {
        $this->socketLogCollector = $socketLogCollector;
        $this->package = $package;
    }

    /**
     * 回复应答
     *
     * @param string $protocol_index
     * @param int $result
     * @param int $failure_reason_code
     * @return void
     * @throws RedisException
     */
    protected function replyAnswer(string $protocol_index, int $result, int $failure_reason_code = ShotsChangeAnswer::FailureReasonCodeNone): void
    {
        $answerPackage = Kernel::create($this->package->getSerialNumber(), $protocol_index, [
            'result' => $result,
            'failure_reason_code' => $failure_reason_code
        ]);
        (new TransferServiceWriteQueue())->push($answerPackage->encode());
    }

    /**
     *
     * @return void
     * @throws RedisException
     */
    public function run(): void
    {
        $this->openExceptionCapture(function () {
            /**
             * @var ShotsChangeProtocol $body
             */
            $body = $this->package->getBody();
            switch ($body->getOperationType()) {
                case ShotsChangeProtocol::OperationTypeAdd:
                    $this->addHandler($body->getShots());
                    break;
                case ShotsChangeProtocol::OperationTypeUpdate:
                    $this->updateHandler($body->getShots());
                    break;
                case ShotsChangeProtocol::OperationTypeDelete:
                    $this->deleteHandler($body->getShots());
                    break;
                case ShotsChangeProtocol::OperationTypeAllSync:
                    $this->allSyncHandler($body->getShots());
                    break;
            }

            $this->replyAnswer(BasePackage::TypeShotsChangeAnswer, ShotsChangeAnswer::ResultSuccess);

        }, true);
    }

    /**
     * 全量同步处理
     *
     * @param array $shots_list
     * @return void
     * @throws DbException
     */
    protected function allSyncHandler(array $shots_list): void
    {
        $shotsModel = (new Shots());
        $shotsModel->deleteAll();
        $inserts = [];
        $charging_shots_ids = $idle_shots_ids = $fault_shots_ids = $normal_shots_ids = [];

        foreach ($shots_list as $shot) {
            $inserts[] = [
                'id' => $shot['id'],
                'name' => $shot['name'],
                'corp_id' => $shot['corp_id'],
                'station_id' => $shot['station_id'],
                'piles_id' => $shot['piles_id'],
                'sequence' => $shot['sequence'],
                'port_num' => $shot['port_num'],
            ];

//            if (isset($shot['is_online']) && (int)$shot['is_online'] === ShotsChangeProtocol::IsOnlineYes) {
//                $online_shots_ids[] = $shot['id'];
//            }

            // 充电中的充电枪
            if (isset($shot['work_status']) && (int)$shot['work_status'] === ShotsChangeProtocol::WorkStatusCharging) {
                $charging_shots_ids[] = $shot['id'];
                // 空闲的充电枪
            } else if (isset($shot['work_status']) && (int)$shot['work_status'] === ShotsChangeProtocol::WorkStatusIdle) {
                $idle_shots_ids[] = $shot['id'];
            }

            // 故障的充电枪
            if (isset($shot['is_fault']) && (int)$shot['is_fault'] === ShotsChangeProtocol::IsFaultYes) {
                $fault_shots_ids[] = $shot['id'];
                // 正常地充电枪
            } else if (isset($shot['is_fault']) && (int)$shot['is_fault'] === ShotsChangeProtocol::IsFaultNot) {
                $normal_shots_ids[] = $shot['id'];
            }
        }
        $shotsModel->batchAddShots($inserts);

        $ShotsStatusModel = new ShotsStatus();

//        if (count($online_shots_ids) > 0) {
//            $ShotsStatusModel->updateIsOnline($online_shots_ids, ShotsStatus::IsOnlineYes);
//        }
        if (count($charging_shots_ids)) {
            $ShotsStatusModel->updateWorkStatus($charging_shots_ids, ShotsStatus::WorkStatusCharging);
        }
        if (count($idle_shots_ids)) {
            $ShotsStatusModel->updateWorkStatus($idle_shots_ids, ShotsStatus::WorkStatusIdle);
        }
        if (count($fault_shots_ids)) {
            $ShotsStatusModel->updateIsFault($fault_shots_ids, ShotsStatus::IsFaultYes);
        }
        if (count($normal_shots_ids)) {
            $ShotsStatusModel->updateIsFault($normal_shots_ids, ShotsStatus::IsFaultNot);
        }
    }

    protected function deleteHandler(array $shots_list): void
    {
        $shots_ids = [];
        foreach ($shots_list as $shot) {
            $shots_ids[] = $shot['id'];
        }
        (new Shots())->whereIn('id', $shots_ids)->delete();
    }

    /**
     * 更新操作
     *
     * @param array $shots_list
     * @return void
     */
    protected function updateHandler(array $shots_list): void
    {
        $shotsModel = new Shots();
        foreach ($shots_list as $shot) {
            $shotsModel->where('id', '=', $shot['id'])->update([
                'name' => $shot['name'],
                'station_id' => $shot['station_id'],
                'piles_id' => $shot['piles_id'],
                'sequence' => $shot['sequence'],
                'port_num' => $shot['port_num'],
            ]);
        }
    }

    /**
     * 添加充电枪操作
     *
     * @param array $shots_list
     * @return void
     */
    protected function addHandler(array $shots_list): void
    {
        $inserts = [];
        foreach ($shots_list as $shot) {
            $inserts[] = [
                'id' => $shot['id'],
                'name' => $shot['name'],
                'corp_id' => $shot['corp_id'],
                'station_id' => $shot['station_id'],
                'piles_id' => $shot['piles_id'],
                'sequence' => $shot['sequence'],
                'port_num' => $shot['port_num'],
            ];
        }
        (new Shots())->batchAddShots($inserts);
    }
}