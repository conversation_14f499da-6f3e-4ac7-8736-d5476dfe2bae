<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\common\lib\transfer_service\logic;

use app\common\log\SocketLogCollector;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\BasePackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\ShotsChangeAnswer;
use Closure;
use think\facade\Db;
use RedisException;
use Throwable;

abstract class Base
{
    protected SocketLogCollector $socketLogCollector;

    /**
     *
     * @param Closure $handler
     * @param bool $isStartTrans
     * @return void
     * @throws RedisException
     */
    protected function openExceptionCapture(Closure $handler, bool $isStartTrans = false): void
    {
        $start_time = microtime(true);
        try {

            if ($isStartTrans === true) Db::startTrans();


            $handler();

            if ($isStartTrans === true) Db::commit();


        } catch (Throwable $e) {
            if ($isStartTrans === true) Db::rollback();
            $this->socketLogCollector->collect($e);
            $this->replyAnswer(
                BasePackage::TypeShotsChangeAnswer,
                ShotsChangeAnswer::ResultFailed,
            );
        }
        $this->socketLogCollector->collectorRunLog(sprintf('耗时: %s 秒', round(microtime(true) - $start_time, 5)));
    }

    /**
     * 回复应答
     *
     * @param string $protocol_index
     * @param int $result
     * @param int $failure_reason_code
     * @return void
     * @throws RedisException
     */
    abstract protected function replyAnswer(string $protocol_index, int $result, int $failure_reason_code = ShotsChangeAnswer::FailureReasonCodeNone): void;
}