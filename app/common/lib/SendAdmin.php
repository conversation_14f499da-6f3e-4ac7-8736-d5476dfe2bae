<?php
declare (strict_types=1);

namespace app\common\lib;

use lib\admin\GatewayAdmin;

class SendAdmin
{

    /**
     * 根据用户id发送消息到后台
     * lwj 2023.8.17 新增
     * lwj 2023.8.24 修改
     * @param string|int $user_id
     * @param array|string $msg
     * @return bool
     */
    public static function send_uid(string|int $user_id,array|string $msg): bool
    {
        if (is_array($msg)) $msg = json_encode_cn($msg);
        GatewayAdmin::sendToUid($user_id, $msg);
        return true;
    }

    /**
     * 根据用户分组发送消息到后台
     * lwj 2023.8.17 新增
     * lwj 2023.8.24 修改
     * @param string|int $group
     * @param array|string $msg
     * @return bool
     */
    public static function send_group(string|int $group,array|string $msg): bool
    {
        if (is_array($msg)) $msg = json_encode_cn($msg);
        GatewayAdmin::sendToGroup($group, $msg);
        return true;
    }



}
