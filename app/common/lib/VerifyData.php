<?php
declare (strict_types=1);

namespace app\common\lib;

use app\common\cache\redis\StationsMonthRankingListDispatch;
use app\common\model\ElectronicInvoiceApplyRecord;
use app\common\model\Order as OrderModel;
use app\common\model\WorkOrder;
use app\common\model\WorkOrderField;
use app\common\model\WorkOrderRelation;
use app\common\repositories\StationsActiveAlarmConfig as StationsActiveAlarmConfigRepositories;
use Respect\Validation\Validator as v;
use app\common\repositories\AlarmRecord as AlarmRecordRepositories;

class VerifyData
{
    public static function login(array $field): array
    {
        $rules = [
            'name' => v::notEmpty()->stringType()->setName('用户名'),
            'password' => v::notEmpty()->stringType()->length(6, 64)->setName('密码'),
            'img_code' => v::notEmpty()->stringType()->length(4, 4)->setName('验证码'),
            'img_code_id' => v::notEmpty()->stringType()->setName('验证码ID')
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 充电桩验证字段
     * lwj 2023.7.31 新增
     * @param array $field
     * @return array
     */
    public static function chargingPile(array $field): array
    {
        $rules = [
            'piles_id' => v::notEmpty()->stringType()->length(14, 14)->setName('桩编码'),
            'shots_id' => v::notEmpty()->stringType()->between(1, 99)->length(2, 2)->setName('枪号'),
            'billing_mode_id' => v::notEmpty()->stringType()->length(4, 4)->setName('计费模型编码'),
            'sharp_fee' => v::notOptional()->intType()->between(0, **********)->setName('尖费电费费率'),
            'sharp_ser_fee' => v::notOptional()->intType()->between(0, **********)->setName('尖服务费费率'),
            'peak_fee' => v::notOptional()->intType()->between(0, **********)->setName('峰电费费率'),
            'peak_ser_fee' => v::notOptional()->intType()->between(0, **********)->setName('峰服务费费率'),
            'flat_fee' => v::notOptional()->intType()->between(0, **********)->setName('平电费费率'),
            'flat_ser_fee' => v::notOptional()->intType()->between(0, **********)->setName('平服务费费率'),
            'valley_fee' => v::notOptional()->intType()->between(0, **********)->setName('谷电费费率'),
            'valley_ser_fee' => v::notOptional()->intType()->between(0, **********)->setName('谷服务费费率'),
            'loss_rate' => v::notOptional()->intType()->between(0, 255)->setName('计损比例'),
            'period_codes' => v::notEmpty()->stringType()->length(96, 96)->setName('时段费率号'),
            'execute_control' => v::notEmpty()->intType()->between(1, 2)->setName('执行控制'),
            'piles_model' => v::notEmpty()->intType()->between(1, 2)->setName('桩型号'),
            'piles_power' => v::notEmpty()->intType()->between(1, 65535)->setName('桩功率'),
            'upgrade_server_address' => v::notEmpty()->stringType()->length(6, **********)->setName('升级服务器地址'),
            'upgrade_server_port' => v::notEmpty()->intType()->between(1, 65535)->setName('升级服务器端口'),
            'username' => v::notEmpty()->stringType()->length(1, 500)->setName('用户名'),
            'password' => v::notEmpty()->stringType()->length(1, 500)->setName('密码'),
            'file_path' => v::notEmpty()->stringType()->length(1, **********)->setName('文件路径'),
            'download_timeout' => v::notEmpty()->intType()->between(1, 255)->setName('下载超时时间'),
            'transaction_serial_number' => v::notEmpty()->stringType()->length(32, 32)->setName('交易流水号'),
            'logical_card_number' => v::notEmpty()->stringType()->length(8, 16)->setName('逻辑卡号'),
            'physical_card_number' => v::notEmpty()->stringType()->length(8, 16)->setName('物理卡号'),
            'account_balance' => v::notOptional()->intType()->between(0, **********)->setName('账户余额'),
            'allow_work' => v::notOptional()->intType()->between(0, 1)->setName('是否允许工作'),
            'maximum_allowed_output_power' => v::notOptional()->intType()->between(30, 100)->setName('充电桩最大允许输出功率'),
            'sequence_number' => v::notEmpty()->stringType()->length(4, 4)->setName('序列号域'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 运营商验证字段
     * lwj 2023.8.1 新增
     * @param array $field
     * @return array
     */
    public static function corp(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('运营商编号'),
            'name' => v::notEmpty()->stringType()->length(1, 128)->setName('运营商名称'),
            'icon_url' => v::stringType()->length(0, 255)->setName('运营商图标url'),
            'province' => v::notEmpty()->stringType()->length(0, 128)->setName('省'),
            'city' => v::notEmpty()->stringType()->length(0, 128)->setName('市'),
            'district' => v::notEmpty()->stringType()->length(0, 128)->setName('区'),
            'city_id' => v::notEmpty()->arrayType()->length(3)->setName('省市区id'),
            'parent_id' => v::notOptional()->intType()->between(0, ********************9999)->setName('上级运营商编号'),
            'audit_status' => v::notEmpty()->intType()->between(1, 2)->setName('审核状态，1-未审核，2-审核通过'),
            'contact' => v::notEmpty()->stringType()->length(1, 64)->setName('联系人'),
            'phone' => v::notEmpty()->stringType()->length(1, 32)->setName('联系电话'),
            'opt_id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('创建人id'),
            'create_time' => v::notEmpty()->stringType()->dateTime('Y-m-d H:i:s')->setName('创建时间'),
            'status' => v::notEmpty()->intType()->between(1, 2)->setName('运营商状态，1-关闭，2-开启'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 公共验证字段
     * lwj 2023.8.2 新增
     * @param array $field
     * @return array
     */
    public static function gg(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('id'),
            'pid' => v::notOptional()->intType()->between(0, **********999)->setName('pid'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 充电站验证字段
     * lwj 2023.8.3 新增
     * lwj 2024.8.13 修改
     * @param array $field
     * @return array
     */
    public static function stations(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('场站id'),
            'corp_id' => v::notEmpty()->intType()->setName('运营商编号'),
            'name' => v::notEmpty()->stringType()->length(1, 128)->setName('场站名称'),
            'all_address' => v::notEmpty()->stringType()->length(1, 500)->setName('完整场站地址，需带省市区'),
            'city_id' => v::notEmpty()->arrayType()->length(3)->setName('省市区id'),
            'address' => v::notEmpty()->stringType()->length(1, 500)->setName('场站地址，不必带省市区'),
            'province' => v::notEmpty()->stringType()->length(1, 128)->setName('省'),
            'city' => v::notEmpty()->stringType()->length(1, 128)->setName('市'),
            'district' => v::notEmpty()->stringType()->length(1, 128)->setName('区'),
            'pile_num' => v::notEmpty()->intType()->length(1, 8)->setName('桩数'),
            'tariff_group_id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('费率组id'),
            'type' => v::notEmpty()->intType()->between(1, 2)->setName('场站类型：1-公共、2-自营'),
            'status' => v::notEmpty()->intType()->between(1, 3)->setName('运行状态：1-正常使用、2-维护中、3-未开放'),
            'pictures' => v::optional(v::arrayType())->setName('场站图片url集'),
            'work_time' => v::optional(v::stringType())->setName('运营时段描述'),
            'tag_pos' => v::notEmpty()->intType()->between(1, 2)->setName('桩位置标示：1-地上,2-地库'),
            'tag_park' => v::optional(v::stringType())->setName('停车费说明'),
            'place_rate' => v::notEmpty()->intType()->setName('占位费（X分/分钟）,X*100'),
            'charge' => v::notEmpty()->stringType()->length(1, 20)->setName('负责人'),
            'charge_phone' => v::notEmpty()->stringType()->length(1, 20)->setName('负责人手机号'),
            'tag_toilet' => v::notEmpty()->intType()->between(1, 2)->setName('场站服务卫生间标识：1-无，2-有'),
            'tag_canopy' => v::notEmpty()->intType()->between(1, 2)->setName('场站服务雨棚标识：1-无，2-有'),
            'tag_rest' => v::notEmpty()->intType()->between(1, 2)->setName('场站服务休息室标识：1-无，2-有'),
            'tag_pnp' => v::notEmpty()->intType()->between(1, 2)->setName('场站特色即插即用标识：1-不支持，2-支持'),
            'tag_insure' => v::notEmpty()->intType()->between(1, 2)->setName('场站特色充电保险标识：1-不支持，2-支持'),
            'tag_protect' => v::notEmpty()->intType()->between(1, 2)->setName('场站特色充电电池防护标识：1-不支持，2-支持'),
            'tag_ultrafast' => v::notEmpty()->intType()->between(1, 2)->setName('场站电桩超快充标识：1-不支持，2-支持'),
            'tag_fast' => v::notEmpty()->intType()->between(1, 2)->setName('场站电桩快充标识：1-不支持，2-支持'),
            'tag_slow' => v::notEmpty()->intType()->between(1, 2)->setName('场站电桩慢充标识：1-不支持，2-支持'),
            'lonlat' => v::notEmpty()->stringType()->length(1, 100)->setName('经纬度，前面纬度，后面经度'),
            'opt_id' => v::notEmpty()->intType()->setName('创建人'),
            'create_time' => v::notEmpty()->dateTime('Y-m-d H:i:s')->setName('创建时间'),
            'station_id' => v::notOptional()->intType()->setName('充电站ID'),
            'start_time' => v::notOptional()->intType()->setName('起始时间'),
            'end_time' => v::notOptional()->intType()->setName('结尾时间'),
            'centralized_controller_id' => v::notOptional()->stringType()->setName('能源路由器ID'),
            'is_support_reservation' => v::notOptional()->intType()->in([0, 1])->setName('是否支持预约充电'),
            'to' => v::notEmpty()->arrayType()->length(1, 25)->setName('场站位置数组'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }


    public static function centralizedController(array $field): array
    {
        $rules = [
            'station_id' => v::notOptional()->intType()->setName('充电站ID'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }


    /**
     * 费率组验证字段
     * lwj 2023.8.7 新增
     * @param array $field
     * @return array
     */
    public static function tariff_group(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1, 9999)->setName('费率组id'),
            'name' => v::notEmpty()->stringType()->length(1, 128)->setName('费率组名称'),
            'sharp_fee' => v::notOptional()->oneOf(v::intType(), v::floatType())->between(0, 42949.67295)->setName('尖费电费费率'),
            'sharp_ser_fee' => v::notOptional()->oneOf(v::intType(), v::floatType())->between(0, 42949.67295)->setName('尖服务费费率'),
            'peak_fee' => v::notOptional()->oneOf(v::intType(), v::floatType())->between(0, 42949.67295)->setName('峰电费费率'),
            'peak_ser_fee' => v::notOptional()->oneOf(v::intType(), v::floatType())->between(0, 42949.67295)->setName('峰服务费费率'),
            'flat_fee' => v::notOptional()->oneOf(v::intType(), v::floatType())->between(0, 42949.67295)->setName('平电费费率'),
            'flat_ser_fee' => v::notOptional()->oneOf(v::intType(), v::floatType())->between(0, 42949.67295)->setName('平服务费费率'),
            'valley_fee' => v::notOptional()->oneOf(v::intType(), v::floatType())->between(0, 42949.67295)->setName('谷电费费率'),
            'valley_ser_fee' => v::notOptional()->oneOf(v::intType(), v::floatType())->between(0, 42949.67295)->setName('谷服务费费率'),
            'surcharge' => v::notOptional()->oneOf(v::intType(), v::floatType())->between(-42949.67295, 42949.67295)->setName('超额附加费率'),
            'loss_rate' => v::notOptional()->intType()->between(0, 255)->setName('计损比例'),
            'period_codes' => v::notEmpty()->stringType()->length(96, 96)->setName('时段费率号'),
            'period' => v::notEmpty()->arrayType()->length(48)->setName('时段费率'),
            'corp_id' => v::notEmpty()->intType()->setName('运营商ID')
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 充电桩验证字段
     * lwj 2023.8.8 新增
     * @param array $field
     * @return array
     */
    public static function piles(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(10000000000000, **********9999)->setName('充电桩编号'),
            'name' => v::notEmpty()->stringType()->length(1, 128)->setName('充电桩名称'),
            'corp_id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('运营商编号'),
            'station_id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('场站id'),
            'ac_dc' => v::notOptional()->intType()->between(0, 1)->setName('交直流：0表示直流桩，1表示交流桩'),
            'type' => v::notEmpty()->intType()->between(1, 3)->setName('桩类型：1-慢充，2-快充，3超快充'),
            'model' => v::notEmpty()->stringType()->length(1, 32)->setName('桩型号'),
            'power' => v::notOptional()->intType()->between(0, 99999999)->setName('桩功率（w）'),
            'comm_id' => v::notEmpty()->stringType()->length(1, 16)->setName('认证id:4G模块IMEI号,mac地址，根据通讯类型区分'),
            'shot_num' => v::intType()->between(1, 99)->setName('枪数量'),
            'comm_ver' => v::intType()->between(0, 99)->setName('通讯协议版本，版本号乘10，v1.0 表示 0x0A'),
            'version' => v::intType()->between(0, ********************9999)->setName('程序版本,如：23070101'),
            'comm_type' => v::intType()->between(0, 3)->setName('通讯类型：0-SIM卡，1-LAN，2-WAN，3-其他'),
            'simid' => v::stringType()->length(0, 16)->setName('SIM卡号'),
            'comm_corp' => v::intType()->between(0, 4)->setName('通讯运营商：0-移动，2-电信，3-联通，4-其他'),
            'phase_name' => v::optional(v::stringType()->in(['a', 'b', 'c']))->setName('相线'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 充电枪验证字段
     * lwj 2023.8.8 新增
     * @param array $field
     * @return array
     */
    public static function shots(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1000000000000000, **********999999)->setName('充电枪编号'),
            'name' => v::notEmpty()->stringType()->length(1, 128)->setName('充电枪名称'),
            'corp_id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('运营商编号'),
            'station_id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('场站id'),
            'piles_id' => v::notEmpty()->intType()->between(10000000000000, **********9999)->setName('充电桩编号'),
            'sequence' => v::notEmpty()->intType()->between(1, 99)->setName('枪序号'),
            'qrcode' => v::notEmpty()->stringType()->setName('枪二维码url'),
            'port_num' => v::notEmpty()->intType()->between(1, 9999)->setName('虚拟枪号，用于485访问的地址'),
            'is_online' => v::notEmpty()->intType()->between(1, 2)->setName('枪在线状态：1-离线，2-在线'),
            'work_status' => v::notEmpty()->intType()->between(1, 2)->setName('枪工作状态：1-空闲，2-充电中'),
            'link_status' => v::notEmpty()->intType()->between(1, 2)->setName('枪连接状态：1-无连接，2-连接中'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 用户验证字段
     * lwj 2023.8.11 新增
     * lwj 2023.8.16 修改
     * @param array $field
     * @return array
     */
    public static function users(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1, ********************99999)->setName('用户id'),
            'nickname' => v::notEmpty()->stringType()->length(1, 64)->setName('用户昵称'),
            'avatar' => v::notEmpty()->stringType()->length(1, 500)->setName('用户头像url'),
            'code' => v::notEmpty()->stringType()->length(10, 64)->setName('code'),
            'openid_code' => v::notEmpty()->stringType()->length(10, 64)->setName('openid_code'),
            'phone_code' => v::notEmpty()->stringType()->length(10, 64)->setName('phone_code'),
            'phone' => v::notEmpty()->intType()->setName('手机号'),
            'open_id' => v::notEmpty()->stringType()->setName('OPEN ID'),
            'order_id' => v::notEmpty()->stringType()->setName('充电订单ID')
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 充值列表验证字段
     * lwj 2023.8.12 新增
     * @param array $field
     * @return array
     */
    public static function recharge_list(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1, **********)->setName('充值列表id'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 支付订单验证字段
     * lwj 2023.8.14 新增
     * lwj 2024.6.13 修改
     * @param array $field
     * @return array
     */
    public static function pay_order(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1, **********)->setName('充值列表id'),
            'price' => v::notEmpty()->intType()->between(1, **********9999999)->setName('支付金额'),
            'order_id' => v::notEmpty()->stringType()->length(32, 64)->setName('支付订单id'),
            'refund_id' => v::notEmpty()->stringType()->length(16, 64)->setName('退款流水号'),
            'recharge_id' => v::notEmpty()->stringType()->length(16, 64)->setName('充值流水号'),
            'refund_screenshot_id' => v::notEmpty()->intType()->setName('退款截图ID'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 开始充电验证字段
     * lwj 2023.8.14 新增
     * @param array $field
     * @return array
     */
    public static function start_charging(array $field): array
    {
        $rules = [
            'shots_id' => v::notEmpty()->intType()->between(1000000000000000, **********999999)->setName('充电枪编号'),
            'pay_balance' => v::notEmpty()->intType()->between(1, **********9999999)->setName('支付余额'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function reservation_charging(array $field): array
    {
        $rules = [
            'shots_id' => v::notEmpty()->intType()->between(1000000000000000, **********999999)->setName('充电枪编号'),
            'reservation_time' => v::notEmpty()->stringType()->setName('预约时间'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function charging(array $field): array
    {
        $rules = [
            'charging_qrcode_id' => v::notEmpty()->intType()->setName('充电二维码ID'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 充电订单验证字段
     * lwj 2023.8.22 新增
     * @param array $field
     * @return array
     */
    public static function charging_order(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->stringType()->length(32, 32)->setName('交易流水号'),
        ];
        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 协议验证字段
     * lwj 2023.9.18 新增
     * @param array $field
     * @return array
     */
    public static function agreement(array $field): array
    {
        $rules = [
            'name' => v::notEmpty()->stringType()->length(1, 255)->setName('协议标题，分类'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 验证订单列表
     * cbj 2023.10.9 修改
     *
     * cbj 2023.10.9
     * @param array $field
     * @return array
     */
    public static function get_order_list(array $field): array
    {
        $rules = [
            'id' => v::stringType()->setName('交易流水号'),
            'corp_id' => v::optional(v::intType())->setName('运营商ID'),
            'station_id' => v::optional(v::intType())->setName('充电站ID'),
            'sequence' => v::optional(v::intType())->setName('充电枪编号'),
            'phone' => v::optional(v::intType()->length(1, 32))->setName('联系电话'),
            'status' => v::arrayType()->setName('订单状态'),
            'start_time' => v::optional(v::stringType())->setName('开始时间'),
            'end_time' => v::optional(v::stringType())->setName('结束时间'),
            'page' => v::optional(v::intType())->setName('页码'),
            'limit' => v::optional(v::intType())->setName('每页显示'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 验证订单列表
     * 系统菜单验证字段
     * lwj 2023.10.9 新增
     * @param array $field
     * @return array
     */
    public static function sys_menu(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1, **********9)->setName('菜单id'),
            'title' => v::notEmpty()->stringType()->length(1, 64)->setName('菜单名称'),
            'rule_name' => v::notEmpty()->stringType()->length(1, 255)->setName('权限名'),
            'level' => v::notEmpty()->intType()->between(1, 99)->setName('所在层级'),
            'pid' => v::intType()->between(0, **********9)->setName('上级菜单id'),
            'icon_url' => v::notEmpty()->stringType()->length(1, 255)->setName('菜单图标icon的url'),
            'url_value' => v::notEmpty()->stringType()->length(1, 255)->setName('菜单地址'),
            'sort' => v::intType()->length(-**********9, **********9)->setName('排序'),
            'api_url' => v::notEmpty()->stringType()->length(1, 255)->setName('接口路径'),
            'menu_state' => v::notEmpty()->intType()->between(1, 2)->setName('显示菜单'),
            'state' => v::notEmpty()->intType()->between(1, 2)->setName('状态'),
            'auth_open' => v::notEmpty()->intType()->between(1, 2)->setName('是否公开'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 验证订单详情
     * cbj 2023.10.9 修改
     *
     * cbj 2023.10.9
     * @param array $field
     * @return array
     */
    public static function get_order_info(array $field): array
    {
        $rules = [
            'id' => v::stringType()->notEmpty()->length(32, 32)->setName('交易流水号'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 验证订单修复
     * cbj 2023.10.9 修改
     *
     * @param array $field
     * @return array
     */
    public static function order_repair(array $field): array
    {
        $rules = [
            'id' => v::stringType()->notEmpty()->length(32, 32)->setName('交易流水号'),
            'update_status' => v::intType()->notEmpty()->in([
                OrderModel::StatusPlaceAnOrder,
                OrderModel::StatusFreeze,
                OrderModel::StatusCharging,
                OrderModel::StatusComplete,
                OrderModel::StatusAbnormal,
                OrderModel::StatusOther
            ])->setName('更新后的状态'),
            'deduction_balance' => v::intType()->min(0)->setName('扣除的余额'),
            'electricity' => v::intType()->min(0)->setName('充电量')
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function order(array $field): array
    {
        $rules = [
            'filter_piles_id' => v::optional(v::intType())->setName('充电桩ID'),
            'order_id' => v::notEmpty()->stringType()->setName('订单ID')
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 管理组验证字段
     * lwj 2023.10.17 新增
     * lwj 2023.10.18 修改
     * @param array $field
     * @return array
     */
    public static function admin_group(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('组id'),
            'group_name' => v::notEmpty()->stringType()->length(2, 64)->setName('组名'),
            'state' => v::notEmpty()->intType()->between(1, 2)->setName('状态'),
            'rule' => v::arrayType()->setName('权限数组'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 管理员用户验证字段
     * lwj 2023.10.18 新增
     * @param array $field
     * @return array
     */
    public static function admin_user(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('管理员id'),
            'name' => v::notEmpty()->stringType()->length(2, 64)->setName('管理员登录用户名'),
            'password' => v::notEmpty()->stringType()->length(4, 20)->setName('密码'),
            'old_password' => v::notEmpty()->stringType()->length(4, 20)->setName('旧密码'),
            'avatar' => v::stringType()->length(0, 255)->setName('管理员头像url'),
            'phone' => v::notEmpty()->stringType()->length(1, 24)->setName('管理员手机号'),
            'email' => v::notEmpty()->stringType()->length(1, 64)->setName('管理员邮箱'),
            'perm_group_id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('管理员组id'),
            'state' => v::notEmpty()->intType()->between(1, 2)->setName('状态'),
            'corp_id' => v::notEmpty()->intType()->min(0)->setName('运营商ID'),
            'station_ids' => v::notOptional()->arrayType()->setName('场站ID集合'),
            'order_name' => v::optional(v::stringType())->setName('排序字段'),
            'order_type' => v::optional(v::stringType())->setName('排序类型'),
            'page' => v::optional(v::intType())->setName('页码'),
            'limit' => v::optional(v::intType())->setName('每页显示'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function admin_user_list(array $field): array
    {
        $rules = [
            'id' => v::optional(v::intType()->between(1, ********************9999))->setName('管理员id'),
            'name' => v::optional(v::stringType()->length(2, 64))->setName('管理员登录用户名'),
            'filter_name' => v::optional(v::stringType())->setName('用户名'),
            'corp_id' => v::optional(v::intType()->min(0))->setName('运营商ID'),
            'phone' => v::optional(v::stringType()->length(1, 24))->setName('管理员手机号'),
            'order_name' => v::optional(v::stringType())->setName('排序字段'),
            'order_type' => v::optional(v::stringType())->setName('排序类型'),
            'page' => v::optional(v::intType())->setName('页码'),
            'limit' => v::optional(v::intType())->setName('每页显示'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }


    public static function statistics(array $field): array
    {
        $rules = [
            'type' => v::notEmpty()->stringType()->in([
                StationsMonthRankingListDispatch::TYPE_ELECTRICITY,
                StationsMonthRankingListDispatch::TYPE_CHARGE_INCOME,
                StationsMonthRankingListDispatch::TYPE_ORDER_COUNT,
                StationsMonthRankingListDispatch::TYPE_UTILIZATION_RATE
            ])->setName('排行榜类型'),
            'station_id' => v::intType()->setName('充电站ID'),
            'filter_station_id' => v::intType()->notEmpty()->setName('充电站ID'),
            'corp_id' => v::optional(v::intType())->setName('运营商ID'),
            'year' => v::notEmpty()->intType()->between(2023, 2100)->setName('年份'),
            'month' => v::notEmpty()->intType()->between(1, 12)->setName('月份'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function electronicInvoice(array $field): array
    {
        $rules = [
            'order_ids' => v::arrayType()->notEmpty()->setName('充电订单流水号集合'),
            'id' => v::stringType()->setName('订单发票申请ID'),
            'nickname' => v::stringType()->setName('用户昵称'),
            'title_type' => v::in([
                ElectronicInvoiceApplyRecord::TitleTypeIndividual,
                ElectronicInvoiceApplyRecord::TitleTypeOrganization,
            ])->setName('抬头类型'),
            'title_name' => v::stringType()->setName('抬头名称'),
            'taxpayer_id' => v::stringType()->setName('纳税人识别号'),
            'stage' => v::in([
                ElectronicInvoiceApplyRecord::StageUserApply,
                ElectronicInvoiceApplyRecord::StageTitleComplete,
                ElectronicInvoiceApplyRecord::StageComplete,
            ])->setName('阶段'),
            'start_time' => v::stringType()->setName('起始时间'),
            'end_time' => v::stringType()->setName('结束时间'),
            'page' => v::intType()->setName('页码'),
            'limit' => v::intType()->setName('每页最大条数')
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function alarm_record(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->setName('告警记录ID'),
            'corp_id' => v::optional(v::intType())->setName('运营商ID'),
            'station_id' => v::optional(v::intType())->setName('充电站ID'),
            'device_type' => v::optional(v::intType()->in([
                AlarmRecordRepositories::DeviceTypeShots,
                AlarmRecordRepositories::DeviceTypePiles,
                AlarmRecordRepositories::DeviceTypeCentralizedController
            ]))->setName('设备类型'),
            'device_id' => v::optional(v::intType())->setName('设备编号'),
            'type' => v::optional(v::intType()->in([
                AlarmRecordRepositories::TypeCRS,
                AlarmRecordRepositories::TypeCTR,
                AlarmRecordRepositories::TypeTCO,
                AlarmRecordRepositories::TypeEMP,
                AlarmRecordRepositories::TypePON,
                AlarmRecordRepositories::TypeMSR
            ]))->setName('告警代码类型'),
            'is_recovery' => v::optional(v::intType()->in([
                AlarmRecordRepositories::StatusIsRecoveryNot,
                AlarmRecordRepositories::StatusIsRecoveryYes
            ]))->setName('恢复状态'),
            'alarm_start_time' => v::optional(v::stringType())->setName('告警起始时间'),
            'alarm_end_time' => v::optional(v::stringType())->setName('告警结尾时间'),
            'code' => v::optional(v::stringType())->setName('告警代码'),
            'page' => v::intType()->setName('页码'),
            'limit' => v::intType()->setName('每页最大条数')
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function service_admin_user(array $field): array
    {
        $rules = [
            'username' => v::notEmpty()->stringType()->length(6, 20)->setName('用户名'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function service_corp(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->min(1)->setName('运营商ID'),
            'name' => v::notEmpty()->stringType()->length(1, 128)->setName('运营商名称'),
            'icon_url' => v::notEmpty()->stringType()->length(1, 255)->setName('运营商图标url'),
            'province' => v::notEmpty()->stringType()->length(1, 128)->setName('省份'),
            'city' => v::notEmpty()->stringType()->length(1, 128)->setName('城市'),
            'district' => v::notEmpty()->stringType()->length(1, 128)->setName('区域'),
            'city_id' => v::notEmpty()->arrayType()->setName('位置ID'),
            'contact' => v::notEmpty()->stringType()->length(1, 64)->setName('联系人'),
            'phone' => v::notEmpty()->stringType()->length(1, 32)->setName('联系电话'),
            'create_time' => v::notEmpty()->stringType()->setName('创建时间'),
            'admin_group_ids' => v::notEmpty()->stringType()->setName('管理员分组ID集合'),
            'admin_username' => v::notEmpty()->stringType()->length(6, 20)->setName('管理员用户名'),
            'admin_password' => v::notEmpty()->stringType()->length(6, 64)->setName('管理员密码'),
            'admin_group_id' => v::notEmpty()->intType()->min(1)->setName('管理员分组ID')
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function service_stations(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->min(1)->setName('场站ID'),
            'name' => v::notEmpty()->stringType()->length(1, 128)->setName('场站名称'),
            'address' => v::notEmpty()->stringType()->setName('场站地址'),
            'corp_id' => v::notEmpty()->intType()->setName('运营商ID'),
            'city' => v::notEmpty()->stringType()->length(1, 128)->setName('城市'),
            'lonlat' => v::notEmpty()->stringType()->setName('经纬度'),
            'city_id' => v::notEmpty()->arrayType()->setName('位置ID'),
            'type' => v::notEmpty()->intType()->in([1, 2])->setName('场站类型'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function work_order_field(array $field): array
    {
        $rules = [
            'page' => v::optional(v::intType())->setName('页码'),
            'limit' => v::optional(v::intType())->setName('每页最大条数'),
            'id' => v::notEmpty()->intType()->setName('字段ID'),
            'name' => v::notEmpty()->stringType()->length(3, 20)->setName('字段名称'),
            'key' => v::notEmpty()->stringType()->length(3, 20)->setName('字段键值'),
            'type' => v::notEmpty()->intType()->in([
                WorkOrderField::TypeSingleLineText,
                WorkOrderField::TypeMultilineText,
                WorkOrderField::TypeSelect,
                WorkOrderField::TypeMultipleSelect,
                WorkOrderField::TypeNumber,
                WorkOrderField::TypeDate,
                WorkOrderField::TypeImage
            ])->setName('字段类型'),
            'is_require' => v::notOptional()->intType()->in([
                WorkOrderField::IsRequireYes,
                WorkOrderField::IsRequireNot
            ])->setName('是否必填'),
            'options' => v::optional(v::arrayType())->setName('字段选项'),
            'screen_template_id' => v::optional(v::intType())->setName('模板ID')
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function work_order_template(array $field): array
    {
        $rules = [
            'page' => v::optional(v::intType())->setName('页码'),
            'limit' => v::optional(v::intType())->setName('每页最大条数'),
            'id' => v::notEmpty()->intType()->setName('模板ID'),
            'name' => v::notEmpty()->stringType()->length(3, 20)->setName('模板名称'),
            'describe' => v::notEmpty()->stringType()->length(0, 100)->setName('模板描述'),
            'template_id' => v::notEmpty()->intType()->setName('模板ID'),
            'field_id' => v::notEmpty()->intType()->setName('模板ID'),
            'sort' => v::notOptional()->intType()->min(0)->max(127)->setName('模板ID'),
            'notice_admin_user_id' => v::notOptional()->intType()->setName('默认的通知人'),
            'corp_id' => v::notOptional()->min(1)->setName('运营商ID'),
            'station_id' => v::notOptional()->min(1)->setName('场站ID'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function work_order(array $field): array
    {
        $rules = [
            'page' => v::optional(v::intType())->setName('页码'),
            'limit' => v::optional(v::intType())->setName('每页最大条数'),
            "id" => v::notEmpty()->stringType()->length(36, 36)->setName('工单ID'),
            'work_order_id' => v::notEmpty()->stringType()->length(36, 36)->setName('工单ID'),
            'screen_status' => v::optional(v::arrayVal())->setName('工单状态'),
            'screen_source' => v::optional(v::intType()->in([
                WorkOrder::SourceAdminCreate,
                WorkOrder::SourceAlarmGeneration,
                WorkOrder::SourceUserCreate
            ]))->setName('工单来源'),
            'screen_create_user_type' => v::optional(v::intType()->in([
                WorkOrder::CreateUserTypeAdmin,
                WorkOrder::CreateUserTypeNormal,
                WorkOrder::CreateUserTypeAuto
            ]))->setName('创建着的用户类型'),
            'screen_priority' => v::optional(v::intType()->in([
                WorkOrder::PriorityLow,
                WorkOrder::PriorityStandard,
                WorkOrder::PriorityHigh,
                WorkOrder::PriorityUrgent
            ]))->setName('优先级'),
            'title' => v::notOptional()->stringType()->length(3, 50)->setName('工单标题'),
            'priority' => v::notOptional()->intType()->in([
                WorkOrder::PriorityLow,
                WorkOrder::PriorityStandard,
                WorkOrder::PriorityHigh,
                WorkOrder::PriorityUrgent
            ])->setName('优先级'),
            'status' => v::notOptional()->intType()->in([
                WorkOrder::StatusOpen,
                WorkOrder::StatusSolving,
                WorkOrder::StatusResolved,
                WorkOrder::StatusClosed
            ])->setName('工单状态'),
            'source' => v::notOptional()->intType()->in([
                WorkOrder::SourceAdminCreate,
                WorkOrder::SourceAlarmGeneration,
                WorkOrder::SourceUserCreate
            ])->setName('工单来源'),
            'template_id' => v::notOptional()->intType()->setName('工单模板ID'),
            'extra_field_data' => v::notOptional()->arrayType()->setName('额外字段'),
            'corp_id' => v::notOptional()->intType()->min(1)->setName('运营商ID'),
            'station_id' => v::notOptional()->intType()->min(1)->setName('场站ID'),
            'device_type' => v::notOptional()->intType()->in([
                WorkOrderRelation::DeviceTypePiles,
                WorkOrderRelation::DeviceTypeShots,
                WorkOrderRelation::DeviceTypeCentralizedController,
                WorkOrderRelation::DeviceTypeNone
            ])->setName('设备类型'),
            'device_id' => v::optional(v::stringType())->setName('设备ID'),
            'screen_corp_id' => v::optional(v::intType()->min(1))->setName('运营商ID'),
            'screen_station_id' => v::optional(v::intType()->min(1))->setName('场站ID'),
            'screen_device_type' => v::optional(v::intType()->in([
                WorkOrderRelation::DeviceTypePiles,
                WorkOrderRelation::DeviceTypeShots,
                WorkOrderRelation::DeviceTypeCentralizedController,
                WorkOrderRelation::DeviceTypeNone
            ]))->setName('设备类型'),
            'screen_device_id' => v::optional(v::stringType())->setName('设备ID'),
            'admin_user_id' => v::notOptional()->intType()->min(1)->setName('处理人员ID'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 用户验证字段
     *
     * @param array $field
     * @return array
     */
    public static function maintenance_login(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1, ********************99999)->setName('用户id'),
            'nickname' => v::notEmpty()->stringType()->length(1, 64)->setName('用户昵称'),
            'avatar' => v::notEmpty()->stringType()->length(1, 500)->setName('用户头像url'),
            'code' => v::notEmpty()->stringType()->length(10, 64)->setName('code'),
            'openid_code' => v::notEmpty()->stringType()->length(10, 64)->setName('openid_code'),
            'phone_code' => v::notEmpty()->stringType()->length(10, 64)->setName('phone_code'),
            'phone' => v::notEmpty()->intType()->setName('手机号'),
            'open_id' => v::notEmpty()->stringType()->setName('OPEN ID')
        ];

        return array_intersect_key($rules, array_flip($field));
    }


    public static function maintenance_work_order(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->stringType()->setName('工单ID'),
            'work_order_id' => v::notEmpty()->stringType()->setName('工单ID'),
            'page' => v::notEmpty()->intType()->setName('页码'),
            'limit' => v::notEmpty()->intType()->setName('每页条数'),
            'screen_status' => v::optional(v::intType()->in([
                WorkOrder::StatusOpen,
                WorkOrder::StatusSolving,
                WorkOrder::StatusResolved,
                WorkOrder::StatusClosed,
            ]))->setName('状态'),
            'screen_title' => v::optional(v::stringType())->setName('标题'),
            'message' => v::notOptional()->stringType()->setName('工作描述'),
            'attachment' => v::notOptional()->stringType()->length(0, 500)->setName('工作描述'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function active_alarm(array $field): array
    {
        $rules = [
            'sf_reason_for_stop_white_list' => v::arrayType()->setName('充电停止原因白名单'),

            'ccsr_cpu_alarm_value' => v::notEmpty()->intType()->setName('CPU使用率警报值'),
            'ccsr_memory_alarm_value' => v::notEmpty()->intType()->setName('剩余内存警报值'),
            'ccsr_disk_alarm_value' => v::notEmpty()->intType()->setName('剩余磁盘警报值'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function station_active_alarm_config(array $field): array
    {
        $rules = [
            'screen_corp_id' => v::optional(v::intType()->min(1))->setName('运营商ID'),
            'screen_station_id' => v::optional(v::intType()->min(1))->setName('场站ID'),
            'page' => v::notEmpty()->intType()->setName('页码'),
            'limit' => v::notEmpty()->intType()->setName('每页条数'),

            'station_id' => v::notEmpty()->intType()->min(1)->setName('场站ID'),
            'enterprise_wechat_key' => v::notOptional()->stringType()->setName('企业微信机器人密钥'),
            'sf_is_generate_work_order' => v::notEmpty()->intType()->in([
                StationsActiveAlarmConfigRepositories::SFIsGenerateWorkOrderNot,
                StationsActiveAlarmConfigRepositories::SFIsGenerateWorkOrderYes
            ])->setName('充电枪故障告警 - 是否自动生成工单'),
            'sf_auto_dispatch_user_id' => v::notOptional()->intType()->min(0)
                ->setName('充电枪故障告警 - 自动派发给指定工作人员'),

            'cso_is_generate_work_order' => v::notEmpty()->intType()->in([
                StationsActiveAlarmConfigRepositories::CSOIsGenerateWorkOrderNot,
                StationsActiveAlarmConfigRepositories::CSOIsGenerateWorkOrderYes
            ])->setName('充电服务离线告警 - 是否自动生成工单'),
            'cso_auto_dispatch_user_id' => v::notOptional()->intType()->min(0)
                ->setName('充电服务离线告警 - 自动派发给指定工作人员'),

            'remp_is_generate_work_order' => v::notEmpty()->intType()->in([
                StationsActiveAlarmConfigRepositories::REMPIsGenerateWorkOrderNot,
                StationsActiveAlarmConfigRepositories::REMPIsGenerateWorkOrderYes
            ])->setName('读取电表功率告警 - 是否自动生成工单'),
            'remp_auto_dispatch_user_id' => v::notOptional()->intType()->min(0)
                ->setName('读取电表功率告警 - 自动派发给指定工作人员'),

            'po_is_generate_work_order' => v::notEmpty()->intType()->in([
                StationsActiveAlarmConfigRepositories::POIsGenerateWorkOrderNot,
                StationsActiveAlarmConfigRepositories::POIsGenerateWorkOrderYes
            ])->setName('充电桩离线告警 - 是否自动生成工单'),
            'po_auto_dispatch_user_id' => v::notOptional()->intType()
                ->min(0)->setName('充电桩离线告警 - 自动派发给指定工作人员'),

            'ccsr_is_generate_work_order' => v::notEmpty()->intType()->in([
                StationsActiveAlarmConfigRepositories::CCSRIsGenerateWorkOrderNot,
                StationsActiveAlarmConfigRepositories::CCSRIsGenerateWorkOrderYes
            ])->setName('能源路由器资源告警 - 是否自动生成工单'),
            'ccsr_auto_dispatch_user_id' => v::notOptional()->intType()
                ->min(0)->setName('能源路由器资源告警 - 自动派发给指定工作人员'),
            'email' => v::optional(v::stringType()->length(5, 50))->setName('邮箱地址'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 充电站运营商清分验证字段
     * lwj 2024.5.11 新增
     * lwj 2024.5.18 修改
     * @param array $field
     * @return array
     * @noinspection PhpParamsInspection
     */
    public static function stations_corp_clearing(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1, **********)->setName('id'),
            'station_id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('场站id'),
            'corp_id' => v::notEmpty()->intType()->between(1, ********************9999)->setName('运营商编号'),
            'ratio_electricity_price' => v::notOptional()->intType()->between(0, 100)->setName('充电费分成比例'),
            'ratio_ser_price' => v::notOptional()->intType()->between(0, 100)->setName('服务费分成比例'),
            'clearing_list' => v::notEmpty()->arrayType()->each(v::keySet(
                v::key('id', v::notOptional()->intType()->between(0, **********)->setName('分成id')),
                v::key('station_id', v::notEmpty()->intType()->between(1, ********************9999)->setName('场站id')),
                v::key('corp_id', v::notEmpty()->intType()->between(1, ********************9999)->setName('运营商编号')),
                v::key('ratio_electricity_price', v::notOptional()->intType()->between(0, 100)->setName('充电费分成比例')),
                v::key('ratio_ser_price', v::notOptional()->intType()->between(0, 100)->setName('服务费分成比例')),
            ))->setName('分成列表'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    /**
     * 订单清分验证字段
     * lwj 2024.5.22 新增
     * @param array $field
     * @return array
     */
    public static function order_clearing(array $field): array
    {
        $rules = [
            'id' => v::notEmpty()->intType()->between(1, **********)->setName('id'),
            'ids' => v::notEmpty()->arrayType()->each(v::intType()->between(1, **********9))->setName('清分id数组'),
            'clearing_time' => v::notEmpty()->dateTime()->setName('结算时间'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function service_piles(array $field): array
    {
        $rules = [
            'piles_id' => v::notEmpty()->intType()->setName('充电桩ID'),
            'shots_count' => v::notEmpty()->intType()->setName('枪数量'),
        ];

        return array_intersect_key($rules, array_flip($field));
    }

    public static function activity(array $field): array
    {
        $rule = [
            'activity_id' => v::notEmpty()->intType()->setName('活动ID'),
            'id' => v::notEmpty()->intType()->setName('活动ID'),
            'title' => v::notEmpty()->stringType()->length(1, 20)->setName('活动标题'),
            'describe' => v::notOptional()->stringType()->length(0, 250)->setName('活动介绍'),
            'discount' => v::notOptional()->intType()->min(0)->max(100)->setName("优惠折扣"),
            'station_id' => v::notEmpty()->intType()->setName('活动场站ID'),
            'station_ids' => v::notOptional()->arrayType()->setName('场站ID集合'),
            'start_time' => v::notEmpty()->stringType()->setName('活动开始时间'),
            'end_time' => v::notEmpty()->stringType()->setName('活动结束时间'),
            'status' => v::notOptional()->intType()->in([1, 0])->setName("活动状态"),
            'filter_title' => v::optional(v::stringType())->setName('筛选活动标题'),
            'filter_status' => v::optional(v::intType())->setName('筛选活动状态'),
            'filter_station_id' => v::optional(v::intType())->setName('筛选场站ID'),
            'page' => v::notEmpty()->intType()->setName('页码'),
            'limit' => v::notEmpty()->intType()->setName('显示条数'),
            'order_name' => v::notEmpty()->stringType()->setName('排序字段'),
            'order_type' => v::notEmpty()->stringType()->setName('排序方式'),
        ];

        return array_intersect_key($rule, array_flip($field));
    }

    public static function report_form(array $field): array
    {
        $rule = [
            'start_time' => v::notEmpty()->stringType()->setName('起始时间'),
            'end_time' => v::notEmpty()->stringType()->setName('末尾时间'),
            'page' => v::notEmpty()->intType()->setName('页码'),
            'limit' => v::notEmpty()->intType()->setName('显示条数'),
        ];

        return array_intersect_key($rule, array_flip($field));
    }

    public static function charging_qrcode(array $field): array
    {
        $rule = [
            'generate_count' => v::notEmpty()->min(1)->max(1000)->setName('生成的数量'),
            'url_prefix' => v::notEmpty()->url()->length(1, 243)->setName('链接前缀'),
            'page' => v::notEmpty()->intType()->setName('页码'),
            'limit' => v::notEmpty()->intType()->setName('显示条数'),
            'sort_field' => v::notEmpty()->intType()->setName('排序字段'),
            'sort_type' => v::notEmpty()->intType()->in([1, 2])->setName('排序方式'),
            'filter_min_id' => v::optional(v::intType()->min(0))->setName('过滤ID最小值'),
            'filter_max_id' => v::optional(v::intType()->min(0))->setName('过滤ID最大值'),
            'charging_qrcode_id' => v::notEmpty()->intType()->setName('充电二维码的ID'),
            'shots_id' => v::notEmpty()->intType()->setName('充电枪ID'),
            'is_force_bind' => v::notOptional()->boolType()->setName('是否强行绑定')
        ];

        return array_intersect_key($rule, array_flip($field));
    }

    public static function debug_tool(array $field): array
    {
        $rule = [
            'transaction_serial_number' => v::notEmpty()->stringType()->setName('订单ID'),
            'power_limit' => v::notEmpty()->intType()->setName('限制功率')
        ];

        return array_intersect_key($rule, array_flip($field));
    }
}
