<?php
/** @noinspection PhpUnused */

namespace app\common\lib\entity;

class ActiveAlarmConfig
{
    use Autofill;

    public function __construct(array $property)
    {
        if (isset($property['sf_reason_for_stop_white_list']) && is_string($property['sf_reason_for_stop_white_list'])) {
            $property['sf_reason_for_stop_white_list'] = explode('#', $property['sf_reason_for_stop_white_list']);
        }

        foreach ($property as $key => $value) {
            if (property_exists($this, $key)) {
                $this->{$key} = $value;
            }
        }
    }

    // 充电枪故障告警 - 充电停止原因白名单(多个以#分隔)
    public array $sf_reason_for_stop_white_list;
    // 能源路由器资源告警 - CPU使用率警报值(单位:百分比)
    public int $ccsr_cpu_alarm_value;

    // 能源路由器资源告警 - 剩余内存警报值(单位:MB)
    public int $ccsr_memory_alarm_value;

    // 能源路由器资源告警 - 剩余内存警报值(单位:MB)
    public int $ccsr_disk_alarm_value;

    public string $update_time;
}