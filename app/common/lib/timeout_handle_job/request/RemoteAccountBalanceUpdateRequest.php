<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\timeout_handle_job\request;

class RemoteAccountBalanceUpdateRequest extends BaseRequest
{
    public string $request_id;
    public string $timer_key;
    public int $time;
    public int $timer_time;
    public int $user_id;
    public int $trigger_time;
    public int $retried_count;
    public int $retry_count;
}