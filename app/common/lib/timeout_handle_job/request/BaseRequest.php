<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\timeout_handle_job\request;


use app\common\lib\exception\RuntimeException;

class BaseRequest implements TimeoutHandleJobContract
{
    public const TypeRemoteAccountBalanceUpdate = '远程账户余额更新';

    public string $type;

    public function __construct(array $fields)
    {
        foreach ($fields as $key => $value) {
            if (property_exists($this, $key)) {
                $this->{$key} = $value;
            }
        }
    }

    public function getType(): string
    {
        return $this->type;
    }


    public function encode(): string
    {
        return json_encode($this->toArray());
    }

    public function toArray(): array
    {
        return (array)$this;
    }

    public static function decode(string $data): TimeoutHandleJobContract
    {
        $data = json_decode($data, true);
        return match ($data['type']) {
            self::TypeRemoteAccountBalanceUpdate => new RemoteAccountBalanceUpdateRequest($data),
            default => throw new RuntimeException('无效消息类型'),
        };
    }
}