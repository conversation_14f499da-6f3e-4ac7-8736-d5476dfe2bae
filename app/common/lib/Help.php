<?php

namespace app\common\lib;

use app\common\lib\transfer_service\SerialNumber;
use app\common\queue\TransferServiceWriteQueue;
use LieHuoHuYu\ChargeTransferServiceProtocol\Kernel;
use RedisException;

class Help
{
    /**
     * @param string $index
     * @param array $body
     * @return void
     * @throws RedisException
     */
    public static function sendToTransferService(string $index, array $body): void
    {
        $serialNumber = SerialNumber::increaseSequence();
        $package = Kernel::create($serialNumber, $index, $body);

        app(TransferServiceWriteQueue::class)->push($package->encode());
    }
}