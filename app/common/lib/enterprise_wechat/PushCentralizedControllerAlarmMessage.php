<?php

/** @noinspection HtmlDeprecatedAttribute */
/** @noinspection XmlDeprecatedElement */
/** @noinspection HtmlDeprecatedTag */
/** @noinspection PhpUnusedParameterInspection */
declare (strict_types=1);

namespace app\common\lib\enterprise_wechat;

use app\event\ChargeNormalEndEvent;

class PushCentralizedControllerAlarmMessage extends GroupRobotMessage
{
    protected ChargeNormalEndEvent $event;

    public function __construct(ChargeNormalEndEvent $event)
    {
        parent::__construct();

        $this->event = $event;
    }
}