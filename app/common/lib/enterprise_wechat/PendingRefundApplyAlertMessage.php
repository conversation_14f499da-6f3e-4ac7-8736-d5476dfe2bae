<?php
/** @noinspection HtmlDeprecatedAttribute */
/** @noinspection XmlDeprecatedElement */
/** @noinspection HtmlDeprecatedTag */
/** @noinspection PhpUnusedParameterInspection */
declare (strict_types=1);

namespace app\common\lib\enterprise_wechat;

use app\common\lib\ExceptionLogCollector;
use app\common\lib\http\Response;
use app\common\model\PayOrder;
use app\common\model\Stations;
use app\common\model\Users;
use app\event\ChargeNormalEndEvent;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class PendingRefundApplyAlertMessage extends GroupRobotMessage
{
    protected string $corp_name;
    protected int $pending_count;

    public function __construct(string $corp_name, int $pending_count)
    {
        parent::__construct();

        $this->corp_name = $corp_name;
        $this->pending_count = $pending_count;
    }


    /**
     * 发送消息
     *
     * @return Response
     */
    public function send(): Response
    {
        $body = $this->generateMessageBody();

        return $this->sendRequest($body);
    }

    protected function generateMessageBody(): string
    {

        $content = sprintf("# 存在待处理的退款申请\n
                 >运营商名称: <font color = \"warning\">%s</font>
                 >待处理数量: <font color=\"comment\">%s</font>
                 >告警时间: <font color=\"comment\">%s</font>
                ", $this->corp_name, $this->pending_count, date('Y-m-d H:i:s'));

        return json_encode([
            'msgtype' => 'markdown',
            'markdown' => [
                'content' => $content,
            ]
        ]);
    }


    protected function getUserLastRechargeAmountAndTime(int $user_id): array
    {
        $payOrderModel = app(PayOrder::class);
        try {
            $lastRechargeAmountAndTime = $payOrderModel->getUserLastRechargeAmountAndTime($user_id);
            if (is_null($lastRechargeAmountAndTime)) {
                $lastRechargeAmountAndTime = [
                    'price' => '用户暂未充值'
                    , 'create_time' => '用户暂未充值'
                ];
            } else {
                $lastRechargeAmountAndTime['price'] = self::formatMoney($lastRechargeAmountAndTime['price']);
            }
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            $lastRechargeAmountAndTime = [
                'price' => '数据库查询失败',
                'create_time' => '数据库查询失败',
            ];
        }
        return $lastRechargeAmountAndTime;
    }

    protected function queryUserData(int $user_id): array
    {
        $userModel = app(Users::class);
        try {
            $userData = $userModel->where('id', $user_id)->field(['balance', 'credit_limit', 'phone', 'create_time'])->find();
            if (empty($userData)) {
                $userData['balance'] = $userData['phone'] = $userData['create_time'] = '无效用户ID';
            } else {
                $userData = $userData->toArray();
                $userData['balance'] = $userData['balance'] + $userData['credit_limit'];
                $userData['balance'] = self::formatMoney($userData['balance']);
                $userData['phone'] = $userData['phone'] ?? '';
                $userData['create_time'] = $userData['create_time'] ?? '';
            }
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            $userData = [
                'balance' => '数据库查询失败',
                'phone' => '数据库查询失败',
                'create_time' => '数据库查询失败'
            ];
        }
        return $userData;
    }
}