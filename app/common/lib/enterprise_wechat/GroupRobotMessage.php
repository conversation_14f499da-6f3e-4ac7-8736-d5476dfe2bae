<?php
/** @noinspection HtmlDeprecatedAttribute */
/** @noinspection XmlDeprecatedElement */
/** @noinspection HtmlDeprecatedTag */
/** @noinspection PhpUnusedParameterInspection */

namespace app\common\lib\enterprise_wechat;

use app\common\lib\http\HttpClient;
use app\common\lib\http\Response;
use app\common\model\Corp;

class GroupRobotMessage
{
    protected HttpClient $httpClient;

    public function __construct()
    {
        $this->httpClient = new HttpClient();
    }

    protected function getCorpName(int $corp_id): string
    {
        $corpName = app(Corp::class)->where('id', '=', $corp_id)->value('name');
        return $corpName ?? '未知';
    }

    public function sendRequest(string $body): Response
    {
        $response = $this->httpClient->setProtocol('https')
            ->setDomain('qyapi.weixin.qq.com')
            ->setMethod('POST')
            ->setUri('cgi-bin/webhook/send')->setQuery([
                'key' => config('wechat.qy_api_key')
            ])
            ->setBody($body)->sendRequest();

        $this->httpClient->reset();

        return $response;
    }
}