<?php
/** @noinspection HtmlDeprecatedAttribute */
/** @noinspection XmlDeprecatedElement */
/** @noinspection HtmlDeprecatedTag */
/** @noinspection PhpUnusedParameterInspection */
declare (strict_types=1);

namespace app\common\lib\enterprise_wechat;

use app\common\lib\ExceptionLogCollector;
use app\common\lib\http\Response;
use app\common\model\PayOrder;
use app\common\model\Stations;
use app\common\model\Users;
use app\event\ChargeNormalEndEvent;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class PushChargeNormalEndMessage extends GroupRobotMessage
{
    protected ChargeNormalEndEvent $event;

    public function __construct(ChargeNormalEndEvent $event)
    {
        parent::__construct();

        $this->event = $event;
    }

    /**
     * 格式化费用
     *
     * @param int $payMoney 充电费用(单位:分)
     * @return string 格式化后的充电费用描述
     */
    protected static function formatMoney(int $payMoney): string
    {
        return sprintf("%.2f元", $payMoney / 100);
    }

    protected static function formatElectricity(int $electricity): string
    {
        return sprintf('%.4fKwh', $electricity / 10000);
    }

    public static function formatChargeDuration(int $chargeDuration): string
    {
        $h = $m = 0;

        if ($chargeDuration >= 3600) {
            $h = floor($chargeDuration / 3600);
            $chargeDuration -= $h * 3600;
        }

        if ($chargeDuration >= 60) {
            $m = floor($chargeDuration / 60);
            $chargeDuration -= $m * 60;
        }

        $s = $chargeDuration;

        if (empty($h) && empty($m)) {
            return sprintf('%d秒', $s);
        } else if (empty($h)) {
            return sprintf('%d分钟%d秒', $m, $s);
        }

        return sprintf('%d小时%d分钟%d秒', $h, $m, $s);
    }

    /**
     * 发送消息
     *
     * @return Response
     */
    public function send(): Response
    {
        $body = $this->generateMessageBody();

        return $this->sendRequest($body);
    }

    protected function generateMessageBody(): string
    {
        $userData = $this->queryUserData($this->event->user_id);
        // 查询用户最后一次充值时间与金额
        $lastRechargeAmountAndTime = $this->getUserLastRechargeAmountAndTime($this->event->user_id);
        $stationName = app(Stations::class)->getStationName($this->event->stationId);
        $stationName = $stationName ?? '未知';
        $corpName = $this->getCorpName($this->event->corpId);
//        $this->event->corpId

        $contentVars = [
            $this->event->stop_charge_reason, $this->event->orderId, $corpName, $stationName, self::formatMoney($this->event->payMoney),
            self::formatChargeDuration($this->event->chargeDuration), self::formatElectricity($this->event->electricityTotal),
            date('Y-m-d H:i:s', $this->event->startTime), date('Y-m-d H:i:s', $this->event->endTime),
            $userData['phone'], $userData['balance'], $userData['create_time'],
            $lastRechargeAmountAndTime['price'], $lastRechargeAmountAndTime['create_time']
        ];
        $content = sprintf("充电结束通知\n
         >停止原因:<font color=\"comment\">%s</font>
         >订单号:<font color=\"comment\">%s</font>
         >运营商名称:<font color=\"comment\">%s</font>
         >场站名称:<font color=\"comment\">%s</font>
         >充电费用:<font color=\"comment\">%s</font>
         >充电耗时:<font color=\"comment\">%s</font>
         >电量消耗:<font color=\"comment\">%s</font>
         >开始充电时间:<font color=\"comment\">%s</font>
         >结束充电时间:<font color=\"comment\">%s</font>
         >用户手机号:<font color=\"comment\">%s</font>
         >用户可用余额:<font color=\"comment\">%s</font>
         >用户注册时间:<font color=\"comment\">%s</font>
         >最近一次充值金额:<font color=\"comment\">%s</font>
         >最近一次充值时间:<font color=\"comment\">%s</font>
         ", ...$contentVars);

        return json_encode([
            'msgtype' => 'markdown',
            'markdown' => [
                'content' => $content,
            ]
        ]);
    }


    protected function getUserLastRechargeAmountAndTime(int $user_id): array
    {
        $payOrderModel = app(PayOrder::class);
        try {
            $lastRechargeAmountAndTime = $payOrderModel->getUserLastRechargeAmountAndTime($user_id);
            if (is_null($lastRechargeAmountAndTime)) {
                $lastRechargeAmountAndTime = [
                    'price' => '用户暂未充值'
                    , 'create_time' => '用户暂未充值'
                ];
            } else {
                $lastRechargeAmountAndTime['price'] = self::formatMoney($lastRechargeAmountAndTime['price']);
            }
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            $lastRechargeAmountAndTime = [
                'price' => '数据库查询失败',
                'create_time' => '数据库查询失败',
            ];
        }
        return $lastRechargeAmountAndTime;
    }

    protected function queryUserData(int $user_id): array
    {
        $userModel = app(Users::class);
        try {
            $userData = $userModel->where('id', $user_id)->field(['balance', 'credit_limit', 'phone', 'create_time'])->find();
            if (empty($userData)) {
                $userData['balance'] = $userData['phone'] = $userData['create_time'] = '无效用户ID';
            } else {
                $userData = $userData->toArray();
                $userData['balance'] = $userData['balance'] + $userData['credit_limit'];
                $userData['balance'] = self::formatMoney($userData['balance']);
                $userData['phone'] = $userData['phone'] ?? '';
                $userData['create_time'] = $userData['create_time'] ?? '';
            }
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            $userData = [
                'balance' => '数据库查询失败',
                'phone' => '数据库查询失败',
                'create_time' => '数据库查询失败'
            ];
        }
        return $userData;
    }
}