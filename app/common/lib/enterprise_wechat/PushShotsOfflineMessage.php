<?php
/** @noinspection HtmlDeprecatedAttribute */
/** @noinspection XmlDeprecatedElement */
/** @noinspection HtmlDeprecatedTag */
/** @noinspection PhpUnusedParameterInspection */
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\enterprise_wechat;

use app\common\lib\http\Response;
use app\common\model\Shots;
use app\event\ShotsOfflineEvent;

class PushShotsOfflineMessage extends GroupRobotMessage
{

    protected ShotsOfflineEvent $event;

    public function __construct(ShotsOfflineEvent $event)
    {
        parent::__construct();

        $this->event = $event;
    }

    /**
     * 发送消息
     *
     * @return Response
     */
    public function send(): Response
    {
        $body = $this->generateMessageBody();

        return $this->sendRequest($body);
    }

    protected function generateMessageBody(): string
    {
        // 场站
        $stationName = app(Shots::class)->getStationName($this->event->shot_id);
        $stationName = $stationName ?? '未知';
        // 运营商
//        $corpName = $this->getCorpName();
        $corpName = '';

        $contentVars = [
            $this->event->shot_id
            , $corpName
            , $stationName
            , $this->event->status_update_time
        ];
        $content = sprintf("充电枪离线通知\n
         >充电枪ID:<font color=\"comment\">%s</font>
         >运营商名称:<font color=\"comment\">%s</font>
         >场站名称:<font color=\"comment\">%s</font>
         >充电枪离线时间:<font color=\"comment\">%s</font>
         ", ...$contentVars);

        return json_encode([
            'msgtype' => 'markdown',
            'markdown' => [
                'content' => $content,
            ]
        ]);
    }
}