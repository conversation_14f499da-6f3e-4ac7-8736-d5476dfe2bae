<?php
/** @noinspection HtmlDeprecatedAttribute */
/** @noinspection XmlDeprecatedElement */
/** @noinspection HtmlDeprecatedTag */
/** @noinspection PhpUnusedParameterInspection */
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\enterprise_wechat;

use app\common\lib\ExceptionLogCollector;
use app\common\lib\http\Response;
use app\common\model\PayOrder;
use app\common\model\Stations;
use app\common\model\Users;
use app\event\ChargeNormalStartEvent;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class PushChargeNormalStartMessage extends GroupRobotMessage
{

    protected ChargeNormalStartEvent $event;

    public function __construct(ChargeNormalStartEvent $event)
    {
        parent::__construct();

        $this->event = $event;
    }

    /**
     * 格式化费用
     *
     * @param int $payMoney 充电费用(单位:分)
     * @return string 格式化后的充电费用描述
     */
    protected static function formatMoney(int $payMoney): string
    {
        return sprintf("%.2f元", $payMoney / 100);
    }


    /**
     * 发送消息
     *
     * @return Response
     */
    public function send(): Response
    {
        $body = $this->generateMessageBody();

        return $this->sendRequest($body);
    }

    protected function generateMessageBody(): string
    {
        $userData = $this->queryUserData($this->event->user_id);
        // 查询用户最后一次充值时间与金额
        $lastRechargeAmountAndTime = $this->getUserLastRechargeAmountAndTime($this->event->user_id);
        // 场站
        $stationName = app(Stations::class)->getStationName($this->event->station_id);
        $stationName = $stationName ?? '未知';
        // 运营商
        $corpName = $this->getCorpName($this->event->corp_id);

        $contentVars = [
            $this->event->order_id
            , $corpName
            , $stationName
            , date('Y-m-d H:i:s', $this->event->start_time)
            , $userData['phone']
            , $userData['balance']
            , $userData['create_time']
            , $lastRechargeAmountAndTime['price']
            , $lastRechargeAmountAndTime['create_time']
        ];
        $content = sprintf("开始充电通知\n
         >订单号:<font color=\"comment\">%s</font>
         >运营商名称:<font color=\"comment\">%s</font>
         >场站名称:<font color=\"comment\">%s</font>
         >开始充电时间:<font color=\"comment\">%s</font>
         >用户手机号:<font color=\"comment\">%s</font>
         >用户可用余额:<font color=\"comment\">%s</font>
         >用户注册时间:<font color=\"comment\">%s</font>
         >最近一次充值金额:<font color=\"comment\">%s</font>
         >最近一次充值时间:<font color=\"comment\">%s</font>
         ", ...$contentVars);

        return json_encode([
            'msgtype' => 'markdown',
            'markdown' => [
                'content' => $content,
            ]
        ]);
    }


    protected function getUserLastRechargeAmountAndTime(int $user_id): array
    {
        $payOrderModel = app(PayOrder::class);
        try {
            $lastRechargeAmountAndTime = $payOrderModel->getUserLastRechargeAmountAndTime($user_id);
            if (is_null($lastRechargeAmountAndTime)) {
                $lastRechargeAmountAndTime = [
                    'price' => '用户暂未充值'
                    , 'create_time' => '用户暂未充值'
                ];
            } else {
                $lastRechargeAmountAndTime['price'] = self::formatMoney($lastRechargeAmountAndTime['price']);
            }
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            $lastRechargeAmountAndTime = [
                'price' => '数据库查询失败',
                'create_time' => '数据库查询失败',
            ];
        }
        return $lastRechargeAmountAndTime;
    }

    protected function queryUserData(int $user_id): array
    {
        $userModel = app(Users::class);
        try {
            $userData = $userModel->where('id', $user_id)->field(['balance', 'credit_limit', 'phone', 'create_time'])->find();
            if (empty($userData)) {
                $userData['balance'] = $userData['phone'] = $userData['create_time'] = '无效用户ID';
            } else {
                $userData = $userData->toArray();
                $userData['balance'] = $userData['balance'] + $userData['credit_limit'];
                $userData['balance'] = self::formatMoney($userData['balance']);
                $userData['phone'] = $userData['phone'] ?? '';
                $userData['create_time'] = $userData['create_time'] ?? '';
            }
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            $userData = [
                'balance' => '数据库查询失败',
                'phone' => '数据库查询失败',
                'create_time' => '数据库查询失败'
            ];
        }
        return $userData;
    }
}