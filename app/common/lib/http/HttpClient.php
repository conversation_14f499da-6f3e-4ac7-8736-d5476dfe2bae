<?php
/** @noinspection PhpUnused */

namespace app\common\lib\http;

use app\common\log\LogCollector;

class HttpClient
{
    public const MethodPost = 'POST';
    public const MethodGet = 'GET';

    protected string $protocol = 'http';
    protected string $domain = '';
    protected string $uri = '';
    protected string $method = 'GET';
    protected array $header = [];
    protected array $query = [];
    protected string $body = '';

    public function reset(): static
    {
        $this->protocol = 'http';
        $this->domain = '';
        $this->uri = '';
        $this->method = 'GET';
        $this->header = [];
        $this->query = [];
        $this->body = '';

        return $this;
    }

    public function setQuery(array $params): static
    {
        $this->query = $params;
        return $this;
    }

    public function setMethod(string $method): static
    {
        $this->method = $method;
        return $this;
    }

    public function getMethod(): string
    {
        return $this->method;
    }

    public function setUrl(string $url): static
    {
        // http://127.0.0.1:9710/GetApi/
        $protocolResult = explode('://', $url);
        $this->protocol = $protocolResult[0];
        $domainResult = explode('/', $protocolResult[1]);
        $this->domain = $domainResult[0];
        $this->uri = str_replace($this->domain . '/', '', $protocolResult[1]);

        return $this;
    }

    public function getUrl(): string
    {
        $uri = $this->uri;
        if (!empty($this->query)) {
            $query = [];
            foreach ($this->query as $key => $value) {
                $query[] = sprintf('%s=%s', $key, $value);
            }
            $uri .= '?' . implode('&', $query);
        }
        return sprintf('%s://%s/%s', $this->protocol, $this->domain, $uri);
    }

    public function setProtocol(string $protocol): static
    {
        $this->protocol = $protocol;
        return $this;
    }

    public function setDomain(string $domain): static
    {
        $this->domain = $domain;
        return $this;
    }

    public function setUri(string $uri): static
    {
        $this->uri = $uri;
        return $this;
    }

    public function setHeader(array $options): static
    {
        $this->header = $options;
        return $this;
    }

    public function setBody(string $body): static
    {
        $this->body = $body;
        return $this;
    }

    public function getBody(): string
    {
        return $this->body;
    }

    protected function formatHeader(): array
    {
        $header = [];
        foreach ($this->header as $key => $value) {
            $header[] = sprintf('%s: %s', $key, $value);
        }
        return $header;
    }

    public function sendRequest(): Response
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->getUrl());
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->formatHeader());
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        if (!empty($this->body)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $this->body);
        }

        if ($this->method === 'GET') {
            curl_setopt($ch, CURLOPT_HTTPGET, true);
        } else if ($this->method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
        } else {
            // 设置自定义方法
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $this->method);
        }

        //判断当前是不是有post数据的发
        $output = curl_exec($ch);
        $Response = new Response();

        if ($output === FALSE) {
            $Response->isFailed = true;
            $Response->failedMsg = curl_error($ch);
        } else {
            $Response->body = $output;
        }
        $Response->httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $Response->header = curl_getinfo($ch);
        curl_close($ch);

        LogCollector::collectorRunLog('向微信支付发送请求:');
        LogCollector::collectorRunLog(print_r([
            'url' => $this->getUrl(),
            'method' => $this->getMethod(),
            'header' => $this->header,
            'query' => $this->query,
            'body' => $this->body
        ], true));

        return $Response;
    }
}
