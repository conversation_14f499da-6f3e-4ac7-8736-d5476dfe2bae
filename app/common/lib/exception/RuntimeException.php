<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\exception;

class RuntimeException extends \RuntimeException
{

    // 异常状态码
    public const CodeBusinessException = 400; // 业务异常
    public const CodeBusinessWarning = 401; // 业务警告
    public const CodeServiceException = 500; // 服务异常
    public const CodeDatabaseException = 501; // 数据库异常

    public const CodeBusinessExceptionChargingQrcodeBeenBound = 4001; // 充电二维码已被绑定
    public const CodeBusinessExceptionShotsBeenBound = 4002; // 充电枪已经被绑定

    protected array $context = [];

    public function __construct(string $message = "", array $context = [], int $code = 0, ?\Throwable $previous = null)
    {
        $this->context = $context;
        parent::__construct($message, $code, $previous);
    }

    public function getContext(): array
    {
        return $this->context;
    }
}