<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

declare (strict_types=1);

namespace app\common\lib;

use app\common\model\Order;
use app\common\model\UserBalanceFreezeLog;
use app\common\model\Users;
use Exception;
use Throwable;
use app\common\model\UserBalanceLog;

class UnfreezeUserBalanceAndPayOrder
{
    /**
     * @var ?int $order_status 需要跟定的订单状态，如果不需要更新订单状态，保留null值即可。
     */
    protected ?int $order_status = null;

    /**
     * @var int $user_id 用户ID
     */
    protected int $user_id;


    /**
     * @var int $amount 消费金额
     */
    protected int $amount;

    /**
     * @var string|int $order_id 交易流水号
     */
    protected string|int $order_id;

    /**
     * @var int $freeze_money 需要解冻的冻结金额
     */
    protected int $freeze_money;

    /**
     * @var int $freeze_status 订单冻结金额状态
     */
    protected int $freeze_status;

    /**
     * @var int $electricity 充电量(除以10000后单位为:Kw.h)
     */
    protected int $electricity;

    /**
     * @var int $end_time 订单充电结束时间(单位:秒)
     */
    protected int $end_time;

    /**
     * @var int $charge_duration 充电时长
     */
    protected int $charge_duration;


    /**
     * @var int 业务异常码
     */
    public const BusinessCode = 400;

    /**
     * cbj 2023.10.9 修改
     *
     * @param int $user_id 用户ID
     * @param int $amount 消费金额
     * @param string|int $order_id 交易流水号
     * @param int $freeze_money 需要解冻的冻结金额
     * @param int $freeze_status 订单冻结金额状态
     * @param int $electricity 订单充电量(除以10000后单位为:Kw.h)
     * @param int $end_time 充电结束时间(单位:秒)
     * @param int $charge_duration 充电时长(单位:秒)
     */
    public function __construct(
        int        $user_id,
        int        $amount,
        string|int $order_id,
        int        $freeze_money,
        int        $freeze_status,
        int        $electricity,
        int        $end_time,
        int        $charge_duration
    )
    {
        $this->user_id = $user_id;
        $this->amount = $amount;
        $this->order_id = $order_id;
        $this->freeze_money = $freeze_money;
        $this->freeze_status = $freeze_status;
        $this->electricity = $electricity;
        $this->end_time = $end_time;
        $this->charge_duration = $charge_duration;
    }

    /**
     * 解冻用户余额并支付订单
     *  lwj 2023.8.22 新增
     *  lwj 2023.9.27 修改
     *  cbj 2023.10.9 修改
     *
     * @return void
     * @throws Throwable
     */
    public function run(): void
    {
        try {
            // 更新订单数据
            $order_update_row = $this->update_order_data();
            // 解冻用户余额
            $user_update_row = $this->unfreeze_user_balance();
            // 新增用户余额变更日志
            $log_insert_result = $this->insert_user_balance_log();
            // 更新用户冻结余额日志的状态为已解冻
            $user_freeze_balance_log_update_row = $this->update_user_freeze_balance_log();

            trace('解冻用户余额并支付订单=》请求：返回1：' . $order_update_row . '，返回2：' . $user_update_row . '，返回3：' . $log_insert_result . '，返回4：' . $user_freeze_balance_log_update_row, '成功');

        } catch (Throwable $e) {
            trace('解冻用户余额并支付订单=》异常：' . $this->error_info($e), '信息');
            throw $e;
        }
    }

    protected function error_info(Throwable $e): string
    {
        return sprintf('file: %s, line: %d, message: %s, code: %d', $e->getFile(), $e->getLine(), $e->getMessage(), $e->getCode());
    }

    /**
     * 设置订单状态
     * cbj 2023.10.9 修改
     *
     * @param ?int $status 订单状态
     * @return self
     */
    public function set_order_status(?int $status): self
    {
        $this->order_status = $status;
        return $this;
    }

    /**
     * 更新订单数据
     * cbj 2023.10.9 修改, cbj 2023.11.2 修改
     *
     * @return int 更新的行数
     * @throws Exception
     */
    protected function update_order_data(): int
    {
        if ($this->freeze_status === Order::FreezeStatusNot) {
            trace('解冻用户余额并支付订单=》无需解冻或已经解冻：' . json_encode_cn([
                    'id' => $this->order_id,
                    'freeze_status' => $this->freeze_status,
                    'money' => $this->amount,
                    'pay_money' => $this->amount,
                    'electricity_price' => $this->amount
                ]), '错误');
            throw new Exception('订单无需解冻或已经解冻', self::BusinessCode);
        }

        $updateRow = app(Order::class)->unfreezeOrder(
            $this->order_id,
            $this->electricity,
            $this->end_time,
            $this->charge_duration,
            $this->order_status
        );
        if (empty($updateRow)) throw new Exception('更新订单数据失败', self::BusinessCode);

        return $updateRow;
    }

    /**
     * 计算剩下金额
     * 在订单完成后，需要计算订单剩下的金额。
     *
     * @param int $freeze_money 原先冻结的金额
     * @param int $amount 订单产生的消费金额
     * @return int 剩余的金额
     */
    public static function calculate_remaining_amount(int $freeze_money, int $amount): int
    {
        return $freeze_money - $amount;
    }

    /**
     * 解冻用户余额
     * cbj 2023.10.9 修改
     *
     * @return int
     * @throws Exception
     */
    protected function unfreeze_user_balance(): int
    {
        $balance = self::calculate_remaining_amount($this->freeze_money, $this->amount);
        $updateRow = app(Users::class)->unfreeze_user_balance(
            $this->user_id,
            $balance,
            $this->freeze_money
        );
        if (empty($updateRow)) throw new Exception('解冻用户余额失败', self::BusinessCode);

        return (int)$updateRow;
    }

    /**
     * 新增用户余额变更日志
     * cbj 2023.10.9 修改
     *
     * @return int
     * @throws Exception
     */
    protected function insert_user_balance_log(): int
    {
        $memo = '订单【' . $this->order_id . '】付款：' . $this->amount . '分';
        $result = app(UserBalanceLog::class)->add($this->user_id, -$this->amount, $memo, UserBalanceLog::TypePayChargeOrder);
        if (!$result) throw new Exception('新增用户余额变更日志 - 失败', self::BusinessCode);

        return $result;
    }

    /**
     * 更新用户冻结余额日志的状态为已解冻
     * cbj 2023.10.9 修改
     *
     * @return int
     * @throws Exception
     */
    protected function update_user_freeze_balance_log(): int
    {
        $updateRow = app(UserBalanceFreezeLog::class)->unfreeze($this->order_id);
        if (empty($updateRow)) throw new Exception('更新用户冻结余额日志的状态为已解冻 - 失败', self::BusinessCode);

        return $updateRow;
    }

}