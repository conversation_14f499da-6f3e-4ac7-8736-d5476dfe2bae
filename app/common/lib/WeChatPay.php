<?php

namespace app\common\lib;

use app\common\model\PayCer as PayCerModel;
use Throwable;
use WeChatPay\BuilderChainable;
use pay\WeiXinAesUtil;


class WeChatPay
{
    public string $appid;
    public string $secret;
    public string|int $mch_id;
    public string $cert_path;
    public string $weixin_callback_url;
    public string $apiclient_key_sn;
    public string $apiclient_key;
    public string $apiclient_cert;
    public string $weixin_pingtai_sn;
    public string $weixin_pingtai_key;
    public string $api_key_v3;
    public BuilderChainable $instance;

    public string $jsapi = 'https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi';


    public function __construct()
    {
        $this->cert_path = root_path() . 'cert/WeChatPay/';
        $this->appid = config('wechat.appid');
        $this->secret = config('wechat.secret');
        $this->mch_id = config('wechat.mch_id');
        $this->weixin_callback_url = config('wechat.weixin_callback_url');
        $this->apiclient_key_sn = config('wechat.apiclient_key_sn');
        $this->apiclient_key = config('wechat.apiclient_key');
        $this->apiclient_cert = config('wechat.apiclient_cert');
        $this->weixin_pingtai_sn = config('wechat.weixin_pingtai_sn');
        $this->weixin_pingtai_key = config('wechat.weixin_pingtai_key');
        $this->api_key_v3 = config('wechat.api_key_v3');
    }

    /**
     * 生成微信签名
     * lwj 2023.8.15 新增
     * @param string $url
     * @param string $body
     * @param string $http_method
     * @return string
     */
    public function get_sign(string $url, string $body = '', string $http_method = 'POST'): string
    {
        $timestamp = time();//时间戳
        $nonce = $timestamp . rand(10000, 99999);//随机字符串
        $url_parts = parse_url($url);
        $canonical_url = ($url_parts['path'] . (!empty($url_parts['query']) ? "?${url_parts['query']}" : ""));
        $message =
            $http_method . "\n" .
            $canonical_url . "\n" .
            $timestamp . "\n" .
            $nonce . "\n" .
            $body . "\n";
        openssl_sign($message, $raw_sign, $this->apiclient_key, 'sha256WithRSAEncryption');
        $sign = base64_encode($raw_sign);
        return sprintf('mchid="%s",nonce_str="%s",timestamp="%d",serial_no="%s",signature="%s"',
            $this->mch_id, $nonce, $timestamp, $this->apiclient_key_sn, $sign);
    }

    /**
     * 从微信获取平台证书内容
     * lwj 2023.8.15 新增
     * @return bool|array
     */
    public function get_Certificates(): bool|array
    {
        $sign = $this->get_sign("https://api.mch.weixin.qq.com/v3/certificates", '', 'GET');
        $header[] = 'User-Agent:' . env('UA');
        $header[] = 'Accept:application/json';
        $header[] = 'Authorization:WECHATPAY2-SHA256-RSA2048 ' . $sign;
        $res = get_http_data("https://api.mch.weixin.qq.com/v3/certificates", $header);
        trace('微信支付回调接口，获取证书，返回数据：' . $res, '信息');
        return json_decode($res, true);
    }

    /**
     * 获取平台证书
     * lwj 2023.8.15 新增
     * @param string $sn
     * @return bool|string
     */
    public function get_cer(string $sn): bool|string
    {
        try {
            $pay_cer = new PayCerModel();
            $cer = $pay_cer->where('sn', $sn)->value('cer');
            if ($cer) return $cer;
            $key_arr = $this->get_Certificates();
            if (!$key_arr) return false;

            $AesUtil = new WeiXinAesUtil($this->api_key_v3);

            foreach ($key_arr['data'] as $v) {
                if ($v['serial_no'] === $sn) {
                    $res_cer = $AesUtil->decryptToString($v['encrypt_certificate']['associated_data'], $v['encrypt_certificate']['nonce'], $v['encrypt_certificate']['ciphertext']);
                    trace('微信支付回调接口，获取证书，返回证书文本：' . $res_cer, '信息');
                    $add_key_arr = [
                        "sn" => $v['serial_no'],
                        "cer" => $res_cer,
                        "remarks" => '微信平台证书',
                        "log" => $key_arr,
                        "create_time" => date("Y-m-d H:i:s"),
                        "expire_time" => date("Y-m-d H:i:s", strtotime($v['expire_time']))
                    ];
                    $pay_cer->json(['log'])->insert($add_key_arr);
                }
            }

            return $pay_cer->where('sn', $sn)->value('cer');
        } catch (Throwable $e) {
            trace('js下单异常=》' . $e->getMessage(), '错误');
            return false;
        }
    }

    /**
     * 生成微信js支付签名
     * lwj 2023.8.15 新增
     * @param string $prepay_id
     * @return array
     */
    public function get_weixin_sign_js(string $prepay_id): array
    {
        $timestamp = strval(time());//时间戳
        $nonce = $timestamp . rand(10000, 99999);//随机字符串
        $message =
            $this->appid . "\n" .
            $timestamp . "\n" .
            $nonce . "\n" .
            $prepay_id . "\n";
        openssl_sign($message, $raw_sign, $this->apiclient_key, 'sha256WithRSAEncryption');
        $sign = base64_encode($raw_sign);
        return [
            'sign' => $sign,
            'timestamp' => $timestamp,
            'nonce' => $nonce,
            'prepay_id' => $prepay_id,
            'sign_type' => 'RSA'
        ];
    }

    /**
     * 生成微信支付请求头
     * lwj 2023.8.15 新增
     * @param string $sign
     * @return array
     */
    public function get_pay_header(string $sign): array
    {
        $header = [];
        $header[] = 'User-Agent:' . env('UA');
        $header[] = 'Content-type:application/json';
        $header[] = 'Accept:application/json';
        $header[] = 'Authorization:WECHATPAY2-SHA256-RSA2048 ' . $sign;
        return $header;
    }

    /**
     * js下单
     * lwj 2023.8.15 新增
     * @param string $order_id
     * @param int $price
     * @param string $description
     * @param string $openid
     * @param string $currency
     * @return string|bool|array
     */
    public function js_place_order(string $order_id, int $price, string $description, string $openid, string $currency = 'CNY'): string|bool|array
    {
        try {
            $pay_data = [
                'mchid' => $this->mch_id,
                'out_trade_no' => $order_id,
                'appid' => $this->appid,
                'description' => $description,
                'notify_url' => $this->weixin_callback_url,
                'amount' => [
                    'total' => $price,
                    'currency' => $currency
                ],
                'payer' => [
                    'openid' => $openid
                ]
            ];
            $sign = $this->get_sign($this->jsapi, json_encode($pay_data));
            $header = $this->get_pay_header($sign);
            $result = get_http_data($this->jsapi, $header, json_encode($pay_data));
            trace('js下单=》' . $result, '信息');
            $result_arr = json_decode($result, true);
            if (!$result_arr || !isset($result_arr['prepay_id'])) return false;
            $prepay_id = 'prepay_id=' . $result_arr['prepay_id'];
            return $this->get_weixin_sign_js($prepay_id);

        } catch (Throwable $e) {
            trace('js下单异常=》' . $e->getMessage(), '错误');
            return false;
        }
    }

    /**
     * 微信支付回调解码
     * lwj 2023.8.15 新增
     * @param string $associated_data
     * @param string $nonce
     * @param string $ciphertext
     * @return array|false
     */
    public function callback_decrypt(string $associated_data, string $nonce, string $ciphertext): array|false
    {
        try {
            $AesUtil = new WeiXinAesUtil($this->api_key_v3);
            $res_data = $AesUtil->decryptToString($associated_data, $nonce, $ciphertext);
            trace('微信支付回调解码：' . $res_data, '信息');
            return json_decode($res_data, true);
        } catch (Throwable $e) {
            trace('微信支付回调解码异常=》' . $e->getMessage(), '错误');
            return false;
        }
    }

    /**
     * 订单退款
     * lwj 2024.2.18 新增
     * @param string $order_id
     * @param string $refund_id
     * @param int $refund_price
     * @param int $order_price
     * @param string $reason
     * @param string $currency
     * @return string|bool|array
     */
    public function order_refund(string $order_id,string $refund_id, int $refund_price, int $order_price, string $reason = '用户主动申请退款', string $currency = 'CNY'): string|bool|array
    {
        try {
            $url='https://api.mch.weixin.qq.com/v3/refund/domestic/refunds';
            $refund_data = [
                'out_trade_no' => $order_id,
                'out_refund_no' => $refund_id,
                'reason' => $reason,
                'notify_url' => config('wechat.weixin_refund_callback_url'),
                'amount' => [
                    'refund' => $refund_price,
                    'total' => $order_price,
                    'currency' => $currency
                ]
            ];
            $sign = $this->get_sign($url, json_encode($refund_data));
            $header = $this->get_pay_header($sign);
            $result = get_http_data($url, $header, json_encode($refund_data));
            trace('订单退款=》' . $result, '信息');
            return json_decode($result, true);
        } catch (Throwable $e) {
            trace('订单退款:异常=》' . $e->getMessage(), '错误');
            return false;
        }
    }


}
