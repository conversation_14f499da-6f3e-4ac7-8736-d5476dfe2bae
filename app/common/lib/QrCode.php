<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib;

use app\common\lib\exception\RuntimeException;
use app\common\log\LogCollector;
use Endroid\QrCode\Builder\Builder;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevelHigh;
use Endroid\QrCode\RoundBlockSizeMode\RoundBlockSizeModeMargin;
use Endroid\QrCode\Writer\PngWriter;

//use Picqer\Barcode\BarcodeGeneratorPNG;

class QrCode
{

    // 'develop' => '开发版', 'trial' => '体验版', 'release' => '正式版'
    public const VersionDevelop = 'develop'; // 开发版
    public const VersionTrial = 'trial'; // 体验版
    public const VersionRelease = 'release'; // 正式版

    public const VersionsName = [
        self::VersionDevelop => '开发版',
        self::VersionTrial => '体验版',
        self::VersionRelease => '正式版'
    ];

    /**
     * 生成枪二维码并保存，返回路径
     * lwj 2023.8.8 新增
     * @param int|string $shots_id
     * @return string
     */
    public static function shots_qrcode(int|string $shots_id): string
    {
        $qrCode = Builder::create()
            ->writer(new PngWriter())
            ->writerOptions([])
            ->data($shots_id) // 内容
            ->encoding(new Encoding('UTF-8'))
            ->errorCorrectionLevel(new ErrorCorrectionLevelHigh())
            ->size(300) // 尺寸
            ->margin(10) // 边距
            ->roundBlockSizeMode(new RoundBlockSizeModeMargin())
            ->validateResult(false)
            ->build();

        $path = public_path() . '/static/shots/qrcode/';
        if (!is_dir($path)) mkdir($path, 0777, true);
        $file = $path . $shots_id . '.png';
        $url = 'static/shots/qrcode/' . $shots_id . '.png';

        $qrCode->saveToFile($file);
        return $url;
    }

    /**
     * 生成充电枪微信二维码并保存
     * lwj 2023.9.27 新增
     * @param string $shots_id
     * @param array $version
     * @return string
     */
    public static function create_wechat_qrcode(string $shots_id, array $version = [
        self::VersionRelease,
        self::VersionTrial
    ]): string
    {
        $url = '';
        foreach ($version as $v) {
            $qr_data = [
                'scene' => $shots_id,
                'page' => 'pages/index/index',
                'check_path' => false,
                'env_version' => $v,
                'width' => 560,
            ];
            $WeChat = new WeChat;
            $qr_bin = $WeChat->get_qr_code($qr_data);
            if (empty($qr_bin)) {
                throw new RuntimeException('生成二维码失败', [], RuntimeException::CodeServiceException);
            }
            LogCollector::collectorRunLog('$qr_bin = ');
            LogCollector::collectorRunLog(print_r($qr_bin, true));

            // 创建图像资源
            $image = imagecreatefromstring($qr_bin);

            // 获取图像的宽度和高度
            $imageWidth = imagesx($image);
            $imageHeight = imagesy($image);

            $add_width = 40;

            // 创建新的图像资源，增加30像素高度
            $newImage = imagecreatetruecolor($imageWidth, $imageHeight + $add_width);

            // 设置白色背景
            $white = imagecolorallocate($newImage, 255, 255, 255);
            imagefill($newImage, 0, 0, $white);

            // 将原图像复制到新图像中
            imagecopy($newImage, $image, 0, 0, 0, 0, $imageWidth, $imageHeight);

            // 文字内容
            $text = $shots_id . ' ' . self::VersionsName[$v];

//            $text = mb_convert_encoding($utf8_chinese, 'UCS-2BE', 'UTF-8');

            // 文字颜色（黑色）
            $color = imagecolorallocate($newImage, 0, 0, 0);

            // 文字大小
            $fontSize = 20;
            $font = public_path() . '/fonts/msyh.ttc';

//            // 获取文字的宽度和高度
//            $textWidth = imagefontwidth($fontSize) * strlen($text);
//            $textHeight = imagefontheight($fontSize);

            // 获取文字的宽度和高度
            $textWidth = imagettfbbox($fontSize, 0, $font, $text)[2] - imagettfbbox($fontSize, 0, $font, $text)[0];
            $textHeight = imagettfbbox($fontSize, 0, $font, $text)[3] - imagettfbbox($fontSize, 0, $font, $text)[5];

            // 文字位置
            $x = ($imageWidth - $textWidth) / 2; // x坐标，水平居中
            $y = $imageHeight + $add_width - ($textHeight / 2); // y坐标，居中在新图像的底部空间

            // 添加文字到新图像
//            imagestring($newImage, $fontSize, $x, (int)$y, $text, $color);

            // 添加文字到新图像
            imagettftext($newImage, $fontSize, 0, (int)$x, (int)$y, $color, $font, $text);

            $dir = 'static/shots/qrcode2/';
            $path = public_path() . '/' . $dir;
            if (!is_dir($path)) mkdir($path, 0777, true);
            if ($v == self::VersionRelease) {
                $name = $shots_id . '.png';
                $url = $dir . $name;
            } else {
                $name = $shots_id . '-' . $v . '.png';
            }
            $file = $path . $name;

            // 保存图像
            imagejpeg($newImage, $file);

            // 释放内存
            imagedestroy($image);
            imagedestroy($newImage);
        }
        return $url;
    }

//    /**
//     * 生成条形码
//     * lwj 2023.8.8 新增
//     * @param int|string $shots_id
//     * @return string
//     */
//    public function shots_bar_code(int|string $shots_id): string
//    {
//        $generator = new BarcodeGeneratorPNG();
//        $barcode = $generator->getBarcode($shots_id, $generator::TYPE_CODE_128);
//        return 'data:image/png;base64,' . base64_encode($barcode);
//    }

}
