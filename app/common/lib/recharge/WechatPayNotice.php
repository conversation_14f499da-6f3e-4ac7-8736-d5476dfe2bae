<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\recharge;

use app\common\lib\exception\RuntimeException;
use app\common\lib\ExceptionLogCollector;
use app\common\lib\recharge\request\WechatPayNoticeRequest;
use app\common\lib\SendApplet;
use app\common\model\PayCallback as PayCallbackModel;
use app\common\model\PayOrder;
use app\common\model\UserBalanceLog as UserBalanceLogModel;
use app\common\model\Users;
use app\event\UserBalanceChangeEvent;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;

class WechatPayNotice
{
    protected WechatPayNoticeRequest $request;
    protected ?array $payOrderData = null;

    protected bool $isSuccess = false;

    public function __construct(WechatPayNoticeRequest $request)
    {
        $this->request = $request;
    }

    protected function payFailed(): void
    {
        $msg = WechatPayNoticeRequest::TradeStateDescription[$this->request->trade_state] ?? '未知错误';
        $payOrderModel = app(PayOrder::class);
        $payOrderModel->updateState(
            $this->request->out_trade_no,
            PayOrder::StateFailed,
            $this->request->transaction_id,
            $msg
        );

        trace('微信支付回调接口=》支付失败返回：' . json_encode_cn($this->request->toArray()), '信息');
        SendApplet::send_uid($this->payOrderData['user_id'], [
            'type' => 'pay_notice',
            'result' => 'fail',
            'msg' => $msg
        ]);
    }

    /**
     * 加载支付订单数据
     *
     * @return void
     * @throws \Throwable
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function loadPayOrderData(): void
    {
        $payOrderModel = app(PayOrder::class);

        $orderData = $payOrderModel->where(
            "id", $this->request->out_trade_no)->find();


        if (empty($orderData)) {
            throw new RuntimeException('订单不存在', [], RuntimeException::CodeBusinessException);
        }

        $this->payOrderData = $orderData->toArray();
    }

    protected function payAmountInconsistent(): void
    {
        $payOrderModel = app(PayOrder::class);
        $payOrderModel->updateState(
            $this->request->out_trade_no,
            PayOrder::StateFailed,
            $this->request->transaction_id,
            '金额校验错误'
        );

        trace('微信支付回调接口=》金额校验错误返回：' . json_encode_cn($this->request->toArray()), '错误');
        SendApplet::send_uid($this->payOrderData['user_id'], [
            'type' => 'pay_notice',
            'result' => 'fail',
            'msg' => '支付失败，金额校验错误'
        ]);
    }

    protected function paySuccess(): void
    {
        $this->isSuccess = true;
        $payOrderModel = app(PayOrder::class);
        $payOrderModel->updateState(
            $this->request->out_trade_no,
            PayOrder::StatePayComplete,
            $this->request->transaction_id,
            '支付成功',
            PayOrder::AddBalanceSuccess
        );

        trace('微信支付回调接口=》支付成功：', '信息');

        $addResult = app(UserBalanceLogModel::class)->add(
            $this->payOrderData['user_id'],
            $this->payOrderData['price'],
            '支付成功，加用户余额：' . $this->payOrderData['price'],
            '充值'
        );
        if (empty($addResult)) {
            throw new RuntimeException('添加余额日志失败', [], RuntimeException::CodeDatabaseException);
        }

        $increaseBalanceResult = app(Users::class)->increaseBalance($this->payOrderData['user_id'], $this->payOrderData['price']);
        if ($increaseBalanceResult === 0) {
            throw new RuntimeException('增加余额失败', [], RuntimeException::CodeDatabaseException);
        }

        SendApplet::send_uid($this->payOrderData['user_id'], [
            'type' => 'pay_notice',
            'result' => 'success',
            'msg' => '支付成功，加余额成功'
        ]);

        trace('微信支付回调接口=》支付成功：加余额结果==》》成功', '信息');

    }

    protected function triggerEvent(): void
    {
        try {
            event('UserBalanceChange', new UserBalanceChangeEvent([
                'type' => UserBalanceChangeEvent::TypeBalanceRecharge,
                'user_id' => $this->payOrderData['user_id'],
                'trigger_time' => time(),
            ]));
        } catch (\Throwable $e) {
            // 在事件中运行的业务都是次要业务，若是出现异常情况，
            // 只记录日志，不做其他处理，避免影响充电主流程。
            ExceptionLogCollector::collect($e);
        }
    }


    protected function insertPayCallbackRecord(): void
    {
        $pay_callback = app(PayCallbackModel::class);
        $res = $pay_callback->json(['log'])->insert([
            "order_id" => $this->request->out_trade_no,
            "trade_no" => $this->request->transaction_id,
            "type" => "微信支付-支付回调-已校验",
            "log" => $this->request->toArray(),
            "create_time" => date("Y-m-d H:i:s")
        ]);
        if (empty($res)) {
            throw new RuntimeException('写入支付回调记录失败', [], RuntimeException::CodeDatabaseException);
        }

        trace('微信支付回调接口，写入回调结果：' . json_encode_cn($res), '信息');
    }

    /**
     * 微信支付通知处理逻辑
     *
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws \Throwable
     */
    public function handler(): array
    {
        // 开启事务
        Db::startTrans();
        try {
            // 写入支付回调记录
            $this->insertPayCallbackRecord();

            // 加载支付订单
            $this->loadPayOrderData();

            // 验证支付订单状态
            if ($this->payOrderData['state'] === PayOrder::StatePayComplete) {
                return [];
            }

            // 验证支付订单金额
            if ($this->request->amount->total !== $this->payOrderData['price']) {
                $this->payAmountInconsistent();
                return [];
            }

            if ($this->request->trade_state === WechatPayNoticeRequest::TradeStateSuccess) {
                // 支付成功处理
                $this->paySuccess();
            } else {
                // 支付失败处理
                $this->payFailed();
            }

            // 提交事务
            Db::commit();
        } catch (\Throwable $e) {
            // 回滚事务
            Db::rollback();
            ExceptionLogCollector::collect($e);
            throw $e;
        }

        // 因为上面开启了事务，要是直接放里面的话，可能会在事务提交之前，队列消费者就将事件产生的队列数据消费了。
        // 所以将事件触发的事件，改到这里是比较保险的。
        if ($this->isSuccess === true) {
            $this->triggerEvent();
        }

        return [];
    }
}