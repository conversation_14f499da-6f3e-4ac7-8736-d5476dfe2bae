<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\recharge\request;

use ReflectionProperty;
use ReflectionException;

class BaseRequest
{
    /**
     * 构造函数
     *
     * @throws ReflectionException
     */
    public function __construct(array $data)
    {
        foreach ($data as $key => $value) {
            if (property_exists($this, $key)) {
                $reflectionProperty = new ReflectionProperty(get_class($this), $key);
                $reflectionType = $reflectionProperty->getType();
                $propertyTypeName = $reflectionType->getName();
                if (in_array($propertyTypeName, ['string', 'int', 'float', 'bool', 'array', 'null', 'mixed'])) {
                    $this->{$key} = $value;
                } else {
                    $this->{$key} = new $propertyTypeName($value);
                }
            }
        }
    }

    public function toArray(): array
    {
        return (array)$this;
    }
}