<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\recharge\request;

/**
 * 微信支付通知解密后的请求对象
 *
 * @document https://pay.weixin.qq.com/docs/merchant/apis/jsapi-payment/payment-notice.html
 */
class WechatPayNoticeRequest extends BaseRequest
{
    public const TradeStateSuccess = 'SUCCESS'; // 支付成功
    public const TradeStateRefund = 'REFUND'; // 转入退款
    public const TradeStateNotpay = 'NOTPAY'; // 未支付
    public const TradeStateClosed = 'CLOSED'; // 已关闭
    public const TradeStateRevoked = 'REVOKED'; // 已撤销(付款码支付)
    public const TradeStateUserPaying = 'USERPAYING'; // 用户支付中(付款码支付)
    public const TradeStatePayError = 'PAYERROR'; // 支付失败(其他原因，如银行返回失败)

    public const TradeStateDescription = [
        self::TradeStateSuccess => '支付成功'
        , self::TradeStateRefund => '转入退款'
        , self::TradeStateNotpay => '未支付'
        , self::TradeStateClosed => '已关闭'
        , self::TradeStateRevoked => '已撤销(付款码支付)'
        , self::TradeStateUserPaying => '用户支付中(付款码支付)'
        , self::TradeStatePayError => '支付失败(其他原因，如银行返回失败)'
    ];

    public string $mchid;
    public string $appid;
    public string $out_trade_no;
    public string $transaction_id;
    public string $trade_type;
    public string $trade_state;
    public string $trade_state_desc;
    public string $bank_type;
    public string $attach;
    public string $success_time;
    public Payer $payer;
    public Amount $amount;
}