<?php
/** @noinspection PhpUnused */

namespace app\common\lib;

use hg\apidoc\annotation\Title;

class Annotate
{
    public static function getAnnotateTitle($class, $method): string
    {
        try {
            $refClass = new \ReflectionClass($class);
            $refMethod = $refClass->getMethod($method);
            $attributes = $refMethod->getAttributes();

            foreach ($attributes as $attribute) {
                if ($attribute->getName() === Title::class) {
                    return $attribute->getArguments()[0];
                }
            }
        } catch (\ReflectionException $e) {
            ExceptionLogCollector::collect($e);
        }
        return '';
    }
}