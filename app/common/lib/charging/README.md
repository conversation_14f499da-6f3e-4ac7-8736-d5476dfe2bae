## charging

这个目录存放充电流程的处理逻辑。

- 请求对象：
  - request/StartChargeRequest.php：开始充电的请求对象
  - request/TransactionRecordRequest.php：充电交易记录的请求对象
  - request/TRATimeoutSettlementRequest.php：交易记录应答超时结算的请求对象
  - request/UploadRealtimeMonitoringDataRequest.php：上报实时监控数据的请求对象
- 充电桩SDK：
  - sdk/StopChargeCommand.php：停止充电指令
  - sdk/StartChargeCommand.php：开始充电指令
- 小程序接口：
  - applet/StartCharge.php：开始充电接口
  - applet/StopCharge.php：停止充电接口
- 充电桩回调接口： 
  - UploadRealtimeMonitoringData.php：上传实时监控书回调接口
  - TRATimeoutSettlement.php：交易记录超时应答处理逻辑(强制结算)
  - Settlement.php：交易记录结算处理逻辑(包含补充扣款逻辑)
  - SettlementResult.php：结算结果对象
  - AlgorithmSettlement.php：结算算法
- 异步处理指令：
  - command/StartChargeCommand.php：开始充电指令(实际发送开始充电执行给到充电桩是在这里)

