<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging;

use app\common\lib\charging\request\ReportCurrentBatteryLevelRequest;
use app\common\log\LogCollector;
use app\common\model\CentralizedController;
use app\common\model\ElectricityMeterPowerRecord;

class ReportCurrentBatteryLevel
{
    protected ReportCurrentBatteryLevelRequest $request;

    public function __construct(ReportCurrentBatteryLevelRequest $request)
    {
        $this->request = $request;
    }

    public function run(): void
    {
        $station_id = (new CentralizedController())->getStationId($this->request->centralized_controller_id);

        $result = (new ElectricityMeterPowerRecord())->addRecord(
            $station_id, $this->request->centralized_controller_id,
            $this->request->a_active_power, $this->request->b_active_power, $this->request->c_active_power,
            $this->request->total_active_power, $this->request->record_time
        );

        LogCollector::collectorRunLog(sprintf('ElectricityMeterPowerRecord::addRecord => %s', $result === true ? '成功' : '失败'));
    }
}