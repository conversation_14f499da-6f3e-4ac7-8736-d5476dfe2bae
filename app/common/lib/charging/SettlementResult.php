<?php
declare (strict_types=1);

namespace app\common\lib\charging;

class SettlementResult
{
    // 类型
    public const TypeNormalSettlement = 1; // 正常结算
    public const TypeAbnormalSettlement = 2; // 异常结算

    public int $type;

    public int $sharp_charge_cost;
    public int $peak_charge_cost;
    public int $flat_charge_cost;
    public int $valley_charge_cost;

    public int $sharp_service_cost;
    public int $peak_service_cost;
    public int $flat_service_cost;
    public int $valley_service_cost;

    // 总电量
    public int $electricity_total;

    public int $total_service_cost;

    public int $total_charge_cost;

    public int $original_total_cost;
    public int $total_cost;

    public function __construct(array $data)
    {
        foreach ($data as $key => $value) {
            if (property_exists($this, $key)) {
                $this->{$key} = $value;
            }
        }
    }


}