<?php
///** @noinspection PhpUnused */
//
//namespace app\common\lib\charging\timer;
//
//use app\common\lib\SendApplet;
//use app\common\model\Order;
//use think\db\exception\DataNotFoundException;
//use think\db\exception\DbException;
//use think\db\exception\ModelNotFoundException;
//use Workerman\Connection\TcpConnection;
//
///**
// * 定时器作用：充电桩开始充电后，会每隔15秒上报一次充电的实时数据。
// * 若连续超过40秒没有上报，触发当前定时器。
// */
//class UploadRealtimeMonitoringDataAnswerTimeout
//{
//
//    public function sendCreateTimer(int $order_id): void
//    {
//        $timer_key2 = '上传实时监测数据' . $order_id;
//        $res4 = send_http_post_timer([
//            'type' => '上传实时监测数据',
//            'order_id' => $order_id,
//        ]);
//        trace('send_http_post_timer：上传实时监测数据=》' . $timer_key2 . '==>' . json_encode_cn($res4), '下发指令');
//    }
//
//    public function createTimer(array $data): array
//    {
//        $order_id = $data['order_id'];
//        $timer_key = '上传实时监测数据' . $order_id;
//        $timer_time = 40;
//        $add_data = [
//            'type' => '上传实时监测数据',
//            'timer_key' => $timer_key,
//            'time' => time(),
//            'order_id' => $order_id,
//            'timer_time' => $timer_time,
//        ];
//
//        return [$timer_key, $add_data, $timer_time];
//    }
//
//    public function deleteTimer(TcpConnection $connection)
//    {
//        trace('删除-上传实时监测数据', '下发指令定时器');
//        if (empty($data['order_id'])) return $connection->send(res_error_text([], '参数错误'));
//        $order_id = $data['order_id'];
//        $timer_key = '上传实时监测数据' . $order_id;
//    }
//
//    /**
//     * 定时器触发处理逻辑
//     *
//     * @param TcpConnection $connection
//     * @param array $data
//     * @return bool|null
//     * @throws DataNotFoundException
//     * @throws DbException
//     * @throws ModelNotFoundException
//     */
//    public function timerTriggerHandler(TcpConnection $connection, array $data): bool|null
//    {
//        trace('上传实时监测数据==>开始：', '超时处理任务');
//        $order_id = $data['order_id'];
//
//        $orderModel = app(Order::class);
//        $order = $orderModel->where('id', $order_id)->find();
//        if (empty($order)) return $connection->send(res_success_text([], '找不到订单'));
//        if ($order['status'] >= Order::StatusComplete) {
//            SendApplet::send_uid($order['user_id'], [
//                'type' => '订单已结束-上传实时监测数据',
//                'result' => 'error',
//                'msg' => '订单已结束',
//                'order_id' => $order_id,
//                'status' => $order['status'],
//            ]);
//            return $connection->send(res_success_text([], '订单已结束'));
//        }
//
//        SendApplet::send_uid($order['user_id'], [
//            'type' => '回传超时-上传实时监测数据',
//            'result' => 'error',
//            'msg' => '回传超时-上传实时监测数据',
//            'order_id' => $order_id,
//        ]);
//        trace('上传实时监测数据==>结束：' . $order_id, '超时处理任务');
//        return $connection->send(res_success_text([], '发送成功'));
//    }
//}