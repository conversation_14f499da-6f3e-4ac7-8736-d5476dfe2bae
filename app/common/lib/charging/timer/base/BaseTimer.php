<?php
///** @noinspection PhpUnused */
//
//namespace app\common\lib\charging\timer\base;
//
//use Workerman\Lib\Timer;
//
//abstract class BaseTimer
//{
//    /**
//     * @var array 定时器容器
//     */
//    protected array $timerContainer;
//
//
//    /**
//     * 添加定时器
//     */
//    protected function addTimer(string $timer_key, array $data, ?int $timeout = null): bool
//    {
//        trace('添加定时器：开始=》' . $timer_key, '下发指令定时器');
//        $this->deleteOldTimer($timer_key);
//
//        if (!$timeout) $timeout = $this->getDefaultTimeout();
//
//        trace('定时器时间=》' . $timeout, '下发指令定时器');
//
//
//        $timerId = Timer::add($timeout, function () use ($data, $timer_key) {
//            trace('执行定时器:数据=》' . json_encode_cn($data), '下发指令定时器');
//            $this->SendTimeoutHandleJob($data);
//            $this->redis->hdel('下发指令处理定时器-列表', $timer_key);
//            unset($this->timer[$timer_key]);
//            trace('定时器执行完', '下发指令定时器');
//        }, '', false);
//
//
//
//        $data['timeout'] = $timeout;
//        $this->redis->hset('下发指令处理定时器-列表', $timer_key, json_encode_cn($data));
//
//
//    }
//
//    protected function getDefaultTimeout(): int
//    {
//        return (int)config('my.send_handle_timer_timeout');
//    }
//
//    /**
//     * 删除旧定时器
//     *
//     * @param string $timerKey 定时器键
//     * @return void
//     */
//    protected function deleteOldTimer(string $timerKey): void
//    {
//        if (isset(self::$timerContainer[$timerKey])) {
//            Timer::del(self::$timerContainer[$timerKey]);
//            unset(self::$timerContainer[$timerKey]);
//            trace('存在定时器，先删除=》' . $timerKey, '下发指令定时器');
//        }
//    }
//
//
//    /**
//     * 添加下发指令处理定时器
//     * lwj 2023.9.12 新增
//     * lwj 2023.9.27 修改
//     * @param string|int $timer_key
//     * @param array $data
//     * @param int|null $timeout
//     * @return void
//     */
//    private function AddTimer(string|int $timer_key, array $data, int|null $timeout = null): void
//    {
//        trace('添加定时器：开始=》' . $timer_key, '下发指令定时器');
//        if (isset($this->timer[$timer_key])) {
//            Timer::del($this->timer[$timer_key]);
//            unset($this->timer[$timer_key]);
//            trace('存在定时器，先删除=》' . $timer_key, '下发指令定时器');
//        }
//
//        if (!$timeout) $timeout = config('my.send_handle_timer_timeout');
//        trace('定时器时间=》' . $timeout, '下发指令定时器');
//        $this->timer[$timer_key] = Timer::add($timeout, function () use ($data, $timer_key) {
//            //Todo 待写 超时要做什么
//            trace('执行定时器:数据=》' . json_encode_cn($data), '下发指令定时器');
//            $this->SendTimeoutHandleJob($data);
//            $this->redis->hdel('下发指令处理定时器-列表', $timer_key);
//            unset($this->timer[$timer_key]);
//            trace('定时器执行完', '下发指令定时器');
//        }, '', false);
//
//        $data['timeout'] = $timeout;
//        $this->redis->hset('下发指令处理定时器-列表', $timer_key, json_encode_cn($data));
//        trace('添加定时器：结束=》' . $timer_key, '下发指令定时器');
//    }
//}