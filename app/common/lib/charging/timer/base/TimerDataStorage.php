<?php

namespace app\common\lib\charging\timer\base;

use Predis\Client as Predis;

trait TimerDataStorage
{
    protected Predis $redis;

    protected string $timerListKey = '下发指令处理定时器-列表';

    protected function initStorage(): void
    {
        $this->redis = new Predis(config('my.redis2'));
    }

    protected function storageTimerData(string $timer_key): bool
    {
        return $this->redis->hdel($this->timerList<PERSON>ey, [$timer_key]) > 0;
    }

    protected function deleteStorageTimerData(string $timer_key): bool
    {
        return $this->redis->hdel($this->timerList<PERSON>ey, [$timer_key]) > 0;
    }

}