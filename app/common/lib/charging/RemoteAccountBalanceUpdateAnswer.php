<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging;

use app\common\lib\charging\request\RemoteAccountBalanceUpdateAnswerRequest;
use app\common\lib\send_handle_timer\request\BaseRequest;
use app\common\lib\send_handle_timer\request\RemoveRemoteAccountBalanceUpdateRequest;
use app\common\log\LogCollector;

class RemoteAccountBalanceUpdateAnswer
{
    protected RemoteAccountBalanceUpdateAnswerRequest $request;

    public function __construct(RemoteAccountBalanceUpdateAnswerRequest $request)
    {
        $this->request = $request;
    }

    public function handler(): void
    {
        if ($this->request->edit_result === RemoteAccountBalanceUpdateAnswerRequest::EditResultSuccess) {
            $this->removeTimer();
        } else {
            LogCollector::collectorRunLog('更新充电桩余额失败', LogCollector::LevelError);
            LogCollector::collectorRunLog(print_r($this->request->toArray(), true), LogCollector::LevelError);
        }
    }

    protected function removeTimer(): void
    {
        $request = new RemoveRemoteAccountBalanceUpdateRequest([
            'type' => BaseRequest::TypeRemoveRemoteAccountBalanceUpdate,
            'request_id' => $this->request->request_id,
        ]);
        $res3 = send_http_post_timer($request->toArray());
        trace('send_http_post_timer：开始充电=》' . json_encode_cn($res3), '开始充电任务');
    }
}