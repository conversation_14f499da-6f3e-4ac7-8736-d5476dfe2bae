<?php

namespace app\common\lib\charging\request;

class UploadRealtimeMonitoringDataRequest extends BaseRequest
{
    public string $order_id;
    public string $transaction_serial_number;
    public int $piles_id;
    public int $shots_id;
    public int $status;
    public int $is_rest;
    public int $is_plugged_int;
    public int $output_voltage;
    public int $output_current;
    public int $gun_wire_temperature;
    public string $gun_wire_code;
    public string $soc;
    public int $maximum_battery_temperature;
    public int $cumulative_charging_time;
    public int $remaining_time;
    public int $charging_percentage;
    public int $calculated_loss_charging_percentage;
    public int $amount_charged;
    public string $hardware_failure;
}