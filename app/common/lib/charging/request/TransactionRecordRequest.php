<?php
/** @noinspection PhpUnused */

namespace app\common\lib\charging\request;

class TransactionRecordRequest
{
    public string $transaction_serial_number;
    public string $piles_id;
    public string $shots_id;
    public string $start_time;
    public string $end_time;
    public int $sharp_price;
    public int $sharp_charge;
    public int $loss_sharp_charge;
    public int $sharp_amount;
    public int $peak_price;
    public int $peak_charge;
    public int $loss_peak_charge;
    public int $peak_amount;
    public int $flat_price;
    public int $flat_charge;
    public int $loss_flat_charge;
    public int $flat_amount;
    public int $valley_price;
    public int $valley_charge;
    public int $loss_valley_charge;
    public int $valley_amount;
    public int $electricity_meter_total_start;
    public int $electricity_meter_total_end;
    public int $total_charge;
    public int $loss_total_charge;
    public int $consumption_amount;
    public string $unique_identifier_of_electric_vehicle;
    public int $transaction_identifier;
    public string $transaction_datetime;
    public int $reason_for_stop;
    public string $physical_card_number;
    public int $sequence_number;
    public int $abnormal_alarm_node;


    public function __construct(array $data)
    {
        foreach ($data['data'] as $key => $value) {
            if (property_exists($this, $key)) {
                $this->{$key} = $value;
            }
        }

        // 桩软件有时会因为没有来得及更新数据，桩硬件就发生了重启。这种情况下桩上报的结束时间会是下边这种格式，处理方式是以开始时间作为结束时间即可。
        if ($this->end_time === "2000-00-00 00:00:00") {
            $this->end_time = $this->start_time;
        }
    }
}