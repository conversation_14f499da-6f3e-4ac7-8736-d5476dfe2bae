<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging\sdk;

use app\common\lib\charging\sdk\request\RemoteAccountBalanceUpdateCommandRequest;
use app\common\lib\http\HttpClient;
use app\common\lib\http\Response;
use app\common\lib\send_handle_timer\request\BaseRequest;
use app\common\lib\send_handle_timer\request\RemoteAccountBalanceUpdateRequest;
use app\common\lib\transfer_service\SerialNumber;
use app\common\queue\TransferServiceWriteQueue;
use LieHuoHuYu\ChargeTransferServiceProtocol\Kernel;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\BasePackage;

class RemoteAccountBalanceUpdateCommand
{
    protected RemoteAccountBalanceUpdateCommandRequest $request;

    public function __construct(RemoteAccountBalanceUpdateCommandRequest $request)
    {
        $this->request = $request;
    }

    public function send(): bool
    {

        $sequence = SerialNumber::increaseSequence();

        $this->sendRequestNew($sequence);

        // 添加应答超时定时器
        $this->addTimer($sequence);

        return true;
    }

    protected function sendRequestNew(int $sequence): void
    {
        $package = Kernel::create($sequence, BasePackage::TypeUpdateBalance, [
            'piles_id' => (int)$this->request->piles_id,
            'shots_number' => (int)$this->request->shots_id,
            'transaction_serial_number' => $this->request->transaction_serial_number,
            'physical_card_number' => $this->request->physical_card_number,
            'balance' => $this->request->account_balance
        ]);

        (new TransferServiceWriteQueue())->push($package->encode());
    }

    protected function sendRequest(): Response
    {
        // 向 充电桩中间服务 发送请求：远程账户余额更新 0x42
        $token = config('my.charge_service_webman_token');
        $urlPrefix = config('my.charge_service_webman_api');
        $httpClient = new HttpClient();
        $body = json_encode_cn((array)$this->request);
        return $httpClient->setMethod('POST')
            ->setUrl($urlPrefix . 'remote_account_balance_update')
            ->setHeader([
                'Content-Type' => 'application/json; charset=utf-8',
                'Authorization' => $token
            ])
            ->setBody($body)
            ->sendRequest();
    }

    protected function addTimer(int $request_id): void
    {
        // 向 下发指令处理定时器进程 发送请求：远程账户余额更新
        $request = new RemoteAccountBalanceUpdateRequest([
            'type' => BaseRequest::TypeRemoteAccountBalanceUpdate,
            'request_id' => $request_id,
            'user_id' => $this->request->user_id,
            'trigger_time' => $this->request->trigger_time,
            'retried_count' => $this->request->retried_count,
            'retry_count' => RemoteAccountBalanceUpdateRequest::RetryCount
        ]);
        $res3 = send_http_post_timer($request->toArray());
        trace('send_http_post_timer：开始充电=》' . json_encode_cn($res3), '开始充电任务');
    }
}