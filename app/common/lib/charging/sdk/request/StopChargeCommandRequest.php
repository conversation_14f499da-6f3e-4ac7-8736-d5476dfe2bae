<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging\sdk\request;

use app\common\cache\base\ChargingContextData;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\EndOrder;

class StopChargeCommandRequest extends BaseRequest
{
    public int $piles_id;
    public int $sequence;
    public int $shot_id;
    public int $operator = EndOrder::OperatorUser;

    public ChargingContextData $context;
}