<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging\sdk\request;

use app\common\cache\base\ChargingContextData;

class StartChargeCommandRequest extends BaseRequest
{
    public string $order_id;
    public int $type;
    public int $piles_id;
    public int $sequence;
    public int $user_id;
    public int $balance;
    public ?string $reservation_time;
    public ChargingContextData $context;

}