<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging\sdk;

use app\common\lib\charging\sdk\request\StartChargeCommandRequest;
use app\common\lib\http\HttpClient;
use app\common\lib\http\Response;
use app\common\lib\transfer_service\SerialNumber;
use app\common\queue\TransferServiceWriteQueue;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\BasePackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\Kernel;
use app\common\context\Order as OrderContext;


class StartChargeCommand
{
    protected StartChargeCommandRequest $request;

    public function __construct(StartChargeCommandRequest $request)
    {
        $this->request = $request;
    }

    public function send(): bool
    {
        // 暂时保存10天
        app(OrderContext::class)->saveOrderIdToContextMap(
            $this->request->order_id,
            (array)$this->request->context
        );
//        $result = cache('充电桩服务订单_' . $this->request->order_id, (array)$this->request->context, 864000);
//        trace('cache - result - ' . print_r($result, true), 'debug');

        $sequence = SerialNumber::increaseSequence();

        $this->sendRequestNew($sequence);

        // 添加应答超时定时器
        $this->addTimer();
        // 暂时保存10天
        app(OrderContext::class)->saveSequenceNumberToContextMap($sequence, (array)$this->request->context);
//        $result = cache('充电桩服务_' . $sequence, (array)$this->request->context, 864000);
//        trace('cache - result2 - ' . print_r($result, true), 'debug');

        return true;
    }

    protected function sendRequestNew(int $sequence): void
    {
        if (!is_null($this->request->reservation_time)) {
            $reservationTime = strtotime($this->request->reservation_time);
        } else {
            $reservationTime = 0;
        }

        $package = Kernel::create(
            $sequence,
            BasePackage::TypeStartOrder,
            [
                'type' => $this->request->type
                , 'piles_id' => $this->request->piles_id
                , 'shots_number' => $this->request->sequence
                , 'transaction_serial_number' => $this->request->order_id
                , 'logical_card_number' => hex_auto_fill_0($this->request->user_id, 16)
                , 'physical_card_number' => hex_auto_fill_0($this->request->user_id, 16)
                , 'account_balance' => $this->request->balance
                , 'account_vip' => 1
                , 'timed_time' => $reservationTime
            ]
        );

        (new TransferServiceWriteQueue())->push($package->encode());
    }

    protected function sendRequest(): Response
    {
        // 向 充电桩中间服务 发送请求：运营平台远程控制启机 0x34
        $data = [
            'transaction_serial_number' => $this->request->order_id,
            'piles_id' => hex_auto_fill_0($this->request->piles_id, 14),
            'shots_id' => hex_auto_fill_0($this->request->sequence, 2),
            'logical_card_number' => hex_auto_fill_0($this->request->user_id, 16),
            'physical_card_number' => hex_auto_fill_0($this->request->user_id, 16),
            'account_balance' => $this->request->balance,
        ];

        $token = config('my.charge_service_webman_token');
        $urlPrefix = config('my.charge_service_webman_api');
        $httpClient = new HttpClient();
        $body = json_encode_cn($data);
        return $httpClient->setMethod('POST')
            ->setUrl($urlPrefix . 'remote_control_startup_of_operating_platform')
            ->setHeader([
                'Content-Type' => 'application/json; charset=utf-8',
                'Authorization' => $token
            ])
            ->setBody($body)
            ->sendRequest();
    }

    protected function addTimer(): void
    {
        // 向 下发指令处理定时器进程 发送请求：运营平台远程控制启机
        $res3 = send_http_post_timer([
            'type' => '运营平台远程控制启机',
            'order_id' => $this->request->order_id,
        ]);
        trace('send_http_post_timer：开始充电=》' . json_encode_cn($res3), '开始充电任务');
    }
}