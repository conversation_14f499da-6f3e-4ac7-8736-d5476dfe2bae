<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging\sdk;

use app\common\lib\charging\sdk\request\StopChargeCommandRequest;
use app\common\lib\http\HttpClient;
use app\common\lib\http\Response;
use app\common\lib\transfer_service\SerialNumber;
use app\common\queue\TransferServiceWriteQueue;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\BasePackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\Kernel;
use RedisException;
use app\common\context\Order as OrderContext;

class StopChargeCommand
{
    protected StopChargeCommandRequest $request;
    protected ?BasePackage $package = null;

    public function __construct(StopChargeCommandRequest $request)
    {
        $this->request = $request;
    }

    /**
     * @return bool
     * @throws RedisException
     */
    public function send(): bool
    {
        $sequence = SerialNumber::increaseSequence();

        $this->sendRequestNew($sequence);

        $this->createTimer($this->request->context->order_id, $this->request->shot_id);
//        cache('充电桩服务_' . $sequence, $this->request->context->toArray(), 864000); //暂时保存10天
        app(OrderContext::class)->saveSequenceNumberToContextMap($sequence, $this->request->context->toArray());
        return true;
    }

    protected function createTimer(string $order_id, int $shot_id): void
    {
        $result = send_http_post_timer([
            'type' => '运营平台远程停机',
            'order_id' => $order_id,
            'shot_id' => $shot_id,
        ]);

        trace('send_http_post_timer：停止充电=》' . json_encode_cn($result), '下发指令');
    }

    protected function sendRequestNew(int $sequence): void
    {
        $package = Kernel::create($sequence, BasePackage::TypeEndOrder, [
            'piles_id' => $this->request->piles_id,
            'shots_number' => $this->request->sequence,
            'transaction_serial_number' => $this->request->context->order_id,
            'operator' => $this->request->operator
        ]);
        (new TransferServiceWriteQueue())->push($package->encode());
    }


    protected function sendRequest(): Response
    {
        $token = config('my.charge_service_webman_token');
        $urlPrefix = config('my.charge_service_webman_api');
        $httpClient = new HttpClient();
        $body = json_encode_cn([
            'piles_id' => $this->request->piles_id,
            'shots_id' => $this->request->sequence,
        ]);
        return $httpClient->setMethod('POST')
            ->setUrl($urlPrefix . 'remote_shutdown_of_operating_platform')
            ->setHeader([
                'Content-Type' => 'application/json; charset=utf-8',
                'Authorization' => $token
            ])
            ->setBody($body)
            ->sendRequest();
    }
}