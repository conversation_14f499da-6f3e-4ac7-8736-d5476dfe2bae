<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging;

use app\common\lib\charging\request\StopChargeAnswerRequest;
use app\common\lib\SendAdmin;
use app\common\lib\SendApplet;
use app\common\log\SocketLogCollector;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\EndOrderAnswer;
use app\common\context\Order as ContextOrder;

class StopChargeAnswer extends Base
{
    protected StopChargeAnswerRequest $request;
    protected SocketLogCollector $socketLogCollector;

    public const failure_reason_text = [
        0 => '无',
        1 => '设备编号不匹配',
        2 => '枪未处于充电状态',
        3 => '其他',
        4 => '设备离线'
    ];

    public function __construct(StopChargeAnswerRequest $request, SocketLogCollector $socketLogCollector)
    {
        $this->request = $request;
        $this->socketLogCollector = $socketLogCollector;
    }

    public function handler(): void
    {
//        $user_data = cache('充电桩服务_' . $this->request->request_id);
        $user_data = app(ContextOrder::class)->getSequenceNumberToContextMap((int)$this->request->request_id);

        // 删除停止充电定时器
        $this->removeStopChangingTimer((int)$user_data['order_id']);

        if ($this->request->stop_result === EndOrderAnswer::ResultFailed) {
            // 删除交易记录定时器
            $this->removeTransactionRecordsTimer($user_data);
        }

        if ($this->request->operator === EndOrderAnswer::OperatorUser) {
            // 发送停止充电指令给小程序
            $this->sendShutdownCommandResponse($user_data);
        }
    }

    protected function removeTransactionRecordsTimer(array $user_data): void
    {
        $res3 = send_http_post_timer([
            'type' => '删除-运营平台远程停机-账单',
            'order_id' => $user_data['order_id']
        ]);
        $this->socketLogCollector->collectorRunLog('send_http_post_timer：交易记录确认=》删除-运营平台远程停机-账单==>' . json_encode_cn($res3));

        $res3 = send_http_post_timer([
            'type' => '删除-自动结算',
            'order_id' => $user_data['order_id']
        ]);
        $this->socketLogCollector->collectorRunLog('send_http_post_timer：交易记录确认=》删除-自动结算==>' . json_encode_cn($res3));
    }

    protected function sendShutdownCommandResponse(array $user_data): void
    {
        $data = $this->request->toArray();
        $data['failure_reason_text'] = self::failure_reason_text[$data['failure_reason']] ?? '未知';
        $message = [
            'type' => 'remote_shutdown_command_response',
            'msg' => '远程停机命令回复',
            'data' => $data
        ];
        switch ($user_data['type']) {
            case '后台':
                SendAdmin::send_group($user_data['token'], $message);
                break;
            case '小程序':
                SendApplet::send_uid($user_data['user_id'], $message);
                break;
        }
    }

    protected function removeStopChangingTimer(int $order_id): void
    {
        $res3 = send_http_post_timer([
            'type' => '删除-运营平台远程停机-指令',
            'order_id' => $order_id
        ]);
        $this->socketLogCollector->collectorRunLog('send_http_post_timer：远程停机命令回复=》' . json_encode_cn($res3));
    }
}
