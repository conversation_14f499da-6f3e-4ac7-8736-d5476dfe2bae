<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging\applet;

use app\common\context\Order as OrderContext;
use app\common\lib\charging\applet\request\StartChargeRequest;
use app\common\lib\ExceptionLogCollector;
use app\common\lib\transfer_service\SerialNumber;
use app\common\log\LogCollector;
use app\common\model\Activity;
use app\common\model\Order as OrderModel;
use app\common\model\PilesStatus;
use app\common\model\Shots as ShotsModel;
use app\common\model\ShotsStatus;
use app\common\model\TariffGroup as TariffGroupModel;
use app\common\model\Users as UsersModel;
use app\common\queue\TransferServiceWriteQueue;
use app\ms\Api;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\BasePackage;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\StartOrderAndSetTariff;
use LieHuoHuYu\ChargeTransferServiceProtocol\Kernel;
use RedisException;
use app\common\lib\exception\RuntimeException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use Throwable;

class StartCharge
{
    protected StartChargeRequest $request;

    protected array $shotsData;
    protected array $tariffGroupData;

    protected string $order_id;
    /**
     * @var int 场站折扣
     */
    protected float $station_discount;
    /**
     * @var int 用户余额(单位:分)
     */
    protected int $user_balance;

    /**
     * @var int 订单类型
     */
    protected int $order_type = OrderModel::TypeChargeNow;

    public function __construct(StartChargeRequest $request)
    {
        $this->request = $request;
    }

    /**
     * 开始充电
     *
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException|RedisException
     */
    public function run(): array
    {
        // 验证充电枪是否有正在运行中的订单
        $this->verifyShotsHasRunOrder($this->request->shots_id);
        // 验证用户余额
        $this->verifyUserBalance($this->request->user_id);
        // 加载充电枪数据
        $this->loadShotsData($this->request->shots_id);
        // 验证充电枪在线状态
        $this->verifyShotsOnlineStatus();
        // 加载费率组数据
        $this->loadTariffGroup($this->shotsData['tariff_group_id']);
        // 生成订单号
        $this->generateOrderId($this->request->shots_id);
        // 加载场站折扣
        $this->loadStationDiscount($this->shotsData['station_id'], $this->request->reservation_time);
        // 加载用户折扣
        $this->loadUserDiscount($this->request->user_id,$this->shotsData['station_id']);
        // 创建订单
        $this->createOrder($this->request->user_id);
        // 记录订单状态
        $this->recordOrderStatus();
        // 下发开始充电指令
        $sequence = $this->sendStartChargeCommand();
        // 添加应答超时定时器
        $this->addAnswerTimeoutTimer();
        // 缓存上下文
        $this->cache_context($sequence);

        return [
            'transaction_serial_number' => $this->order_id
        ];
    }

    protected function cache_context(int $sequence): void
    {
        $orderContext = app(OrderContext::class);
        $orderContext->recordOrderStatus($this->order_id, '创建订单');

        $context = [
            'token' => $this->request->user_token
            , 'user_id' => $this->request->user_id
            , 'type' => "小程序"
            , 'order_id' => $this->order_id
            , 'corp_id' => $this->shotsData['corp_id']
            , 'station_id' => $this->shotsData['station_id']
        ];

        $orderContext->saveOrderIdToContextMap($this->order_id, $context);

        $orderContext->saveSequenceNumberToContextMap($sequence, $context);
    }

    protected function getPackage(int $sequence): BasePackage
    {
        return Kernel::create(
            $sequence,
            BasePackage::TypeStartOrderAndSetTariff,
            [
                'type' => StartOrderAndSetTariff::TypeNormal
                , 'piles_id' => $this->shotsData['piles_id']
                , 'shots_number' => $this->shotsData['sequence']
                , 'transaction_serial_number' => $this->order_id
                , 'logical_card_number' => hex_auto_fill_0($this->request->user_id, 16)
                , 'physical_card_number' => hex_auto_fill_0($this->request->user_id, 16)
                , 'account_balance' => $this->user_balance
                , 'account_vip' => 1
                , 'timed_time' => !empty($this->request->reservation_time) ? strtotime($this->request->reservation_time) : 0
                // 计费模型
                , 'billing_mode_id' => $this->tariffGroupData['id']
                , 'sharp_fee' => $this->tariffGroupData['sharp_fee']
                , 'sharp_ser_fee' => (int)(ceil($this->tariffGroupData['sharp_ser_fee'] * ($this->station_discount / 100)))
                , 'peak_fee' => $this->tariffGroupData['peak_fee']
                , 'peak_ser_fee' => (int)(ceil($this->tariffGroupData['peak_ser_fee'] * ($this->station_discount / 100)))
                , 'flat_fee' => $this->tariffGroupData['flat_fee']
                , 'flat_ser_fee' => (int)(ceil($this->tariffGroupData['flat_ser_fee'] * ($this->station_discount / 100)))
                , 'valley_fee' => $this->tariffGroupData['valley_fee']
                , 'valley_ser_fee' => (int)(ceil($this->tariffGroupData['valley_ser_fee'] * ($this->station_discount / 100)))
                , 'loss_rate' => $this->tariffGroupData['loss_rate']
                , 'period_codes' => $this->tariffGroupData['period_codes']
                , 'power_limit' => $this->request->power_limit
            ]
        );
    }

    /**
     * @return int
     * @throws RedisException
     */
    protected function sendStartChargeCommand(): int
    {
        $sequence = SerialNumber::increaseSequence();

        $package = $this->getPackage($sequence);

        app(TransferServiceWriteQueue::class)->push($package->encode());

        return $sequence;
    }

    /**
     * @param int $station_id
     * @param string|null $charging_time
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function loadStationDiscount(int $station_id, ?string $charging_time = null): void
    {
        $this->station_discount = app(Activity::class)->getStationDiscount($station_id, $charging_time);
    }

    protected function loadUserDiscount(int $user_id,int $station_id): void{
        // 从微服务获取用户信息
        $user_info = Api::send('/auth/user/info','GET', ['user_id'=>$user_id])['data'];

        // 在场站折扣基础上 加上用户折扣
        $discount = Api::send("/auth/user/discount",'GET', ['plate'=>$user_info['plate'] ?? '','station_id'=>$station_id])['data'];
        $this->station_discount = $this->station_discount * $discount / 100;
    }

    protected function addAnswerTimeoutTimer(): void
    {
        // 向 下发指令处理定时器进程 发送请求：运营平台远程控制启机
        $res3 = send_http_post_timer([
            'type' => '运营平台远程控制启机',
            'order_id' => $this->order_id,
        ]);
        LogCollector::collectorRunLog(sprintf('send_http_post_timer：开始充电=》 %s', json_encode_cn($res3)));
    }

    protected function recordOrderStatus(): void
    {
//        $context = new OrderContext();
//        $context->recordOrderStatus($this->order_id, '创建订单');
//        $redis5 = new Predis(config('my.redis5'));
//        $redis5->set('运营平台远程控制启机-状态-' . $this->order_id, '创建订单', 'EX', 300);
    }

    /**
     * 创建订单号
     *
     * @param int $user_id
     * @return void
     */
    protected function createOrder(int $user_id): void
    {
        // 生成订单
        $order_data = [
            'id' => $this->order_id,
            'corp_id' => $this->shotsData['corp_id'],
            'station_id' => $this->shotsData['station_id'],
            'piles_id' => $this->shotsData['piles_id'],
            'sequence' => $this->shotsData['sequence'],
            'shot_id' => $this->shotsData['id'],
            'freeze_money' => 0,
            'user_id' => $user_id,
            'ip' => get_ip(),
            'billing_mode_id' => $this->tariffGroupData['id'],
            'sharp_fee' => $this->tariffGroupData['sharp_fee'],
            'peak_fee' => $this->tariffGroupData['peak_fee'],
            'flat_fee' => $this->tariffGroupData['flat_fee'],
            'valley_fee' => $this->tariffGroupData['valley_fee'],
            'sharp_ser_fee' => $this->tariffGroupData['sharp_ser_fee'],
            'peak_ser_fee' => $this->tariffGroupData['peak_ser_fee'],
            'flat_ser_fee' => $this->tariffGroupData['flat_ser_fee'],
            'valley_ser_fee' => $this->tariffGroupData['valley_ser_fee'],
            'loss_rate' => $this->tariffGroupData['loss_rate'],
            'period_codes' => $this->tariffGroupData['period_codes'],
            'surcharge' => $this->tariffGroupData['surcharge'],
            'type' => $this->order_type,
            'reservation_time' => $this->request->reservation_time,
            'discount' => $this->station_discount
        ];
        $orderModel = app(OrderModel::class);
        // 同步订单数据到订单微服务
        Api::send('/order/orders','POST',$order_data,['Authorization' => 'mpQWnQ0zZ7KGl3JEV5DeUoeNXec0mohT'],'',true);
        $res = $orderModel->createOrder($order_data);
        if (!$res) throw new RuntimeException('创建订单失败', [], RuntimeException::CodeServiceException);
    }


    /**
     * 生成订单号
     *
     * @param int $shots_id
     * @return void
     */
    protected function generateOrderId(int $shots_id): void
    {
        $this->order_id = $this->get_order_no($shots_id);
        if (!$this->order_id) throw new RuntimeException('获取订单号失败', [], RuntimeException::CodeServiceException);
    }

    /**
     * 获取订单编号
     * lwj 2023.8.21 新增
     * lwj 2023.9.13 修改
     * @param string|int $shots_id
     * @return string|bool|int
     */
    protected function get_order_no(string|int $shots_id): string|bool|int
    {
        $order_model = new OrderModel();
        $max_attempts = 10;
        $attempt = 0;
        while ($attempt < $max_attempts) {
            $order_no = get_order_id_sn($shots_id);
            if (strlen($order_no) > 32) continue;
            try {
                $exist_order_no = $order_model->field('id')->find($order_no);
            } catch (Throwable $e) {
                ExceptionLogCollector::collect($e);
                return false;
            }
            if (!$exist_order_no) {
                break;
            }
            $attempt++;
        }
        if (empty($order_no)) return false;
        return $order_no;
    }

    /**
     * 加载费率组数据
     *
     * @param int $tariff_group_id
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function loadTariffGroup(int $tariff_group_id): void
    {
        $tariff_group_model = app(TariffGroupModel::class);
        $tariff_group = $tariff_group_model->getTariffGroup($tariff_group_id);
        if (!$tariff_group) throw new RuntimeException('找不到费率模型', [], RuntimeException::CodeServiceException);
        $this->tariffGroupData = $tariff_group;
    }

    /**
     * 检测充电枪是否在线
     *
     * @return void
     */
    protected function verifyShotsOnlineStatus(): void
    {
        if ($this->shotsData['piles_is_online'] === PilesStatus::IsOnlineNot) throw new RuntimeException('充电桩不在线', [], RuntimeException::CodeBusinessException);
        if ($this->shotsData['is_fault'] == ShotsStatus::IsFaultYes) throw new RuntimeException('充电枪故障', [], RuntimeException::CodeBusinessException);
        if ($this->shotsData['work_status'] == ShotsStatus::WorkStatusCharging) throw new RuntimeException('充电枪充电中', [], RuntimeException::CodeBusinessException);
    }

    /**
     * 加载充电枪数据
     *
     * @param int $shots_id
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function loadShotsData(int $shots_id): void
    {
        $shots_model = app(ShotsModel::class);
        $shots = $shots_model->getShotsData($shots_id);
        if (is_null($shots)) throw new RuntimeException('找不到充电枪', [], RuntimeException::CodeBusinessException);
        $this->shotsData = $shots;
    }

    /**
     * 验证用户余额是否足够
     *
     * @param int $user_id
     * @return void
     */
    protected function verifyUserBalance(int $user_id): void
    {
        $users_model = app(UsersModel::class);
        $balance = $users_model->getUserAvailableBalance($user_id);

        if (0 >= $balance) throw new RuntimeException('余额不足', [], RuntimeException::CodeBusinessException);
        $this->user_balance = $balance;
    }


    /**
     * 充电枪是否有进行中的订单
     *
     * @param int $shots_id
     * @return void
     */
    protected function verifyShotsHasRunOrder(int $shots_id): void
    {
        $orderModel = app(OrderModel::class);
        $count = $orderModel->getChargingCount($shots_id);
        if ($count > 0) {
            throw new RuntimeException('该枪正在充电中', [], RuntimeException::CodeBusinessException);
        }
    }
}