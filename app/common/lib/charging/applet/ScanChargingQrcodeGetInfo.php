<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging\applet;

use app\common\cache\redis\entity\AppletLoginUser;
use app\common\lib\charging\Base;
use app\common\lib\exception\RuntimeException;
use app\common\lib\ExceptionLogCollector;
use app\common\lib\VerifyData;
use app\common\model\Activity;
use app\common\model\ChargingQrcodeToShots;
use app\common\model\Order as OrderModel;
use app\common\model\PilesStatus;
use app\common\model\ScanCodeRecords;
use app\common\model\Shots as ShotsModel;
use app\common\model\ShotsStatus;
use app\common\model\Users as UsersModel;
use Respect\Validation\Validator as v;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Request;
use Throwable;

class ScanChargingQrcodeGetInfo extends Base
{
    protected Request $request;
    protected AppletLoginUser $loginUser;
    protected mixed $period = '';

    public function __construct(Request $request, AppletLoginUser $loginUser)
    {
        $this->request = $request;
        $this->loginUser = $loginUser;
    }

    /**
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     */
    public function run(): array
    {
        try {
            // 验证参数
            $params = $this->verifyParams($this->request->post());
            // 获取绑定的充电枪ID
            $shots_id = $this->getBindShotsId($params['charging_qrcode_id']);
            if (is_null($shots_id)) {
                throw new RuntimeException('充电二维码ID未绑定或无效', [], RuntimeException::CodeBusinessException);
            }
            // 加载充电枪数据
            $shotData = $this->loadShotData($shots_id);
            // 检查该充电枪是否存在充电中的订单
            $this->checkHasChargingOrder($shots_id);
            // 检测状态
            $this->checkStatus($shotData);
            // 获取当前时段费率
            $this->getPeriodRate($shotData);
            // 加载用户数据
            $userData = $this->loadUserData($this->loginUser->id);
            // 查询场站优惠折扣
            $discount = (new Activity())->getStationDiscount($shotData['station_id']);
        } catch (Throwable $e) {

            $message = $e->getMessage();

            if (in_array($message, ['该枪正在充电中', '存在未完成订单', '充电枪充电中'])) {
                $scan_code_result = ScanCodeRecords::ScanCodeResultCharging;
            } else if ($message === '充电桩不在线') {
                $scan_code_result = ScanCodeRecords::ScanCodeResultOffline;
            } else if ($message === '充电枪故障') {
                $scan_code_result = ScanCodeRecords::ScanCodeResultFault;
            } else {
                $scan_code_result = ScanCodeRecords::ScanCodeResultUnknown;
            }

            // 记录扫码操作
            if (!empty($shotData)) {
                (new ScanCodeRecords())->add(
                    $this->loginUser->id,
                    $shotData['corp_id'],
                    $shotData['station_id'],
                    $shotData['piles_id'],
                    $shotData['sequence'],
                    $scan_code_result
                );
            }

            throw $e;
        }

        // 记录扫码操作
        (new ScanCodeRecords())->add(
            $this->loginUser->id,
            $shotData['corp_id'],
            $shotData['station_id'],
            $shotData['piles_id'],
            $shotData['sequence'],
        );

        return [
            'shots' => $shotData
            , 'user' => $userData
            , 'period' => $this->period
            , 'discount' => $discount
        ];
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::charging([
            'charging_qrcode_id',
        ]));
    }

    protected function getBindShotsId(int $charging_qrcode_id): ?int
    {
        $model = new ChargingQrcodeToShots();
        return $model->getBindShotsId($charging_qrcode_id);
    }

    protected function checkHasChargingOrder(int $shots_id): void
    {
        try {
            $OrderModel = app(OrderModel::class);
            $orderData = $OrderModel
                ->where('shot_id', $shots_id)
                ->where('status', 'in', [
                    OrderModel::StatusPlaceAnOrder,
                    OrderModel::StatusFreeze,
                    OrderModel::StatusCharging,
//                    OrderModel::StatusCompulsorySettlement
                ])
                ->field(['id', 'status', 'user_id'])
                ->find();
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            throw new RuntimeException('数据库异常', [], RuntimeException::CodeServiceException);
        }

        if ($orderData) {
            if ($orderData['user_id'] === $this->loginUser->id) {
                throw new RuntimeException('存在未完成订单', $orderData->toArray(), RuntimeException::CodeBusinessWarning);
            }
            throw new RuntimeException('该枪正在充电中', [], RuntimeException::CodeBusinessException);
        }
    }

    /**
     * @param int $shots_id
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function loadShotData(int $shots_id): array
    {
        $shots_model = new ShotsModel();
        $shots = $shots_model->getShotsData($shots_id);

        if (is_null($shots)) throw new RuntimeException('找不到充电枪', [], RuntimeException::CodeBusinessException);

        return $shots;
    }

    protected function loadUserData(int $user_id): array
    {
        try {
            $users_model = new UsersModel();
            $userData = $users_model
                ->field('id,(balance+credit_limit) as balance,freeze_balance')
                ->where('id', $user_id)
                ->find();
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            throw new RuntimeException('数据库异常', [], RuntimeException::CodeServiceException);
        }
        if (empty($userData)) {
            throw new RuntimeException('用户不存在', [], RuntimeException::CodeBusinessException);
        }
        return $userData->toArray();
    }

    protected function checkStatus(array $shotData): void
    {
        if ($shotData['piles_is_online'] === PilesStatus::IsOnlineNot) throw new RuntimeException('充电桩不在线', [], RuntimeException::CodeBusinessException);

        if ($shotData['is_fault'] == ShotsStatus::IsFaultYes) throw new RuntimeException('充电枪故障', [], RuntimeException::CodeBusinessException);
        if ($shotData['work_status'] == ShotsStatus::WorkStatusCharging) throw new RuntimeException('充电枪充电中', [], RuntimeException::CodeBusinessException);
    }

    protected function getPeriodRate(array $shotData): void
    {
        $period = get_current_period_rate(json_decode($shotData['period_json_sum'], true));
        if (!$period) throw new RuntimeException('找不到时段费率', [], RuntimeException::CodeBusinessException);
        $this->period = $period;
    }
}