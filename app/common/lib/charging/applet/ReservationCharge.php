<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging\applet;

use app\common\model\Order as OrderModel;
use app\common\repositories\StationsExtraInfo;
use app\common\lib\exception\RuntimeException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class ReservationCharge extends StartCharge
{
    /**
     * @var int 订单类型
     */
    protected int $order_type = OrderModel::TypeReservationCharge;

    /**
     * 加载充电枪数据
     *
     * @param int $shots_id
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function loadShotsData(int $shots_id): void
    {
        parent::loadShotsData($shots_id);
        $this->verifyIsSupportReservation($this->shotsData['station_id']);
    }


    protected function verifyIsSupportReservation(int $station_id): void
    {
        $is_support_reservation = StationsExtraInfo::getIsSupportReservation($station_id);
        if ($is_support_reservation === StationsExtraInfo::IsSupportReservationNot || is_null($is_support_reservation)) {
            throw new RuntimeException('该场站不支持预约充电', [], RuntimeException::CodeBusinessException);
        }
    }
}