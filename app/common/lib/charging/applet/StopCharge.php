<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging\applet;

use app\common\cache\base\ChargingContextData;
use app\common\lib\charging\applet\request\StopChargeRequest;
use app\common\lib\charging\Base;
use app\common\lib\charging\request\TRATimeoutSettlementRequest;
use app\common\lib\charging\sdk\request\StopChargeCommandRequest;
use app\common\lib\charging\sdk\StopChargeCommand;
use app\common\lib\charging\TRATimeoutSettlement;
use app\common\lib\exception\RuntimeException;
use app\common\model\Order as OrderModel;
use app\common\model\PilesStatus;
use app\common\model\ShotsStatus;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class StopCharge extends Base
{
    public StopChargeRequest $request;
    protected ?array $orderData = null;

    public function __construct(StopChargeRequest $request)
    {
        $this->request = $request;
    }

    /**
     * 加载订单数据
     *
     * @return void
     * @throws RuntimeException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function loadOrderData(): void
    {
        $Order = new OrderModel();
        $orderData = $Order->where('id', $this->request->order_id)->find();

        if (!$orderData | $orderData['user_id'] !== $this->request->loginUser->id) {
            throw new RuntimeException('订单不存在', [], RuntimeException::CodeBusinessException);
        }
        $orderData['discount'] = (float) $orderData['discount'];
        $this->orderData = $orderData->toArray();
        if (
            !in_array($this->orderData['status'], [
                OrderModel::StatusCharging,
                OrderModel::StatusPlaceAnOrder,
                OrderModel::StatusReservation
            ])
        ) {
            throw new RuntimeException('该订单已结束', [], RuntimeException::CodeBusinessException);
        }
    }

    /**
     * 异常结算
     *
     * @return void
     * @throws DbException
     */
    protected function abnormalSettlement(): void
    {
        $tRATimeoutSettlementRequest = new TRATimeoutSettlementRequest([
            'order_id' => $this->request->order_id
        ]);

        $tRATimeoutSettlement = new TRATimeoutSettlement($tRATimeoutSettlementRequest);
        $tRATimeoutSettlement->run();
    }

    protected function checkPilesAndShotStatus(): void
    {
//        $pilesStatus = app(Piles::class)->getStatus($this->orderData['piles_id']);
//
//         if ($pilesStatus === Piles::StatusOffline) throw new RuntimeException('充电桩不在线', [], RuntimeException::CodeBusinessException);

        $ShotsStatusModel = app(ShotsStatus::class);
        $shotWorkStatus = $ShotsStatusModel->getWorkStatus($this->orderData['shot_id']);
        if ($shotWorkStatus != ShotsStatus::WorkStatusCharging) throw new RuntimeException('充电枪状态不是充电中', [], RuntimeException::CodeBusinessException);


    }

    /**
     * (小程序)停止充电
     *
     * @return array
     * @throws RuntimeException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function run(): array
    {
        // 加载订单数据
        $this->loadOrderData();

        $piles_is_online = app(PilesStatus::class)->getIsOnline($this->orderData['piles_id']);

        if ($piles_is_online === PilesStatus::IsOnlineNot) {

            $this->abnormalSettlement();

            return [];
        } else {

            $ShotsStatusModel = new ShotsStatus();
            $workStatus = $ShotsStatusModel->getWorkStatus($this->orderData['shot_id']);
            if ($workStatus != ShotsStatus::WorkStatusCharging) {
                $this->abnormalSettlement();
            }

//            // 检测充电桩与充电枪状态
//            $this->checkPilesAndShotStatus();
        }


        // 发送停止充电指令
        $this->sendStopChargeCommand();

        return [];
    }

    protected function sendStopChargeCommand(): void
    {
        $context = new ChargingContextData([
            'token' => $this->request->loginUser->token,
            'user_id' => $this->request->loginUser->id,
            'type' => '小程序',
            'order_id' => $this->request->order_id,
            'corp_id' => $this->orderData['corp_id'],
            'station_id' => $this->orderData['station_id'],
            'shot_id' => $this->orderData['shot_id']
        ]);
        $stopChargeCommand = new StopChargeCommand(new StopChargeCommandRequest([
            'piles_id' => (int)$this->orderData['piles_id'],
            'sequence' => (int)$this->orderData['sequence'],
            'shot_id' => $this->orderData['shot_id'],
            'context' => $context
        ]));
        $stopChargeCommand->send();
    }
}