<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging\applet;

use app\common\lib\charging\applet\request\ScanCodeGetInfoRequest;
use app\common\lib\charging\Base;
use app\common\lib\exception\RuntimeException;
use app\common\lib\ExceptionLogCollector;
use app\common\model\Activity;
use app\common\model\Order as OrderModel;
use app\common\model\PilesStatus;
use app\common\model\ScanCodeRecords;
use app\common\model\Shots as ShotsModel;
use app\common\model\ShotsStatus;
use app\common\model\Users as UsersModel;
use app\ms\Api;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Log;
use Throwable;

class ScanCodeGetInfo extends Base
{
    protected ScanCodeGetInfoRequest $request;
    protected array $userData = [];
    protected array $shotData = [];
    protected mixed $period = '';

    public function __construct(ScanCodeGetInfoRequest $request)
    {
        $this->request = $request;
    }

    /**
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     */
    public function run(): array
    {
        try {
            // 加载充电枪数据
            $this->loadShotData();
            // 检查该充电枪是否存在充电中的订单
            $this->checkHasChargingOrder();
            // 检测状态
            $this->checkStatus();
            // 加载用户数据
            $this->loadUserData();
            // ========== 从微服务查询充电站信息(是否需要用户输入车牌) ==========
            $stationInfo = Api::send("/device/stations/info", 'GET', [
                'id' => $this->shotData['station_id']
            ])['data'];
            $this->shotData['is_require_plate'] = $stationInfo['is_require_plate'];

            // ========= 从微服务获取用户信息 =========
            $userInfo = Api::send('/auth/user/info', 'GET', ['user_id' => $this->request->loginUser->id, 'station_id' => $this->shotData['station_id']])['data'];
            $this->userData['plate'] = $userInfo['plate'];
            if (!empty($this->userData['plate'])) {
                // 如果车牌不为空自动注册为场站用户
                Api::send('/auth/user/stations/scan_register', 'POST', ['station_id' => $this->shotData['station_id']], [
                    'Authorization' => $this->request->loginUser->token
                ]);
                Log::info('用户自动注册场站用户', ['user_id' => $this->request->loginUser->id, 'station_id' => $this->shotData['station_id']]);
            }
            // 获取用户在当前场站的等级信息
            if (!empty($userInfo['users_stations'])) {
                foreach ($userInfo['users_stations'] as $station) {
                    if ($station['station_id'] == $this->shotData['station_id']) {
                        $this->userData['level'] = $station['level'];
                        break;
                    }
                }
            }
            // 获取当前时段费率
            $this->getPeriodRate();
            // 查询场站优惠折扣
            $discount = (new Activity())->getStationDiscount($this->shotData['station_id']);
        } catch (Throwable $e) {

            $message = $e->getMessage();

            if (in_array($message, ['该枪正在充电中', '存在未完成订单', '充电枪充电中'])) {
                $scan_code_result = ScanCodeRecords::ScanCodeResultCharging;
            } else if ($message === '充电桩不在线') {
                $scan_code_result = ScanCodeRecords::ScanCodeResultOffline;
            } else if ($message === '充电枪故障') {
                $scan_code_result = ScanCodeRecords::ScanCodeResultFault;
            } else {
                $scan_code_result = ScanCodeRecords::ScanCodeResultUnknown;
            }

            // 记录扫码操作
            if (!empty($this->shotData)) {
                (new ScanCodeRecords())->add(
                    $this->request->loginUser->id,
                    $this->shotData['corp_id'],
                    $this->shotData['station_id'],
                    $this->shotData['piles_id'],
                    $this->shotData['sequence'],
                    $scan_code_result
                );
            }

            throw $e;
        }

        // 记录扫码操作
        (new ScanCodeRecords())->add(
            $this->request->loginUser->id,
            $this->shotData['corp_id'],
            $this->shotData['station_id'],
            $this->shotData['piles_id'],
            $this->shotData['sequence'],
        );

        // 长三院兼容代码
        if (str_contains($this->shotData['station_name'], '长三院')){
            $this->period['is_only_show_sum'] = true;
            $this->shotData['copyright'] = '长三院能源互联网研究中心技术支持';
        }
        if (str_contains($this->shotData['tariff_name'], '长三院A区')){
            $this->shotData['tips'] = 'CDE北侧车位调节费优惠 -0.1元/度';
        }

        return [
            'shots' => $this->shotData
            , 'user' => $this->userData
            , 'period' => $this->period
            , 'discount' => $discount
        ];
    }

    protected function checkHasChargingOrder(): void
    {
        try {
            $OrderModel = app(OrderModel::class);
            $orderData = $OrderModel
                ->where('shot_id', $this->request->id)
                ->where('status', 'in', [
                    OrderModel::StatusPlaceAnOrder,
                    OrderModel::StatusFreeze,
                    OrderModel::StatusCharging,
//                    OrderModel::StatusCompulsorySettlement
                ])
                ->field(['id', 'status', 'user_id'])
                ->find();
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            throw new RuntimeException('数据库异常', [], RuntimeException::CodeServiceException);
        }

        if ($orderData) {
            if ($orderData['user_id'] === $this->request->loginUser->id) {
                throw new RuntimeException('存在未完成订单', $orderData->toArray(), RuntimeException::CodeBusinessWarning);
            }
            throw new RuntimeException('该枪正在充电中', [], RuntimeException::CodeBusinessException);
        }
    }

    /**
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function loadShotData(): void
    {
        $shots_model = new ShotsModel();
        $shots = $shots_model->getShotsData($this->request->id);

        if (is_null($shots)) throw new RuntimeException('找不到充电枪', [], RuntimeException::CodeBusinessException);

        $this->shotData = $shots;
    }

    protected function loadUserData(): void
    {
        try {
            $users_model = new UsersModel();
            $userData = $users_model
                ->field('id,(balance+credit_limit) as balance,freeze_balance')
                ->where('id', $this->request->loginUser->id)
                ->find();
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            throw new RuntimeException('数据库异常', [], RuntimeException::CodeServiceException);
        }
        if (empty($userData)) {
            throw new RuntimeException('用户不存在', [], RuntimeException::CodeBusinessException);
        }
        $this->userData = $userData->toArray();
    }

    protected function checkStatus(): void
    {
        if ($this->shotData['piles_is_online'] === PilesStatus::IsOnlineNot) throw new RuntimeException('充电桩不在线', [], RuntimeException::CodeBusinessException);

//        if ($this->shotData['is_online'] == ShotsStatus::IsOnlineNot) throw new RuntimeException('充电枪离线', [], RuntimeException::CodeBusinessException);
        if ($this->shotData['is_fault'] == ShotsStatus::IsFaultYes) throw new RuntimeException('充电枪故障', [], RuntimeException::CodeBusinessException);
        if ($this->shotData['work_status'] == ShotsStatus::WorkStatusCharging) throw new RuntimeException('充电枪充电中', [], RuntimeException::CodeBusinessException);
    }

    protected function getPeriodRate(): void
    {
        $periodData = json_decode($this->shotData['period_json_sum'], true);
        if (!$periodData) throw new RuntimeException('找不到时段费率', [], RuntimeException::CodeBusinessException);

        // 获取场站折扣和用户折扣
        $stationDiscount = (new Activity())->getStationDiscount($this->shotData['station_id']);
        $userDiscount = $this->userData['level']['discount'] ?? 100;

        // 将附加费算入所有时段费率
        $discountedPeriodData = $this->calculateDiscountedRate($periodData);
        $this->shotData['period_json_sum'] = json_encode($discountedPeriodData);

        // 获取当前时段费率并应用折扣
        $period = get_current_period_rate($periodData);
        if (!$period) throw new RuntimeException('找不到时段费率', [], RuntimeException::CodeBusinessException);

        // 应用折扣到当前时段费率，surcharge不参与折扣
        $finalDiscount = ($stationDiscount * $userDiscount) / 10000;
        $surcharge = $this->shotData['surcharge'];
        // 记录原价服务费
        $ser_fee = $period['fee']['ser_fee'];
        // 确保服务费不会小于0
        $period['fee']['ser_fee'] = max(0, $period['fee']['ser_fee'] + $surcharge);
        $period['sum_fee'] = $period['fee']['fee'] + $period['fee']['ser_fee'];
        // 计算折扣后的优惠金额，确保服务费不会小于0
        $period['sum_discount_fee'] = $period['fee']['fee'] + max(0, (int)($ser_fee * $finalDiscount) + $surcharge);
        unset($this->shotData['surcharge']);

        $this->period = $period;
    }

    /**
     * 计算附加费到所有时段费率
     * @param array $periodData 费率数据
     * @return array
     */
    protected function calculateDiscountedRate(array $periodData): array
    {
        foreach ($periodData as &$period) {
            $surcharge = $this->shotData['surcharge'];
            // 确保服务费不会小于0
            $period['fee']['ser_fee'] = max(0, $period['fee']['ser_fee'] + $surcharge);
            $period['sum_fee'] = $period['fee']['fee'] + $period['fee']['ser_fee'];
        }

        return $periodData;
    }
}