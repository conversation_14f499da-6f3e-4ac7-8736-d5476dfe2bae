<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging;

use app\common\lib\charging\request\TransactionRecordRequest;
use app\common\lib\SendAdmin;
use app\common\lib\SendApplet;
use app\common\lib\WeChat;
use app\common\log\SocketLogCollector;
use app\common\model\Order;
use app\common\model\Order as OrderModel;
use app\common\model\ShotsStatus;
use app\common\model\Users;
use app\event\ChargeNormalEndEvent;
use app\event\DeviceAbnormalityEvent;
use app\event\ShotsStatusChangeEvent;
use app\common\lib\exception\RuntimeException;
use app\ms\Api;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use Throwable;

/**
 * @class 充电订单结算 (包括正常结算与补扣结算)
 * 如果数据库中的订单状态为 "强制结算" 那么将进行 补扣结算
 * 反之则进行 正常结算
 */
class Settlement
{
    use AlgorithmSettlement;

    public const ReturnCodeBusinessAbnormal = 400;
    public const ReturnCodeDatabaseAbnormal = 501;
    protected array $orderData = [];

    protected TransactionRecordRequest $request;

    protected ?SettlementResult $settlementResult = null;
    protected SocketLogCollector $socketLogCollector;

    protected int $chargeDuration = 0;

    public const StopChargeStatusNormal = 1; // 正常停止
    public const StopChargeStatusAbnormal = 0; // 异常停止
    public const StopChargeStatusUnknown = 2; // 未知原因

    /**
     * @var int 停止充电状态
     */
    protected int $stop_charge_status;
    /**
     * @var string 停止充电原因描述(这里的停止原因是在接收到充电桩上报过来的数据，再结合自身业务逻辑进行调整的)
     */
    protected string $stop_charge_reason;
    protected string $reason_for_stop_text = '';

    public function __construct(TransactionRecordRequest $request, SocketLogCollector $socketLogCollector)
    {
        $this->request = $request;
        $this->socketLogCollector = $socketLogCollector;
        $this->reason_for_stop_text = $this->getReasonForStopText();
    }

    /**
     * 充电订单结算
     *
     * @return array
     * @throws DbException|Throwable
     */
    public function run(): array
    {
        // 加载订单数据
        $this->loadOrderData();
        // 删除定时器
        $this->deleteTimer();
        // 停止原因检测
        $this->reasonForStopDetection();
        // 计算总费用
        $this->calculateTotalCost();

        try {
            Db::startTrans();
            // 更新订单数据
            $this->updateOrderData();
            // 更新用户数据
            $this->updateUserBalance();

            // TODO:临时增加，还需要完善。
            (new ShotsStatus())->updateWorkStatus($this->orderData['shot_id'], ShotsStatus::WorkStatusIdle);

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw $e;
        }

        // 推送Socket消息
        $this->pushSocketMessage();
        // 触发事件
        $this->triggerEvent();
        // 充电停止成功，发送微信通知
        $this->sendWechatEndNotification($this->orderData, $this->socketLogCollector);

        return [
            'corp_id' => $this->orderData['corp_id'],
            'station_id' => $this->orderData['station_id']
        ];
    }

    public function getReasonForStopText(): string
    {
        return match ($this->request->reason_for_stop) {
            0x00 => "充电异常中止，充电桩重启",
            0x40 => "充电完成，APP远程停止",
            0x41 => "充电完成，SOC达到100%",
            0x42 => "充电完成，充电电量满足设定条件",
            0x43 => "充电完成，充电金额满足设定条件",
            0x44 => "充电完成，充电时间满足设定条件",
            0x45 => "充电完成，手动停止充电",
            0x46 => "充电桩空闲中，直接结束订单",
            0x47 => "未收到交易记录，直接停止",
            0x48, 0x49 => "充电完成，其他方式（预留）",
            0x4A => "充电启动失败，充电桩控制系统故障(需要重启或自动恢复)",
            0x4B => "充电启动失败，控制导引断开",
            0x4C => "充电启动失败，断路器跳位",
            0x4D => "充电启动失败，电表通信中断",
            0x4E => "充电启动失败，余额不足",
            0x4F => "充电启动失败，充电模块故障",
            0x50 => "充电启动失败，急停开入",
            0x51 => "充电启动失败，防雷器异常",
            0x52 => "充电启动失败，BMS未就绪",
            0x53 => "充电启动失败，温度异常",
            0x54 => "充电启动失败，电池反接故障",
            0x55 => "充电启动失败，电子锁异常",
            0x56 => "充电启动失败，合闸失败",
            0x57 => "充电启动失败，绝缘异常",
            0x58 => "充电启动失败，预留",
            0x59 => "充电启动失败，接收 BMS 握手报文 BHM 超时",
            0x5A => "充电启动失败，接收 BMS 和车辆的辨识报文超时 BRM",
            0x5B => "充电启动失败，接收电池充电参数报文超时 BCP",
            0x5C => "充电启动失败，接收 BMS 完成充电准备报文超时 BRO AA",
            0x5D => "充电启动失败，接收电池充电总状态报文超时 BCS",
            0x5E => "充电启动失败，接收电池充电要求报文超时 BCL",
            0x5F => "充电启动失败，接收电池充电电流报文超时 BCC",
            0x60 => "充电启动失败，GB2015 电池在 BHM 阶段有电压不允许充电",
            0x61 => "充电启动失败，GB2015 辨识阶段在 BRO_AA 时候电池实际电压与 BCP 报文电池电压差距大于 5%",
            0x62 => "充电启动失败，B2015 充电机在预充电阶段从 BRO_AA 变成 BRO_00 状态",
            0x63 => "充电启动失败，接收主机配置报文超时",
            0x64 => "充电启动失败，充电机未准备就绪,我们没有回 CRO AA，对应老国标",
            0x65, 0x66, 0x67, 0x68, 0x69 => "充电启动失败，（其他原因）预留",
            0x6A => "充电异常中止，系统闭锁",
            0x6B => "充电异常中止，导引断开",
            0x6C => "充电异常中止，断路器跳位",
            0x6D => "充电异常中止，电表通信中断",
            0x6E => "充电异常中止，余额不足",
            0x6F => "充电异常中止，地线丢失异常",
            0x70 => "充电异常中止，直流保护动作",
            0x71 => "充电异常中止，继电器粘连故障",
            0x72 => "充电异常中止，急停开入",
            0x73 => "充电异常中止，防雷器异常",
            0x74 => "充电异常中止，温度异常",
            0x75 => "充电异常中止，电流超限异常",
            0x76 => "充电异常中止，充电无流",
            0x77 => "充电异常中止，电子锁异常",
            0x78 => "充电异常中止，预留",
            0x79 => "充电异常中止，总充电电压异常",
            0x7A => "充电异常中止，剩余电流异常",
            0x7B => "充电异常中止，单体充电电压异常",
            0x7C => "充电异常中止，电池组过温",
            0x7D => "充电异常中止，最高单体充电电压异常",
            0x7E => "充电异常中止，最高电池组过温",
            0x7F => "充电异常中止，BMV 单体充电电压异常",
            0x80 => "充电异常中止，BMT 电池组过温",
            0x81 => "充电异常中止，电池状态异常停止充电",
            0x82 => "充电异常中止，车辆发报文禁止充电",
            0x83 => "充电异常中止，充电桩断电",
            0x84 => "充电异常中止，接收电池充电总状态报文超时",
            0x85 => "充电异常中止，接收电池充电要求报文超时",
            0x86 => "充电异常中止，接收电池状态信息报文超时",
            0x87 => "充电异常中止，接收 BMS 中止充电报文超时",
            0x88 => "充电异常中止，接收 BMS 充电统计报文超时",
            0x89 => "充电异常中止，接收对侧 CCS 报文超时",
            0x8A, 0x8B, 0x8C, 0x8D, 0x8E, 0x8F => "充电异常中止，（其他原因）预留",
            0x90 => "未知原因停止",
            0x100 => '调度算法结束充电失败',
            0x101 => '下调功率失败',
            0x102 => '充电开启失败，设备编号不匹配',
            0x103 => '充电开启失败，枪已在充电',
            0x104 => '充电开启失败，设备故障',
            0x105 => '充电开启失败，设备离线',
            0x106 => '充电开启失败，未插枪',
            0x107 => '充电开启失败，其他',
            0x110 => '充电停止失败，设备编号不匹配',
            0x111 => '充电停止失败，枪未处于充电状态',
            0x112 => '充电停止失败，其他',
            0x113 => '充电停止失败，设备离线',
            default => "未知原因停止(默认)"
        };
    }

    protected function reasonForStopDetection(): void
    {
        // 如果订单状态已经是强制结算了，说明这次处理是补充扣款逻辑。
        if ($this->orderData['status'] === Order::StatusCompulsorySettlement) {
            $this->stop_charge_reason = '订单自动结算后，补扣成功。';
            $this->stop_charge_status = self::StopChargeStatusNormal;
        } else {
            // 余额不足导致停止充电。
            if (in_array($this->request->reason_for_stop, [0x4E, 0x6E])) {
                $this->stop_charge_reason = '用户下发充电桩余额已用完';
                $this->stop_charge_status = self::StopChargeStatusNormal;
                // 手动拔枪
            } else if (in_array($this->request->reason_for_stop, [0x4B, 0x6B])) {
                $this->stop_charge_reason = '手动拔枪';
                $this->stop_charge_status = self::StopChargeStatusNormal;
            } else if (
                $this->request->reason_for_stop >= 0x40 &&
                $this->request->reason_for_stop <= 0x49
            ) {
                $this->stop_charge_reason = $this->reason_for_stop_text;
                $this->stop_charge_status = self::StopChargeStatusNormal;
            } else {
                $this->stop_charge_reason = $this->reason_for_stop_text;
                $this->stop_charge_status = self::StopChargeStatusAbnormal;
            }


        }
    }

    protected function triggerEvent(): void
    {
        try {
            event('ChargeNormalEnd', new ChargeNormalEndEvent([
                'order_id' => $this->request->transaction_serial_number,
                'corp_id' => $this->orderData['corp_id'],
                'station_id' => $this->orderData['station_id'],
                'pay_money' => $this->settlementResult->total_cost,
                'electricity_total' => $this->request->total_charge,
                'valley_electricity' => $this->request->valley_charge,
                'flat_electricity' => $this->request->flat_charge,
                'peak_electricity' => $this->request->peak_charge,
                'sharp_electricity' => $this->request->sharp_charge,
                'service_money' => $this->settlementResult->total_service_cost,
                'charge_duration' => $this->chargeDuration,
                'start_time' => strtotime($this->request->start_time),
                'end_time' => strtotime($this->request->end_time),
                'user_id' => $this->orderData['user_id'],
                'stop_charge_reason' => $this->stop_charge_reason,
                'stop_charge_status' => $this->stop_charge_status
            ]));
        } catch (Throwable $e) {
            // 在事件中运行的业务都是次要业务，若是出现异常情况，
            // 只记录日志，不做其他处理，避免影响充电主流程。
            $this->socketLogCollector->collect($e);
        }


        $ShotsStatusModel = app(ShotsStatus::class);
        $beforeStatus = $ShotsStatusModel->getWorkStatus($this->orderData['shot_id']);
        if ($beforeStatus !== ShotsStatus::WorkStatusIdle) {
            $ShotsStatusModel->updateWorkStatus($this->orderData['shot_id'], $ShotsStatusModel::WorkStatusIdle);

            try {
                event('ShotsStatusChange', new ShotsStatusChangeEvent([
                    'corp_id' => $this->orderData['corp_id'],
                    'station_id' => $this->orderData['station_id'],
                    'pile_id' => $this->orderData['piles_id'],
                    'shot_id' => $this->orderData['shot_id'],
                    'before_status' => $beforeStatus,
                    'after_status' => ShotsStatus::WorkStatusIdle
                ]));
            } catch (Throwable $e) {
                // 在事件中运行的业务都是次要业务，若是出现异常情况，
                // 只记录日志，不做其他处理，避免影响充电主流程。
                $this->socketLogCollector->collect($e);
            }
        }

        if ($this->request->reason_for_stop > 0x49) {
            try {
                event('DeviceAbnormality', new DeviceAbnormalityEvent([
                    'abnormality_type' => DeviceAbnormalityEvent::AbnormalityTypeTransactionRecord,
                    'pile_id' => $this->orderData['piles_id'],
                    'shot_id' => $this->orderData['shot_id'],
                    'corp_id' => $this->orderData['corp_id'],
                    'station_id' => $this->orderData['station_id'],
                    'start_time' => time(),
                    'abnormal_cause' => (string)$this->request->reason_for_stop
                ]));
            } catch (Throwable $e) {
                // 在事件中运行的业务都是次要业务，若是出现异常情况，
                // 只记录日志，不做其他处理，避免影响充电主流程。
                $this->socketLogCollector->collect($e);
            }
        }
    }

    protected function pushSocketMessage(): void
    {
        $user_data = app(\app\common\context\Order::class)->getOrderIdToContextMap($this->request->transaction_serial_number);
//        $user_data = cache('充电桩服务订单_' . $this->request->transaction_serial_number);
        // 强制结算
        if ($this->orderData['status'] === Order::StatusCompulsorySettlement) {
            switch ($user_data['type']) {
                case '后台':
                    SendAdmin::send_group($user_data['token'], [
                        'type' => 'supplementary_deduction',
                        'msg' => '补充扣款',
                        'data' => [
                            'order_id' => $this->request->transaction_serial_number
                        ]
                    ]);
                    break;
                case '小程序':
                    SendApplet::send_uid($user_data['user_id'], [
                        'type' => 'supplementary_deduction',
                        'msg' => '补充扣款',
                        'data' => [
                            'order_id' => $this->request->transaction_serial_number
                        ]
                    ]);
                    break;
            }
        } else {
            switch ($user_data['type']) {
                case '后台':
                    SendAdmin::send_group($user_data['token'], [
                        'type' => 'transaction_record_confirmation',
                        'msg' => '交易记录确认',
                        'data' => (array)$this->request
                    ]);
                    break;
                case '小程序':
                    SendApplet::send_uid($user_data['user_id'], [
                        'type' => 'transaction_record_confirmation',
                        'msg' => '交易记录确认',
                        'data' => (array)$this->request
                    ]);
                    break;
            }
        }
    }

    protected function loadOrderData(): void
    {
        try {
            $Order = new Order();
            $orderData = $Order->where('id', $this->request->transaction_serial_number)->find();
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            $this->socketLogCollector->collect($e);
            throw new RuntimeException('数据库异常', [], RuntimeException::CodeDatabaseException);
        }

        if (!$orderData) {
            throw new RuntimeException('订单不存在', [], RuntimeException::CodeBusinessException);
        }
        $orderData['discount'] = (float) $orderData['discount'];
        $this->orderData = $orderData->toArray();
        if (
            !in_array($this->orderData['status'], [
                Order::StatusPlaceAnOrder,
                Order::StatusCharging,
                Order::StatusCompulsorySettlement,
                Order::StatusReservation
            ])
        ) {
            throw new RuntimeException('订单已结算', [], RuntimeException::CodeBusinessException);
        }
    }

    // 删除定时器
    protected function deleteTimer(): void
    {
        $res3 = send_http_post_timer([
            'type' => '删除-运营平台远程停机-账单',
            'order_id' => $this->request->transaction_serial_number
        ]);
        $this->socketLogCollector->collectorRunLog('send_http_post_timer：交易记录确认=》删除-运营平台远程停机-账单==>' . json_encode_cn($res3));

        $res3 = send_http_post_timer([
            'type' => '删除-上传实时监测数据',
            'order_id' => $this->request->transaction_serial_number
        ]);
        $this->socketLogCollector->collectorRunLog('send_http_post_timer：交易记录确认=》删除-上传实时监测数据==>' . json_encode_cn($res3));

        $res3 = send_http_post_timer([
            'type' => '删除-自动结算',
            'order_id' => $this->request->transaction_serial_number
        ]);
        $this->socketLogCollector->collectorRunLog('send_http_post_timer：交易记录确认=》删除-自动结算==>' . json_encode_cn($res3));
    }

    protected function verifySettlementIsConsistent(int $consumption_amount): void
    {
        if ($consumption_amount === $this->settlementResult->total_cost) {
            return;
        }
        $message = '服务层与充电桩结算出来的费用不一致，';
        $messageArray = [
            '总费用' => $this->settlementResult->total_cost,
            '尖时充电费用' => $this->settlementResult->sharp_charge_cost,
            '尖时服务费用' => $this->settlementResult->sharp_service_cost,
            '峰时充电费用' => $this->settlementResult->peak_charge_cost,
            '峰时服务费用' => $this->settlementResult->peak_service_cost,
            '平时充电费用' => $this->settlementResult->flat_charge_cost,
            '平时服务费用' => $this->settlementResult->flat_service_cost,
            '谷时充电费用' => $this->settlementResult->valley_charge_cost,
            '谷时服务费用' => $this->settlementResult->valley_service_cost,
        ];
        $message .= sprintf('服务层结算结果:%s', json_encode_cn($messageArray));
        $messageArray = [
            '总费用' => $consumption_amount,
            '尖时充电费用' => $this->request->sharp_amount,
            '峰时充电费用' => $this->request->peak_amount,
            '平时充电费用' => $this->request->flat_amount,
            '谷时充电费用' => $this->request->valley_amount,
        ];
        $message .= sprintf('充电桩结算结果:%s', json_encode_cn($messageArray));
        $this->socketLogCollector->collectorRunLog($message, SocketLogCollector::LevelError);
    }

    /**
     * 更新订单数据
     * lwj 2024.5.10 修改
     */
    protected function updateOrderData(): void
    {
        if ($this->settlementResult->type === SettlementResult::TypeNormalSettlement) {
            // 充电耗时
            $this->chargeDuration = strtotime($this->request->end_time) - strtotime($this->request->start_time);

            $sharp_amount = $this->settlementResult->sharp_charge_cost + $this->settlementResult->sharp_service_cost;
            $peak_amount = $this->settlementResult->peak_charge_cost + $this->settlementResult->peak_service_cost;
            $flat_amount = $this->settlementResult->flat_charge_cost + $this->settlementResult->flat_service_cost;
            $valley_amount = $this->settlementResult->valley_charge_cost + $this->settlementResult->valley_service_cost;

            $update_data = [
//                'money' => $this->settlementResult->total_cost,
//                'pay_money' => $this->settlementResult->total_cost,

                'money' => $this->settlementResult->original_total_cost,
                'pay_money' => $this->settlementResult->total_cost,
                'coupon_money' => $this->settlementResult->original_total_cost - $this->settlementResult->total_cost,


                'settled_amount' => $this->settlementResult->total_cost,
                'trans_start_time' => $this->request->start_time,
                'trans_end_time' => $this->request->end_time,

                'sharp_price' => $this->request->sharp_price,
                'sharp_electricity' => $this->request->sharp_charge,
                'sharp_loss' => $this->request->loss_sharp_charge,
                'sharp_amount' => $sharp_amount,

                'peak_price' => $this->request->peak_price,
                'peak_electricity' => $this->request->peak_charge,
                'peak_loss' => $this->request->loss_peak_charge,
                'peak_amount' => $peak_amount,

                'flat_price' => $this->request->flat_price,
                'flat_electricity' => $this->request->flat_charge,
                'flat_loss' => $this->request->loss_flat_charge,
                'flat_amount' => $flat_amount,

                'valley_price' => $this->request->valley_price,
                'valley_electricity' => $this->request->valley_charge,
                'valley_loss' => $this->request->loss_valley_charge,
                'valley_amount' => $valley_amount,

                'meter_start_value' => $this->request->electricity_meter_total_start,
                'meter_end_value' => $this->request->electricity_meter_total_end,
                'electricity_total' => $this->request->total_charge,
                'electricity_loss' => $this->request->loss_total_charge,

                'vin_code' => $this->request->unique_identifier_of_electric_vehicle,
                'reason_for_stop' => $this->request->reason_for_stop,
                'reason_for_stop_text' => $this->reason_for_stop_text,
                'msg' => $this->stop_charge_reason,
                'consumption_amount' => $this->request->consumption_amount,

                'electricity_price' => $this->settlementResult->total_charge_cost,
                'ser_price' => $this->settlementResult->total_service_cost,
                'amount_charged' => $this->settlementResult->total_cost * 100,
                'electricity_charged' => $this->request->total_charge,
                'charge_duration' => $this->chargeDuration,
                'freeze_status' => Order::FreezeStatusNot,

                'clearing_electricity_price' => $this->settlementResult->total_charge_cost,
                'clearing_ser_price' => $this->settlementResult->total_service_cost,
                'abnormal_alarm_node' => $this->request->abnormal_alarm_node
            ];
        } else {
            // 充电耗时
            if (is_null($this->orderData['trans_end_time'])) {
                $end_time = time();
                // 桩软件有时会因为没有来得及更新数据，桩硬件就发生了重启。这种情况下桩上报的结束时间会是下边这种格式，处理方式是以开始时间作为结束时间即可。
            } else if ($this->orderData['trans_end_time'] === "2000-00-00 00:00:00") {
                $end_time = strtotime($this->orderData['trans_start_time']);
            } else {
                $end_time = strtotime($this->orderData['trans_end_time']);
            }
            $this->chargeDuration = $end_time - strtotime($this->orderData['trans_start_time']);

            $update_data = [
                'money' => $this->settlementResult->total_cost,
                'pay_money' => $this->settlementResult->total_cost,
                'consumption_amount' => $this->settlementResult->total_cost,
                'settled_amount' => $this->settlementResult->total_cost,
                'trans_end_time' => date('Y-m-d H:i:s', $end_time),
                'meter_start_value' => $this->request->electricity_meter_total_start,
                'meter_end_value' => $this->request->electricity_meter_total_end,
                'electricity_total' => $this->settlementResult->electricity_total,
                'electricity_loss' => 0,

                'vin_code' => $this->request->unique_identifier_of_electric_vehicle,
                'reason_for_stop' => $this->request->reason_for_stop,
                'reason_for_stop_text' => $this->reason_for_stop_text,
                'msg' => $this->stop_charge_reason,

                'electricity_price' => $this->settlementResult->total_charge_cost,
                'ser_price' => $this->settlementResult->total_service_cost,

                'charge_duration' => $this->chargeDuration,
                'freeze_status' => Order::FreezeStatusNot,

                'clearing_electricity_price' => $this->settlementResult->total_charge_cost,
                'clearing_ser_price' => $this->settlementResult->total_service_cost,
                'abnormal_alarm_node' => $this->request->abnormal_alarm_node
            ];
        }

        if ($this->stop_charge_status === self::StopChargeStatusNormal) {
            $update_data['status'] = Order::StatusComplete;
        } else {
            $update_data['status'] = Order::StatusAbnormal;
        }

        $this->socketLogCollector->collectorRunLog('交易记录确认:更新数据=》' . $this->request->transaction_serial_number . '===》' . json_encode_cn($update_data));

        $order_model = new Order();
        $order_model->where('id', $this->request->transaction_serial_number)->update($update_data);
        $orderData = $order_model->where('id', $this->request->transaction_serial_number)->find()->toArray();
        $orderData['discount'] = (float) $orderData['discount'];
        // 同步订单数据到订单微服务
        Api::send('/order/orders','PUT',$orderData,['Authorization' => 'mpQWnQ0zZ7KGl3JEV5DeUoeNXec0mohT'],'',true);
    }

    /**
     * 更新用户余额
     *
     * @return void
     * @throws DbException
     */
    protected function updateUserBalance(): void
    {
        if ($this->settlementResult->total_cost > 0) {
            $order_model = app(OrderModel::class);
            $amountCharged = $order_model->sumOrderTotalAmountCharged($this->orderData['user_id']);

            if ($this->orderData['status'] === Order::StatusCompulsorySettlement) {
                $deduct_cost = $this->settlementResult->total_cost - $this->orderData['settled_amount'];
                $this->socketLogCollector->collectorRunLog(
                    sprintf('该订单已经被强制结算过了，所以本次结算为补扣结算，结算费用需要减去原先强制结算的费用，之前的结算费用为：%s, 本次结算为: %s',
                        $this->orderData['settled_amount'], $this->settlementResult->total_cost)
                );
            } else {
                $deduct_cost = $this->settlementResult->total_cost;
            }

            if ($deduct_cost < 0) {
                $this->socketLogCollector->collectorRunLog(sprintf('该订单补扣结算费用为%s, 不需要再扣',  $deduct_cost), SocketLogCollector::LevelError);
                return;
            }

            Db::name('users')
                ->where('id', $this->orderData['user_id'])
                ->update([
                    'balance' => Db::raw('balance-' . $deduct_cost),
                    'amount_charged' => $amountCharged,
                    'update_time' => date('Y-m-d H:i:s')
                ]);

            if ($this->orderData['status'] !== Order::StatusCompulsorySettlement) {
                $add_data = [
                    'user_id' => $this->orderData['user_id'],
                    'amount' => -$deduct_cost,
                    'memo' => '订单【' . $this->request->transaction_serial_number . '】付款：' . $deduct_cost . '分',
                    'type' => '支付充电订单',
                ];
            } else {
                $add_data = [
                    'user_id' => $this->orderData['user_id'],
                    'amount' => -$deduct_cost,
                    'memo' => '订单【' . $this->request->transaction_serial_number . '】补扣：' . $deduct_cost . '分',
                    'type' => '支付充电订单',
                ];
            }
            Db::name('user_balance_log')->insert($add_data);
        }
    }

    protected function calculateTotalCost(): void
    {
        // 在设备断电后，原先进行中的订单会收到交易记录回调，不过这个回调中的电量和费用都会为空。
        // 所以这里会以充电过程中记录到的数据来进行结算。
        if (
            $this->request->total_charge === 0 &&
            $this->request->reason_for_stop === 131
        ) {
            $this->settlementResult = $this->abnormalSettlement($this->orderData);
        } else {
            $this->settlementResult = $this->normalSettlement($this->request, $this->orderData);
            // 验证结算费用是否与充电桩的一致
            $this->verifySettlementIsConsistent(
                (int)ceil($this->request->consumption_amount / 100)
            );
        }
    }
}
