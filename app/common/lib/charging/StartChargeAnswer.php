<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging;

use app\common\lib\charging\request\StartChargeAnswerRequest;
use app\common\lib\SendAdmin;
use app\common\lib\SendApplet;
use app\common\log\SocketLogCollector;
use app\common\model\Order;
use app\common\model\ShotsStatus;
use app\event\ChargeAbnormalEndEvent;
use app\event\ChargeNormalStartEvent;
use app\event\DeviceAbnormalityEvent;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use Throwable;
use RuntimeException;
use app\common\context\Order as OrderContext;

class StartChargeAnswer extends Base
{
    protected StartChargeAnswerRequest $request;

    protected array $orderData = [];
    protected SocketLogCollector $socketLogCollector;

    public function __construct(StartChargeAnswerRequest $request, SocketLogCollector $socketLogCollector)
    {
        $this->request = $request;
        $this->socketLogCollector = $socketLogCollector;
    }

    public function handler(): void
    {
        // 删除定时器
        $this->removeTimer();
        // 加载订单数据
        $this->loadOrderData();
        // 检测订单是否已经异常结束
        if ($this->orderData['status'] >= Order::StatusComplete) {
            $this->socketLogCollector->collectorRunLog('订单已结束', SocketLogCollector::LevelError);
            return;
        }

        if ($this->request->start_result === 1) {
            $this->onStartChargeSuccess();
        } else {
            $this->onStartChargeFailed();
        }
    }

    protected function removeTimer(): void
    {
        $res3 = send_http_post_timer([
            'type' => '删除-运营平台远程控制启机',
            'order_id' => $this->request->transaction_serial_number,
        ]);
        $this->socketLogCollector->collectorRunLog('send_http_post_timer：远程启动充电命令回复=》' . json_encode_cn($res3));
    }


    protected function loadOrderData(): void
    {
        try {
            $Order = app(Order::class);
            $orderData = $Order->where('id', $this->request->transaction_serial_number)
                ->field([
                    'id',
                    'type',
                    'corp_id',
                    'station_id',
                    'shot_id',
                    'piles_id',
                    'user_id',
                    'status'
                ])
                ->find();
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            $this->socketLogCollector->collect($e);
            throw new RuntimeException('数据库异常', self::ErrorCodeService);
        }

        if (empty($orderData)) {
            throw new RuntimeException('无效交易流水号', self::ErrorCodeBusiness);
        }
        $this->orderData = $orderData->toArray();
    }


    protected function triggerAbnormalEndEvent(): void
    {
        try {
            event('ChargeAbnormalEnd', new ChargeAbnormalEndEvent([
                'abnormal_reason' => ChargeAbnormalEndEvent::ABNORMAL_REASON_START_CHARGE_FAILED
                , 'order_id' => $this->request->transaction_serial_number
                , 'corp_id' => $this->orderData['corp_id']
                , 'station_id' => $this->orderData['station_id']
                , 'pay_money' => 0
                , 'electricity_total' => 0
                , 'abnormal_sub_reason' => $this->request->failure_reason
            ]));
        } catch (Throwable $e) {
            // 在事件中运行的业务都是次要业务，若是出现异常情况，
            // 只记录日志，不做其他处理，避免影响充电主流程。
            $this->socketLogCollector->collect($e);
        }

        try {
            event('DeviceAbnormality', new DeviceAbnormalityEvent([
                'abnormality_type' => DeviceAbnormalityEvent::AbnormalityTypeRemoteStartChargingCommandResponse,
                'pile_id' => $this->orderData['piles_id'],
                'shot_id' => $this->orderData['shot_id'],
                'corp_id' => $this->orderData['corp_id'],
                'station_id' => $this->orderData['station_id'],
                'start_time' => time(),
                'abnormal_cause' => (string)$this->request->failure_reason
            ]));
        } catch (Throwable $e) {
            // 在事件中运行的业务都是次要业务，若是出现异常情况，
            // 只记录日志，不做其他处理，避免影响充电主流程。
            $this->socketLogCollector->collect($e);
        }
    }

    protected function getFailureReasonText(): string
    {
        $faults = [
            0 => '无',
            1 => '设备编号不匹配',
            2 => '枪已在充电',
            3 => '设备故障',
            4 => '设备离线',
            5 => '未插枪',
            6 => '充电桩未知状态',
            7 => '前一个订单尚未结束',
            100 => '设备升级中',
            101 => '设置计费模型失败',
            0x102 => '设备编号不匹配',
            0x103 => '枪已在充电',
            0x104 => '设备故障',
            0x105 => '设备离线',
            0x106 => '未插枪',
            0x107 => '地线丢失',
            0x108 => '剩余电流异常',
            0x109 => '未知异常'
        ];
        return $faults[$this->request->failure_reason];
    }

    protected function onStartChargeFailed(): void
    {
        $order_model = app(Order::class);

        $res = $order_model->updateStatus(
            $this->request->transaction_serial_number,
            Order::StatusAbnormal,
            $this->getFailureReasonText(),
            $this->request->abnormal_alarm_node
        );
        $this->socketLogCollector->collectorRunLog('远程启动充电命令回复:异常，更改状态为6，并解冻=》' . $this->request->transaction_serial_number . '，结果：' . $res);

        // 触发异常结束事件
        $this->triggerAbnormalEndEvent();

        // 推送Socket消息
        $this->pushSocketMessage();
    }

    protected function onStartChargeSuccess(): void
    {
        // TODO:临时增加，还需要完善。
        (new ShotsStatus())->updateWorkStatus($this->orderData['shot_id'], ShotsStatus::WorkStatusCharging);

        $order_model = app(Order::class);
        if ($this->orderData['type'] === Order::TypeChargeNow) {
            $status = Order::StatusCharging;
        } else {
            $status = Order::StatusReservation;
        }
        $res = $order_model->updateStatusAndTransStartTime(
            $this->request->transaction_serial_number,
            $status,
            '收到远程启动充电命令回复',
            date('Y-m-d H:i:s')
        );
        $this->socketLogCollector->collectorRunLog('远程启动充电命令回复:正常，更改状态为充电中3=》' . $this->request->transaction_serial_number . '，结果：' . $res);

        // 推送Socket消息
        $this->pushSocketMessage();

        // 触发充电正常开启事件
        $this->triggerChargeNormalStartEvent();
    }

    protected function triggerChargeNormalStartEvent(): void
    {
        try {
            event('ChargeNormalStart', new ChargeNormalStartEvent([
                'order_id' => $this->request->transaction_serial_number,
                'pile_id' => $this->orderData['piles_id'],
                'shot_id' => $this->orderData['shot_id'],
                'corp_id' => $this->orderData['corp_id'],
                'station_id' => $this->orderData['station_id'],
                'start_time' => time(),
                'user_id' => $this->orderData['user_id']
            ]));
        } catch (Throwable $e) {
            // 在事件中运行的业务都是次要业务，若是出现异常情况，
            // 只记录日志，不做其他处理，避免影响充电主流程。
            $this->socketLogCollector->collect($e);
        }
    }

    protected function pushSocketMessage(): void
    {
//        $user_data = cache('充电桩服务_' . $this->request->request_id);
        $user_data = app(OrderContext::class)->getSequenceNumberToContextMap((int)$this->request->request_id);


        $message = [
            'type' => 'remote_start_charging_command_response',
            'msg' => '远程启动充电命令回复',
            'data' => $this->request->toArray()
        ];

        switch ($user_data['type']) {
            case '后台':
                SendAdmin::send_group($user_data['token'], $message);
                break;
            case '小程序':
                SendApplet::send_uid($user_data['user_id'], $message);
                break;
        }
    }

}
