<?php
/** @noinspection PhpUnused */

namespace app\common\lib\charging\command;

use app\common\cache\base\ChargingContextData;
use app\common\lib\charging\command\request\StartChargeCommandRequest;
use app\common\lib\charging\sdk\request\StartChargeCommandRequest as StartChargeCommandRequestSDK;
use app\common\lib\charging\sdk\StartChargeCommand as StartChargeCommandSDK;
use app\common\lib\ExceptionLogCollector;
use app\common\lib\SendAdmin;
use app\common\lib\SendApplet;
use app\common\model\Order;
use app\event\ChargeAbnormalEndEvent;
use Throwable;
use Workerman\Connection\AsyncTcpConnection;

class StartChargeCommand
{
    protected StartChargeCommandRequest $request;
    protected array $order;

    public function __construct(StartChargeCommandRequest $request, array $order)
    {
        $this->request = $request;
        $this->order = $order;
    }

    public function run(): string
    {
        //lwj 2023.10.8 修改
//        if (!empty($this->request->line) && !empty($this->request->centralized_controller_id)) {
//            // 能源路由器开始充电处理流程
//            return $this->energyRouterStartChargeHandler();
//        }

        // 不包含能源路由器的开始充电处理流程
        return $this->startChargeHandler();
    }


    // 触发事件: 调用开始充电API失败
    protected function triggerEventStartChargeApiFailed(): void
    {
        try {
            event('ChargeAbnormalEnd', new ChargeAbnormalEndEvent([
                'abnormal_reason' => ChargeAbnormalEndEvent::ABNORMAL_REASON_START_CHARGE_API_FAILED,
                'order_id' => $this->request->order_id,
                'corp_id' => $this->order['corp_id'],
                'station_id' => $this->order['station_id'],
                'pay_money' => 0,
                'electricity_total' => 0
            ]));
        } catch (Throwable $e) {
            // 在事件中运行的业务都是次要业务，若是出现异常情况，
            // 只记录日志，不做其他处理，避免影响充电主流程。
            ExceptionLogCollector::collect($e);
        }
    }

    // 触发事件: 获取充电桩功率接口失败
    protected function triggerEventGetPilePowerApiFailed(): void
    {
        try {
            event('ChargeAbnormalEnd', new ChargeAbnormalEndEvent([
                'abnormal_reason' => ChargeAbnormalEndEvent::ABNORMAL_REASON_GET_PILE_POWER_API_FAILED
                , 'order_id' => $this->request->order_id
                , 'corp_id' => $this->order['corp_id']
                , 'station_id' => $this->order['station_id']
                , 'pay_money' => 0
                , 'electricity_total' => 0
            ]));
        } catch (Throwable $e) {
            // 在事件中运行的业务都是次要业务，若是出现异常情况，
            // 只记录日志，不做其他处理，避免影响充电主流程。
            ExceptionLogCollector::collect($e);
        }
    }

    protected function energyRouterStartChargeHandler(): string
    {
        // 向 设备管理服务 发送：获取充电桩开启功率 0xDB
        $res = send_http_post_to_centralized_api(
            'get_charging_station_power_output',
            [
                'transaction_serial_number' => $this->request->order_id,
                'centralized_controller_id' => $this->request->centralized_controller_id,
                'line' => $this->request->line,
                'shots_ids' => (string)$this->order['shot_id'],
            ],
            $this->request->user_data->toArray()
        );
        if (!$res) {
            // 更新订单数据：状态=90，描述：发送到集中控制器失败
            $OrderMadel = app(Order::class);
            $res2 = $OrderMadel->updateStatus($this->request->order_id, Order::StatusAbnormal, '发送到集中控制器失败');

            echo get_date(2) . $this->request->order_id . '==》发送到集中控制器失败==>写入订单结果：' . json_encode_cn($res2) . PHP_EOL;

            $this->triggerEventGetPilePowerApiFailed();

            $this->sendSocketMessage([
                'type' => '运营平台远程控制启机-失败',
                'result' => 'error',
                'msg' => '发送到集中控制器失败',
                'order_id' => $this->request->order_id,
            ]);
        } else {
            // 向 开始充电定时器进程 发送：接收集中控制器
            $this->send_start_charging_timer([
                'type' => '接收集中控制器',
                'order_id' => $this->request->order_id,
                'data' => $this->request->toArray(),
                'order' => $this->order,
                'user_data' => $this->request->user_data->toArray(),
            ]);

            // 向 小程序Socket客户端 发送消息：运营平台远程控制启机-成功
            $this->sendSocketMessage([
                'type' => '运营平台远程控制启机-成功',
                'result' => 'success',
                'msg' => '发送到集中控制器成功',
                'order_id' => $this->request->order_id,
            ]);
        }

        // 返回响应
        return res_success_text([], '发送到集中控制器-完成');
    }

    protected function startChargeHandler(): string
    {
        // 向 设备管理服务 发送请求：运营平台远程控制启机 0x34
        $res3 = (new StartChargeCommandSDK(
            new StartChargeCommandRequestSDK([
                'type' => $this->order['type'],
                'order_id' => $this->request->order_id,
                'piles_id' => $this->order['piles_id'],
                'sequence' => $this->order['sequence'],
                'user_id' => $this->order['user_id'],
                'balance' => $this->order['balance'],
                'reservation_time' => $this->order['reservation_time'],
                'context' => new ChargingContextData([
                    'token' => $this->request->user_data->token
                    , 'user_id' => $this->request->user_data->user_id
                    , 'type' => $this->request->user_data->type
                    , 'order_id' => $this->request->user_data->order_id
                    , 'corp_id' => $this->request->user_data->corp_id
                    , 'station_id' => $this->request->user_data->station_id
                ])
            ])
        ))->send();

        if ($res3) {
            $this->sendSocketMessage([
                'type' => '运营平台远程控制启机-成功',
                'result' => 'success',
                'msg' => '发起充电成功',
                'order_id' => $this->request->order_id,
            ]);
        } else {
            $OrderMadel = app(Order::class);
            $res2 = $OrderMadel->updateStatus(
                $this->request->order_id,
                Order::StatusAbnormal,
                '发起充电失败'
            );
            echo get_date(2) . $this->request->order_id . '==》发起充电失败==>写入订单结果：' . json_encode_cn($res2) . PHP_EOL;

            // 触发事件: 调用开始充电API失败
            $this->triggerEventStartChargeApiFailed();

            $this->sendSocketMessage([
                'type' => '运营平台远程控制启机-失败',
                'result' => 'error',
                'msg' => '发起充电失败',
                'order_id' => $this->request->order_id,
            ]);
        }

        return res_success_text([], '发起充电-完成');
    }

    protected function sendSocketMessage(array $msgData): void
    {
        switch ($this->request->user_data->type) {
            case '后台':
                SendAdmin::send_group($this->request->user_data->token, $msgData);
                break;
            case '小程序':
                SendApplet::send_uid($this->request->user_data->user_id, $msgData);
                break;
        }
    }


    /**
     * 发送到开始充电定时器
     * lwj 2023.10.7 新增
     * @param array $task_data 数据
     * @return void
     */
    private function send_start_charging_timer(array $task_data): void
    {
        try {
            // 与远程task服务建立异步连接，ip为远程task服务的ip，如果是本机就是127.0.0.1，如果是集群就是lvs的ip
            $task_connection = new AsyncTcpConnection(config('my.StartChargingTimerApi'));
            $send_data = json_encode_cn($task_data);
            trace('发送到开始充电定时器=>参数：==》' . $send_data, '开始充电任务');
            $task_connection->send($send_data);
            // 异步获得结果
            $task_connection->onMessage = function (AsyncTcpConnection $task_connection, $task_result) {
                trace('发送到开始充电定时器=>结果：==》' . json_encode_cn($task_result), '开始充电任务');
                // 获得结果后记得关闭异步连接
                $task_connection->close();
            };
            // 执行异步连接
            $task_connection->connect();
        } catch (Throwable $e) {
            trace('发送到开始充电定时器=>异常：==》' . $e->getMessage(), '开始充电任务');
        }
    }
}