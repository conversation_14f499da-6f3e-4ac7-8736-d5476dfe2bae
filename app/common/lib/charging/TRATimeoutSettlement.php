<?php
/** @noinspection PhpUnused */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
declare (strict_types=1);

namespace app\common\lib\charging;

use app\common\lib\charging\request\TRATimeoutSettlementRequest;
use app\common\lib\ExceptionLogCollector;
use app\common\lib\SendAdmin;
use app\common\lib\SendApplet;
use app\common\log\SocketLogCollector;
use app\common\model\Order;
use app\common\model\Order as OrderModel;
use app\common\model\ShotsStatus;
use app\ms\Api;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\TransactionRecord;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use RuntimeException;

/**
 * @class 交易记录应答超时结算
 * 应用场景：
 *      用户或系统发出停止充电的指令时候，充电桩没有在指定时间内应答交易记录。
 *      故此进行强制结算，避免由于充电桩的不可控因素导致订单没有结算。
 */
class TRATimeoutSettlement
{
    use AlgorithmSettlement;

    public const ReturnCodeBusinessAbnormal = 400;
    public const ReturnCodeDatabaseAbnormal = 501;
    protected array $orderData = [];

    protected TRATimeoutSettlementRequest $request;
    protected SocketLogCollector $socketLogCollector;

    protected ?SettlementResult $settlementResult = null;

    protected int $chargeDuration = 0;

    public function __construct(TRATimeoutSettlementRequest $request)
    {
        $this->request = $request;
        $this->socketLogCollector = new SocketLogCollector();
    }

    /**
     * 充电订单结算
     *
     * @return void
     * @throws DbException
     */
    public function run(): void
    {
        // 加载订单数据
        $this->loadOrderData();
        // 计算总费用
        $this->calculateTotalCost();
        // 更新订单数据
        $this->updateOrderData();
        // 更新用户数据
        $this->updateUserBalance();
        // TODO:临时增加，还需要完善。
        (new ShotsStatus())->updateWorkStatus($this->orderData['shot_id'], ShotsStatus::WorkStatusIdle);
        // 推送Socket消息
        $this->pushSocketMessage();
        // 触发事件
        $this->triggerEvent();
        // 发送微信通知
        $this->sendWechatEndNotification($this->orderData, $this->socketLogCollector);
    }

    protected function triggerEvent(): void
    {
        // todo: 完善补充参数
//        try {
//            event('ChargeNormalEnd', new ChargeNormalEndEvent([
//                'order_id' => $this->request->order_id,
//                'corp_id' => $this->orderData['corp_id'],
//                'station_id' => $this->orderData['station_id'],
//                'pay_money' => $this->settlementResult->total_cost,
//                'electricity_total' => $this->orderData['electricity_total'],
//                'valley_electricity' => 0,
//                'flat_electricity' => 0,
//                'peak_electricity' => 0,
//                'sharp_electricity' => 0,
//                'service_money' => 0,
//                'charge_duration' => $this->chargeDuration,
//                'start_time' => strtotime($this->orderData['trans_start_time']),
//                'end_time' => time()
//            ]));
//        } catch (\Throwable $e) {
//            // 在事件中运行的业务都是次要业务，若是出现异常情况，
//            // 只记录日志，不做其他处理，避免影响充电主流程。
//            ExceptionLogCollector::collect($e);
//        }

        // TODO: 这里理论上应该要触发充电枪状态变更的，只不过目前还没有增加充电枪上线后的回调机制，暂时注释。
//        $ShotsStatusCache = app(ShotsStatusCache::class);
//        $beforeStatus = $ShotsStatusCache->getStatus($this->orderData['shot_id']);
//        if ($beforeStatus !== Shots::StatusOffline) {
//            $ShotsStatusCache->setStatus($this->orderData['shot_id'], Shots::StatusOffline);
//
//            try {
//                event('ShotsStatusChange', new ShotsStatusChangeEvent([
//                    'corp_id' => $this->orderData['corp_id'],
//                    'station_id' => $this->orderData['station_id'],
//                    'pile_id' => $this->orderData['piles_id'],
//                    'shot_id' => $this->orderData['shot_id'],
//                    'before_status' => $beforeStatus,
//                    'after_status' => Shots::StatusOffline
//                ]));
//            } catch (\Throwable $e) {
//                // 在事件中运行的业务都是次要业务，若是出现异常情况，
//                // 只记录日志，不做其他处理，避免影响充电主流程。
//                ExceptionLogCollector::collect($e);
//            }
//        }


        // TODO: 增加告警记录

//        try {
//            event('DeviceAbnormality', new DeviceAbnormalityEvent([
//                'abnormality_type' => DeviceAbnormalityEvent::AbnormalityTypeTransactionRecord,
//                'pile_id' => $this->orderData['piles_id'],
//                'shot_id' => $this->orderData['shot_id'],
//                'corp_id' => $this->orderData['corp_id'],
//                'station_id' => $this->orderData['station_id'],
//                'start_time' => time(),
//                'abnormal_cause' => $this->transactionRecordRequest->reason_for_stop
//            ]));
//        } catch (\Throwable $e) {
//            // 在事件中运行的业务都是次要业务，若是出现异常情况，
//            // 只记录日志，不做其他处理，避免影响充电主流程。
//            ExceptionLogCollector::collect($e);
//        }
    }

    protected function pushSocketMessage(): void
    {
//        $user_data = cache('充电桩服务订单_' . $this->request->order_id);
        $user_data = app(\app\common\context\Order::class)->getOrderIdToContextMap($this->request->order_id);
        switch ($user_data['type']) {
            case '后台':
                SendAdmin::send_group($user_data['token'], [
                    'type' => 'auto_settlement',
                    'msg' => '自动结算',
                    'data' => [
                        'order_id' => $this->request->order_id
                    ]
                ]);
                break;
            case '小程序':
                SendApplet::send_uid($user_data['user_id'], [
                    'type' => 'auto_settlement',
                    'msg' => '自动结算',
                    'data' => [
                        'order_id' => $this->request->order_id
                    ]
                ]);
                break;
        }
    }

    protected function loadOrderData(): void
    {
        try {
            $Order = new Order();
            $orderData = $Order->where('id', $this->request->order_id)->find();
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            throw new RuntimeException('数据库异常', self::ReturnCodeDatabaseAbnormal);
        }

        if (empty($orderData)) {
            throw new RuntimeException('订单不存在', self::ReturnCodeBusinessAbnormal);
        }

        $orderData['discount'] = (float) $orderData['discount'];
        $this->orderData = $orderData->toArray();

        if ($this->orderData['status'] >= OrderModel::StatusComplete) {
            throw new RuntimeException('订单已结算', self::ReturnCodeBusinessAbnormal);
        }
    }

    /**
     * 更新订单数据
     * lwj 2024.5.10 修改
     */
    protected function updateOrderData(): void
    {
        $endTime = time();
        // 充电耗时
        if (empty($this->orderData['trans_start_time'])) {
            $startTime = $endTime;
        } else {
            $startTime = strtotime($this->orderData['trans_start_time']);
        }
        $this->chargeDuration = $endTime - $startTime;

        $electricity_total = $this->orderData['sharp_electricity'] +
            $this->orderData['peak_electricity'] +
            $this->orderData['flat_electricity'] +
            $this->orderData['valley_electricity'];

        $update_data = [
            'money' => $this->settlementResult->original_total_cost,
            'pay_money' => $this->settlementResult->total_cost,
            'coupon_money' => $this->settlementResult->original_total_cost - $this->settlementResult->total_cost,
            'settled_amount' => $this->settlementResult->total_cost,
            'trans_end_time' => date('Y-m-d H:i:s', $endTime),
            'msg' => '系统自动结算',
            'reason_for_stop_text' => '系统自动结算',
            'electricity_total' => $electricity_total,
            'electricity_price' => $this->settlementResult->total_charge_cost,
            'ser_price' => $this->settlementResult->total_service_cost,
            'charge_duration' => $this->chargeDuration,
            'freeze_status' => Order::FreezeStatusNot,
            'status' => Order::StatusCompulsorySettlement,
            'clearing_electricity_price' => $this->settlementResult->total_charge_cost,
            'clearing_ser_price' => $this->settlementResult->total_service_cost,
            'abnormal_alarm_node' => TransactionRecord::AbnormalAlarmNodeChargeManage
        ];

        trace('交易记录确认:更新数据=》' . $this->request->order_id . '===》' . json_encode_cn($update_data), '充电桩Socket');

        $order_model = new OrderModel();
        $order_model->where('id', $this->request->order_id)->update($update_data);
        $this->orderData = array_merge($this->orderData, $update_data);
        // 同步订单数据到订单微服务
        Api::send('/order/orders','PUT',$order_model->toArray(),['Authorization' => 'mpQWnQ0zZ7KGl3JEV5DeUoeNXec0mohT'],'',true);
    }

    /**
     * 更新用户余额
     *
     * @return void
     * @throws DbException
     */
    protected function updateUserBalance(): void
    {
        if ($this->settlementResult->total_cost > 0) {
            $order_model = app(Order::class);
            $amountCharged = $order_model->sumOrderTotalAmountCharged($this->orderData['user_id']);

            Db::name('users')
                ->where('id', $this->orderData['user_id'])
                ->update([
                    'balance' => Db::raw('balance-' . $this->settlementResult->total_cost),
                    'amount_charged' => $amountCharged,
                    'update_time' => date('Y-m-d H:i:s')
                ]);

            $add_data = [
                'user_id' => $this->orderData['user_id'],
                'amount' => -$this->settlementResult->total_cost,
                'memo' => '订单【' . $this->request->order_id . '】付款：' . $this->settlementResult->total_cost . '分',
                'type' => '订单自动结算',
            ];
            Db::name('user_balance_log')->insert($add_data);
        }
    }


    protected function calculateTotalCost(): void
    {
        $this->settlementResult = $this->abnormalSettlement($this->orderData);
    }
}