<?php /** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\common\lib\wechat\answer;

use app\common\lib\ExceptionLogCollector;
use app\common\lib\wechat\BlockchainElectronicInvoiceClient;
use app\common\lib\wechat\ElectronicInvoiceClient;
use app\common\lib\wechat\entity\answer\AnswerInterface;
use app\common\lib\wechat\entity\answer\TitleCompleteAnswer;
use app\common\lib\wechat\entity\answer\UserTitleAnswer;
use app\common\lib\wechat\entity\request\IssueFapiaoInfo;
use app\common\lib\wechat\entity\request\IssueItem;
use app\common\lib\wechat\entity\request\UserTitleEntity;
use app\common\model\ElectronicInvoiceApplyRecord;
use think\facade\Db;

class TitleCompleteAnswerHandler extends AnswerHandler
{
    /**
     * 业务处理
     *
     * @param AnswerInterface $answer
     * @return bool
     * @throws \Throwable
     */
    public function businessHandler(AnswerInterface $answer): bool
    {
        /**
         * @var TitleCompleteAnswer $answer
         */
        try {
            Db::startTrans();
            // 获取用户填写的抬头信息
            $BlockchainElectronicInvoiceClient = app(BlockchainElectronicInvoiceClient::class);
            $response = $BlockchainElectronicInvoiceClient->getUserTitle(
                $answer->fapiao_apply_id,
                ElectronicInvoiceClient::SceneWithoutWechatPay
            );
            if ($response->isFailed === true || $response->httpCode !== 200) {
                ExceptionLogCollector::recordErrorLog('获取用户填写的抬头失败 = ' . json_encode($response), debug_backtrace());
                throw new \RuntimeException('获取用户填写的抬头');
            }
            $UserTitleAnswer = new UserTitleAnswer($response->analysisJsonBody());

            // 触发事件：WechatFapiaoTitleComplete
//            event('WechatFapiaoTitleComplete', new TitleCompleteEvent([
//                'TitleCompleteAnswer' => $answer,
//                'UserTitleAnswer' => $UserTitleAnswer
//            ]));


            $ElectronicInvoiceApplyRecord = app(ElectronicInvoiceApplyRecord::class);
            $totalAmount = $ElectronicInvoiceApplyRecord->getTotalAmount($answer->fapiao_apply_id);

            // 个人
            if ($UserTitleAnswer->type == UserTitleAnswer::TypeIndividual) {
                $ElectronicInvoiceApplyRecord->updateTitleInfo(
                    $answer->fapiao_apply_id,
                    ElectronicInvoiceApplyRecord::TitleTypeIndividual,
                    $UserTitleAnswer->name
                );
            } else if ($UserTitleAnswer->type === UserTitleAnswer::TypeOrganization) {
                $ElectronicInvoiceApplyRecord->updateTitleInfo(
                    $answer->fapiao_apply_id,
                    ElectronicInvoiceApplyRecord::TitleTypeOrganization,
                    $UserTitleAnswer->name,
                    $UserTitleAnswer->taxpayer_id
                );
            } else {
                ExceptionLogCollector::recordErrorLog('未知抬头类型');
            }


            // 开具发票(暂时废弃) 不再通过微信区块链开具发票,而是在手动开具发票后在后台审批时传入发票文件
            /*$UserTitleEntity = new UserTitleEntity(
                $UserTitleAnswer->type
                , $UserTitleAnswer->name
            );
            if ($UserTitleAnswer->type == UserTitleAnswer::TypeOrganization) {
                $UserTitleEntity->taxpayer_id = $UserTitleAnswer->taxpayer_id;
            }
            // TODO: 这个先写死在这里，测试完成了在移到配置文件中。
            $item = new IssueItem('1100101020100000000', 1, $totalAmount, false);
            $IssueFapiaoInfo = new IssueFapiaoInfo(
                $BlockchainElectronicInvoiceClient->generateFapiaoApplyId(),
                $totalAmount,
                [$item]
            );
            $fpResponse = $BlockchainElectronicInvoiceClient->fapiaoApplications(
                ElectronicInvoiceClient::SceneWithoutWechatPay,
                $answer->fapiao_apply_id,
                $UserTitleEntity,
                [$IssueFapiaoInfo]
            );

            if ($fpResponse->isFailed === true || $fpResponse->httpCode !== 202) {
                ExceptionLogCollector::recordErrorLog('开具发票失败 = ' . json_encode($fpResponse), debug_backtrace());
                throw new \RuntimeException('开具发票失败');
            }*/

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();
            ExceptionLogCollector::collect($e);
            throw $e;
        }


        return true;
    }
}