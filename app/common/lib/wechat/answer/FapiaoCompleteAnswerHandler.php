<?php

namespace app\common\lib\wechat\answer;

use app\common\lib\ExceptionLogCollector;
use app\common\lib\wechat\entity\answer\AnswerInterface;
use app\common\lib\wechat\entity\answer\FapiaoCompleteAnswer;
use app\common\lib\wechat\event\FapiaoCompleteEvent;
use think\facade\Db;

class FapiaoCompleteAnswerHandler extends AnswerHandler
{
    /**
     * 业务处理
     *
     * @param AnswerInterface $answer
     * @return bool
     * @throws \Throwable
     * @noinspection PhpDynamicAsStaticMethodCallInspection
     */
    public function businessHandler(AnswerInterface $answer): bool
    {
        try {
            Db::startTrans();
            /**
             * @var FapiaoCompleteAnswer $answer
             */
            // 触发事件：WechatFapiaoComplete
            event('WechatFapiaoComplete', new FapiaoCompleteEvent([
                'answer' => $answer
            ]));

            Db::commit();
        } catch (\Throwable $e) {
            Db::rollback();
            ExceptionLogCollector::collect($e);
            throw $e;
        }


        return true;
    }
}