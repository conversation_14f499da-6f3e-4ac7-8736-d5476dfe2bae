<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat;

class Sign
{
    protected string $mch_id = '';
    protected string $api_client_key = '';
    protected string $api_client_key_sn = '';
    protected string $api_client_public_key = '';

    public function __construct()
    {
        $this->mch_id = config('wechat.mch_id');
        $this->api_client_key_sn = config('wechat.apiclient_key_sn');
        $this->api_client_key = config('wechat.apiclient_key');
        $this->api_client_public_key = config('wechat.api_client_public_key');
    }

    public function generateSign(string $method, string $url, string $body = ''): string
    {
        // 时间戳
        $timestamp = time();
        // 随机字符串
        $nonce = rand(10000, 99999);

        $url_parts = parse_url($url);
        $canonical_url = ($url_parts['path'] . (!empty($url_parts['query']) ? "?${url_parts['query']}" : ""));
        $message = $method . "\n" .
            $canonical_url . "\n" .
            $timestamp . "\n" .
            $nonce . "\n" .
            $body . "\n";

        openssl_sign($message, $raw_sign, $this->api_client_key, OPENSSL_ALGO_SHA256);
        $sign = base64_encode($raw_sign);

        $schema = 'WECHATPAY2-SHA256-RSA2048';
        $token = sprintf('mchid="%s",nonce_str="%s",timestamp="%d",serial_no="%s",signature="%s"',
            $this->mch_id, $nonce, $timestamp, $this->api_client_key_sn, $sign);


        return sprintf('%s %s', $schema, $token);
    }

    /**
     * 验证签名
     *
     * @param int $timestamp 应答的时间戳(header.wechatpay-timestamp)
     * @param string $nonce 应答的随机字符串(header.wechatpay-nonce)
     * @param string $body 应答的消息主体(body)
     * @param string $signature 应答的签名(header.wechatpay-signature)
     * @return bool
     */
    public function verifySignature(int $timestamp, string $nonce, string $body, string $signature): bool
    {
        $message = sprintf("%d\n%s\n%s\n", $timestamp, $nonce, $body);

        return openssl_verify(
            $message,
            base64_decode($signature),
            $this->api_client_public_key,
            OPENSSL_ALGO_SHA256
        );
    }
}