<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat;

use app\common\lib\http\Response;
use app\common\lib\wechat\entity\request\IssueFapiaoInfo;
use app\common\lib\wechat\entity\request\UserTitleEntity;

// 微信支付平台 - 区块链电子发票API
class BlockchainElectronicInvoiceClient extends ElectronicInvoiceClient
{
    public function getTaxCodes(int $offset = 0, int $limit = 10): Response
    {
        $response = $this->httpClient->setMethod('GET')->setProtocol('https')
            ->setDomain('api.mch.weixin.qq.com')
            ->setUri('v3/new-tax-control-fapiao/merchant/tax-codes')
            ->setQuery([
                'offset' => $offset,
                'limit' => $limit
            ])
            ->setHeader([
                'User-Agent' => env('UA'),
                'Content-type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => $this->sign->generateSign(
                    $this->httpClient->getMethod(),
                    $this->httpClient->getUrl(),
                    $this->httpClient->getBody()
                )
            ])->sendRequest();

        $this->httpClient->reset();

        return $response;
    }

    public function getBaseInformation(?string $subMchid = null): Response
    {
        $query = [];
        if (!is_null($subMchid)) {
            $query['sub_mchid'] = $subMchid;
        }
        $response = $this->httpClient->setMethod('GET')->setProtocol('https')
            ->setDomain('api.mch.weixin.qq.com')
            ->setUri('v3/new-tax-control-fapiao/merchant/base-information')
            ->setQuery($query)
            ->setHeader([
                'User-Agent' => env('UA'),
                'Content-type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => $this->sign->generateSign(
                    $this->httpClient->getMethod(),
                    $this->httpClient->getUrl(),
                    $this->httpClient->getBody()
                )
            ])->sendRequest();

        $this->httpClient->reset();

        return $response;
    }

    public function fapiaoApplications(
        string          $scene,
        string          $fapiaoApplyId,
        UserTitleEntity $buyerInformation,
        array           $fapiaoInformation
    ): Response
    {
        /**
         * @var IssueFapiaoInfo $value
         */
        foreach ($fapiaoInformation as &$value) {
            $value = $value->toArray();
        }

        $response = $this->httpClient->setMethod('POST')->setProtocol('https')
            ->setDomain('api.mch.weixin.qq.com')
            ->setUri('v3/new-tax-control-fapiao/fapiao-applications')
            ->setBody(json_encode([
                'scene' => $scene,
                'fapiao_apply_id' => $fapiaoApplyId,
                'buyer_information' => $buyerInformation->toArray(),
                'fapiao_information' => $fapiaoInformation
            ]))
            ->setHeader([
                'User-Agent' => env('UA'),
                'Content-type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => $this->sign->generateSign(
                    $this->httpClient->getMethod(),
                    $this->httpClient->getUrl(),
                    $this->httpClient->getBody()
                ),
                'Wechatpay-Serial' => config('wechat.weixin_pingtai_sn')
            ])->sendRequest();

        $this->httpClient->reset();


        return $response;
    }
}