<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat;

use SodiumException;
use function base64_decode;
use function openssl_get_cipher_methods;
use function Sodium\crypto_aead_aes256gcm_decrypt;
use function Sodium\crypto_aead_aes256gcm_is_available;
use function sodium_crypto_aead_aes256gcm_decrypt;
use function sodium_crypto_aead_aes256gcm_is_available;

class AesUtil
{
    /**
     * AES key
     *
     * @var string
     */
    private string $aesKey;

    const KEY_LENGTH_BYTE = 32;
    const AUTH_TAG_LENGTH_BYTE = 16;

    public function __construct()
    {
        $this->aesKey = config('wechat.api_key_v3');
    }



    // {"data":[{
    //"serial_no":"5157F09EFDC096DE15EBE81A47057A7232F1B8E1",
    //"effective_time":"2018-03-26T11:39:50+08:00",
    //"expire_time":"2023-03-25T11:39:50+08:00",
    //"encrypt_certificate":{
    //"algorithm":"AEAD_AES_256_GCM",
    //"nonce":"4de73afd28b6",
    //"associated_data":"certificate",
    //"ciphertext":"..."
    //}}]}

    /**
     * Decrypt AEAD_AES_256_GCM ciphertext
     *
     * @param string $associatedData AES GCM additional authentication data
     * @param string $nonceStr AES GCM nonce
     * @param string $ciphertext AES GCM cipher text
     *
     * @return string|bool      Decrypted string on success or FALSE on failure
     * @throws SodiumException
     */
    public function decryptToString(string $associatedData, string $nonceStr, string $ciphertext): string|bool
    {
        $ciphertext = base64_decode($ciphertext);
        if (strlen($ciphertext) <= self::AUTH_TAG_LENGTH_BYTE) {
            return false;
        }

        // ext-sodium (default installed on >= PHP 7.2)
        if (function_exists('\sodium_crypto_aead_aes256gcm_is_available') && sodium_crypto_aead_aes256gcm_is_available()) {
            return sodium_crypto_aead_aes256gcm_decrypt($ciphertext, $associatedData, $nonceStr, $this->aesKey);
        }

        // ext-libsodium (need install libsodium-php 1.x via pecl)
        if (function_exists('\Sodium\crypto_aead_aes256gcm_is_available') && crypto_aead_aes256gcm_is_available()) {
            return crypto_aead_aes256gcm_decrypt($ciphertext, $associatedData, $nonceStr, $this->aesKey);
        }

        // openssl (PHP >= 7.1 support AEAD)
        if (PHP_VERSION_ID >= 70100 && in_array('aes-256-gcm', openssl_get_cipher_methods())) {
            $ctext = substr($ciphertext, 0, -self::AUTH_TAG_LENGTH_BYTE);
            $authTag = substr($ciphertext, -self::AUTH_TAG_LENGTH_BYTE);

            return \openssl_decrypt(
                $ctext,
                'aes-256-gcm',
                $this->aesKey,
                \OPENSSL_RAW_DATA,
                $nonceStr,
                $authTag,
                $associatedData
            );
        }

        throw new \RuntimeException('AEAD_AES_256_GCM需要PHP 7.1以上或者安装libsodium-php');
    }
}