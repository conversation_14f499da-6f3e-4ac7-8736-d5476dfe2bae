<?php

namespace app\common\lib\wechat\entity\answer;

class TitleCompleteAnswer implements AnswerInterface
{
    public string $mchid;
    public ?string $sub_mchid = null;
    public string $fapiao_apply_id;
    public string $apply_time;

    public function __construct(array $data)
    {
        $this->mchid = $data['mchid'];
        if (isset($data['sub_mchid']) === true) {
            $this->sub_mchid = $data['sub_mchid'];
        }
        $this->fapiao_apply_id = $data['fapiao_apply_id'];
        $this->apply_time = $data['apply_time'];
    }
}