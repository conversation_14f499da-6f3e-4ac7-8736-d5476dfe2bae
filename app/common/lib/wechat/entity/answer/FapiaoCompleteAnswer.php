<?php

namespace app\common\lib\wechat\entity\answer;

class FapiaoCompleteAnswer implements AnswerInterface
{
    public string $mchid;
    public string $fapiao_apply_id;
    public array $fapiao_information;

    public function __construct(array $data)
    {
        $this->mchid = $data['mchid'];
        $this->fapiao_apply_id = $data['fapiao_apply_id'];
        foreach ($data['fapiao_information'] as $value) {
            $this->fapiao_information[] = new FapiaoInformation($value);
        }
    }
}