<?php

namespace app\common\lib\wechat\entity\answer;

class UserTitleAnswer
{
    // 抬头类型
    public const TypeIndividual = 'INDIVIDUAL'; // 个人
    public const TypeOrganization = 'ORGANIZATION'; // 单位

    public string $type;
    public string $name;
    public string $taxpayer_id;
    public string $address;
    public string $telephone;
    public string $bank_name;
    public string $bank_account;
    public string $phone;
    public string $email;

    public function __construct(array $data)
    {
        $this->type = $data['type'];
        $this->name = $data['name'];
        $this->taxpayer_id = $data['taxpayer_id'] ?? '';
        $this->address = $data['address'] ?? '';
        $this->telephone = $data['telephone'] ?? '';
        $this->bank_name = $data['bank_name'] ?? '';
        $this->bank_account = $data['bank_account'] ?? '';
        $this->phone = $data['phone'] ?? '';
        $this->email = $data['email'] ?? '';
    }
}