<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

/**
 * 电子发票文件元信息
 */
class FapiaoFileMetaInfo extends BaseEntity
{
    /**
     * @var string|null 【子商户号】微信支付分配的子商户号，服务商模式下必传
     */
    public ?string $sub_mchid = null;

    /**
     * @var string 【文件类型】发票文件的类型
     * PDF - PDF文件类型
     */
    public string $file_type;

    /**
     * @var string 【文件摘要算法】文件摘要算法
     * SM3 - 国密SM3算法
     */
    public string $digest_algorithm;

    /**
     * @var string 【文件摘要】根据文件摘要算法对电子发票文件二进制内容计算出的文件摘要，结果为16进制编码
     */
    public string $digest;

    /**
     * 构造函数
     *
     * @param string $fileType 文件类型，目前支持 PDF
     * @param string $digest 文件摘要（16进制编码）
     * @param string $digestAlgorithm 摘要算法，默认 SM3
     * @param string|null $subMchid 子商户号（服务商模式下必传）
     */
    public function __construct(
        string $fileType,
        string $digest,
        string $digestAlgorithm = 'SM3',
        ?string $subMchid = null
    ) {
        $this->file_type = $fileType;
        $this->digest = $digest;
        $this->digest_algorithm = $digestAlgorithm;
        $this->sub_mchid = $subMchid;
    }

    /**
     * 创建PDF文件元信息
     *
     * @param string $digest 文件摘要
     * @param string|null $subMchid 子商户号
     * @return static
     */
    public static function createPdfMeta(string $digest, ?string $subMchid = null): static
    {
        return new static('PDF', $digest, 'SM3', $subMchid);
    }

    /**
     * 计算文件的SM3摘要
     *
     * @param string $filePath 文件路径
     * @return string 16进制编码的摘要
     */
    public static function calculateSM3Digest(string $filePath): string
    {
        if (!file_exists($filePath)) {
            throw new \RuntimeException("文件不存在: {$filePath}");
        }

        $fileContent = file_get_contents($filePath);
        if ($fileContent === false) {
            throw new \RuntimeException("无法读取文件: {$filePath}");
        }

        // 使用hash函数计算SM3摘要（如果系统支持）
        // 注意：SM3可能需要额外的扩展支持，这里先用sha256作为示例
        // 实际使用时需要根据系统环境选择合适的摘要算法
        if (in_array('sm3', hash_algos())) {
            return hash('sm3', $fileContent);
        } else {
            // 如果不支持SM3，可以考虑使用其他算法或第三方库
            throw new \RuntimeException('系统不支持SM3算法，请安装相应扩展或使用其他摘要算法');
        }
    }
}
