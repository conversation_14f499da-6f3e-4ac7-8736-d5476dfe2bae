<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

/**
 * 销售方信息
 */
class SellerInformation extends BaseEntity
{
    /**
     * @var string 【销售方名称】销售方名称
     */
    public string $name;

    /**
     * @var string 【销售方纳税人识别号】销售方纳税人识别号
     */
    public string $taxpayer_id;

    /**
     * @var string|null 【销售方地址】销售方地址
     */
    public ?string $address = null;

    /**
     * @var string|null 【销售方电话】销售方电话
     */
    public ?string $telephone = null;

    /**
     * @var string|null 【销售方开户银行】销售方开户银行
     */
    public ?string $bank_name = null;

    /**
     * @var string|null 【销售方银行账号】销售方银行账号
     */
    public ?string $bank_account = null;

    /**
     * 构造函数
     *
     * @param string $name 销售方名称
     * @param string $taxpayerId 纳税人识别号
     */
    public function __construct(string $name, string $taxpayerId)
    {
        $this->name = $name;
        $this->taxpayer_id = $taxpayerId;
    }

    /**
     * 设置地址
     *
     * @param string $address 地址
     * @return $this
     */
    public function setAddress(string $address): static
    {
        $this->address = $address;
        return $this;
    }

    /**
     * 设置电话
     *
     * @param string $telephone 电话
     * @return $this
     */
    public function setTelephone(string $telephone): static
    {
        $this->telephone = $telephone;
        return $this;
    }

    /**
     * 设置银行信息
     *
     * @param string $bankName 银行名称
     * @param string $bankAccount 银行账号
     * @return $this
     */
    public function setBankInfo(string $bankName, string $bankAccount): static
    {
        $this->bank_name = $bankName;
        $this->bank_account = $bankAccount;
        return $this;
    }
}
