<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

/**
 * 电子发票卡券信息
 */
class FapiaoCardInfo extends BaseEntity
{
    /**
     * @var string 【电子发票文件ID】上传的电子发票文件对应的ID
     */
    public string $fapiao_media_id;

    /**
     * @var string 【发票号码】发票号码
     */
    public string $fapiao_number;

    /**
     * @var string 【发票代码】发票代码
     */
    public string $fapiao_code;

    /**
     * @var string 【开票时间】开票时间，格式：2020-07-01T12:00:00+08:00
     */
    public string $fapiao_time;

    /**
     * @var string 【校验码】校验码
     */
    public string $check_code;

    /**
     * @var string|null 【密码区】密码区
     */
    public ?string $password = null;

    /**
     * @var int 【价税合计】价税合计，单位：分
     */
    public int $total_amount;

    /**
     * @var int 【税额】税额，单位：分
     */
    public int $tax_amount;

    /**
     * @var int 【金额】金额，单位：分
     */
    public int $amount;

    /**
     * @var SellerInformation 【销售方信息】销售方信息
     */
    public SellerInformation $seller_information;

    /**
     * @var ExtraInformation|null 【额外信息】额外信息
     */
    public ?ExtraInformation $extra_information = null;

    /**
     * @var FapiaoItem[] 【发票行信息】发票行信息列表
     */
    public array $items = [];

    /**
     * @var string|null 【备注】备注
     */
    public ?string $remark = null;

    /**
     * 构造函数
     *
     * @param string $fapiaoMediaId 电子发票文件ID
     * @param string $fapiaoNumber 发票号码
     * @param string $fapiaoCode 发票代码
     * @param string $fapiaoTime 开票时间
     * @param string $checkCode 校验码
     * @param int $totalAmount 价税合计（分）
     * @param int $taxAmount 税额（分）
     * @param int $amount 金额（分）
     * @param SellerInformation $sellerInformation 销售方信息
     * @param array $items 发票行信息
     */
    public function __construct(
        string $fapiaoMediaId,
        string $fapiaoNumber,
        string $fapiaoCode,
        string $fapiaoTime,
        string $checkCode,
        int $totalAmount,
        int $taxAmount,
        int $amount,
        SellerInformation $sellerInformation,
        array $items = []
    ) {
        $this->fapiao_media_id = $fapiaoMediaId;
        $this->fapiao_number = $fapiaoNumber;
        $this->fapiao_code = $fapiaoCode;
        $this->fapiao_time = $fapiaoTime;
        $this->check_code = $checkCode;
        $this->total_amount = $totalAmount;
        $this->tax_amount = $taxAmount;
        $this->amount = $amount;
        $this->seller_information = $sellerInformation;
        
        // 验证发票行信息类型
        foreach ($items as $item) {
            if (!$item instanceof FapiaoItem) {
                throw new \RuntimeException(sprintf('发票行信息必须是%s类型', FapiaoItem::class));
            }
        }
        $this->items = $items;
    }

    /**
     * 设置密码区
     *
     * @param string $password 密码区
     * @return $this
     */
    public function setPassword(string $password): static
    {
        $this->password = $password;
        return $this;
    }

    /**
     * 设置额外信息
     *
     * @param ExtraInformation $extraInformation 额外信息
     * @return $this
     */
    public function setExtraInformation(ExtraInformation $extraInformation): static
    {
        $this->extra_information = $extraInformation;
        return $this;
    }

    /**
     * 添加发票行
     *
     * @param FapiaoItem $item 发票行信息
     * @return $this
     */
    public function addItem(FapiaoItem $item): static
    {
        $this->items[] = $item;
        return $this;
    }

    /**
     * 设置备注
     *
     * @param string $remark 备注
     * @return $this
     */
    public function setRemark(string $remark): static
    {
        $this->remark = $remark;
        return $this;
    }

    /**
     * 格式化开票时间
     *
     * @param int $timestamp 时间戳
     * @return string 格式化后的时间字符串
     */
    public static function formatFapiaoTime(int $timestamp): string
    {
        return date('Y-m-d\TH:i:sP', $timestamp);
    }

    /**
     * 创建当前时间的开票时间
     *
     * @return string 当前时间的开票时间字符串
     */
    public static function getCurrentFapiaoTime(): string
    {
        return self::formatFapiaoTime(time());
    }
}
