<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

class IssueFapiaoInfo extends BaseEntity
{
    // 【商户发票单号】 商户发票单号，唯一标识一张要开具的发票。只能是字母、数字、中划线-、下划线_、竖线|、星号*这些英文半角字符，且该单号在每个商户下必须唯一
    public string $fapiao_id;
    // 【总价税合计】 总价税合计，所有发票行单行金额合计的累加，展示在发票的价税合计处，单位：分 注意：若是微信支付后开票，所有发票的总价税合计之和不能超过对应的微信支付单总金额；若是非微信支付开票，所有发票的总价税合计之和不能超过【获取抬头填写链接】接口中指定的总金额
    public int $total_amount;
    // 【是否以清单形式开具发票】 若商户使用的是区块链电子发票产品，则不支持以清单形式开具发票，该字段不需要传值
    public ?bool $need_list = null;
    // 【发票备注】 发票备注
    public ?string $remark = null;

    // 【发票行信息】 发票行信息，单张发票的发票行不能超过8行
    public array $items = [];

    public function __construct(string $fapiao_id, int $total_amount, array $items)
    {
        $this->fapiao_id = $fapiao_id;
        $this->total_amount = $total_amount;
        foreach ($items as $item) {
            if (!$item instanceof IssueItem) {
                throw new \RuntimeException(sprintf('$items 参数的子元素必须是%s类型', IssueItem::class));
            }
        }
        $this->items = $items;
    }
}