<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

/**
 * 购买方信息（发票抬头）
 */
class BuyerInformation extends BaseEntity
{
    /**
     * 抬头类型常量
     */
    public const TYPE_INDIVIDUAL = 'INDIVIDUAL';    // 个人
    public const TYPE_ORGANIZATION = 'ORGANIZATION'; // 单位

    /**
     * @var string 【抬头类型】抬头类型
     * INDIVIDUAL - 个人
     * ORGANIZATION - 单位
     */
    public string $type;

    /**
     * @var string 【抬头购买方名称】抬头购买方名称
     */
    public string $name;

    /**
     * @var string|null 【购买方纳税人识别号】购买方纳税人识别号，抬头类型为单位时必填
     */
    public ?string $taxpayer_id = null;

    /**
     * @var string|null 【购买方地址】购买方地址，抬头类型为单位时选填
     */
    public ?string $address = null;

    /**
     * @var string|null 【购买方电话】购买方电话，抬头类型为单位时选填
     */
    public ?string $telephone = null;

    /**
     * @var string|null 【购买方开户银行】购买方开户银行，抬头类型为单位时选填
     */
    public ?string $bank_name = null;

    /**
     * @var string|null 【购买方银行账号】购买方银行账号，抬头类型为单位时选填
     */
    public ?string $bank_account = null;

    /**
     * @var string|null 【购买方手机号码】购买方手机号码，抬头类型为个人时选填
     */
    public ?string $phone = null;

    /**
     * @var string|null 【购买方邮箱】购买方邮箱，抬头类型为个人时选填
     */
    public ?string $email = null;

    /**
     * 构造函数
     *
     * @param string $type 抬头类型
     * @param string $name 购买方名称
     */
    public function __construct(string $type, string $name)
    {
        $this->type = $type;
        $this->name = $name;
    }

    /**
     * 创建个人抬头
     *
     * @param string $name 个人姓名
     * @param string|null $phone 手机号码
     * @param string|null $email 邮箱
     * @return static
     */
    public static function createIndividual(string $name, ?string $phone = null, ?string $email = null): static
    {
        $buyer = new static(self::TYPE_INDIVIDUAL, $name);
        $buyer->phone = $phone;
        $buyer->email = $email;
        return $buyer;
    }

    /**
     * 创建单位抬头
     *
     * @param string $name 单位名称
     * @param string $taxpayerId 纳税人识别号
     * @param string|null $address 地址
     * @param string|null $telephone 电话
     * @param string|null $bankName 开户银行
     * @param string|null $bankAccount 银行账号
     * @return static
     */
    public static function createOrganization(
        string $name,
        string $taxpayerId,
        ?string $address = null,
        ?string $telephone = null,
        ?string $bankName = null,
        ?string $bankAccount = null
    ): static {
        $buyer = new static(self::TYPE_ORGANIZATION, $name);
        $buyer->taxpayer_id = $taxpayerId;
        $buyer->address = $address;
        $buyer->telephone = $telephone;
        $buyer->bank_name = $bankName;
        $buyer->bank_account = $bankAccount;
        return $buyer;
    }

    /**
     * 设置手机号码
     *
     * @param string $phone 手机号码
     * @return $this
     */
    public function setPhone(string $phone): static
    {
        $this->phone = $phone;
        return $this;
    }

    /**
     * 设置邮箱
     *
     * @param string $email 邮箱
     * @return $this
     */
    public function setEmail(string $email): static
    {
        $this->email = $email;
        return $this;
    }

    /**
     * 设置地址
     *
     * @param string $address 地址
     * @return $this
     */
    public function setAddress(string $address): static
    {
        $this->address = $address;
        return $this;
    }

    /**
     * 设置电话
     *
     * @param string $telephone 电话
     * @return $this
     */
    public function setTelephone(string $telephone): static
    {
        $this->telephone = $telephone;
        return $this;
    }

    /**
     * 设置银行信息
     *
     * @param string $bankName 银行名称
     * @param string $bankAccount 银行账号
     * @return $this
     */
    public function setBankInfo(string $bankName, string $bankAccount): static
    {
        $this->bank_name = $bankName;
        $this->bank_account = $bankAccount;
        return $this;
    }
}
