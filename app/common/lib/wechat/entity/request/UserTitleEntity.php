<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

class UserTitleEntity extends BaseEntity
{
    // 个人
    public const TypeIndividual = 'INDIVIDUAL';
    // 组织
    public const TypeOrganization = 'ORGANIZATION';

    public string $type;
    public string $name;
    public ?string $taxpayer_id = null;
    public ?string $address = null;
    public ?string $telephone = null;
    public ?string $bank_name = null;
    public ?string $bank_account = null;
    public ?string $phone = null;
    public ?string $email = null;

    public function __construct(string $type, string $name)
    {
        $this->type = $type;
        $this->name = $name;
    }
}