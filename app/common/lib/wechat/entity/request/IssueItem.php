<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

class IssueItem extends BaseEntity
{
    public string $tax_code;
    public ?string $goods_category = null;
    public ?string $goods_name = null;
    public ?int $goods_id = null;
    public ?string $specification = null;
    public ?string $unit = null;
    public int $quantity;
    public int $total_amount;
    public ?int $tax_rate = null;
    public ?string $tax_prefer_mark = null;
    public bool $discount;

    public function __construct(string $tax_code, int $quantity, int $total_amount, bool $discount)
    {
        $this->tax_code = $tax_code;
        $this->quantity = $quantity;
        $this->total_amount = $total_amount;
        $this->discount = $discount;
    }
}