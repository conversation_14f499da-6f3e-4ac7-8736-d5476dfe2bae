<?php

/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

class BaseEntity implements EntityInterface
{
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $key => $value) {
            if (is_null($value) === false) {
                if (is_object($value)) {
                    if (method_exists($value, 'toArray')) {
                        $result[$key] = $value->toArray();
                    } else {
                        $message = sprintf('%s类必须实现toArray方法', $value::class);
                        throw new \RuntimeException($message);
                    }
                } else if (is_array($value)) {
                    foreach ($value as $item) {
                        if (is_object($item)) {
                            $result[$key][] = $item->toArray();
                        } else {
                            $result[$key][] = $item;
                        }
                    }
                } else {
                    $result[$key] = $value;
                }
            }
        }
        return $result;
    }
}