<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

/**
 * 发票行项目信息
 */
class FapiaoItem extends BaseEntity
{
    /**
     * 税收优惠标识常量
     */
    public const TAX_PREFER_NO_FAVORABLE = 'NO_FAVORABLE';           // 不使用优惠政策
    public const TAX_PREFER_VAT_SPECIAL = 'VAT_SPECIAL';             // 增值税特殊政策
    public const TAX_PREFER_VAT_SPECIAL_POLICY = 'VAT_SPECIAL_POLICY'; // 增值税特殊政策

    /**
     * @var string 【税收分类编码】税收分类编码
     */
    public string $tax_code;

    /**
     * @var string 【商品名称】商品名称
     */
    public string $goods_name;

    /**
     * @var string|null 【规格型号】规格型号
     */
    public ?string $specification = null;

    /**
     * @var string|null 【单位】单位
     */
    public ?string $unit = null;

    /**
     * @var int 【数量】数量，精确到小数点后8位
     */
    public int $quantity;

    /**
     * @var int 【单价】单价，精确到小数点后8位，单位：分
     */
    public int $unit_price;

    /**
     * @var int 【金额】金额，单位：分
     */
    public int $amount;

    /**
     * @var int 【税额】税额，单位：分
     */
    public int $tax_amount;

    /**
     * @var int 【价税合计】价税合计，单位：分
     */
    public int $total_amount;

    /**
     * @var int|null 【税率】税率，精确到小数点后2位，如13%的税率传1300
     */
    public ?int $tax_rate = null;

    /**
     * @var string|null 【税收优惠标识】税收优惠标识
     */
    public ?string $tax_prefer_mark = null;

    /**
     * @var bool 【是否为折扣行】是否为折扣行
     */
    public bool $discount;

    /**
     * 构造函数
     *
     * @param string $taxCode 税收分类编码
     * @param string $goodsName 商品名称
     * @param int $quantity 数量
     * @param int $unitPrice 单价（分）
     * @param int $amount 金额（分）
     * @param int $taxAmount 税额（分）
     * @param int $totalAmount 价税合计（分）
     * @param bool $discount 是否为折扣行
     */
    public function __construct(
        string $taxCode,
        string $goodsName,
        int $quantity,
        int $unitPrice,
        int $amount,
        int $taxAmount,
        int $totalAmount,
        bool $discount = false
    ) {
        $this->tax_code = $taxCode;
        $this->goods_name = $goodsName;
        $this->quantity = $quantity;
        $this->unit_price = $unitPrice;
        $this->amount = $amount;
        $this->tax_amount = $taxAmount;
        $this->total_amount = $totalAmount;
        $this->discount = $discount;
    }

    /**
     * 设置规格型号
     *
     * @param string $specification 规格型号
     * @return $this
     */
    public function setSpecification(string $specification): static
    {
        $this->specification = $specification;
        return $this;
    }

    /**
     * 设置单位
     *
     * @param string $unit 单位
     * @return $this
     */
    public function setUnit(string $unit): static
    {
        $this->unit = $unit;
        return $this;
    }

    /**
     * 设置税率
     *
     * @param int $taxRate 税率（如13%传1300）
     * @return $this
     */
    public function setTaxRate(int $taxRate): static
    {
        $this->tax_rate = $taxRate;
        return $this;
    }

    /**
     * 设置税收优惠标识
     *
     * @param string $taxPreferMark 税收优惠标识
     * @return $this
     */
    public function setTaxPreferMark(string $taxPreferMark): static
    {
        $this->tax_prefer_mark = $taxPreferMark;
        return $this;
    }

    /**
     * 创建充电服务发票行
     *
     * @param int $quantity 充电次数
     * @param int $unitPrice 单价（分）
     * @param int $amount 金额（分）
     * @param int $taxAmount 税额（分）
     * @param int $totalAmount 价税合计（分）
     * @param int|null $taxRate 税率
     * @return static
     */
    public static function createChargingService(
        int $quantity,
        int $unitPrice,
        int $amount,
        int $taxAmount,
        int $totalAmount,
        ?int $taxRate = null
    ): static {
        $item = new static(
            '3010101020203000000', // 出租汽车客运服务税收分类编码
            '充电服务',
            $quantity,
            $unitPrice,
            $amount,
            $taxAmount,
            $totalAmount
        );
        
        if ($taxRate !== null) {
            $item->setTaxRate($taxRate);
        }
        
        $item->setUnit('次')
             ->setTaxPreferMark(self::TAX_PREFER_NO_FAVORABLE);
        
        return $item;
    }
}
