<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

/**
 * 发票额外信息
 */
class ExtraInformation extends BaseEntity
{
    /**
     * @var string|null 【收款人】收款人
     */
    public ?string $payee = null;

    /**
     * @var string|null 【复核人】复核人
     */
    public ?string $reviewer = null;

    /**
     * @var string|null 【开票人】开票人
     */
    public ?string $drawer = null;

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 空构造函数，所有字段都是可选的
    }

    /**
     * 设置收款人
     *
     * @param string $payee 收款人
     * @return $this
     */
    public function setPayee(string $payee): static
    {
        $this->payee = $payee;
        return $this;
    }

    /**
     * 设置复核人
     *
     * @param string $reviewer 复核人
     * @return $this
     */
    public function setReviewer(string $reviewer): static
    {
        $this->reviewer = $reviewer;
        return $this;
    }

    /**
     * 设置开票人
     *
     * @param string $drawer 开票人
     * @return $this
     */
    public function setDrawer(string $drawer): static
    {
        $this->drawer = $drawer;
        return $this;
    }

    /**
     * 创建完整的额外信息
     *
     * @param string $payee 收款人
     * @param string $reviewer 复核人
     * @param string $drawer 开票人
     * @return static
     */
    public static function create(string $payee, string $reviewer, string $drawer): static
    {
        $extra = new static();
        $extra->payee = $payee;
        $extra->reviewer = $reviewer;
        $extra->drawer = $drawer;
        return $extra;
    }
}
