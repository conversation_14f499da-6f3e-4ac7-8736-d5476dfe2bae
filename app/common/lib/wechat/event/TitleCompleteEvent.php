<?php

namespace app\common\lib\wechat\event;

use app\common\lib\wechat\entity\answer\TitleCompleteAnswer;
use app\common\lib\wechat\entity\answer\UserTitleAnswer;

class TitleCompleteEvent
{
    public UserTitleAnswer $userTitleAnswer;
    public TitleCompleteAnswer $titleCompleteAnswer;

    public function __construct(array $data)
    {
        $this->titleCompleteAnswer = $data['TitleCompleteAnswer'];
        $this->userTitleAnswer = $data['UserTitleAnswer'];
    }
}