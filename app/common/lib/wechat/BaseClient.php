<?php

namespace app\common\lib\wechat;

use app\common\lib\http\HttpClient;
use Ramsey\Uuid\Uuid;

class BaseClient
{
    protected HttpClient $httpClient;
    protected Sign $sign;
    protected string $appid;

    public function __construct()
    {
        $this->httpClient = new HttpClient();
        $this->sign = new Sign();
        $this->appid = config('wechat.appid');
    }

    public function generateFapiaoApplyId(): string
    {
        $uuid = Uuid::uuid4();
        $value = $uuid->getBytes();
        return bin2hex($value);
    }
}