<?php

namespace app\common\lib\electronic_invoice;

use app\common\lib\wechat\BlockchainElectronicInvoiceClient;
use app\common\lib\wechat\ElectronicInvoiceClient;
use app\common\model\ElectronicInvoiceApplyDetail;
use app\common\model\ElectronicInvoiceApplyRecord;
use app\common\model\Order;
use RuntimeException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Apply
{
    use Tool;

    /**
     * 用户申请开电子发票处理
     *
     * @param int $userId 用户ID
     * @param string $openId 微信用户open_id
     * @param array $orderIds 选择要开发票的充电订单
     * @return array
     */
    public function handler(int $userId, string $openId, array $orderIds): array
    {
        // 验证用户是否已绑定微信
        if (empty($openId)) {
            throw new RuntimeException('用户未绑定微信', 400);
        }

        // 验证充电订单是否存在
        $Order = app(Order::class);
        if ($Order->verifyUserOrderExist($userId, $orderIds) === false) {
            throw new RuntimeException('存在无效订单流水号', 400);
        }

        // 验证这些订单是否存在已开电子发票的
        $ElectronicInvoiceApplyDetail = app(ElectronicInvoiceApplyDetail::class);
        if ($ElectronicInvoiceApplyDetail->isExistEffective($orderIds) === true) {
            throw new RuntimeException('存在已开发票的订单', 400);
        }

        // 查询这些订单的总消费金额
        $totalAmount = $Order->sumOrderTotalAmount($userId, $orderIds);

        $ElectronicInvoiceApplyRecord = app(ElectronicInvoiceApplyRecord::class);
        $applyId = self::generateUuid();
        $createResult = $ElectronicInvoiceApplyRecord->createApply(
            $applyId,
            $userId,
            $totalAmount
        );
        if ($createResult === false) {
            throw new RuntimeException('创建电子发票申请记录失败');
        }

        $row = $ElectronicInvoiceApplyDetail->addDetails($applyId, $userId, $orderIds);
        if ($row === 0) {
            throw new RuntimeException('写入电子发票明细失败');
        }

        // 调用获取填写发票抬头信息Url
        $BlockchainElectronicInvoiceClient = app(BlockchainElectronicInvoiceClient::class);
        $response = $BlockchainElectronicInvoiceClient->getTitleUrl(
            $applyId,
            $openId,
            $totalAmount,
            ElectronicInvoiceClient::SourceMiniprogram
        );
        return $response->analysisJsonBody();
    }
}