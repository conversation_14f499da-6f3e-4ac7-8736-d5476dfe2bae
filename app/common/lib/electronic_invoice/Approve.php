<?php

namespace app\common\lib\electronic_invoice;

use app\common\lib\wechat\ElectronicInvoiceClient;
use app\common\lib\wechat\entity\request\FapiaoFileMetaInfo;
use app\common\lib\wechat\entity\request\BuyerInformation;
use app\common\lib\wechat\entity\request\FapiaoCardInfo;
use app\common\lib\wechat\entity\request\SellerInformation;
use app\common\lib\wechat\entity\request\ExtraInformation;
use app\common\lib\wechat\entity\request\FapiaoItem;
use app\common\model\ElectronicInvoiceApplyRecord;
use app\common\model\ElectronicInvoiceApplyDetail;
use app\common\model\ElectronicInvoiceRecord;
use app\common\model\Order;
use RuntimeException;
use think\facade\Db;
use think\facade\Log;

/**
 * 电子发票审批处理类
 */
class Approve
{
    use Tool;

    /**
     * 审批状态常量
     */
    public const APPROVE_STATUS_PASS = 1;    // 审批通过
    public const APPROVE_STATUS_REJECT = 2;  // 审批拒绝

    /**
     * 处理电子发票审批
     *
     * @param string $fapiaoApplyId 发票申请单号
     * @param string $filePath PDF文件地址
     * @param int $approveStatus 审批状态 1:通过 2:拒绝
     * @param string $remark 审批备注
     * @param int $adminUserId 审批人ID
     * @return string 处理结果信息
     * @throws RuntimeException
     */
    public function handleApprove(
        string $fapiaoApplyId,
        string $filePath,
        int $approveStatus,
        string $remark = '',
        int $adminUserId = 0
    ): string {
        // 验证发票申请记录是否存在
        $applyRecord = $this->validateApplyRecord($fapiaoApplyId);
        
        // 验证文件是否存在
        $this->validateFile($filePath);

        // 记录审批日志
        $this->logApprove($fapiaoApplyId, $approveStatus, $remark, $adminUserId);

        if ($approveStatus === self::APPROVE_STATUS_REJECT) {
            // 审批拒绝，更新状态并返回
            $this->updateApproveStatus($fapiaoApplyId, $approveStatus, $remark, $adminUserId, $filePath);
            return '发票申请已拒绝';
        }

        // 审批通过，处理发票上传和插卡
        return $this->processApprovedInvoice($fapiaoApplyId, $filePath, $applyRecord, $remark, $adminUserId);
    }

    /**
     * 验证发票申请记录
     *
     * @param string $fapiaoApplyId 发票申请单号
     * @return array 申请记录数据
     * @throws RuntimeException
     */
    protected function validateApplyRecord(string $fapiaoApplyId): array
    {
        $ElectronicInvoiceApplyRecord = app(ElectronicInvoiceApplyRecord::class);
        $applyRecord = $ElectronicInvoiceApplyRecord->where('id', $fapiaoApplyId)->find();
        
        if (!$applyRecord) {
            throw new RuntimeException('发票申请记录不存在');
        }

        $applyRecord = $applyRecord->toArray();

        // 检查申请记录状态
        if ($applyRecord['stage'] !== ElectronicInvoiceApplyRecord::StageTitleComplete) {
            throw new RuntimeException('发票申请记录状态不正确，只能审批已填写抬头的申请');
        }

        return $applyRecord;
    }

    /**
     * 验证文件
     *
     * @param string $filePath 文件路径
     * @throws RuntimeException
     */
    protected function validateFile(string $filePath): void
    {
        // 检查文件是否存在
        if (!file_exists($filePath)) {
            throw new RuntimeException("PDF文件不存在: {$filePath}");
        }

        // 检查文件格式
        $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        if ($fileExtension !== 'pdf') {
            throw new RuntimeException('只支持PDF格式的发票文件');
        }

        // 检查文件大小（不能超过2MB）
        $fileSize = filesize($filePath);
        if ($fileSize > 2 * 1024 * 1024) {
            throw new RuntimeException('PDF文件大小不能超过2MB');
        }
    }

    /**
     * 处理审批通过的发票
     *
     * @param string $fapiaoApplyId 发票申请单号
     * @param string $filePath PDF文件路径
     * @param array $applyRecord 申请记录数据
     * @return string 处理结果信息
     * @throws RuntimeException
     */
    protected function processApprovedInvoice(
        string $fapiaoApplyId,
        string $filePath,
        array $applyRecord,
        string $remark = '',
        int $adminUserId = 0
    ): string
    {
        try {
            // 1. 上传PDF文件到微信
            $fapiaoMediaId = $this->uploadFileToWechat($filePath);
            
            // 2. 获取申请详情和订单信息
            $orderInfo = $this->getOrderInfo($fapiaoApplyId);
            
            // 3. 构建发票信息并插入卡包
            $this->insertInvoiceToCardBag($fapiaoApplyId, $fapiaoMediaId, $applyRecord, $orderInfo);
            
            // 4. 更新审批状态为通过并完成申请
            $this->updateApproveStatus($fapiaoApplyId, self::APPROVE_STATUS_PASS, $remark, $adminUserId, $filePath);
            $this->updateApplyRecordStatus($fapiaoApplyId, 'completed');
            
            return '发票审批通过，已成功上传并插入用户卡包';
            
        } catch (\Exception $e) {
            Log::error('电子发票审批处理失败', [
                'fapiao_apply_id' => $fapiaoApplyId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new RuntimeException('发票处理失败: ' . $e->getMessage());
        }
    }

    /**
     * 上传文件到微信
     *
     * @param string $filePath 文件路径
     * @return string 微信文件ID
     * @throws RuntimeException
     */
    protected function uploadFileToWechat(string $filePath): string
    {
        // 计算文件摘要
        $digest = hash_file('sha256', $filePath); // 如果系统支持SM3，应该使用SM3
        
        // 创建文件元信息
        $metaInfo = FapiaoFileMetaInfo::createPdfMeta($digest);
        
        // 创建微信客户端并上传文件
        $client = new ElectronicInvoiceClient();
        $response = $client->uploadFapiaoFile($filePath, $metaInfo);
        
        if ($response->httpCode !== 200) {
            throw new RuntimeException('文件上传到微信失败: ' . $response->body);
        }
        
        $result = $response->analysisJsonBody();
        return $result['fapiao_media_id'];
    }

    /**
     * 获取订单信息
     *
     * @param string $fapiaoApplyId 发票申请单号
     * @return array 订单信息
     * @throws RuntimeException
     */
    protected function getOrderInfo(string $fapiaoApplyId): array
    {
        $ElectronicInvoiceApplyDetail = app(ElectronicInvoiceApplyDetail::class);
        $details = $ElectronicInvoiceApplyDetail->where('apply_record_id', $fapiaoApplyId)->select();
        
        if ($details->isEmpty()) {
            throw new RuntimeException('未找到发票申请详情');
        }
        
        $orderIds = $details->column('order_id');
        
        // 获取订单总金额等信息
        $Order = app(Order::class);
        $totalAmount = $Order->sumOrderTotalAmount($details[0]['user_id'], $orderIds);
        
        return [
            'order_ids' => $orderIds,
            'total_amount' => $totalAmount,
            'order_count' => count($orderIds)
        ];
    }

    /**
     * 插入发票到卡包
     *
     * @param string $fapiaoApplyId 发票申请单号
     * @param string $fapiaoMediaId 微信文件ID
     * @param array $applyRecord 申请记录
     * @param array $orderInfo 订单信息
     * @throws RuntimeException
     */
    protected function insertInvoiceToCardBag(
        string $fapiaoApplyId,
        string $fapiaoMediaId,
        array $applyRecord,
        array $orderInfo
    ): void {
        // 1. 创建购买方信息
        $buyerInfo = $this->createBuyerInformation($applyRecord);
        
        // 2. 创建销售方信息（从配置获取）
        $sellerInfo = $this->createSellerInformation();
        
        // 3. 创建发票行项目
        $items = $this->createFapiaoItems($orderInfo);
        
        // 4. 创建发票卡券信息
        $cardInfo = $this->createFapiaoCardInfo($fapiaoMediaId, $applyRecord, $orderInfo, $sellerInfo, $items);
        
        // 5. 调用微信接口插入卡包
        $client = new ElectronicInvoiceClient();
        $response = $client->insertFapiaoCards(
            $fapiaoApplyId,
            ElectronicInvoiceClient::SceneWithWechatPay,
            $buyerInfo,
            [$cardInfo]
        );
        
        if ($response->httpCode !== 202) {
            throw new RuntimeException('发票插入卡包失败: ' . $response->body);
        }
    }

    /**
     * 创建购买方信息
     *
     * @param array $applyRecord 申请记录
     * @return BuyerInformation
     */
    protected function createBuyerInformation(array $applyRecord): BuyerInformation
    {
        if ($applyRecord['title_type'] === ElectronicInvoiceApplyRecord::TitleTypeIndividual) {
            return BuyerInformation::createIndividual($applyRecord['title_name']);
        } else {
            return BuyerInformation::createOrganization(
                $applyRecord['title_name'],
                $applyRecord['taxpayer_id']
            );
        }
    }

    /**
     * 创建销售方信息
     *
     * @return SellerInformation
     */
    protected function createSellerInformation(): SellerInformation
    {
        // 这里应该从配置中获取销售方信息
        // 暂时使用示例数据
        $sellerInfo = new SellerInformation(
            '深圳市南山区测试公司',
            '202003261233701778'
        );
        
        $sellerInfo->setAddress('深圳市南山区深南大道10000号')
                   ->setTelephone('************')
                   ->setBankInfo('测试银行', '**************');
        
        return $sellerInfo;
    }

    /**
     * 创建发票行项目
     *
     * @param array $orderInfo 订单信息
     * @return array
     */
    protected function createFapiaoItems(array $orderInfo): array
    {
        // 计算税额和金额（假设13%税率）
        $totalAmount = $orderInfo['total_amount'];
        $taxRate = 1300; // 13%
        $taxAmount = intval($totalAmount * 0.13);
        $amount = $totalAmount - $taxAmount;
        
        $item = FapiaoItem::createChargingService(
            $orderInfo['order_count'],  // 充电次数
            intval($amount / $orderInfo['order_count']), // 单价
            $amount,                    // 金额
            $taxAmount,                 // 税额
            $totalAmount,               // 价税合计
            $taxRate                    // 税率
        );
        
        return [$item];
    }

    /**
     * 创建发票卡券信息
     *
     * @param string $fapiaoMediaId 微信文件ID
     * @param array $applyRecord 申请记录
     * @param array $orderInfo 订单信息
     * @param SellerInformation $sellerInfo 销售方信息
     * @param array $items 发票行项目
     * @return FapiaoCardInfo
     */
    protected function createFapiaoCardInfo(
        string $fapiaoMediaId,
        array $applyRecord,
        array $orderInfo,
        SellerInformation $sellerInfo,
        array $items
    ): FapiaoCardInfo {
        $totalAmount = $orderInfo['total_amount'];
        $taxAmount = intval($totalAmount * 0.13);
        $amount = $totalAmount - $taxAmount;
        
        $cardInfo = new FapiaoCardInfo(
            $fapiaoMediaId,
            $this->generateInvoiceNumber(),     // 发票号码
            '044001911211',                     // 发票代码（应该从配置获取）
            FapiaoCardInfo::getCurrentFapiaoTime(), // 开票时间
            $this->generateCheckCode(),         // 校验码
            $totalAmount,                       // 价税合计
            $taxAmount,                         // 税额
            $amount,                           // 金额
            $sellerInfo,                       // 销售方信息
            $items                             // 发票行信息
        );
        
        // 设置额外信息
        $extraInfo = ExtraInformation::create('收款人', '复核人', '开票人');
        $cardInfo->setExtraInformation($extraInfo)
                 ->setRemark('充电服务费');
        
        return $cardInfo;
    }

    /**
     * 生成发票号码
     *
     * @return string
     */
    protected function generateInvoiceNumber(): string
    {
        return date('Ymd') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
    }

    /**
     * 生成校验码
     *
     * @return string
     */
    protected function generateCheckCode(): string
    {
        return str_pad(mt_rand(1, 999999999999999999), 18, '0', STR_PAD_LEFT);
    }

    /**
     * 更新申请记录状态
     *
     * @param string $fapiaoApplyId 发票申请单号
     * @param string $status 状态
     */
    protected function updateApplyRecordStatus(string $fapiaoApplyId, string $status): void
    {
        $ElectronicInvoiceApplyRecord = app(ElectronicInvoiceApplyRecord::class);

        if ($status === 'completed') {
            $ElectronicInvoiceApplyRecord->completeApply($fapiaoApplyId);
        }
        // 其他状态可以根据需要扩展
    }

    /**
     * 更新审批状态
     *
     * @param string $fapiaoApplyId 发票申请单号
     * @param int $approveStatus 审批状态
     * @param string $remark 审批备注
     * @param int $adminUserId 审批人ID
     * @param string $filePath 文件路径
     */
    protected function updateApproveStatus(
        string $fapiaoApplyId,
        int $approveStatus,
        string $remark,
        int $adminUserId,
        string $filePath
    ): void {
        $ElectronicInvoiceApplyRecord = app(ElectronicInvoiceApplyRecord::class);
        $ElectronicInvoiceApplyRecord->updateApproveStatus(
            $fapiaoApplyId,
            $approveStatus,
            $remark,
            $adminUserId,
            $filePath
        );
    }

    /**
     * 记录审批日志
     *
     * @param string $fapiaoApplyId 发票申请单号
     * @param int $approveStatus 审批状态
     * @param string $remark 审批备注
     * @param int $adminUserId 审批人ID
     */
    protected function logApprove(string $fapiaoApplyId, int $approveStatus, string $remark, int $adminUserId): void
    {
        $statusText = $approveStatus === self::APPROVE_STATUS_PASS ? '通过' : '拒绝';
        
        Log::info('电子发票审批', [
            'fapiao_apply_id' => $fapiaoApplyId,
            'approve_status' => $approveStatus,
            'status_text' => $statusText,
            'remark' => $remark,
            'admin_user_id' => $adminUserId,
            'approve_time' => date('Y-m-d H:i:s')
        ]);
    }
}
