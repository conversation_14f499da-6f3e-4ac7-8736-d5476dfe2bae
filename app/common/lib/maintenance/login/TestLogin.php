<?php

namespace app\common\lib\maintenance\login;

use app\common\cache\redis\MaintenanceUserLogin;
use app\common\lib\exception\RuntimeException;
use app\common\model\AdminUsers;
use app\common\model\AdminUsersRelationApplet;

class TestLogin
{
    protected int $admin_user_id;

    public function __construct(array $options)
    {
        $this->admin_user_id = $options['id'];
    }


    public function run(): array
    {

        $query_fields = [
            'id',
            'state',
            'avatar',
            'phone',
            'email',
            'create_time'
        ];

        // 验证手机号是否已注册
        $adminUserData = (new AdminUsers())->getUserData($this->admin_user_id, $query_fields);
        if (is_null($adminUserData)) {
            throw new RuntimeException('未注册账号，请联系管理员注册。');
        } else if ($adminUserData['state'] === AdminUsers::StateDisabled) {
            throw new RuntimeException('账号已被禁用');
        }
        $openId = app(AdminUsersRelationApplet::class)->getOpenID($this->admin_user_id);
        if (is_null($openId)) throw new RuntimeException('还没有绑定小程序,需要先使用小程序登陆一下');


        $tokenData = $this->get_token($adminUserData, $openId);
        if (empty($tokenData)) {
            throw new RuntimeException('登录失败');
        }

        $adminUserData['token'] = $tokenData['token'];
        $adminUserData['expire_time'] = $tokenData['expire_time'];
        return $adminUserData;
    }

    /**
     * 登录获取token
     * lwj 2023.8.12 新增
     * lwj 2023.9.15 修改
     * @param array $user
     * @param string $open_id
     * @return array
     */
    private function get_token(array $user, string $open_id): array
    {
        $last_time = microtime();
        $token = sha1($last_time . $user['id'] . '-' . $open_id);

        $user['last_time'] = $last_time;
        $user['token'] = $token;
        $user['open_id'] = $open_id;
        $UserLogin = app(MaintenanceUserLogin::class);
        $user['expire_time'] = $UserLogin->saveUserLoginData($token, $user);
        if (is_null($user['expire_time'])) return [];
        return $user;
    }

}