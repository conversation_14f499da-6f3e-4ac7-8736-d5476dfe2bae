<?php

namespace app\common\lib\maintenance\login;

use app\common\cache\redis\MaintenanceUserLogin;
use app\common\lib\exception\RuntimeException;
use app\common\lib\WeChat;
use app\common\model\AdminUsers;
use app\common\model\AdminUsersRelationApplet;

class WechatAppletPhoneLogin
{
    protected string $openid_code = '';
    protected string $phone_code = '';
    protected string $open_id;
    protected int $phone;

    public function __construct(array $options)
    {
//        $this->phone = $options['phone'];
//        $this->open_id = $options['open_id'];
        $this->openid_code = $options['openid_code'];
        $this->phone_code = $options['phone_code'];
    }

    protected function analysisOpenID(): void
    {
        $WeChat = new WeChat;
        $WeChat->appid = config('maintenance_applet.appid');
        $WeChat->secret = config('maintenance_applet.secret');
        $res_session = $WeChat->get_open_id($this->openid_code);
        if (!isset($res_session['openid'])) throw new RuntimeException('获取openid失败');
        $this->open_id = $res_session['openid'];
    }

    protected function analysisPhone(): void
    {
        $WeChat = new WeChat;
        $WeChat->appid = config('maintenance_applet.appid');
        $WeChat->secret = config('maintenance_applet.secret');
        $phone = $WeChat->get_phone_number($this->phone_code);
        if (empty($phone)) throw new RuntimeException('获取手机号失败');
        $this->phone = $phone;
    }


    public function run(): array
    {
        // 解析 Open ID
        $this->analysisOpenID();

        // 未绑定微信小程序
        $bind_admin_user_id = (new AdminUsersRelationApplet())->getOpenIDRelationUserId($this->open_id);

        $query_fields = [
            'id',
            'state',
            'avatar',
            'phone',
            'email',
            'create_time'
        ];

        if (is_null($bind_admin_user_id)) {
            // 解析手机号
            $this->analysisPhone();

            // 验证手机号是否已注册
            $adminUserData = (new AdminUsers())->usePhoneGetData($this->phone, $query_fields);
            if (is_null($adminUserData)) {
                throw new RuntimeException('未注册账号，请联系管理员注册。');
            } else if ($adminUserData['state'] === AdminUsers::StateDisabled) {
                throw new RuntimeException('账号已被禁用');
            }
            app(AdminUsersRelationApplet::class)->bindApplet($adminUserData['id'], $this->open_id);

        } else {
            $adminUserData = (new AdminUsers())->getUserData($bind_admin_user_id, $query_fields);
        }

        $tokenData = $this->get_token($adminUserData, $this->open_id);
        if (empty($tokenData)) {
            throw new RuntimeException('登录失败');
        }

        $adminUserData['token'] = $tokenData['token'];
        $adminUserData['expire_time'] = $tokenData['expire_time'];
        return $adminUserData;
    }

    /**
     * 登录获取token
     * lwj 2023.8.12 新增
     * lwj 2023.9.15 修改
     * @param array $user
     * @param string $open_id
     * @return array
     */
    private function get_token(array $user, string $open_id): array
    {
        $last_time = microtime();
        $token = sha1($last_time . $user['id'] . '-' . $open_id);

        $user['last_time'] = $last_time;
        $user['token'] = $token;
        $user['open_id'] = $open_id;
        $UserLogin = app(MaintenanceUserLogin::class);
        $user['expire_time'] = $UserLogin->saveUserLoginData($token, $user);
        if (is_null($user['expire_time'])) return [];
        return $user;
    }

}