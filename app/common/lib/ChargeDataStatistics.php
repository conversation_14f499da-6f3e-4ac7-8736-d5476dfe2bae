<?php
declare (strict_types=1);

namespace app\common\lib;

use app\common\model\Order;
use app\common\model\Order as OrderModel;
use app\common\model\ShotsStatus;
use app\common\model\StationsMonthStatistics;
use app\common\model\StationsStats as StationsStatsModel;
use app\common\model\Shots as ShotsModel;
use app\common\repositories\AlarmRecord;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 充电数据统计
 * cbj 2023.10.19 新增
 */
class ChargeDataStatistics
{

    public function last_7_days_charging_data(int $station_id, int $corp_id = 0): array
    {
        // 当天充电统计
        $Order = app(OrderModel::class);
        $dailyStatistics = $Order->todayStationStatistics($station_id, $corp_id);

        // 最近十天充电统计平均值
        $StationsStatsModel = app(StationsStatsModel::class);
        $Last7DaysData = $StationsStatsModel->stationLast7Days($station_id, $corp_id);
        $Last7DaysData[] = [
            'day' => date('Y-m-d 00:00:00'),
            'money' => $dailyStatistics['money'],
            'electricity_total' => $dailyStatistics['electricity_total']
        ];

        return [
            'last_7_days_data' => $Last7DaysData
        ];
    }


    /**
     * 首页统计
     *
     * @param int $corp_id [default=0] 运营商ID
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function dataOverview(int $corp_id = 0): array
    {
        if ($corp_id < 0) return [];

        // 当天充电统计
        $Order = app(OrderModel::class);
        $dailyStatistics = $Order->todayStatistics($corp_id);

        // 最近十天充电统计平均值
        $StationsStatsModel = app(StationsStatsModel::class);
        $lastTenDaysStatisticsSummary = $StationsStatsModel->statisticsSummary($corp_id, TimeTool::getHistoryTimestamp(9));
        $Last10DaysData = $StationsStatsModel->Last10Days($corp_id);
        $Last10DaysData[] = [
            'day' => date('Y-m-d 00:00:00'),
            'money' => $dailyStatistics['money'],
            'electricity_total' => $dailyStatistics['electricity_total']
        ];

        $averageMoney = round(($lastTenDaysStatisticsSummary['money'] + $dailyStatistics['money']) / 10);
        $averageElectricityTotal = round(($lastTenDaysStatisticsSummary['electricity_total'] + $dailyStatistics['electricity_total']) / 10);

        // 本月历史充电统计(查询这个月的历史每日统计)
        $thisMonthStatisticsSummary = $StationsStatsModel->statisticsSummary($corp_id, TimeTool::getThisMonthTimestamp());
        $thisMonthStatisticsSummary['money'] += $dailyStatistics['money'];
        $thisMonthStatisticsSummary['electricity_total'] += $dailyStatistics['electricity_total'];

        // 本年历史充电统计(查询这年的历史每月统计)
        $StationsMonthStatistics = app(StationsMonthStatistics::class);
        $field = [
            'sum(amount) as money',
            'sum(electricity) as electricity_total'
        ];
        $thisYearStatisticsSummary = $StationsMonthStatistics->thisYearStatistic($corp_id, 0, $field);
        $thisYearStatisticsSummary['money'] += $thisMonthStatisticsSummary['money'];
        $thisYearStatisticsSummary['electricity_total'] += $thisMonthStatisticsSummary['electricity_total'];

        // 充电枪总数
        $ShotsModel = app(ShotsModel::class);
        $TotalShotsCount = $ShotsModel->totalCount($corp_id);

        // 查询告警记录
        $alarmRecordCount = AlarmRecord::alarmRecordCount($corp_id);

        // 充电枪在线数量
        $ShotsStatusModel = app(ShotsStatus::class);
        $onlineCount = $ShotsStatusModel->onlineCount($corp_id);

        return [
            'daily_charge_income' => $dailyStatistics['money'],
            'daily_electricity_total' => $dailyStatistics['electricity_total'],
            'daily_charge_duration' => $dailyStatistics['charge_duration'],
            'average_charge_income' => $averageMoney,
            'average_electricity_total' => $averageElectricityTotal,
            'last_10_days_data' => $Last10DaysData,
            'this_year_charge_income' => $thisYearStatisticsSummary['money'],
            'this_year_electricity_total' => $thisYearStatisticsSummary['electricity_total'],
            'total_shots_count' => $TotalShotsCount,
            'online_shots_count' => $onlineCount,
            'alarm_record_count' => $alarmRecordCount
        ];
    }

    /**
     * 每月统计列表
     *
     * @param int $year 年份
     * @param int $corp_id [default=0] 运营商ID
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function monthlyStatistics(int $year, int $corp_id = 0): array
    {
        if ($year < 0 || $corp_id < 0) return [];

        // 查询今天的
        $Order = app(Order::class);
        $todayData = $Order->todayStatistics($corp_id);

        // 查询这个月的
        $StationsStatsModel = app(StationsStatsModel::class);
        $thisMonth = $StationsStatsModel->thisMonthStatistics($corp_id);
        // 这个月的统计累加上今天的统计
        $thisMonth['money'] += $todayData['money'];
        $thisMonth['electricity_total'] += $todayData['electricity_total'];
        $thisMonth['order_count'] += $todayData['order_count'];

        // 查询历史月份的
        $StationsMonthStatistics = app(StationsMonthStatistics::class);
        $monthlyStatisticsData = $StationsMonthStatistics->monthlyStatisticsData($year, $corp_id);
        foreach ($monthlyStatisticsData as &$value) {
            $value['electricity_total'] = (int)$value['electricity_total'];
            $value['month'] = date('Y-m-d 00:00:00', $value['month']);
        }
        $monthlyStatisticsData[] = $thisMonth;
        return $monthlyStatisticsData;
    }

    /**
     * 每日统计列表
     *
     * @param int $year 年份
     * @param int $month 月份
     * @param int $corp_id [default=0] 运营商ID
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function dailyStatistics(int $year, int $month, int $corp_id = 0): array
    {
        if ($year < 0 || $corp_id < 0) return [];

        $StationsStatsModel = app(StationsStatsModel::class);
        $dailyData = $StationsStatsModel->dailyStatistics($year, $month, $corp_id);

        if ($month === (int)date('m') && $year === (int)date('Y')) {
            $Order = app(Order::class);
            $todayData = $Order->todayStatistics($corp_id);

            $dailyData[] = [
                'money' => $todayData['money'],
                'electricity_total' => $todayData['electricity_total'],
                'order_count' => $todayData['order_count'],
                'day' => date('Y-m-d 00:00:00')
            ];
        }

        return array_values($dailyData);
    }

}