<?php
/** @noinspection PhpUnused */

namespace app\common\lib\order;

use app\common\model\Order;
use PhpOffice\PhpSpreadsheet\Exception;
use Generator;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ExportSpreadsheet
{
    // 列
    public array $columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

    protected array $request;

    protected ?Generator $export_data = null;
    protected ?array $summary_data = null;

    protected string $title;

    protected array $field_config = [
        'id' => ['name' => '交易流水号', 'width' => 40]
        , 'sequence' => ['name' => '枪号', 'width' => 7]
        , 'pay_mode' => ['name' => '支付方式', 'width' => 12]
        , 'status' => ['name' => '订单状态', 'width' => 12]
        , 'pay_money' => ['name' => '订单实付金额', 'width' => 18]
        , 'trans_end_time' => ['name' => '交易结束时间', 'width' => 25]
        , 'electricity_total' => ['name' => '总电量', 'width' => 16]
        , 'vin_code' => ['name' => '电动汽车唯一标识', 'width' => 25]
        , 'station_name' => ['name' => '充电站名称', 'width' => 15]
        , 'build_type' => ['name' => '生成类型', 'width' => 15]
        , 'reason_for_stop_text' => ['name' => '停止原因', 'width' => 30]
    ];

    protected array $summary_field_config = [
        'pay_money' => [
            'name' => '订单实付金额汇总',
            'key' => [
                'start_column' => 'A',
                'is_merge' => false
            ],
            'value' => [
                'start_column' => 'B',
                'is_merge' => true,
                'merge_column' => ['B', 'E']
            ]
        ],
        'electricity_total' => [
            'name' => '电量汇总',
            'key' => [
                'start_column' => 'F',
                'is_merge' => false
            ],
            'value' => [
                'start_column' => 'G',
                'is_merge' => true,
                'merge_column' => ['G', 'K']
            ]
        ]
    ];

    public function __construct(array $request, string $title)
    {
        $this->request = $request;
        $this->title = $title;
    }

    /**
     * 运行
     *
     * @return string
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function run(): string
    {
        $OrderModel = new Order();
        $this->export_data = $OrderModel->get_export_data($this->request);
        $this->summary_data = $OrderModel->get_order_summary($this->request);

        return $this->exportDataTableStructure();
    }


    /**
     * 导出表结构
     *
     * @return string
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function exportDataTableStructure(): string
    {
        $spreadsheet = new Spreadsheet();
        $writer = new Xlsx($spreadsheet);

        // 设置列宽度
        $i = 0;
        foreach ($this->field_config as $value) {
            $spreadsheet->getActiveSheet()->getColumnDimension($this->columns[$i])->setWidth($value['width']);
            $i++;
        }

        $row = 1;

        $xMin = 'A';
        $xMax = $this->columns[count($this->field_config) - 1];

        // 设置标题
        $spreadsheet->getActiveSheet()->mergeCells(sprintf('%s%d:%s%d', $xMin, $row, $xMax, $row))->getCell(sprintf('%s%d', $xMin, $row))
            ->setValue('订单列表');
        $spreadsheet->getActiveSheet()->getStyle(sprintf('%s%d:%s%d', $xMin, $row, $xMax, $row))->getFont()
            ->setName('宋体')->setSize(16)->setBold(true);
        $styleArray = [
            'alignment' => [
                'vertical' => Alignment::VERTICAL_CENTER,
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ];
        $spreadsheet->getActiveSheet()->getStyle(sprintf('%s%d:%s%d', $xMin, $row, $xMax, $row))->applyFromArray($styleArray);

        $row++;


        // 设置表头信息(字段的)
        $i = 0;
        foreach ($this->field_config as $value) {
            $spreadsheet->getActiveSheet()->getCell($this->columns[$i] . $row)->setValue($value['name']);
            $spreadsheet->getActiveSheet()->getStyle($this->columns[$i] . $row)->getFont()
                ->setName('宋体')->setSize(12)->setBold(true)->getColor()->setARGB('000000');
            $i++;
        }
//        $styleArray = [
//            'alignment' => [
//                'vertical' => Alignment::VERTICAL_CENTER,
//                'horizontal' => Alignment::HORIZONTAL_CENTER,
//            ],
//        ];
        $spreadsheet->getActiveSheet()->getStyle(sprintf('%s%d:%s%d', $xMin, $row, $xMax, $row))->applyFromArray($styleArray)->getFill()
            ->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB('E9F3EB');
        $row++;


        //  设置字段信息
        foreach ($this->export_data as $row_data) {
            $row_data = $row_data->toArray();
            $i = 0;
            foreach ($this->field_config as $field_name => $field_config) {
                $value = $row_data[$field_name];
                if ($field_name === 'status') {
                    $value = Order::StatusNameOptions[$value];
                } else if ($field_name === 'pay_money') {
                    $value = ($value / 100) . '元';
                } else if ($field_name === 'pay_mode') {
                    $value = Order::PayModeNameOptions[$value];
                } else if ($field_name === 'build_type') {
                    $value = Order::BuildTypeNameOptions[$value];
                } else if ($field_name === 'electricity_total') {
                    $value = ($value / 10000) . '千瓦';
                }

                $column = $this->columns[$i];
                $spreadsheet->getActiveSheet()->getCell($column . $row)->setValue($value);
                $spreadsheet->getActiveSheet()->getStyle($column . $row)->getFont()->setName('宋体')
                    ->setSize(12)->getColor()->setARGB('000000');
                $i++;
            }
            $row++;
        }

        // 设置汇总的数据

        // 'pay_money' => [
        //            'name' => '订单实付金额汇总',
        //            'key' => [
        //                'start_column' => 'A',
        //                'is_merge' => false
        //            ],
        //            'value' => [
        //                'start_column' => 'B',
        //                'is_merge' => true,
        //                'merge_column' => ['B', 'E']
        //            ]
        //        ],
        foreach ($this->summary_field_config as $field_name => $field_config) {

            if ($field_name === 'pay_money') {
                $value = ($this->summary_data[$field_name] / 100) . '元';
            } else if ($field_name === 'electricity_total') {
                $value = ($this->summary_data[$field_name] / 10000) . '千瓦';
            } else {
                $value = $this->summary_data[$field_name];
            }

            if ($field_config['key']['is_merge'] === true) {
                $range = sprintf('%s%d:%s%d', $field_config['key']['merge_column'][0], $row, $field_config['key']['merge_column'][1], $row);
                $spreadsheet->getActiveSheet()->mergeCells($range)->getCell($field_config['key']['start_column'] . $row)->setValue($field_config['name']);
            } else {
                $spreadsheet->getActiveSheet()->getCell($field_config['key']['start_column'] . $row)->setValue($field_config['name']);
            }
            $spreadsheet->getActiveSheet()->getStyle($field_config['key']['start_column'] . $row)->getFont()
                ->setName('宋体')->setSize(12)->setBold(true)->getColor()->setARGB('000000');
            //        $styleArray = [
            //            'alignment' => [
            //                'vertical' => Alignment::VERTICAL_CENTER,
            //                'horizontal' => Alignment::HORIZONTAL_CENTER,
            //            ],
            //        ];
            $spreadsheet->getActiveSheet()->getStyle(sprintf('%s%d:%s%d', $xMin, $row, $xMax, $row))->applyFromArray($styleArray)->getFill()
                ->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB('E9F3EB');

            if ($field_config['value']['is_merge'] === true) {
                $range = sprintf('%s%d:%s%d', $field_config['value']['merge_column'][0], $row, $field_config['value']['merge_column'][1], $row);
                $spreadsheet->getActiveSheet()->mergeCells($range)->getCell($field_config['value']['start_column'] . $row)->setValue($value);
            } else {
                $spreadsheet->getActiveSheet()->getCell($field_config['value']['start_column'] . $row)->setValue($value);
            }
            $spreadsheet->getActiveSheet()->getStyle($field_config['value']['start_column'] . $row)->getFont()
                ->setName('宋体')->setSize(12)->setBold(true)->getColor()->setARGB('000000');
        }


        $row++;

        // 设置边框
        $styleArray = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['argb' => '000'],
                ],
            ],
        ];

        $spreadsheet->getActiveSheet()->getStyle(sprintf('%s%d:%s%d', $xMin, 1, $xMax, $row - 1))->applyFromArray($styleArray);


        $file_name = sprintf('订单列表-%s.xlsx', $this->title);
        $save_dir = 'export' . DIRECTORY_SEPARATOR . date('Y-m') . DIRECTORY_SEPARATOR;

        if (!is_dir($save_dir)) {
            mkdir($save_dir, 0775, true);
        }


//        header("Pragma: public");
//        header("Expires: 0");
//        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
//        header("Content-Type:application/force-download");
//        header("Content-Type:application/vnd.ms-execl");
//        header("Content-Type:application/octet-stream");
//        header("Content-Type:application/download");
//        header('Content-Disposition:attachment;filename=' . $file_name);
//        header("Content-Transfer-Encoding:binary");
//        $writer->save('php://output');
        $writer->save(public_path() . $save_dir . $file_name);

        return $save_dir . $file_name;
    }
}