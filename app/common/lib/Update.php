<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
declare (strict_types=1);

namespace app\common\lib;

use app\common\model\UserBalanceLog as UserBalanceLogModel;
use app\common\model\Users as UsersModel;
use app\common\model\PayOrder as PayOrderModel;
use Exception;
use think\facade\Db;
use Throwable;


class Update
{

    /**
     * 支付后加用户余额
     * lwj 2023.8.15 新增
     * lwj 2023.8.24 修改
     * @param string $order_id
     * @return bool
     */
    public function pay_add_user_balance(string $order_id): bool
    {
        try {
            $pay_order = new PayOrderModel();
            $order = $pay_order->where("id", $order_id)->find();
            if (!$order) {
                trace('支付后加用户余额=》找不到订单：' . $order_id, '信息');
                return false;
            }

            if ($order['state'] !== 3) {
                trace('支付后加用户余额=》支付状态不是成功：' . json_encode_cn($order), '信息');
                SendApplet::send_uid($order['user_id'],[
                    'type' => 'pay_notice',
                    'result' => 'error',
                    'msg' => '支付状态异常，不能加余额'
                ]);
                return false;
            }

            if ($order['price'] === 0) {
                trace('支付后加用户余额=》支付金额为0，无需加余额：' . json_encode_cn($order), '信息');
                SendApplet::send_uid($order['user_id'],[
                    'type' => 'pay_notice',
                    'result' => 'success',
                    'msg' => '支付成功，支付金额为0,无需加余额'
                ]);
                return true;
            }

            $res = $this->add_user_balance($order['user_id'], $order['price'], '支付成功，加用户余额：' . $order['price']);
            if ($res) {
                $pay_order->where("id", $order_id)->update(['add_balance' => 1]);
                SendApplet::send_uid($order['user_id'],[
                    'type' => 'pay_notice',
                    'result' => 'success',
                    'msg' => '支付成功，加余额成功'
                ]);
                return true;
            }
            $pay_order->where("id", $order_id)->update(['add_balance' => 2]);
            SendApplet::send_uid($order['user_id'],[
                'type' => 'pay_notice',
                'result' => 'warning',
                'msg' => '支付成功，但加余额失败'
            ]);

            return false;

        } catch (Throwable $e) {
            trace('支付后加用户余额=》异常：' . $e->getMessage(), '信息');
            return false;
        }
    }

    /**
     * 加用户余额
     * lwj 2023.8.15 新增
     * lwj 2023.8.24 修改
     * @param int $user_id
     * @param int $amount
     * @param string $memo
     * @param string $type
     * @return bool
     */
    public function add_user_balance(int $user_id, int $amount, string $memo = '', string $type = '充值'): bool
    {
        $add_data = [
            'user_id' => $user_id,
            'amount' => $amount,
            'memo' => $memo,
            'type' => $type,
        ];

        $user_balance_log = new UserBalanceLogModel();
        $res1 = $user_balance_log->insert($add_data);

        $users = new UsersModel();

        $res2 = $users
            ->where('id', $user_id)
            ->limit(1)
            ->update([
                'balance'=>Db::raw('balance+'.$amount),
            ]);

        if ($res1 && $res2) {
            trace('加用户余额=》请求：' . json_encode_cn($add_data) . '，加用户余额成功返回1：' . $res1 . '，加用户余额成功返回2：' . $res2, '成功');
            return true;
        }

        trace('加用户余额=》请求：' . json_encode_cn($add_data) . '，加用户余额失败返回1：' . $res1 . '，加用户余额失败返回2：' . $res2, '错误');
        return false;
    }

    /**
     * 冻结用户余额
     * lwj 2023.8.21 新增
     * lwj 2023.9.27 修改
     * @param int $user_id
     * @param int $amount
     * @param string|int|null $order_id
     * @return bool
     */
    public function freeze_user_balance(int $user_id, int $amount, string|int|null $order_id = null): bool
    {
        Db::startTrans();
        try {
            if($amount<=0) return false;
            $users = Db::name('users')
                ->field('id,balance,freeze_balance')
                ->where('id',$user_id)
                ->find();

            if($amount > $users['balance']) return false;

            $res1=Db::name('users')
                ->where('id',$user_id)
                ->update([
                    'balance'=>Db::raw('balance-'.$amount),
                    'freeze_balance'=>Db::raw('freeze_balance+'.$amount),
                ]);

            $users2 = Db::name('users')
                ->field('id,balance,freeze_balance')
                ->where('id',$user_id)
                ->find();

            if($users2['balance']<0){
                throw new Exception("余额不足");
            }

            if($users2['freeze_balance']<=0){
                throw new Exception("冻结失败");
            }

            $add_data = [
                'user_id' => $user_id,
                'balance' => $amount,
                'memo' => '订单【'.$order_id.'】冻结余额：'.$amount.'分',
                'order_id' => $order_id,
            ];

            $res2 = Db::name('user_balance_freeze_log')->insert($add_data);

            if ($res1 && $res2) {
                Db::commit();
                trace('冻结用户余额=》请求：' . json_encode_cn($add_data) . '，返回1：' . $res1 . '，返回2：' . $res2, '成功');
                return true;
            }

            trace('冻结用户余额=》请求：' . json_encode_cn($add_data) . '，返回1：' . $res1 . '，返回2：' . $res2, '错误');
            throw new Exception("冻结失败2");
        } catch (Throwable $e) {
            Db::rollback();
            trace('冻结用户余额=》异常：' . $e->getMessage(), '信息');
            return false;
        }
    }

    /**
     * 解冻用户余额并支付订单
     * lwj 2023.8.22 新增
     * lwj 2023.9.27 修改
     * @param int $user_id
     * @param int $amount
     * @param string|int $order_id
     * @param int $freeze_money
     * @return bool
     */
    public function unfreeze_user_balance_and_pay_order(int $user_id, int $amount, string|int $order_id,int $freeze_money): bool
    {
        Db::startTrans();
        try {
//            if($amount<=0) return false;

//            $users = $users_model
//                ->field('id,balance,freeze_balance')
//                ->where('id',$user_id)
//                ->find();

//            if($amount > $freeze_money) return false;


//        if($balance>0) $users->balance = Db::raw('balance+'.$balance);
//        $users->freeze_balance = Db::raw('freeze_balance-'.$amount);
//        $res1=$users->save();
            $order=Db::name('order')
                ->where('id', $order_id)
                ->field('id,freeze_status')
                ->find();
            if($order['freeze_status']!==1){
                trace('解冻用户余额并支付订单=》无需解冻或已经解冻：'.json_encode_cn($order), '错误');
                return false;
            }

            $res4=Db::name('order')
                ->where('id', $order_id)
                ->update([
                    'freeze_status'=>2,
                    'update_time'=>date('Y-m-d H:i:s'),
                ]);

            $balance=$freeze_money-$amount;
            $res1=Db::name('users')
                ->where('id',$user_id)
                ->update([
                    'balance'=>Db::raw('balance+'.$balance),
                    'freeze_balance'=>Db::raw('freeze_balance-'.$freeze_money),
                ]);

            $add_data = [
                'user_id' => $user_id,
                'amount' => -$amount,
                'memo' => '订单【'.$order_id.'】付款：'.$amount.'分',
                'type' => '支付充电订单',
            ];

            $res2 = Db::name('user_balance_log')->insert($add_data);

            $res3 = Db::name('user_balance_freeze_log')
                ->where('order_id',$order_id)
                ->update([
                    'state'=>2,
                    'update_time'=>date('Y-m-d H:i:s'),
                ]);

            if ($res1 && $res2 && $res3 && $res4) {
                Db::commit();
                trace('解冻用户余额并支付订单=》请求：' . json_encode_cn($add_data) . '，返回1：' . $res1 . '，返回2：' . $res2 . '，返回3：' . $res3.'，返回4：' . $res4, '成功');
                return true;
            }

            trace('解冻用户余额并支付订单=》请求：' . json_encode_cn($add_data) . '，返回1：' . $res1 . '，返回2：' . $res2 . '，返回3：' . $res3, '错误');
            throw new Exception("解冻失败2");
        } catch (Throwable $e) {
            Db::rollback();
            trace('解冻用户余额并支付订单=》异常：' . $e->getMessage(), '信息');
            return false;
        }
    }

}
