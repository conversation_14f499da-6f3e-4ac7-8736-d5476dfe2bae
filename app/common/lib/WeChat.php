<?php

namespace app\common\lib;

class WeChat
{
    public string $appid;
    public string $secret;

    public function __construct() {
        $this->appid=config('wechat.appid');
        $this->secret=config('wechat.secret');
    }


    /**
     * 获取open_id
     * lwj 2023.8.12 新增
     * @param string $code
     * @return array
     */
    public function get_open_id(string $code): array
    {
        $params = [
            'appid' => $this->appid,
            'secret' => $this->secret,
            'js_code' => $code,
            'grant_type' => 'authorization_code',
        ];

        $query = http_build_query($params);
        $url = 'https://api.weixin.qq.com/sns/jscode2session?' . $query;
        return send_get_json_to_shuzu($url);
    }

    /**
     * 获取微信js基础access_token
     * lwj 2023.8.12 新增
     * @return string|bool
     */
    public function get_js_access_token(): string|bool
    {
        $access_token=cache('微信access_token'.$this->appid);
        if($access_token) return $access_token;

        $params = [
            'grant_type'      => 'client_credential',
            'appid' => $this->appid,
            'secret' => $this->secret,
        ];

        $query = http_build_query($params);
        $url = 'https://api.weixin.qq.com/cgi-bin/token?' . $query;
        $token_arr=send_get_json_to_shuzu($url);
        trace('获取微信js基础access_token=》' . json_encode_cn($token_arr),'信息');

        if(isset($token_arr['access_token']) && $token_arr['expires_in'] ){
            $time=$token_arr['expires_in']-200;
            cache('微信access_token'.$this->appid,$token_arr['access_token'],$time);
            return $token_arr['access_token'];
        }
        return false;
    }

    /**
     * 获取微信js基础access_token(稳定版)
     * 一般只需调用本版本即可
     * lwj 2023.8.12 新增
     * @return string|bool
     */
    public function get_js_access_token_stable(): string|bool
    {
        $access_token=cache('微信access_token'.$this->appid);
        if($access_token) return $access_token;

        $params = [
            'grant_type' => 'client_credential',
            'appid' => $this->appid,
            'secret' => $this->secret,
        ];
        $url = 'https://api.weixin.qq.com/cgi-bin/stable_token';
        $token_arr=http_post_json($url,$params);
        trace('获取微信js基础access_token(稳定版)=》' . json_encode_cn($token_arr),'信息');

        if(isset($token_arr['access_token']) && $token_arr['expires_in'] ){
            $time=$token_arr['expires_in']-200;
            cache('微信access_token'.$this->appid,$token_arr['access_token'],$time);
            return $token_arr['access_token'];
        }
        return false;
    }

    /**
     * 获取手机号
     * lwj 2023.8.12 新增
     * @param string $code
     * @return string|bool
     */
    public function get_phone_number(string $code): string|bool
    {
        $params = [
            'code' => $code,
        ];
        trace('获取手机号；$params=》' . json_encode_cn($params),'信息');

        $url = 'https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token='.$this->get_js_access_token_stable();
        $token_arr=http_post_json($url,$params);
        trace('获取手机号=》' . json_encode_cn($token_arr),'信息');

        if(isset($token_arr['phone_info']['phoneNumber']) && $token_arr['phone_info']['phoneNumber'] ){
            return $token_arr['phone_info']['phoneNumber'];
        }
        return false;
    }

    /**
     * 获取二维码
     * lwj 2023.9.22 新增
     * lwj 2023.9.27 修改
     * @param array $params
     * @return mixed
     */
    public function get_qr_code(array $params): mixed
    {
        trace('获取手机号；$params=》' . json_encode_cn($params),'信息');
        $url = 'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token='.$this->get_js_access_token_stable();
        $token_arr=http_post_json($url,$params,false);
        trace('获取手机号=》' . json_encode_cn($token_arr),'信息');
        return $token_arr;
    }

    /**
     * 发送微信模板消息
     * @param string $openid 用户openid
     * @param array $params 消息参数
     * @param string $type 消息类型 (exception:异常消息, start:充电开始通知, end:充电完成通知)
     * @return mixed
     */
    public function send_template_message(string $openid, string $orderId, array $params, string $type): mixed
    {
        // 根据类型选择不同的模板ID和页面
        $template_config = [
            'exception' => [
                'template_id' => 'yZkLxELuVzojKSwouSTUltcrQhbZh0dCnN-etSv-u4U',
                'page' => '/subpage/Charging/Charging?id='.$orderId,
                'log_message' => '发送订单异常消息'
            ],
            'start' => [
                'template_id' => 'ZD0mM85rdDJj_i2y3vyxCVa_NKKGh2ZwyHmplkG9-sY',
                'page' => '/subpage/Charging/Charging?id='.$orderId,
                'log_message' => '发送充电开始通知'
            ],
            'end' => [
                'template_id' => 'fI0OjaqDogeoMX3w20xAdnbKfVjQAdD09FTBEOFf7ss',
                'page' => '/subpage/OrderDetails/OrderDetails?id='.$orderId,
                'log_message' => '发送充电完成通知'
            ]
        ];

        $config = $template_config[$type];

        $data = [
            'template_id' => $config['template_id'],
            'page' => $config['page'],
            'touser' => $openid,
            'data' => $params,
            'miniprogram_state' => config('app.env')=='production' ? 'formal' : 'trial',
            'lang' => 'zh_CN',
        ];

        $url = 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token='.$this->get_js_access_token_stable();
        $res = http_post_json($url, $data);
        trace("微信通知:".$config['log_message'] . '；$data=》' . json_encode_cn($data));
        trace("微信通知:".$config['log_message'] . '；$res=》' . json_encode_cn($res));
        trace("微信通知,用户openid:{$openid},订单id:{$orderId}");
        return $res;
    }

}
