<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\common\model;

use app\common\cache\redis\entity\AdminLoginUser;
use think\facade\Db;
use think\Model;

class CentralizedController extends Model
{
    protected $table = 'centralized_controller';


    public function getStationId(string $centralized_controller_id): ?int
    {
        return $this->where('id', '=', $centralized_controller_id)->value('station_id');
    }


    public function getStationCentralizedControllerOptions(AdminLoginUser $loginUser, int $station_id): array
    {

        $where = [];
        // 如果是运营商管理员账号
        if ($loginUser->corp_id > 0 && $loginUser->pid === 0) {
            $where[] = ['corp_id', '=', $loginUser->corp_id];
            // 如果是运营商子账号
        } else if ($loginUser->corp_id > 0) {
            $where[] = ['corp_id', '=', $loginUser->corp_id];
            // 设置默认是查询哪些场站的数据
            $station_ids = (new StationDataAuthority())->getStationIds($loginUser->id);
            if (empty($station_id)) {
                $where[] = ['station_id', 'in', $station_ids];
            } else {
                // 如果用户对场站进行了筛选，则检查选择的场站是否具体权限。如果没，有则返回空列表。
                if (in_array($station_id, $station_ids) === false) {
                    return [];
                } else {
                    $where[] = ['station_id', '=', $station_id];
                }
            }
            // 非运营商类型的管理员账号
        } else {
            $where[] = ['station_id', '=', $station_id];
        }

        return $this->field(['id', 'name'])->where($where)->select()->toArray();
    }

    public function deleteAll(): int
    {
        return Db::name($this->table)->delete(true);
    }

    public function deleteStationAllCentralizedController(int $station_id): int
    {
        return Db::name($this->table)->where('station_id', '=', $station_id)->delete(true);
    }
}