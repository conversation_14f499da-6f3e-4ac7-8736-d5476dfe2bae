<?php

namespace app\common\model;

use think\Model;

class RestartOrderLog extends Model
{
    protected $table = 'restart_order_log';

    public function addLog(
        string $order_id,
        int    $applet_user_id,
        int    $op_user_id,
        string $op_client_ip,
        int    $power_limit
    ): int
    {
        return $this->insertGetId([
            'order_id' => $order_id,
            'applet_user_id' => $applet_user_id,
            'op_user_id' => $op_user_id,
            'op_client_ip' => $op_client_ip,
            'power_limit' => $power_limit,
            'create_time' => date('Y-m-d H:i:s')
        ]);
    }

    public function getLogInfo(int $id)
    {
        $data = $this->where('id', '=', $id)->find();
        if (empty($data)) {
            return null;
        }
        return $data->toArray();
    }

    public function getCount(): int
    {
        return $this->count();
    }

    public function deleteLog(int $id): int
    {
        $this->where('id', '=', $id)->delete();
        return $this->getNumRows();
    }
}
