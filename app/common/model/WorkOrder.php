<?php

namespace app\common\model;

use app\common\log\LogCollector;
use Ramsey\Uuid\Uuid;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Model;

class WorkOrder extends Model
{
    protected $table = 'work_order';


    // 优先级
    public const PriorityLow = 1; // 低
    public const PriorityStandard = 2; // 标准
    public const PriorityHigh = 3; // 高
    public const PriorityUrgent = 4; // 紧急

    // 状态
    public const StatusOpen = 1; // 开启
    public const StatusSolving = 2; // 解决中
    public const StatusResolved = 3; // 已解决
    public const StatusClosed = 4; // 已关闭

    // 来源
    public const SourceAdminCreate = 1; // 后台创建
    public const SourceAlarmGeneration = 2; // 告警生成
    public const SourceUserCreate = 3; // 用户创建

    // 创建者的用户类型
    public const CreateUserTypeAdmin = 1; // 后台用户
    public const CreateUserTypeNormal = 2; // 普通用户
    public const CreateUserTypeAuto = 3; // 自动生成


    /**
     * 工单列表
     *
     * @param int|null $corp_id
     * @param int|array|null $station_id
     * @param int|null $device_type
     * @param string|null $device_id
     * @param array|null $status
     * @param int|null $source
     * @param int|null $priority
     * @param int|null $create_user_type
     * @param int $page
     * @param int $limit
     * @return array
     * @throws DbException
     */
    public function getListData(
        ?int           $corp_id = null,
        null|int|array $station_id = null,
        ?int           $device_type = null,
        ?string        $device_id = null,
        ?array         $status = null,
        ?int           $source = null,
        ?int           $create_user_type = null,
        ?int           $priority = null,
        int            $page = 1,
        int            $limit = 10
    ): array
    {
        $where = [];
        if (!is_null($corp_id)) {
            $where[] = ['wor.corp_id', '=', $corp_id];
        }
        if (!is_null($station_id)) {
            if (is_int($station_id)) {
                $where[] = ['wor.station_id', '=', $station_id];
            } else {
                $where[] = ['wor.station_id', 'in', $station_id];
            }
        }
        if (!is_null($device_type)) {
            $where[] = ['wor.device_type', '=', $device_type];
        }
        if (!is_null($device_id)) {
            $where[] = ['wor.device_id', '=', $device_id];
        }
        if (!empty($status)) {
            $where[] = ['wo.status', 'in', $status];
        }
        if (!is_null($source)) {
            $where[] = ['wo.source', '=', $source];
        }
        if (!is_null($priority)) {
            $where[] = ['wo.priority', '=', $priority];
        }
        if (!is_null($create_user_type)) {
            $where[] = ['wo.create_user_type', '=', $create_user_type];
        }

        /**
         * @var Query $query
         */
        $query = $this;
        $field = [
            'wo.id', 'wo.title', 'wo.priority', 'wo.status', 'wo.source', 'wo.template_id', 'wot.name as template_name',
            'wo.create_user_type', 'wo.create_user_id', 'wo.create_time', 'wo.update_time',
            'c.name as corp_name', 's.name as station_name', 'wor.device_type', 'wor.device_id',
            'au.name as responsible_user_name'
        ];
        return $query->alias('wo')
            ->leftJoin('work_order_template wot', 'wot.id = wo.template_id')
            ->leftJoin('work_order_relation wor', 'wor.work_order_id = wo.id')
            ->leftJoin('work_order_responsible wore', 'wore.work_order_id = wo.id and wore.is_activate = ' . WorkOrderResponsible::IsActivateYes)
            ->leftJoin('admin_users au', 'au.id = wore.admin_user_id')
            ->leftJoin('corp c', 'c.id = wor.corp_id')
            ->leftJoin('stations s', 's.id = wor.station_id')
            ->where($where)
            ->field($field)
            ->order('wo.create_time', 'desc')
            ->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();
    }


    public function addWorkOrder(
        string $title,
        int    $priority,
        int    $source,
        int    $template_id,
        int    $create_user_type,
        int    $create_user_id
    ): string
    {
        $new_id = Uuid::uuid4()->toString();
        LogCollector::collectorRunLog('$new_id = ' . $new_id);
        $this->insertAll([[
            'id' => $new_id,
            'title' => $title,
            'priority' => $priority,
            'status' => self::StatusOpen,
            'source' => $source,
            'template_id' => $template_id,
            'create_user_type' => $create_user_type,
            'create_user_id' => $create_user_id,
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => NULL
        ]]);
        return $new_id;
    }

    /**
     * @param string $id
     * @param array|null $field
     * @return array|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getInfo(
        string $id,
        ?array $field = null
    ): ?array
    {
        /**
         * @var Query $query
         */
        $query = $this;
        if (is_null($field)) {
            $field = [
                'wo.id', 'wo.title', 'wo.priority', 'wo.status', 'wo.source', 'wo.template_id', 'wot.name as template_name',
                'wo.create_user_type', 'wo.create_user_id', 'wo.create_time', 'wo.update_time',
                'wor.corp_id', 'wor.station_id', 'wor.device_type', 'wor.device_id', 'woed.extra_data',
                'wore.admin_user_id as responsible_user_id', 'au.name as responsible_user_name'
            ];
        }

        $data = $query->alias('wo')
            ->leftJoin('work_order_template wot', 'wot.id = wo.template_id')
            ->leftJoin('work_order_relation wor', 'wor.work_order_id = wo.id')
            ->leftJoin('work_order_extra_data woed', 'woed.work_order_id = wo.id')
            ->leftJoin('work_order_responsible wore', 'wore.work_order_id = wo.id and wore.is_activate = ' . WorkOrderResponsible::IsActivateYes)
            ->leftJoin('admin_users au', 'au.id = wore.admin_user_id')
            ->where('wo.id', '=', $id)
            ->field($field)
            ->find();
        if (empty($data)) {
            return null;
        }

        return $data->toArray();
    }


    public function updateStatus(string $work_order_id, int $status): bool
    {
        return $this->where('id', '=', $work_order_id)->update([
                'status' => $status,
                'update_time' => date('Y-m-d H:i:s')
            ]) > 0;
    }


}
