<?php

namespace app\common\model;

use app\common\log\LogCollector;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Model;

class ScanCodeRecords extends Model
{
    protected $table = 'scan_code_records';

    // 扫码结果 0:正常 1:已离线 2:已故障 3:充电中 20:未知异常
    public const ScanCodeResultNormal = 0; // 正常
    public const ScanCodeResultOffline = 1; // 已离线
    public const ScanCodeResultFault = 2; // 已故障
    public const ScanCodeResultCharging = 3; // 充电中
    public const ScanCodeResultUnknown = 20; // 未知异常

    public const ScanCodeResultMessages = [
        self::ScanCodeResultNormal => '正常',
        self::ScanCodeResultOffline => '已离线',
        self::ScanCodeResultFault => '已故障',
        self::ScanCodeResultCharging => '充电中',
        self::ScanCodeResultUnknown => '未知异常'
    ];

    public function add(
        int $user_id,
        int $corp_id,
        int $station_id,
        int $piles_id,
        int $shots_number,
        int $scan_code_result = self::ScanCodeResultNormal
    ): bool
    {
        LogCollector::collectorRunLog(print_r([
            'user_id' => $user_id,
            'corp_id' => $corp_id,
            'station_id' => $station_id,
            'piles_id' => $piles_id,
            'shots_number' => $shots_number,
            'create_time' => date("Y-m-d H:i:s")
        ], true), LogCollector::LevelInfo);
        return $this->insert([
                'user_id' => $user_id,
                'corp_id' => $corp_id,
                'station_id' => $station_id,
                'piles_id' => $piles_id,
                'shots_number' => $shots_number,
                'scan_code_result' => $scan_code_result,
                'create_time' => date("Y-m-d H:i:s")
            ]) > 0;
    }


    /**
     * @param array $filter
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function get_all(array $filter): array
    {
        $where = [];
        if (!empty($filter['start_time'])) {
            $where[] = ['scr.create_time', '>=', $filter['start_time']];
        }
        if (!empty($filter['end_time'])) {
            $where[] = ['scr.create_time', '<=', $filter['end_time']];
        }
        if (!empty($filter['corp_id'])) {
            $where[] = ['scr.corp_id', '=', $filter['corp_id']];
        }
        $fields = [
            'scr.create_time as time',
            'u.phone',
            'scr.piles_id',
            'scr.shots_number',
            'scr.scan_code_result'
        ];

        /**
         * @var Query $this
         */
        return $this->alias('scr')
            ->leftJoin('users u', 'u.id = scr.user_id')
            ->where($where)
            ->field($fields)
            ->withAttr('scan_code_result', function ($value) {
                return self::ScanCodeResultMessages[$value] ?? '未知';
            })
            ->order('scr.id', 'desc')
            ->select()
            ->toArray();
    }

    /**
     * @param int $page
     * @param int $limit
     * @param array $filter
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function get_list(int $page, int $limit, array $filter): array
    {
        $where = [];
        if (!empty($filter['start_time'])) {
            $where[] = ['scr.create_time', '>=', $filter['start_time']];
        }
        if (!empty($filter['end_time'])) {
            $where[] = ['scr.create_time', '<=', $filter['end_time']];
        }
        if (!empty($filter['corp_id'])) {
            $where[] = ['scr.corp_id', '=', $filter['corp_id']];
        }
        $fields = [
            'scr.create_time as time',
            'u.phone',
            'scr.piles_id',
            'scr.shots_number',
            'scr.scan_code_result'
        ];

        /**
         * @var Query $this
         */
        return $this->alias('scr')
            ->leftJoin('users u', 'u.id = scr.user_id')
            ->where($where)
            ->field($fields)
            ->withAttr('scan_code_result', function ($value) {
                return self::ScanCodeResultMessages[$value] ?? '未知';
            })
            ->order('scr.id', 'desc')
            ->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();
    }
}
