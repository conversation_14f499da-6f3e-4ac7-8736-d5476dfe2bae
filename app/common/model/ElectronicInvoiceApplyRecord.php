<?php

namespace app\common\model;

use think\db\exception\DbException;
use think\db\Query;
use think\Model;

class ElectronicInvoiceApplyRecord extends Model
{
    protected $table = 'electronic_invoice_apply_record';

    /**
     * 创建时间字段 false表示关闭.
     *
     * @var false|string
     */
    protected $createTime = false;

    /**
     * 更新时间字段 false表示关闭.
     *
     * @var false|string
     */
    protected $updateTime = false;

    // 抬头类型
    public const TitleTypeIndividual = 0; // 个人
    public const TitleTypeOrganization = 1; // 单位

    // 阶段
    public const StageUserApply = 0; // 用户发起申请
    public const StageTitleComplete = 1; // 用户填写抬头完成
    public const StageComplete = 2; //开具发票完成


    // 第三方服务类型
    public const ServiceTypeWechat = 1; // 微信支付服务

    public function getUserData(string $id): array
    {
        return $this->where('id', $id)->field(['user_id'])->find()->toArray();
    }

    protected function arrangeWhere(array $params = []): array
    {
        $where = [];
        if (!empty($params['id'])) {
            $where[] = ['eiar.id', '=', $params['id']];
        }

        if (!empty($params['user_id'])) {
            $where[] = ['u.id', '=', $params['user_id']];
        }

        if (!empty($params['nickname'])) {
            $where[] = ['u.nickname', 'like', '%' . $params['nickname'] . '%'];
        }

        if (is_int($params['title_type'])) {
            $where[] = ['eiar.title_type', '=', $params['title_type']];
        }

        if (!empty($params['title_name'])) {
            $where[] = ['eiar.title_name', 'like', '%' . $params['title_name'] . '%'];
        }

        if (!empty($params['taxpayer_id'])) {
            $where[] = ['eiar.taxpayer_id', 'like', '%' . $params['taxpayer_id'] . '%'];
        }

        if (is_int($params['stage'])) {
            $where[] = ['eiar.stage', '=', $params['stage']];
        }

        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $where[] = ['eiar.create_time', 'between', [$params['start_time'], $params['end_time']]];
        } else if (!empty($params['start_time'])) {
            $where[] = ['eiar.create_time', '>=', $params['start_time']];
        } else if (!empty($params['end_time'])) {
            $where[] = ['eiar.create_time', '<=', $params['end_time']];
        }

        return $where;
    }

    /**
     * 列表
     *
     * @param array $params [default=[]] 查询参数
     * @param int $page [default=1] 页码
     * @param int $pageSize [default=10] 每页最大条数
     * @return array
     * @throws DbException
     */
    public function list(array $params = [], int $page = 1, int $pageSize = 10): array
    {
        $where = $this->arrangeWhere($params);
        $field = [
            'eiar.id',
            'eiar.user_id',
            'u.nickname',
            'eiar.total_amount',
            'eiar.title_type',
            'eiar.title_name',
            'eiar.taxpayer_id',
            'eiar.stage',
            'eiar.create_time',
            'eiar.update_time'
        ];
        /**
         * @var Query $query
         */
        $query = $this->alias('eiar');
        $query->join('users u', 'u.id = eiar.user_id', 'LEFT');
        return $query->where($where)->order('create_time', 'DESC')->field($field)->paginate(['list_rows' => $pageSize, 'page' => $page])->toArray();
    }

    protected function arrangeStatisticsWhere(array $params = []): array
    {
        $where = [];
        if (!empty($params['nickname'])) {
            $where[] = ['u.nickname', 'like', '%' . $params['nickname'] . '%'];
        }

        if (is_int($params['stage'])) {
            $where[] = ['eiar.stage', '=', $params['stage']];
        }

        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $where[] = ['eiar.create_time', 'between', [$params['start_time'], $params['end_time']]];
        } else if (!empty($params['start_time'])) {
            $where[] = ['eiar.create_time', '>=', $params['start_time']];
        } else if (!empty($params['end_time'])) {
            $where[] = ['eiar.create_time', '<=', $params['end_time']];
        }

        return $where;
    }

    /**
     * 统计列表
     *
     * @param array $params [default=[]] 查询参数
     * @param int $page [default=1] 页码
     * @param int $pageSize [default=10] 每页最大条数
     * @return array
     * @throws DbException
     */
    public function statistics(array $params = [], int $page = 1, int $pageSize = 10): array
    {
        $where = $this->arrangeStatisticsWhere($params);
        $field = [
            'eiar.id',
            'eiar.user_id',
            'u.nickname',
            'sum(eiar.total_amount) as total_amount',
            'count(eiar.id) as count',
            'eiar.stage',
        ];
        /**
         * @var Query $query
         */
        $query = $this->alias('eiar');
        $query->join('users u', 'u.id = eiar.user_id', 'LEFT')
            ->group(['eiar.user_id', 'stage']);
        return $query->where($where)->field($field)->paginate(['list_rows' => $pageSize, 'page' => $page])->toArray();
    }

    public function createApply(
        string $id,
        int    $userId,
        int    $totalAmount
    ): bool
    {
        $row = $this->insert([
            'id' => $id,
            'user_id' => $userId,
            'total_amount' => $totalAmount,
            'service_type' => self::ServiceTypeWechat,
            'title_type' => self::TitleTypeIndividual,
            'title_name' => '',
            'taxpayer_id' => '',
            'stage' => self::StageUserApply,
            'create_time' => time(),
            'update_time' => time()
        ]);

        return $row > 0;
    }


    public function updateTitleInfo(string $id, int $titleType,
                                    string $titleName, string $taxpayerId = ''): bool
    {
        $row = $this->where('id', $id)->update([
            'title_type' => $titleType,
            'title_name' => $titleName,
            'taxpayer_id' => $taxpayerId,
            'stage' => self::StageTitleComplete,
            'update_time' => time()
        ]);
        return (int)$row > 0;
    }

    public function completeApply(string $id): bool
    {
        $row = $this->where('id', $id)->update([
            'stage' => self::StageComplete,
            'update_time' => time()
        ]);
        return (int)$row > 0;
    }

    public function getTotalAmount(string $id): int
    {
        return $this->where('id', $id)->value('total_amount');
    }

}