<?php

namespace app\common\model;

use app\common\lib\ExceptionLogCollector;
use app\common\lib\TimeTool;
use app\common\model\Order as OrderModel;
use Closure;
use Generator;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\facade\Db;
use think\Model;

/**
 * @method join(string $string, string $string1)
 */
class Order extends Model
{
    protected $table = 'order';

    // 订单状态 1下单，2 已冻结，3 充电中，4充电完成，5完成，6异常 ,20其它
    public const StatusPlaceAnOrder = 1; // 下单
    public const StatusFreeze = 2; // 已冻结
    public const StatusCharging = 3; // 充电中
    public const StatusChargeComplete = 4; // 充电完成
    public const StatusComplete = 5; // 完成
    public const StatusAbnormal = 6; // 异常
    public const StatusCompulsorySettlement = 7; // 自动结算
    public const StatusOther = 20; // 其他
    public const StatusReservation = 8; // 预约

    public const StatusNameOptions = [
        self::StatusPlaceAnOrder => '下单',
        self::StatusCharging => '充电中',
        self::StatusComplete => '完成',
        self::StatusAbnormal => '异常',
        self::StatusCompulsorySettlement => '自动结算',
        self::StatusReservation => '预约',
        self::StatusOther => '其他'
    ];

    // 支付方式
    public const PayModeWechat = 1; // 微信
    public const PayModeAlipay = 2; // 支付宝
    public const PayModeBalance = 3; // 余额
    public const PayModeMembershipCard = 4; // 会员卡

    public const PayModeNameOptions = [
        self::PayModeWechat => '微信',
        self::PayModeAlipay => '支付宝',
        self::PayModeBalance => '零钱',
        self::PayModeMembershipCard => '会员卡'
    ];

    // 生成类型，1小程序，2后台模拟
    public const BuildTypeApplet = 1; // 小程序
    public const BuildTypeAdmin = 2; // 后台模拟

    public const BuildTypeNameOptions = [
        self::BuildTypeApplet => '小程序',
        self::BuildTypeAdmin => '后台模拟'
    ];


    // 冻结金额状态 ，1冻结，2解冻
    public const FreezeStatusYes = 1; // 已冻结
    public const FreezeStatusNot = 2; // 已解冻

    // 订单类型
    public const TypeChargeNow = 1; // 立即充电
    public const TypeReservationCharge = 2; // 预约充电

    /**
     * 解冻订单的冻结金额
     *
     * @param string $id 交易流水号
     * @param int $electricity 充电量
     * @param int $end_time 充电结束时间戳(单位:秒)
     * @param int $charge_duration 充电耗时(单位:秒)
     * @param ?int $update_status default=null 更新的订单状态(不需要更新状态可不传递)
     * @return int 更新的行数
     */
    public function unfreezeOrder(string $id, int $electricity, int $end_time, int $charge_duration, ?int $update_status = null): int
    {
        if (empty($id)) return 0;
        $update = [
            'freeze_status' => self::FreezeStatusNot,
            'electricity_total' => $electricity,
            'trans_end_time' => date('Y-m-d H:i:s', $end_time),
            'update_time' => date('Y-m-d H:i:s'),
            'charge_duration' => $charge_duration
        ];
        if (is_null($update_status) === false) {
            $update['status'] = $update_status;
        }

        return (int)$this->where('id', $id)->update($update);
    }

    /**
     * 当天统计
     *
     * @param int $corp_id 运营商ID [default=0] 不传递或传递0则表示查询所有运营商的
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function todayStatistics(int $corp_id = 0): array
    {
        $field = [
            'sum(pay_money) as money',
            'sum(electricity_total) as electricity_total',
            'count(id) as order_count',
            'sum(charge_duration) as charge_duration'
        ];
        $where = [
            ['trans_end_time', '>=', date('Y-m-d 00:00:00')]
        ];
        if (!empty($corp_id)) {
            $where[] = ['corp_id', '=', $corp_id];
        }

        try {
            $result = $this->field($field)->where($where)->find()->toArray();
            foreach ($result as &$value) {
                if (is_null($value)) {
                    $value = 0;
                }
            }
            return $result;
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            throw $e;
        }
    }

    /**
     * 场站当天统计
     *
     * @param int $station_id 场站ID
     * @param int $corp_id 运营商ID(为0时表示不过滤运营商ID)
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function todayStationStatistics(int $station_id, int $corp_id): array
    {
        $field = [
            'sum(money) as money',
            'sum(electricity_total) as electricity_total',
            'count(id) as order_count',
            'sum(charge_duration) as charge_duration'
        ];
        $where = [
            ['trans_start_time', '>=', date('Y-m-d 00:00:00')],
            ['station_id', '=', $station_id]
        ];
        if (!empty($corp_id)) {
            $where[] = ['corp_id', '=', $corp_id];
        }


        try {
            $result = $this->field($field)->where($where)->find()->toArray();
            foreach ($result as &$value) {
                if (is_null($value)) {
                    $value = 0;
                }
            }
            return $result;
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            throw $e;
        }
    }

    /**
     * 电量使用情况统计
     *
     * @param int $corp_id [default=0] 运营商ID
     * @param int $station_id [default=0] 充电站ID
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function electricityUsageStatistics(int $corp_id = 0, int $station_id = 0): array
    {
        $field = [
            'sum(sharp_electricity) as sharp_electricity',
            'sum(peak_electricity) as peak_electricity',
            'sum(flat_electricity) as flat_electricity',
            'sum(valley_electricity) as valley_electricity',
        ];
        $where = [];
        $where[] = ['trans_start_time', '>=', date('Y-m-01 00:00:00')];
        if (!empty($corp_id)) {
            $where[] = ['corp_id', '=', $corp_id];
        }
        if (!empty($station_id)) {
            $where[] = ['station_id', '=', $station_id];
        }
        try {
            $result = $this->field($field)->where($where)->find()->toArray();
            foreach ($result as &$value) {
                if (empty($value)) {
                    $value = 0;
                }
            }
            return $result;
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            throw $e;
        }
    }


    /**
     * 查询月统计数据
     *
     * @param int $year 年份
     * @param int $month 月份
     * @param int $day 日
     * @return array
     */
    public function queryDayStatisticsData(int $year, int $month, int $day): array
    {
        return Db::query($this->queryDayStatisticsDataSQL($year, $month, $day));
    }

    protected function queryDayStatisticsDataSQL(int $year, int $month, int $day): string
    {
        $field = [
            'corp_id',
            'station_id',
            'sum(sharp_electricity) as sharp',
            'sum(peak_electricity) as peak',
            'sum(flat_electricity) as flat',
            'sum(valley_electricity) as valley',
            'sum(electricity_total) as electricity',
            'sum(pay_money) as amount',
            'sum(electricity_price) as elec_amount',
            'sum(ser_price) as serv_amount',
            'count(id) as order_count',
            'sum(charge_duration) as charge_tick'
        ];
        [$startTime, $endTime] = TimeTool::yearMonthDayTimeFrame($year, $month, $day);
        $where = [];
        $where[] = sprintf('status in(%s)', implode(',', [
            self::StatusComplete,
            self::StatusAbnormal
        ]));
        $where[] = sprintf('UNIX_TIMESTAMP(trans_end_time) >= %d and UNIX_TIMESTAMP(trans_end_time) < %d', $startTime, $endTime);
        $sql = 'SELECT %s FROM `%s` WHERE %s GROUP BY station_id';

        return sprintf(
            $sql,
            implode(', ', $field),
            $this->getTable(),
            implode(' and ', $where)
        );
    }

    /**
     * 当天统计概况
     *
     * @param int $corp_id 运营商ID
     * @param int|array $station_id 充电站ID
     * @return array
     */
    public function thisDayStatistic(int $corp_id = 0, int|array $station_id = 0): array
    {
        $field = [
            'sum(money) as money',
            'sum(electricity_total) as electricity_total',
            'sum(ser_price) as service_money',
            'count(id) as order_count',
        ];
        $startTime = TimeTool::getTodayTimestamp();
        $where[] = sprintf('UNIX_TIMESTAMP(trans_end_time) >= %d', $startTime);
        if (!empty($corp_id)) {
            $where[] = sprintf('corp_id = %d', $corp_id);
        }
        if (!empty($station_id)) {
            if (is_int($station_id)) {
                $where[] = sprintf('station_id = %d', $station_id);
            } else {
                $where[] = sprintf('station_id in (%s)', implode(',', $station_id));
            }
        }
        $sql = 'SELECT %s FROM `%s` WHERE %s';
        $sql = sprintf(
            $sql,
            implode(', ', $field),
            $this->getTable(),
            implode(' AND ', $where)
        );
        $result = Db::query($sql);
        $result = $result[0];
        $result['money'] = $result['money'] ?? 0;
        $result['electricity_total'] = $result['electricity_total'] ?? 0;
        $result['service_money'] = $result['service_money'] ? $result['service_money'] * 10 : 0;
        $result['order_count'] = $result['order_count'] ?? 0;

        return $result;
    }


    /**
     * 验证用户订单是否存在
     *
     * @param int $userId 用户ID
     * @param array $orderIds 需要检查的用户订单流水号
     * @return bool 是否都存在
     */
    public function verifyUserOrderExist(int $userId, array $orderIds): bool
    {
        $count = $this->where('user_id', $userId)
            ->where('id', 'in', $orderIds)
            ->where('status', '>=', self::StatusComplete)
            ->where('status', '<>', self::StatusCompulsorySettlement)
            ->count('id');
        return $count === count($orderIds);
    }

    public function sumOrderTotalAmount(int $userId, array $orderIds): int
    {
        if (empty($userId) || empty($orderIds)) {
            return 0;
        }

        return $this->where('user_id', $userId)->where('id', 'in', $orderIds)->sum('pay_money');
    }

    public function sumOrderTotalAmountCharged(int $userId): int
    {
        return $this->where('user_id', '=', $userId)
            ->where('status', '<', self::StatusComplete)
            ->sum('amount_charged');
    }

    public function getUserChargingOrders(int $userId): array|false
    {
        try {
            return $this->where('user_id', '=', $userId)
                ->where('status', '<', self::StatusComplete)
                ->field(['id', 'piles_id', 'sequence', 'shot_id', 'status', 'corp_id', 'station_id'])
                ->select()->toArray();
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            return false;
        }
    }

    public function updateStatus(string $order_id, int $status, string $msg, ?int $abnormal_alarm_node = null): int
    {
        $updateData = [
            'status' => $status,
            'msg' => $msg,
            'reason_for_stop_text' => $msg,
            'update_time' => date('Y-m-d H:i:s'),
        ];
        if (!is_null($abnormal_alarm_node)) {
            $updateData['abnormal_alarm_node'] = $abnormal_alarm_node;
        }

        return (int)$this->where('id', $order_id)->update($updateData);
    }

    public function updateStatusAndTransStartTime(string $order_id, int $status, string $msg, string $trans_start_time): int
    {
        return (int)$this->where('id', $order_id)
            ->update([
                'status' => $status,
                'msg' => $msg,
                'update_time' => date('Y-m-d H:i:s'),
                'trans_start_time' => $trans_start_time
            ]);
    }

    /**
     * 查询用户充电中的所有订单
     *
     * @param int $user_id 用户ID
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function userChargingOrders(int $user_id): array
    {
        $where = [
            ['user_id', '=', $user_id],
            ['status', 'in', [self::StatusPlaceAnOrder, self::StatusCharging]]
        ];
        return $this->where($where)->select()->toArray();
    }


    protected function arrangeWhere(array $data): Closure
    {
        return function ($query) use ($data) {
            if (!empty($data['id'])) $query->where('o.id', '=', $data['id']);
            if (!empty($data['corp_id'])) $query->where('o.corp_id', '=', $data['corp_id']);
            if (!empty($data['station_id'])) {
                if (is_array($data['station_id'])) {
                    $query->where('o.station_id', 'in', $data['station_id']);
                } else {
                    $query->where('o.station_id', '=', $data['station_id']);
                }
            }
            if (!empty($data['sequence'])) $query->where('o.sequence', '=', $data['sequence']);
            if (!empty($data['phone'])) $query->where('u.phone', '=', $data['phone']);

            if (!empty($data['status'])) {
                if (is_array($data['status'])) {
                    $query->where('o.status', 'in', $data['status']);
                } else {
                    $query->where('o.status', $data['status']);
                }
            }

            if (!empty($data['start_time']) && !empty($data['end_time'])) {
                $query->where('o.create_time >= "' . $data['start_time'] . ' 00:00:00"');
                $query->where('o.create_time <= "' . $data['end_time'] . ' 23:59:59"');
            } else if (!empty($data['start_time'])) {
                $query->where('o.create_time >= "' . $data['start_time'] . ' 00:00:00"');
            } else if (!empty($data['end_time'])) {
                $query->where('o.create_time <= "' . $data['end_time'] . ' 23:59:59"');
            }

            return $query;
        };
    }

    public function get_export_data(array $data): Generator
    {
        $field = [
            'o.id',
            'o.sequence',
            'o.pay_mode',
            'o.status',
            'o.pay_money',
            'o.trans_end_time',
            'o.electricity_total',
            'o.vin_code',
            's.name as station_name',
            'o.build_type',
            'o.reason_for_stop_text',
            'o.freeze_status',
        ];

        $where = $this->arrangeWhere($data);

        /**
         * @var Query $query
         */
        $query = $this->alias('o');
        return $query
            ->join('stations s', 'o.station_id = s.id')
            ->join('users u', 'u.id = o.user_id')
            ->field($field)
            ->where($where)
            ->order('o.create_time', 'DESC')
            ->cursor();
    }

    public function get_list_data(array $data, int $page, int $pageSize): array
    {
        $field = [
            'o.id',
            'o.type',
            'o.sequence',
            'o.pay_mode',
            'o.status',
            'o.pay_money',
            'o.trans_end_time',
            'o.electricity_total',
            'o.vin_code',
            's.name as station_name',
            'o.build_type',
            'o.reason_for_stop_text',
            'o.freeze_status',
            'o.reservation_time'
        ];

        $where = $this->arrangeWhere($data);

        return $this->alias('o')
            ->join('stations s', 'o.station_id = s.id')
            ->join('users u', 'u.id = o.user_id')
            ->field($field)
            ->where($where)
            ->order('o.create_time', 'DESC')
            ->paginate(['list_rows' => $pageSize, 'page' => $page])
            ->toArray();
    }

    /**
     * @param array $data
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function get_export_data_array(array $data): array
    {
        $field = [
            'o.id',
            'o.sequence',
            'o.pay_mode',
            'o.status',
            'o.pay_money',
            'o.trans_end_time',
            'o.electricity_total',
            'o.vin_code',
            's.name as station_name',
            'o.build_type',
            'o.reason_for_stop_text',
            'o.freeze_status',
        ];

        $where = $this->arrangeWhere($data);

        /**
         * @var Query $query
         */
        $query = $this->alias('o');
        return $query
            ->join('stations s', 'o.station_id = s.id')
            ->join('users u', 'u.id = o.user_id')
            ->field($field)
            ->where($where)
            ->order('o.create_time', 'DESC')
            ->select()
            ->toArray();
    }

    /**
     * @param int $piles_id
     * @return array|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getChargingOrderTariffData(int $piles_id): ?array
    {
        $where = [
            ['piles_id', '=', $piles_id],
            ['status', 'in', [self::StatusPlaceAnOrder, self::StatusCharging, self::StatusReservation]]
        ];
        $fields = [
            'billing_mode_id',
            'sharp_fee', 'sharp_ser_fee', 'peak_fee', 'peak_ser_fee',
            'flat_fee', 'flat_ser_fee', 'valley_fee', 'valley_ser_fee',
            'period_codes', 'loss_rate', 'discount'
        ];
        $orderData = $this->where($where)->field($fields)->find();
        if (empty($orderData)) return null;
        return $orderData->toArray();
    }

    public function get_order_summary(array $data): array
    {
        $field = [
            'sum(o.pay_money) as pay_money',
            'sum(o.electricity_total) as electricity_total',
        ];

        $where = $this->arrangeWhere($data);


        $list = $this->alias('o')
            ->join('stations s', 'o.station_id = s.id')
            ->join('users u', 'u.id = o.user_id')
            ->field($field)
            ->where($where)
            ->find();

        if (empty($list)) {
            $list = [
                'pay_money' => 0,
                'electricity_total' => 0
            ];
        } else {
            $list = $list->toArray();
            $list['pay_money'] = (int)$list['pay_money'];
            $list['electricity_total'] = (int)$list['electricity_total'];
        }

        return $list;
    }

    public function clearing()
    {
        return $this->hasMany(StationsCorpClearing::class, 'station_id', 'station_id');
    }

    /**
     * @param string $date
     * @param int|null $corp_id
     * @return int[]
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function dailyStatistics(string $date, ?int $corp_id = null): array
    {
        $startTime = $date;
        $endTime = date('Y-m-d H:i:s', strtotime($date) + 24 * 3600);
        $where = [
            ['create_time', '>=', $startTime],
            ['create_time', '<=', $endTime],
        ];
        if (!is_null($corp_id)) {
            $where[] = ['corp_id', '=', $corp_id];
        }
        $data = $this->where($where)
            ->field(['ifnull(sum(pay_money), 0) as money', 'ifnull(count(id), 0) as count'])
            ->find();
        if (empty($data)) {
            return [
                'money' => 0,
                'count' => 0
            ];
        }
        return $data->toArray();
    }

    public function getChargingCount(int $shots_id): int
    {
        return $this->where('shot_id', $shots_id)
            ->where('status', 'in', [
                OrderModel::StatusPlaceAnOrder,
                OrderModel::StatusCharging,
                OrderModel::StatusReservation
            ])
            ->count('id');
    }

    public function getOrder(string $order_id, array|string $fields = '*'): ?array
    {
        $data = $this->where('id', '=', $order_id)->field($fields)->find();
        if (empty($data)) return null;
        return $data->toArray();
    }

    public function createOrder(array $insert): int|string
    {
        return $this->insert($insert);
    }
}
