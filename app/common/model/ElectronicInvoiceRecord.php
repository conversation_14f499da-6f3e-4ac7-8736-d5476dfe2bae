<?php

namespace app\common\model;

use think\Model;

class ElectronicInvoiceRecord extends Model
{
    protected $table = 'electronic_invoice_record';

    public function addRecords(array $insertRows): int
    {
        return $this->insertAll($insertRows);
    }

    public function updateStatus(string $id, int $status, int $card_status): bool
    {
        $row = $this->where('id', $id)->update([
            'status' => $status,
            'card_status' => $card_status,
            'update_time' => time()
        ]);

        return $row > 0;
    }
}