<?php

namespace app\common\model;

use think\facade\Log;
use think\Model;

/**
 * @method join(string $string, string $string1)
 */
class OrderException extends Order
{
    protected $table = 'order_exception';

    public static function saveOrder(array $insert): int|string
    {
        Log::error('订单异常数据: '. json_encode($insert));
        // 查询订单是否存在
        $order = self::find($insert['id']);
        if (empty($order)) {
            $order = new self();
        }
        return $order->save($insert);
    }
}
