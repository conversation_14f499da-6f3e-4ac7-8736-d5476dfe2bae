<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\common\model;

use app\common\lib\exception\RuntimeException;
use think\Model;

class PilesStatus extends Model
{
    protected $table = 'piles_status';

    // 在线状态
    public const IsOnlineYes = 1; // 在线
    public const IsOnlineNot = 2; // 离线

    // 是否已删除
    public const IsDeleteYes = 1; // 已删除
    public const IsDeleteNot = 0; // 未删除

    /**
     * 验证是否存在
     *
     * @param int $piles_id
     * @return bool
     */
    public function isExistence(int $piles_id): bool
    {
        return $this->where('id', '=', $piles_id)->count() > 0;
    }

    /**
     * 批量添加充电桩状态
     *
     * @param array $piles_ids
     * @return bool
     */
    public function batchAddShotsStatus(array $piles_ids): bool
    {
        $inserts = [];
        $newPilesIds = $this->where('id', 'in', $piles_ids)->column('id');

        foreach ($piles_ids as $piles_id) {
            if (!in_array($piles_id, $newPilesIds)) {
                $inserts[] = [
                    'id' => $piles_id,
                    'is_online' => self::IsOnlineNot,
                    'is_online_update_time' => date('Y-m-d H:i:s'),
                ];
            }
        }

        if (empty($inserts)) return true;
        $this->insertAll($inserts);
        return true;
    }

    public function updateIsOnline(int|array $id, int $is_online): int
    {
        $where = [];
        if (is_int($id)) {
            $where[] = ['id', '=', $id];
        } else {
            $where[] = ['id', 'in', $id];
        }
        if ($is_online === self::IsOnlineNot) {
            $where[] = ['is_online', '=', self::IsOnlineYes];
        } else if ($is_online === self::IsOnlineYes) {
            $where[] = ['is_online', '=', self::IsOnlineNot];
        } else {
            throw new RuntimeException('无效状态', [], RuntimeException::CodeServiceException);
        }

        return (int)$this->where($where)->update([
            'is_online' => $is_online,
            'is_online_update_time' => date('Y-m-d H:i:s')
        ]);
    }

    public function allPilesOffline(): bool
    {
        $where = [['is_online', '=', self::IsOnlineYes]];
        $this->where($where)->update([
            'is_online' => self::IsOnlineNot,
            'is_online_update_time' => date('Y-m-d H:i:s')
        ]);
        return true;
    }

    public function getIsOnline(int $id): int
    {
        return (int)$this->where('id', $id)->value('is_online');
    }
}
