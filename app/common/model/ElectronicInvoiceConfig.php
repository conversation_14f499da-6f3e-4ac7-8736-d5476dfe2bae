<?php

namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

class ElectronicInvoiceConfig extends Model
{
    protected $table = 'electronic_invoice_config';

    // 状态
    public const StatusNotEffective = 0; // 未生效
    public const StatusEffective = 1; // 已生效

    // 类型
    public const TypeCardTemplateInfo = 'card_template_info'; // 卡券模板信息
    public const TypeDevelopmentConfig = 'development_config'; // 开发配置

    // 键
    public const KeyCardAppid = 'card_appid'; // 卡券模板所属的AppID
    public const KeyCardId = 'card_id'; //卡券模板ID
    public const KeyLogoUrl = 'logo_url'; // Logo 地址
    public const KeyCallbackUrl = 'callback_url'; // 回调地址

    /**
     * 获取全部配置项
     *
     * @return array
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function getAllConfig(): array
    {
        return $this->formatAllConfigResult(
            $this->field(['type', 'key', 'value'])->select()->toArray()
        );
    }

    protected function formatAllConfigResult(array $data): array
    {
        $newData = [];
        foreach ($data as $key => $value) {
            $newData[$value['type']][$value['key']] = $value['value'];
        }

        return $newData;
    }

    public function updateCardTemplateCardAppid(string $cardAppid): int
    {
        $where = [];
        $where[] = ['type', '=', self::TypeCardTemplateInfo];
        $where[] = ['key', '=', self::KeyCardAppid];
        return (int)$this->where($where)->update([
            'value' => $cardAppid,
        ]);
    }

    public function updateCardTemplateCardId(string $cardId): int
    {
        $where = [];
        $where[] = ['type', '=', self::TypeCardTemplateInfo];
        $where[] = ['key', '=', self::KeyCardId];
        return (int)$this->where($where)->update([
            'value' => $cardId,
        ]);
    }

    public function effectiveConfig(string $type): int
    {
        return (int)$this->where('type', $type)->update([
            'status' => self::StatusEffective
        ]);
    }

}