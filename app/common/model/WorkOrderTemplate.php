<?php /** @noinspection PhpUnused */

namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

class WorkOrderTemplate extends Model
{
    protected $table = 'work_order_template';


    public function addTemplate(string $name, string $describe): int
    {
        return $this->insertGetId([
            'name' => $name,
            'describe' => $describe,
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ]);
    }

    public function updateTemplate(
        int $id, string $name, string $describe
    ): bool
    {
        return $this->where('id', '=', $id)->update([
                'name' => $name,
                'describe' => $describe,
                'update_time' => date('Y-m-d H:i:s')
            ]) > 0;
    }

    /**
     * 获取工单模板
     *
     * @param int $id
     * @return array|null
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function getTemplate(
        int $id
    ): ?array
    {
        $field = ['id', 'name', 'describe', 'create_time', 'update_time'];
        $data = $this->where('id', '=', $id)->field($field)->find();
        if (empty($data)) {
            return null;
        }
        return $data->toArray();
    }

    /**
     * 验证名称是否存在
     *
     * @param string $name
     * @param int|null $exclude_field_id
     * @return bool true: 已存在 false: 不存在
     */
    public function verifyNameExist(string $name, ?int $exclude_field_id = null): bool
    {
        if (is_null($exclude_field_id) === true) {
            return $this->where('name', '=', $name)
                    ->count() > 0;
        } else {
            return $this->where('name', '=', $name)
                    ->where('id', '<>', $exclude_field_id)
                    ->count() > 0;
        }

    }

    /**
     * 验证是否存在
     *
     * @param int $field_id
     * @return bool
     */
    public function isTemplateExists(int $field_id): bool
    {
        return $this->where('id', '=', $field_id)->count() > 0;
    }

}
