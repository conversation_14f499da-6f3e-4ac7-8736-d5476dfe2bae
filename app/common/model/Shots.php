<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\common\model;

use app\ms\Api;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\facade\Db;
use think\Model;

class Shots extends Model
{
    protected $table = 'shots';

    // 是否插枪
    public const IsPutBackNot = 0; // 否
    public const IsPutBackYes = 1; // 是
    public const IsPutBackUnknown = 2; // 未知

    // 是否检测离线
    public const IsDetectionYes = 1; // 检测
    public const IsDetectionNot = 0; // 不检测

    // 是否已删除
    public const IsDelYes = 1; // 已删除
    public const IsDelNot = 0; // 未删除

    public function verifyIfExists(int $shots_id): bool
    {
        $where = [
            ['id', '=', $shots_id],
            ['is_del', '=', self::IsDelNot]
        ];
        return $this->where($where)->count() > 0;
    }

    /**
     * 充电枪总数
     * @param int $corp_id [default=0] 运营商ID 不传递或传递0则表示查询所有运营商的
     * @return int
     */
    public function totalCount(int $corp_id = 0): int
    {
        $where = [
            ['s.is_del', '=', Shots::IsDelNot]
        ];
        if ($corp_id > 0) {
            $where[] = ['s.corp_id', '=', $corp_id];
        }

        /**
         * @var Query $this
         */
        return $this->alias('s')
            ->where($where)
            ->count('s.id');
    }

//    /**
//     * !!! 已放弃使用
//     * 充电枪状态统计
//     *
//     * @param int $corp_id [default=0] 运营商ID 不传递或传递0则表示查询所有运营商的
//     * @param int $station_id [default=0] 充电站ID 不传递或传递0则表示查询所有充电站的
//     * @return array
//     * @throws Throwable
//     */
//    public function statusStatistics(int $corp_id = 0, int $station_id = 0): array
//    {
//        try {
//            $where = [];
//
//            if (!empty($corp_id)) {
//                $where[] = ['corp_id', '=', $corp_id];
//            }
//
//            if (!empty($station_id)) {
//                $where[] = ['station_id', '=', $station_id];
//            }
//
//
//            /**
//             * @var Query $query
//             */
//            $query = $this;
//            $chargingCount = $query->where($where)->where('work_status', '=', self::WorkStatusCharging)->count();
//
//            $idleCount = $query->where($where)->where('work_status', '=', self::WorkStatusIdle)->count();
//
//            $offlineCount = $query->where($where)->where('is_online', '=', self::IsOnlineNot)->count();
//
//            $faultCount = $query->where($where)->where('is_fault', '=', self::IsFaultYes)->count();
//
//            $totalCount = $query->where($where)->count();
//
//            return [
//                [
//                    'status' => -1,
//                    'count' => $totalCount
//                ],
//                [
//                    'status' => self::StatusOffline,
//                    'count' => $offlineCount
//                ],
//                [
//                    'status' => self::StatusFault,
//                    'count' => $faultCount
//                ],
//                [
//                    'status' => self::StatusIdle,
//                    'count' => $idleCount
//                ],
//                [
//                    'status' => self::StatusCharging,
//                    'count' => $chargingCount
//                ],
//            ];
//
//        } catch (Throwable $e) {
//            ExceptionLogCollector::collect($e);
//            throw $e;
//        }
//    }

//
//    /**
//     * !!! 已放弃使用
//     * 查询充电枪在线数量
//     *
//     * @param int $corp_id [default=0] 运营商ID
//     * @return int
//     * @throws DbException
//     */
//    public function onlineCount(int $corp_id = 0): int
//    {
//        $where = [];
//        $where[] = ['is_online', '=', self::IsOnlineYes];
//        if (!empty($corp_id)) {
//            $where[] = ['corp_id', '=', $corp_id];
//        }
//
//        try {
//            return $this->where($where)->count();
//        } catch (DbException $e) {
//            ExceptionLogCollector::collect($e);
//            throw $e;
//        }
//    }

//    public function updatePilesShotsStatus(int $piles_id, int $status): int
//    {
//        return (int)$this->where('piles_id', $piles_id)->update([
//            'status' => $status,
//            'status_update_time' => date('Y-m-d H:i:s')
//        ]);
//    }

    public function getPilesShotIds(int $piles_id): array
    {
        return $this->where('piles_id', $piles_id)->column('id');
    }

//    public function updateStatus(int $shotId, int $status): int
//    {
//        return (int)$this->where('id', $shotId)->update([
//            'status' => $status,
//            'status_update_time' => date('Y-m-d H:i:s')
//        ]);
//    }

//    public function getStatus(int $shotId): int
//    {
//        return (int)$this->where('id', $shotId)->value('status');
//    }


//    /**
//     * !!! 已放弃使用
//     * 查询离线超过1小时的充电枪
//     *
//     * @return array
//     * @throws DbException
//     * @throws DataNotFoundException
//     * @throws ModelNotFoundException
//     */
//    public function queryOfflineMoreThanOneHour(): array
//    {
//        return $this->where('is_online', '=', self::IsOnlineNot)
//            ->where('is_detection', '=', self::IsDetectionYes)
//            ->where('status_update_time', '<=', date('Y-m-d H:i:s', strtotime('-1 hour')))
//            ->field(['id', 'piles_id', 'status_update_time'])
//            ->select()->toArray();
//    }

//    /**
//     * !!! 已经放弃使用
//     * @param int $id
//     * @param int $is_fault
//     * @return void
//     */
//    public function updateShotIsFault(int $id, int $is_fault): void
//    {
//        $this->where('id', $id)->update([
//            'is_fault' => $is_fault
//        ]);
//    }
//
//
//    /**
//     * !!! 已经放弃使用
//     *
//     * @param int $id
//     * @param int|null $is_online
//     * @return void
//     */
//    public function updateShotIsOnline(int $id, int $is_online = null): void
//    {
//        $this->where('id', $id)->update([
//            'is_online' => $is_online
//        ]);
//    }
//
//    /**
//     * !!! 已经放弃使用
//     *
//     * @param int $id
//     * @return int|null
//     */
//    public function getWorkStatus(int $id): ?int
//    {
//        return $this->where('id', $id)->value('work_status');
//    }
//
//    /**
//     * !!! 已经放弃使用
//     *
//     * @param int $id
//     * @param int $work_status
//     * @return void
//     */
//    public function updateWorkStatus(int $id, int $work_status): void
//    {
//        $this->where('id', $id)->update([
//            'work_status' => $work_status
//        ]);
//    }
//
//    /**
//     * !!! 已经放弃使用
//     *
//     * @param int $id
//     * @return int|null
//     */
//    public function getIsOnline(int $id): ?int
//    {
//        return $this->where('id', $id)->value('is_online');
//    }

//    /**
//     * !!! 已经放弃使用
//     *
//     * @param int $id
//     * @param int $is_online
//     * @return void
//     */
//    public function updateIsOnline(int $id, int $is_online): void
//    {
//        $this->where('id', $id)->update([
//            'is_online' => $is_online,
//            'status_update_time' => date('Y-m-d H:i:s')
//        ]);
//    }

    public function getStationName(int $shots_id): ?string
    {
        /**
         * @var Query $query
         */
        $query = $this;
        return $query->alias('s')
            ->leftJoin('stations ss', 'ss.id = s.station_id')
            ->where('s.id', '=', $shots_id)
            ->value('ss.name');
    }

    /**
     * @return int
     * @throws DbException
     */
    public function deleteAll(): int
    {
        return Db::name($this->table)->delete(true);
    }

    public function deleteStationAllShots(int $station_id): int
    {
        $this->where('station_id', '=', $station_id)->delete();
        return $this->getNumRows();
    }

    /**
     * 获取充电枪数据
     *
     * @param int $shots_id
     * @param array|null $fields
     * @return ?array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getShotsData(int $shots_id, ?array $fields = null): ?array
    {
        if (empty($fields)) {
            $fields = [
                's.id', 's.name', 's.sequence', 'ps.is_online', 'ss.is_fault', 'ss.work_status',
                's.station_id', 's.corp_id', 's.piles_id', 'sei.tariff_group_id', 'p.line', 'p.centralized_controller_id',
                's.link_status', 'c.name as corp_name', 'stat.name as station_name',
                'p.name as piles_name', 't.period_json_sum','t.surcharge','t.name as tariff_name','s.status',
                'ps.is_online as piles_is_online', 'sei.is_support_reservation'
            ];
        }

        /**
         * @var Query $query
         */
        $query = $this;
        $shots = $query->alias('s')
            ->join('shots_status ss', 'ss.id = s.id')
            ->join('corp c', 's.corp_id = c.id')
            ->join('stations stat', 's.station_id = stat.id')
            ->join('stations_extra_info sei', 'sei.id = s.station_id')
            ->join('piles p', 's.piles_id = p.id')
            ->join('piles_status ps', 's.piles_id = ps.id')
            ->join('tariff_group t', 'sei.tariff_group_id = t.id')
            ->withAttr("is_online", function ($value) {
                if ($value === 1) {
                    return 2;
                } else {
                    return 1;
                }
            })
            ->field($fields)
            ->where('s.id', $shots_id)
            ->where('s.is_del', '=', Shots::IsDelNot)
            ->find();
        if (empty($shots)) {
            return null;
        }
        $shots = $shots->toArray();
        // ========== 从微服务查询充电桩是否有单独的费率ID ==========
        $tariff_id = Api::send("/device/piles/tariff",'GET',['pile_id' => $shots_id / 100])['data'];
        if (!empty($tariff_id)) {
            $shots['tariff_id'] = $tariff_id;
            // 如果有则替换原来的费率
            $tariff = Db::name('tariff_group')->where('id', $tariff_id)->field('id,name,period_json_sum,surcharge')->find();
            $shots['period_json_sum'] = $tariff['period_json_sum'];
            $shots['surcharge'] = $tariff['surcharge'];
            $shots['tariff_group_id'] = $tariff_id;
            $shots['tariff_name'] = $tariff['name'];
        }
        return $shots;
    }

    /**
     * 批量添加充电枪
     *
     * @param array $shots_list
     * @return bool
     */
    public function batchAddShots(array $shots_list): bool
    {
        $inserts = [];
        $shots_ids = [];
        foreach ($shots_list as $shot) {
            $inserts[] = [
                'id' => $shot['id'],
                'name' => $shot['name'],
                'corp_id' => $shot['corp_id'],
                'station_id' => $shot['station_id'],
                'piles_id' => $shot['piles_id'],
                'sequence' => $shot['sequence'],
                'port_num' => $shot['port_num'],
                'qrcode' => '',
                'is_online' => 0,
                'work_status' => 0,
                'link_status' => 1,
                'opt_id' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'status' => 0,
                'is_put_back' => Shots::IsPutBackNot,
                'is_fault' => 0,
                'status_update_time' => date('Y-m-d H:i:s'),
                'is_detection' => Shots::IsDetectionNot
            ];
            $shots_ids[] = $shot['id'];
        }
        $this->insertAll($inserts);
        (new ShotsStatus())->batchAddShotsStatus($shots_ids);
        (new ShotsQrcode())->batchAddShotsQrcode($shots_ids);

        return true;
    }

    /**
     * @param array $piles_ids
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function batchGetPilesShotsStatus(array $piles_ids): array
    {
        /**
         * @var Query $query
         */
        $query = $this;

        $where = [
            ['s.piles_id', 'in', $piles_ids],
            ['s.is_del', '=', Shots::IsDelNot]
        ];

        $data = $query->alias('s')
            ->leftJoin('shots_status ss', 's.id = ss.id')
            ->field(['s.piles_id', 's.sequence as shots_number', 'ss.work_status', 'ss.is_fault'])
            ->where($where)
            ->select()
            ->toArray();
        $map = [];
        foreach ($data as $value) {
            $map[$value['piles_id']][] = [
                'shots_number' => $value['shots_number'],
                'work_status' => $value['work_status'],
                'is_fault' => $value['is_fault'],
            ];
        }
        return $map;
    }

    public function isExistent(int $shots_id): bool
    {
        $where = [
            ['id', '=', $shots_id],
            ['is_del', '=', self::IsDelNot]
        ];
        return $this->where($where)->count() > 0;
    }

    public function corpStationIsExistent(int $corp_id, int $shots_id): bool
    {
        $where = [
            ['id', '=', $shots_id],
            ['is_del', '=', self::IsDelNot],
            ['corp_id', '=', $corp_id]
        ];
        return $this->where($where)->count() > 0;
    }
}
