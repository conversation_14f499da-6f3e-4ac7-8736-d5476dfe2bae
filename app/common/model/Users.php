<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\model;


use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\Model;

class Users extends Model
{
    protected $table = 'users';


    /**
     * 解冻用户余额
     *
     * @param int $id 用户ID
     * @param int $increase_balance 增加的用户余额
     * @param int $reduce_freeze_balance 减少的用户冻结余额
     * @return int 更新的函数
     */
    public function unfreeze_user_balance(int $id, int $increase_balance, int $reduce_freeze_balance): int
    {
        if ($id <= 0 || ($increase_balance === 0 && $reduce_freeze_balance === 0)) return 0;

        return (int)$this->where('id', $id)
            ->limit(1)
            ->update([
                'balance' => Db::raw('balance+' . $increase_balance),
                'freeze_balance' => Db::raw('freeze_balance-' . $reduce_freeze_balance),
            ]);
    }


    public function increaseBalance(int $id, int $increase_balance): int
    {
        if ($id <= 0 || $increase_balance === 0) return 0;

        return (int)$this->where('id', $id)
            ->limit(1)
            ->update([
                'balance' => Db::raw('balance+' . $increase_balance),
                'update_time' => date('Y-m-d H:i:s')
            ]);
    }

    /**
     * 查询用户绑定的微信信息
     *
     * @param int $userId 用户ID
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getUserWechatData(int $userId): array
    {
        return $this->where('id', $userId)->field(['open_id'])->find();
    }

    /**
     * 更新用户的目前正在充电的订单已使用金额(保留4为小数)
     *
     * @param int $userId 用户ID
     * @param int $amountCharged 目前正在充电的订单已使用金额(保留4为小数)
     * @return int 响应的记录条数
     */
    public function updateAmountCharged(int $userId, int $amountCharged): int
    {
        return (int)$this->where('id', $userId)->update([
            'amount_charged' => $amountCharged,
            'update_time' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 查询用户可用余额
     *
     * @param int $userId 用户ID
     * @return int 可用余额(单位:分)
     */
    public function getUserAvailableBalance(int $userId): int
    {
        return $this->where('id', $userId)->value('credit_limit + balance');
    }

    public function getUsersNameMap(array $user_ids): array
    {
        return $this->where('id', 'in', $user_ids)->column('phone', 'id');
    }
}
