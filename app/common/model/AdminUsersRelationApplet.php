<?php
/** @noinspection PhpUnused */

namespace app\common\model;

use think\db\exception\DbException;
use think\Model;

class AdminUsersRelationApplet extends Model
{
    protected $table = 'admin_users_relation_applet';


    /**
     * 验证 OPEN ID 是否已经存在
     *
     * @param string $open_id
     * @return bool true: 存在 false: 不存在
     */
    public function isOpenIDExist(string $open_id): bool
    {
        return $this->where('open_id', '=', $open_id)->count() > 0;
    }

    /**
     * 获取 open id 关联的用户ID
     *
     * @param string $open_id
     * @return ?int
     */
    public function getOpenIDRelationUserId(string $open_id): ?int
    {
        $admin_user_id = $this->where('open_id', '=', $open_id)->value('admin_user_id');
        if (empty($admin_user_id)) return null;
        return $admin_user_id;
    }

    /**
     * 获取 open id
     *
     * @param int $admin_user_id
     * @return ?string
     */
    public function getOpenID(int $admin_user_id): ?string
    {
        $openid = $this->where('admin_user_id', '=', $admin_user_id)
            ->value('open_id');
        if (empty($openid)) return null;
        return $openid;
    }


    /**
     * 绑定微信小程序
     *
     * @param int $admin_user_id
     * @param string $open_id
     * @return bool
     */
    public function bindApplet(int $admin_user_id, string $open_id): bool
    {
        return $this->insert([
                'admin_user_id' => $admin_user_id,
                'open_id' => $open_id
            ]) > 0;
    }

    /**
     * 解除绑定微信小程序
     *
     * @param int $admin_user_id
     * @return bool
     */
    public function unbindApplet(int $admin_user_id): bool
    {
        return $this->where([
            ['admin_user_id', '=', $admin_user_id]
        ])->delete();
    }


}