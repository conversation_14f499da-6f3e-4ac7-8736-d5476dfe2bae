<?php /** @noinspection PhpUnused */

namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Model;

class WorkOrderTemplateField extends Model
{
    protected $table = 'work_order_template_field';

    /**
     * 获取指定字段绑定了多少个模板
     *
     * @param int $field_id
     * @return int
     */
    public function getFieldBindTemplateCount(int $field_id): int
    {
        return $this->where('field_id', '=', $field_id)->count();
    }

    /**
     * 验证是否存在
     *
     * @param int $template_id
     * @param int $field_id
     * @return bool
     */
    public function isExist(int $template_id, int $field_id): bool
    {
        return $this->where('template_id', '=', $template_id)->where('field_id', '=', $field_id)->count() > 0;
    }

    public function addField(int $template_id, int $field_id, int $sort): bool
    {
        return $this->insert([
                'template_id' => $template_id,
                'field_id' => $field_id,
                'sort' => $sort
            ]) > 0;
    }

    public function deleteField(int $template_id, int $field_id): bool
    {
        return $this->where('template_id', '=', $template_id)
                ->where('field_id', '=', $field_id)
                ->delete() > 0;
    }

    /**
     * 查询模板的所有字段
     *
     * @param int $template_id
     * @return array
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function getFields(int $template_id): array
    {
        /**
         * @var Query $query
         */
        $query = $this;

        $field = ['tf.field_id', 'tf.sort', 'f.name', 'f.key', 'f.type', 'f.is_require', 'f.options'];
        return $query->alias('tf')
            ->leftJoin('work_order_field f', 'f.id = tf.field_id')
            ->where('template_id', '=', $template_id)
            ->field($field)
            ->select()
            ->toArray();
    }
}
