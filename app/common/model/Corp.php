<?php

namespace app\common\model;

use app\common\log\LogCollector;
use think\Model;

/**
 * 运营商模型
 * @package app\admin\model
 */
class Corp extends Model
{
    protected $table = 'corp';
    protected $json = ['city_id'];
    // 设置JSON数据返回数组
    protected $jsonAssoc = true;

    // 审核状态，1-未审核，2-审核通过
    public const AuditStatusWait = 1; // 未审核
    public const AuditStatusPassed = 2; // 审核通过
    public const AuditStatusNotPassed = 3; // 审核未通过


    // 运营商状态，1-关闭，2-开启
    public const StatusClose = 1; // 关闭
    public const StatusOpen = 2; // 开启

    public function isExistent(int $corp_id): bool
    {
        return $this->where('id', '=', $corp_id)->count() > 0;
    }

    public function addCorpGetId(
        string $name, string $icon_url, string $province, string $city,
        string $district, string $contact, string $phone, array $city_id
    ): int
    {
        return $this->insertGetId([
            'name' => $name,
            'icon_url' => $icon_url,
            'province' => $province,
            'city' => $city,
            'district' => $district,
            'parent_id' => 0,
            'audit_status' => self::AuditStatusPassed,
            'contact' => $contact,
            'phone' => $phone,
            'opt_id' => 1,
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s'),
            'status' => self::StatusOpen,
            'token' => '',
            'city_id' => json_encode($city_id),
        ]);
    }

    public function addCorpData(
        int    $id, string $name, string $icon_url, string $province, string $city,
        string $district, string $contact, string $phone, array $city_id, string $create_time
    ): bool
    {
        return $this->insert([
                'id' => $id,
                'name' => $name,
                'icon_url' => $icon_url,
                'province' => $province,
                'city' => $city,
                'district' => $district,
                'parent_id' => 0,
                'audit_status' => self::AuditStatusPassed,
                'contact' => $contact,
                'phone' => $phone,
                'opt_id' => 1,
                'create_time' => $create_time,
                'update_time' => $create_time,
                'status' => self::StatusOpen,
                'token' => '',
                'city_id' => json_encode($city_id),
            ]) > 0;
    }

    public function addCorpsData(array $corps): int
    {
        $inserts = [];
        foreach ($corps as $corp) {
            $inserts[] = [
                'id' => $corp['id'],
                'name' => $corp['name'],
                'icon_url' => $corp['icon_url'],
                'province' => $corp['province'],
                'city' => $corp['city'],
                'district' => $corp['district'],
                'parent_id' => 0,
                'audit_status' => self::AuditStatusPassed,
                'contact' => $corp['contact'],
                'phone' => $corp['phone'],
                'opt_id' => 1,
                'create_time' => $corp['create_time'],
                'status' => self::StatusOpen,
                'token' => '',
                'city_id' => $corp['city_id'],
                'update_time' => date('Y-m-d H:i:s')
            ];
        }
        return $this->insertAll($inserts);
    }


    public function updateCorpData(
        int    $id, string $name, string $icon_url, string $province, string $city,
        string $district, string $contact, string $phone, array $city_id
    ): bool
    {
        $this->where('id', '=', $id)->update([
            'name' => $name,
            'icon_url' => $icon_url,
            'province' => $province,
            'city' => $city,
            'district' => $district,
            'contact' => $contact,
            'phone' => $phone,
            'city_id' => json_encode($city_id),
            'update_time' => date('Y-m-d H:i:s')
        ]);
        LogCollector::collectorRunLog('$this->getNumRows() = ' . $this->getNumRows());
        return $this->getNumRows() > 0;
    }

    public function deleteCorpData(int $id): bool
    {
        return $this->where('id', '=', $id)->delete();
    }

    public function deleteAllCorpData(): bool
    {
        return $this->delete();
    }

    public function getAllCorpId(): array
    {
        return $this->column('id');
    }
}
