<?php

namespace app\common\model;

use think\Model;

class WorkOrderRelation extends Model
{
    protected $table = 'work_order_relation';

    // 设备类型 1:充电桩 2:充电枪 3:能源路由器 9:无
    public const DeviceTypePiles = 1; // 充电桩
    public const DeviceTypeShots = 2; // 充电枪
    public const DeviceTypeCentralizedController = 3; // 能源路由器
    public const DeviceTypeNone = 9; // 无

    public function addRelation(
        string $work_order_id,
        int    $corp_id,
        int    $station_id,
        int    $device_type,
        string $device_id
    ): bool
    {
        return $this->insert([
                'work_order_id' => $work_order_id,
                'corp_id' => $corp_id,
                'station_id' => $station_id,
                'device_type' => $device_type,
                'device_id' => $device_id
            ]) > 0;
    }

    /**
     * 验证运营商工单是否存在
     *
     * @param int $corp_id
     * @param string $work_order_id
     * @return bool
     */
    public function isCorpWorkOrderExist(int $corp_id, string $work_order_id): bool
    {
        $where = [
            ['corp_id', '=', $corp_id],
            ['work_order_id', '=', $work_order_id]
        ];
        return $this->where($where)->count() > 0;
    }
}
