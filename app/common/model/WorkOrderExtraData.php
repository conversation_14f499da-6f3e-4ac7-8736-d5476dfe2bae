<?php /** @noinspection PhpUnused */

namespace app\common\model;

use think\Model;

class WorkOrderExtraData extends Model
{
    protected $table = 'work_order_extra_data';

    public function addExtraData(string $work_order_id, array $extra_data): bool
    {
        return $this->insert([
                'work_order_id' => $work_order_id,
                'extra_data' => json_encode($extra_data)
            ]) > 0;
    }

    public function getExtraData(string $work_order_id): array
    {
        $extra_data = $this->where('work_order_id', '=', $work_order_id)->value('extra_data');
        if (empty($extra_data)) return [];
        return json_decode($extra_data, true);
    }

    public function updateExtraData(string $work_order_id, array $extra_data): bool
    {
        return $this->where('work_order_id', '=', $work_order_id)->update([
                'extra_data' => $extra_data
            ]) > 0;
    }
}
