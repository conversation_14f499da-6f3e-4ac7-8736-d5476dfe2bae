<?php
/** @noinspection PhpUnused */

namespace app\common\model;


use app\common\cache\redis\entity\AdminLoginUser;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Model;

class Activity extends Model
{
    protected $table = 'activity';

    // 活动开关
    public const StatusOpen = 1; // 开启
    public const StatusClose = 0; // 关闭

    public function addActivity(
        AdminLoginUser $loginUser,
        string         $title,
        string         $describe,
        int            $discount
    ): ?int
    {
        $activity_id = $this->insertGetId([
            'title' => $title,
            'describe' => $describe,
            'discount' => $discount,
            'corp_id' => $loginUser->corp_id,
            'status' => self::StatusOpen,
            'create_user_id' => $loginUser->id,
            'create_time' => date("Y-m-d H:i:s"),
            'update_time' => date("Y-m-d H:i:s"),
        ]);
        if (empty($activity_id)) return null;

        return $activity_id;
    }

    public function updateActivity(
        int    $activity_id,
        string $title,
        string $describe,
        int    $discount,
        int    $status
    ): bool
    {
        $result = $this->where('id', '=', $activity_id)->update([
            'title' => $title,
            'describe' => $describe,
            'discount' => $discount,
            'status' => $status,
            'update_time' => date("Y-m-d H:i:s"),
        ]);
        return $result > 0;
    }

    public function removeActivity(int $activity_id): bool
    {
        $where = [
            ['id', '=', $activity_id]
        ];
        return $this->where($where)->delete();
    }

    /**
     * @param array $filter
     * @param int $page
     * @param int $limit
     * @param string $order_name
     * @param string $order_type
     * @return array
     * @throws DbException
     */
    public function getListData(array $filter, int $page, int $limit, string $order_name, string $order_type): array
    {

        $where = [];
        if (isset($filter['filter_corp_id'])) {
            $where[] = ['a.corp_id', '=', $filter['filter_corp_id']];
        }
        if (isset($filter['filter_station_id'])) {
            $where[] = ['as.station_id', '=', $filter['filter_station_id']];
        }
        if (isset($filter['filter_title'])) {
            $where[] = ['a.title', 'like', '%' . $filter['filter_title'] . '%'];
        }
        if (isset($filter['filter_status'])) {
            $where[] = ['a.status', '=', $filter['filter_status']];
        }


        $field = [
            'a.id', 'a.title', 'a.describe', 'a.discount', 'a.status',
            'at.start_time', 'at.end_time', 's.name as station_name',
            'a.create_time', 'a.update_time', 'au.name as create_username',
        ];
        /**
         * @var Query $this
         */
        return $this->alias('a')
            ->leftJoin('activity_stations as', 'as.activity_id = a.id')
            ->leftJoin('activity_time at', 'at.activity_id = a.id')
            ->leftJoin('admin_users au', 'au.id = a.create_user_id')
            ->leftJoin('stations s', 's.id = as.station_id')
            ->field($field)
            ->where($where)
            ->order($order_name, $order_type)
            ->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();
    }

    /**
     * @param int $activity_id
     * @param int|null $filter_corp_id
     * @return array|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getInfoData(int $activity_id, ?int $filter_corp_id = null): ?array
    {
        $where = [['a.id', '=', $activity_id]];
        if (!empty($filter_corp_id)) $where[] = ['a.corp_id', '=', $filter_corp_id];

        $field = [
            'a.id', 'a.title', 'a.describe', 'a.discount', 'a.status',
            'at.start_time', 'at.end_time', 'as.station_id', 's.name as station_name',
            'a.create_time', 'a.update_time', 'a.create_user_id', 'au.name as create_username',
        ];
        /**
         * @var Query $this
         */
        $data = $this->alias('a')
            ->leftJoin('activity_stations as', 'as.activity_id = a.id')
            ->leftJoin('activity_time at', 'at.activity_id = a.id')
            ->leftJoin('admin_users au', 'au.id = a.create_user_id')
            ->leftJoin('stations s', 's.id = as.station_id')
            ->field($field)
            ->where($where)
            ->find();
        if (empty($data)) return null;
        return $data->toArray();
    }

    /**
     * @param int $activity_id
     * @param array $field
     * @return array|null
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function getActivity(int $activity_id, array $field = []): ?array
    {
        $where = [
            ['id', '=', $activity_id]
        ];
        $data = $this->where($where)->field($field)->find();
        if (empty($data)) return null;
        return $data->toArray();
    }

    /**
     * @param int $station_id
     * @param string|null $charging_time
     * @return int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getStationDiscount(int $station_id, ?string $charging_time = null): int
    {
        if(is_null($charging_time)) $charging_time = date("Y-m-d H:i:s");

        $where = [
            ['a.status', '=', self::StatusOpen],
            ['as.station_id', '=', $station_id],
            ['at.start_time', '<=', $charging_time],
            ['at.end_time', '>=', $charging_time],
        ];
        /**
         * @var Query $this
         */
        $data = $this->alias('a')
            ->leftJoin('activity_stations as', 'as.activity_id = a.id')
            ->leftJoin('activity_time at', 'at.activity_id = a.id')
            ->where($where)->field(['a.discount'])->find();
        if (empty($data)) return 100;
        return $data['discount'];
    }
}