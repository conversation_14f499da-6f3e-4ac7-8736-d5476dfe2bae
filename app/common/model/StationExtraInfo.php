<?php
/** @noinspection PhpUnused */

namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Model;

class StationExtraInfo extends Model
{
    protected $table = 'stations_extra_info';
    protected $json = ['pictures'];

    // 状态
    public const StatusNormal = 1; // 正常
    public const StatusMaintenance = 2; // 维护中
    public const StatusNotOpen = 3; // 未开放

    // 充电桩位置
    public const TagPosLand = 1; // 地上
    public const TagPosBasement = 2; // 地库

    // 场站服务卫生间标识
    public const TagToiletNot = 1; // 无
    public const TagToiletYes = 2; // 有

    // 场站服务雨棚标识
    public const TagCanopyNot = 1; // 无
    public const TagCanopyYes = 2; // 有

    // 场站服务休息室标识
    public const TagLoungeNot = 1; // 无
    public const TagLoungeYes = 2; // 有

    // 场站特色即插即用标识
    public const TagPlugAndPlayNot = 1; // 无
    public const TagPlugAndPlayYes = 2; // 有

    // 场站特色充电保险标识
    public const TagChargingInsuranceNot = 1; // 无
    public const TagChargingInsuranceYes = 2; // 有

    // 场站特色充电电池防护标识
    public const TagBatteryProtectionNot = 1; // 无
    public const TagBatteryProtectionYes = 2; // 有

    // 场站电桩超快充标识
    public const TagUltraFastChargingNot = 1; // 无
    public const TagUltraFastChargingYes = 2; // 有

    // 场站电桩快充标识
    public const TagFastChargingNot = 1; // 无
    public const TagFastChargingYes = 2; // 有

    // 场站电桩慢充标识
    public const TagSlowChargingNot = 1; // 不持支
    public const TagSlowChargingYes = 2; // 支持

    public function insertDefaultData(int $station_id): bool
    {
        return $this->insert([
                'id' => $station_id,
                'tariff_group_id' => 0,
                'status' => self::StatusNotOpen,
                'pictures' => [],
                'work_time' => '',
                'tag_pos' => self::TagPosLand,
                'tag_park' => '',
                'place_rate' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'charge' => '',
                'charge_phone' => '',
                'tag_toilet' => self::TagToiletNot,
                'tag_canopy' => self::TagCanopyNot,
                'tag_rest' => self::TagLoungeNot,
                'tag_pnp' => self::TagPlugAndPlayNot,
                'tag_insure' => self::TagChargingInsuranceNot,
                'tag_protect' => self::TagBatteryProtectionNot,
                'tag_ultrafast' => self::TagUltraFastChargingNot,
                'tag_fast' => self::TagFastChargingNot,
                'tag_slow' => self::TagSlowChargingNot,
            ]) > 0;
    }

    /**
     * @param int $station_id
     * @return bool
     */
    public function isExistence(int $station_id): bool
    {
        return $this->where('id', '=', $station_id)->count() > 0;
    }

    /**
     * @param array $station_ids
     * @return bool
     */
    public function insertDefaultDataAll(array $station_ids): bool
    {
        $inserts = [];
        foreach ($station_ids as $station_id) {
            if ($this->isExistence($station_id) === true) {
                continue;
            }
            $inserts[] = [
                'id' => $station_id,
                'tariff_group_id' => 0,
                'status' => self::StatusNotOpen,
                'pictures' => [],
                'work_time' => '',
                'tag_pos' => self::TagPosLand,
                'tag_park' => '',
                'place_rate' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
                'charge' => '',
                'charge_phone' => '',
                'tag_toilet' => self::TagToiletNot,
                'tag_canopy' => self::TagCanopyNot,
                'tag_rest' => self::TagLoungeNot,
                'tag_pnp' => self::TagPlugAndPlayNot,
                'tag_insure' => self::TagChargingInsuranceNot,
                'tag_protect' => self::TagBatteryProtectionNot,
                'tag_ultrafast' => self::TagUltraFastChargingNot,
                'tag_fast' => self::TagFastChargingNot,
                'tag_slow' => self::TagSlowChargingNot,
            ];
        }
        if (empty($inserts)) return true;
        return $this->insertAll($inserts) > 0;
    }

    /**
     * @param int $station_id
     * @return array|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getTariffGroup(int $station_id): ?array
    {
        $where = [
            ['b.id', '=', $station_id]
        ];
        /**
         * @var Query $this
         */
        $tariff_group = $this->alias('b')
            ->join('tariff_group c', 'b.tariff_group_id = c.id')
            ->field('c.id as billing_mode_id,c.sharp_fee,c.peak_fee,c.flat_fee,c.valley_fee,
                c.sharp_ser_fee,c.peak_ser_fee,c.flat_ser_fee,c.valley_ser_fee,c.loss_rate,c.period_codes')
            ->where($where)
            ->find();
        if (empty($tariff_group)) return null;
        return $tariff_group->toArray();
    }

}
