<?php

namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

class WorkOrderField extends Model
{
    protected $table = 'work_order_field';

    //     `type`  2:多行文本 3:下拉框 4:多选框 5:数字 6:日期 7:图片',
    //    `is_require`  TINYINT UNSIGNED NOT NULL COMMENT '是否必填 1:是 0:否',
    // 类型
    public const TypeSingleLineText = 1; // 单行文本
    public const TypeMultilineText = 2; // 多行文本
    public const TypeSelect = 3; // 下拉框
    public const TypeMultipleSelect = 4; // 多选框
    public const TypeNumber = 5; // 数字
    public const TypeDate = 6; // 日期
    public const TypeImage = 7; // 图片

    // 是否必填
    public const IsRequireYes = 1; // 必填
    public const IsRequireNot = 0; // 非必填

    public function deleteField(int $id): bool
    {
        return $this->where('id', '=', $id)->delete();
    }

    public function addField(string $name, string $key, int $type, int $is_require, array $options = []): int
    {
        return $this->insertGetId([
            'name' => $name,
            'key' => $key,
            'type' => $type,
            'is_require' => $is_require,
            'options' => json_encode($options),
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ]);
    }

    public function updateField(
        int $id, string $name, string $key, int $type,
        int $is_require, array $options
    ): bool
    {
        return $this->where('id', '=', $id)->update([
                'name' => $name,
                'key' => $key,
                'type' => $type,
                'is_require' => $is_require,
                'options' => json_encode($options),
                'update_time' => date('Y-m-d H:i:s')
            ]) > 0;
    }

    /**
     * @param int $id
     * @return array|null
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function getField(
        int $id
    ): ?array
    {
        $field = ['id', 'name', 'key', 'type', 'is_require', 'options', 'create_time', 'update_time'];
        $data = $this->where('id', '=', $id)->field($field)->find();
        if (empty($data)) {
            return null;
        }
        $data['options'] = json_decode($data['options'], true);
        return $data->toArray();
    }

    /**
     * 验证名称是否存在
     *
     * @param string $name
     * @param int|null $exclude_field_id
     * @return bool true: 已存在 false: 不存在
     */
    public function verifyNameExist(string $name, ?int $exclude_field_id = null): bool
    {
        if (is_null($exclude_field_id) === true) {
            return $this->where('name', '=', $name)
                    ->count() > 0;
        } else {
            return $this->where('name', '=', $name)
                    ->where('id', '<>', $exclude_field_id)
                    ->count() > 0;
        }

    }

    /**
     * 验证键值是否存在
     *
     * @param string $key
     * @param int|null $exclude_field_id
     * @return bool true: 已存在 false: 不存在
     */
    public function verifyKeyExist(string $key, ?int $exclude_field_id = null): bool
    {
        if (is_null($exclude_field_id) === true) {
            return $this->where('key', '=', $key)
                    ->count() > 0;
        } else {
            return $this->where('key', '=', $key)
                    ->where('id', '<>', $exclude_field_id)
                    ->count() > 0;
        }
    }

    /**
     * 验证是否存在
     *
     * @param int $field_id
     * @return bool
     */
    public function isFieldExists(int $field_id): bool
    {
        return $this->where('id', '=', $field_id)->count() > 0;
    }

}
