<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\common\model;

use app\common\cache\redis\entity\AppletLoginUser;
use Closure;
use think\db\exception\DbException;
use think\db\Query;
use think\facade\Db;
use think\Model;

class Stations extends Model
{
    protected $table = 'stations';
    protected $json = ['city_id', 'pictures'];
    // 设置JSON数据返回数组
    protected $jsonAssoc = true;

    // 是否已删除
    public const IsDelYes = 1; // 已删除
    public const IsDelNot = 0; // 未删除

    /**
     * 查询充电站名称
     *
     * @param array $ids 充电站ID集合
     * @return array
     */
    public function getStationNames(array $ids): array
    {
        return $this->where('id', 'in', $ids)->column('name', 'id');
    }

    public function getStationName(int $station_id): ?string
    {
        return $this->where('id', '=', $station_id)->value('name');
    }

    public function deleteAll(): int
    {
        return 0;
//        return Db::name($this->table)->delete(true);
    }

    protected function arrangeWhere(array $data): Closure
    {
        return function ($query) use ($data) {
            $query->where('a.is_del', '=', self::IsDelNot);

            if (isset($data['id']) && $data['id']) {
                $query->where('a.id', $data['id']);
            }
            if (isset($data['status']) && $data['status']) {
                if (is_array($data['status'])) {
                    $query->where('sei.status', 'in', $data['status']);
                } else {
                    $query->where('sei.status', $data['status']);
                }
            }

            if (isset($data['type']) && $data['type']) {
                $query->where('a.type', $data['type']);
            }

            if (isset($data['tag_pos']) && $data['tag_pos']) {
                $query->where('sei.tag_pos', $data['tag_pos']);
            }

            if (isset($data['tag_toilet']) && $data['tag_toilet']) {
                $query->where('sei.tag_toilet', $data['tag_toilet']);
            }

            if (isset($data['tag_canopy']) && $data['tag_canopy']) {
                $query->where('sei.tag_canopy', $data['tag_canopy']);
            }

            if (isset($data['tag_rest']) && $data['tag_rest']) {
                $query->where('sei.tag_rest', $data['tag_rest']);
            }

            if (isset($data['tag_pnp']) && $data['tag_pnp']) {
                $query->where('sei.tag_pnp', $data['tag_pnp']);
            }

            if (isset($data['tag_insure']) && $data['tag_insure']) {
                $query->where('sei.tag_insure', $data['tag_insure']);
            }

            if (isset($data['tag_protect']) && $data['tag_protect']) {
                $query->where('sei.tag_protect', $data['tag_protect']);
            }

            if (isset($data['tag_ultrafast']) && $data['tag_ultrafast']) {
                $query->where('sei.tag_ultrafast', $data['tag_ultrafast']);
            }

            if (isset($data['tag_fast']) && $data['tag_fast']) {
                $query->where('sei.tag_fast', $data['tag_fast']);
            }

            if (isset($data['tag_slow']) && $data['tag_slow']) {
                $query->where('sei.tag_slow', $data['tag_slow']);
            }

            if (isset($params['start_time']) && isset($params['end_time']) && $params['start_time'] && $params['end_time']) {
                $query->where('a.create_time >= "' . $params['start_time'] . ' 00:00:00"');
                $query->where('a.create_time <= "' . $params['end_time'] . ' 23:59:59"');
            }

            return $query;
        };

    }

    /**
     * 获取充电站列表
     *
     * @param array $data
     * @param int $page
     * @param int $pageSize
     * @param AppletLoginUser|null $user
     * @return array
     * @throws DbException
     */
    public function getListData(array $data, int $page, int $pageSize, ?AppletLoginUser $user = null): array
    {

        $center_lon = ($data['center_lon'] ?? false) ?: 114.12772;
        $center_lat = ($data['center_lat'] ?? false) ?: 22.54764;
        $radius = ($data['radius'] ?? false) ?: 9000;

        // 整理筛选条件
        $where = $this->arrangeWhere($data);

        // 查询字段
        $fields = [
            "a.id",
            "a.name",
            "a.all_address",
            "a.type",
            "sei.status",
            "sei.tag_pos",
            "sei.tag_park",
            "sei.place_rate",
            "sei.tag_toilet",
            "sei.tag_canopy",
            "sei.tag_rest",
            "sei.tag_pnp",
            "sei.tag_insure",
            "sei.tag_protect",
            "sei.tag_ultrafast",
            "sei.tag_fast",
            "sei.tag_slow",
            "a.lonlat",
            "ST_Distance_Sphere(ST_PointFromText(CONCAT('POINT(', SUBSTRING_INDEX(a.lonlat, ',', -1) ,' ', SUBSTRING_INDEX(a.lonlat, ',', 1), ')')), ST_PointFromText('POINT($center_lon $center_lat)')) AS distance",
            "sei.is_support_reservation"
        ];

        $order_class = ($data['order_class'] ?? false) ?: '距离最近';
        $order_arr = match ($order_class) {
            '综合排序' => ['status' => 'asc', 'distance' => 'asc'],
            default => ['distance' => 'asc'],
        };

        $activityModel = new Activity();

        /**
         * @var Query $query
         */
        $query = $this;
        if ($user) {
            $fields[] = "f.period_json_sum";
            $sql1 = $query->alias('a')
                ->join('piles d', 'a.id = d.station_id')
                ->join('shots e', 'a.id = e.station_id and d.id=e.piles_id')
                ->join('stations_extra_info sei', 'sei.id = a.id')
                ->join('tariff_group f', 'sei.tariff_group_id = f.id')
                ->field($fields)
                ->where($where)
                ->order($order_arr)
                ->group('a.id')
//                ->having('distance<=' . $radius)
                ->buildSql();
        } else {
            $sql1 = $query->alias('a')
                ->append(['period_json_sum'])
                ->join('piles d', 'a.id = d.station_id')
                ->join('shots e', 'a.id = e.station_id and d.id=e.piles_id')
                ->join('stations_extra_info sei', 'sei.id = a.id')
                ->field($fields)
                ->where($where)
                ->order($order_arr)
                ->group('a.id')
//                ->having('distance<=' . $radius)
                ->buildSql();
        }

        return Db::table($sql1 . ' s')
            ->append(['period_json_sum', 'discount'])
            ->order($order_arr)
            ->withAttr('period_json_sum', function ($value) {
                if ($value) {
                    return get_current_period_rate(json_decode($value, true));
                }
                return null;
            })
            ->withAttr('shots_sum', function ($value, $data) {
                return app(ShotsStatus::class)->getShotsIdleAndTotalCount($data['id']);
            })
            ->withAttr('discount', function ($value, $data) use ($activityModel) {
                return $activityModel->getStationDiscount($data['id']);
            })
            ->paginate(['list_rows' => $pageSize, 'page' => $page])
            ->toArray();
    }


    /**
     * 获取用户收藏的充电站列表
     *
     * @param array $data
     * @param int $page
     * @param int $pageSize
     * @param AppletLoginUser $user
     * @return array
     * @throws DbException
     */
    public function getUserCollectStationsList(array $data, int $page, int $pageSize, AppletLoginUser $user): array
    {
        $order_name = 'distance';
        $order_type = 'asc';

        //longitude 经度，浮点数，范围为-180~180，负数表示西经
        //latitude 纬度，浮点数，范围为-90~90，负数表示南纬
        $center_lon = ($data['center_lon'] ?? false) ?: 114.12772;
        $center_lat = ($data['center_lat'] ?? false) ?: 22.54764;

        $fields = [
            "a.id",
            "a.name",
            "a.all_address",
            "a.type",
            "sei.status",
            "sei.tag_pos",
            "sei.tag_park",
            "sei.place_rate",
            "sei.tag_toilet",
            "sei.tag_canopy",
            "sei.tag_rest",
            "sei.tag_pnp",
            "sei.tag_insure",
            "sei.tag_protect",
            "sei.tag_ultrafast",
            "sei.tag_fast",
            "sei.tag_slow",
            "a.lonlat",
            "ST_Distance_Sphere(ST_PointFromText(CONCAT('POINT(', SUBSTRING_INDEX(a.lonlat, ',', -1) ,' ', SUBSTRING_INDEX(a.lonlat, ',', 1), ')')), ST_PointFromText('POINT($center_lon $center_lat)')) AS distance",
            "f.period_json_sum",
            "sei.is_support_reservation"
        ];

        $where = [
            ['g.user_id', '=', $user->id],
            ['a.is_del', '=', self::IsDelNot]
        ];

        $activityModel = new Activity();
        /**
         * @var Query $query
         */
        $query = $this;
        $sql1 = $query->alias('a')
            ->join('piles d', 'a.id = d.station_id')
            ->join('shots e', 'a.id = e.station_id and d.id=e.piles_id')
            ->join('users_collect_stations g', 'a.id = g.station_id')
            ->join('stations_extra_info sei', 'sei.id = a.id')
            ->join('tariff_group f', 'sei.tariff_group_id = f.id')
            ->where($where)
            ->field($fields)
            ->order($order_name, $order_type)
            ->group('a.id')
            ->buildSql();


        return Db::table($sql1 . ' s')
            ->append(['discount'])
            ->order($order_name, $order_type)
            ->withAttr('period_json_sum', function ($value) {
                return get_current_period_rate(json_decode($value, true));
            })
            ->withAttr('shots_sum', function ($value, $data) {
                return app(ShotsStatus::class)->getShotsIdleAndTotalCount($data['id']);
            })
            ->withAttr('discount', function ($value, $data) use ($activityModel) {
                return $activityModel->getStationDiscount($data['id']);
            })
            ->paginate(['list_rows' => $pageSize, 'page' => $page])
            ->toArray();
    }

    public function getCorpId(int $station_id): ?int
    {
        $where = [
            ['id', '=', $station_id],
            ['is_del', '=', self::IsDelNot]
        ];
        return $this->where($where)->value('corp_id');
    }


    public function corpStationIsExistent(int $corp_id, int $station_id): bool
    {
        return $this->where([
                ['corp_id', '=', $corp_id],
                ['id', '=', $station_id],
                ['is_del', '=', self::IsDelNot]
            ])->count() > 0;
    }

    public function queryBindStationName(int $id): ?string
    {
        /**
         * @var Query $query
         */
        $query = $this;

        return $query->alias('s')
            ->leftJoin('stations_extra_info sei', 'sei.id = s.id')
            ->where('sei.tariff_group_id', '=', $id)
            ->value('s.name');
    }
}
