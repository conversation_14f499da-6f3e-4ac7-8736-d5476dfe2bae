<?php
/** @noinspection PhpUnused */

namespace app\common\model;

use app\common\lib\ExceptionLogCollector;
use app\common\lib\TimeTool;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\Model;

/**
 * 充电站日统计表
 * cbj 2023.10.19 新增
 */
class StationsStats extends Model
{
    protected $table = 'stations_stats';

    /**
     * 当月统计概况
     *
     * @param int $corp_id 运营商ID
     * @param int|array $station_id 充电站ID
     * @return array
     */
    public function thisMonthStatistic(int $corp_id = 0, int|array $station_id = 0): array
    {
        $field = [
            'sum(amount) as money',
            'sum(electricity) as electricity_total',
            'sum(serv_amount) as service_money',
            'sum(order_count) as order_count',
        ];
        $startTime = TimeTool::getThisMonthTimestamp();
        $where[] = sprintf('UNIX_TIMESTAMP(day) >= %d', $startTime);
        if (!empty($corp_id)) {
            $where[] = sprintf('corp_id = %d', $corp_id);
        }
        if (!empty($station_id)) {
            if (is_int($station_id)) {
                $where[] = sprintf('station_id = %d', $station_id);
            } else {
                $where[] = sprintf('station_id in (%s)', implode(',', $station_id));
            }
        }
        $sql = 'SELECT %s FROM %s WHERE %s';
        $sql = sprintf(
            $sql,
            implode(', ', $field),
            $this->getTable(),
            implode(' AND ', $where)
        );
        $result = Db::query($sql);

        $result = $result[0];
        $result['money'] = $result['money'] ?? 0;
        $result['electricity_total'] = $result['electricity_total'] ?? 0;
        $result['service_money'] = $result['service_money'] ?? 0;
        $result['order_count'] = $result['order_count'] ?? 0;

        return $result;
    }

    /**
     * 最近几天平均数据
     *
     * @param int $corp_id 运营商ID [default=0] 不传递或传递0则表示查询所有运营商的
     * @return ?array 查询出现异常时，返回null。
     */
    public function Last10Days(int $corp_id = 0): ?array
    {
        $field = [
            'sum(amount) as money',
            'sum(electricity) as electricity_total',
            'day'
        ];
        $start_time = TimeTool::getHistoryTimestamp(9);
        $where = [
            'day >= ' . $start_time
        ];

        if (!empty($corp_id)) $where[] = 'corp_id = ' . $corp_id;

        $sql = 'SELECT %s FROM %s WHERE %s GROUP BY day';
        $sql = sprintf($sql, implode(', ', $field), $this->getTable(), implode(' AND ', $where));


        try {
            $result = Db::query($sql);
            return $this->fillLast10DaysEmptyData($result);
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            return null;
        }
    }

    /**
     * 最近场站几天平均数据
     *
     * @param int $station_id
     * @param int $corp_id 运营商ID(为0时表示不过滤运营商ID)
     * @return ?array 查询出现异常时，返回null。
     */
    public function stationLast7Days(int $station_id, int $corp_id = 0): ?array
    {
        $field = [
            'sum(amount) as money',
            'sum(electricity) as electricity_total',
            'day'
        ];
        $start_time = TimeTool::getHistoryTimestamp(6);
        $where = [
            'day >= ' . $start_time
        ];

        $where[] = 'station_id = ' . $station_id;
        if (!empty($corp_id)) {
            $where[] = 'corp_id = ' . $corp_id;
        }

        $sql = 'SELECT %s FROM %s WHERE %s GROUP BY day';
        $sql = sprintf($sql, implode(', ', $field), $this->getTable(), implode(' AND ', $where));


        try {
            $result = Db::query($sql);
            return $this->fillLast7DaysEmptyData($result);
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            return null;
        }
    }

    protected function fillLast7DaysEmptyData(array $list): array
    {
        $newList = [];
        foreach ($list as $value) {
            $newList[$value['day']] = $value;
        }

        $result = [];
        for ($i = 6; $i > 0; $i--) {
            $day = date('Y-m-d 00:00:00', strtotime('-' . $i . ' day'));
            if (isset($newList[$day]) === true) {
                $result[] = $newList[$day];
            } else {
                $result[] = [
                    'money' => 0,
                    'electricity_total' => 0,
                    'day' => $day
                ];
            }
        }

        return $result;
    }

    protected function fillLast10DaysEmptyData(array $list): array
    {
        $newList = [];
        foreach ($list as $value) {
            $newList[$value['day']] = $value;
        }

        $result = [];
        for ($i = 9; $i > 0; $i--) {
            $day = date('Y-m-d 00:00:00', strtotime('-' . $i . ' day'));
            if (isset($newList[$day]) === true) {
                $result[] = $newList[$day];
            } else {
                $result[] = [
                    'money' => 0,
                    'electricity_total' => 0,
                    'day' => $day
                ];
            }
        }

        return $result;
    }

    public function statisticsSummary(int $corp_id = 0, int $start_time = 1): ?array
    {
        $field = [
            'sum(amount) as money',
            'sum(electricity) as electricity_total'
        ];
        $where = [
            ['day', '>=', date('Y-m-d H:i:s', $start_time)]
        ];
        if (!empty($corp_id)) $where['corp_id'] = $corp_id;

        try {
            $result = $this->field($field)->where($where)->find()->toArray();
            foreach ($result as &$value) {
                if (empty($value)) $value = 0;
            }
            return $result;
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            return null;
        }
    }

    /**
     * 每月统计列表
     *
     * @param int $year 年份
     * @param int $corp_id [default=0] 运营商ID
     * @return array
     */
    public function monthlyStatistics(int $year, int $corp_id = 0): array
    {
        $field = [
            'sum(amount) as money',
            'sum(electricity) as electricity_total',
            'sum(order_count) as order_count',
            'month',
            'corp_id'
        ];
        [$startTime, $endTime] = TimeTool::yearTimeFrame($year);
        $where = sprintf('UNIX_TIMESTAMP(day) >= %d and UNIX_TIMESTAMP(day) < %d', $startTime, $endTime);
        if (!empty($corp_id)) {
            $where .= sprintf(' and corp_id = %d', $corp_id);
        }
        $sql = 'SELECT %s FROM %s WHERE %s GROUP BY %s';
        $sql = sprintf(
            $sql,
            implode(', ', $field),
            $this->getTable(),
            $where,
            'month'
        );
        return Db::query($sql);
    }

    /**
     * 当月统计列表
     *
     * @param int $corp_id [default=0] 运营商ID
     * @return array
     */
    public function thisMonthStatistics(int $corp_id = 0): array
    {
        $field = [
            'sum(amount) as money',
            'sum(electricity) as electricity_total',
            'sum(order_count) as order_count',
            'month'
        ];
        [$startTime, $endTime] = TimeTool::yearMonthTimeFrame(
            (int)date('Y'),
            (int)date('m')
        );
        $where = sprintf('UNIX_TIMESTAMP(day) >= %d AND UNIX_TIMESTAMP(day) < %d', $startTime, $endTime);
        if (!empty($corp_id)) {
            $where .= sprintf(' and corp_id = %d', $corp_id);
        }
        $sql = 'SELECT %s FROM %s WHERE %s';
        $sql = sprintf(
            $sql,
            implode(', ', $field),
            $this->getTable(),
            $where
        );
        $result = Db::query($sql);
        if (is_null($result[0]['month'])) {
            return [
                'money' => 0,
                'electricity_total' => 0,
                'order_count' => 0,
                'month' => date('Y-m-01 00:00:00')
            ];
        }

        return $result[0];
    }

    /**
     * 每日统计列表
     *
     * @param int $year 年份
     * @param int $month 月份
     * @param int $corp_id [default=0] 运营商ID
     * @return array
     */
    public function dailyStatistics(int $year, int $month, int $corp_id = 0): array
    {
        $field = [
            'sum(amount) as money',
            'sum(electricity) as electricity_total',
            'sum(order_count) as order_count',
            'day'
        ];
        [$startTime, $endTime] = TimeTool::yearMonthTimeFrame($year, $month);
        $where = sprintf('UNIX_TIMESTAMP(day) >= %d and UNIX_TIMESTAMP(day) < %d', $startTime, $endTime);
        if (!empty($corp_id)) {
            $where .= sprintf(' and corp_id = %d', $corp_id);
        }
        $sql = 'SELECT %s FROM %s WHERE %s GROUP BY day';
        $sql = sprintf(
            $sql,
            implode(', ', $field),
            $this->getTable(),
            $where
        );

        $result = Db::query($sql);

        foreach ($result as $key => &$value) {
            if (is_null($value['day'])) {
                unset($result[$key]);
            } else {
                $value['money'] = $value['money'] ?? 0;
                $value['electricity_total'] = $value['electricity_total'] ?? 0;
                $value['order_count'] = $value['order_count'] ?? 0;
            }
        }

        return $result;
    }

    /**
     * 查询月统计数据
     *
     * @param int $year 年份
     * @param int $month 月份
     * @return array
     */
    public function queryMonthStatisticsData(int $year, int $month): array
    {
        $field = [
            'corp_id',
            'station_id',
            'sum(sharp) as sharp',
            'sum(peak) as peak',
            'sum(flat) as flat',
            'sum(valley) as valley',
            'sum(electricity) as electricity',
            'sum(amount) as amount',
            'sum(elec_amount) as elec_amount',
            'sum(serv_amount) as serv_amount',
            'sum(order_count) as order_count',
            'sum(charge_tick) as charge_tick',
            'sum(utility_rate) as utility_rate',
            'count(id) as count'
        ];
        [$startTime, $endTime] = TimeTool::yearMonthTimeFrame($year, $month);
        $where = sprintf('UNIX_TIMESTAMP(day) >= %d and UNIX_TIMESTAMP(day) < %d', $startTime, $endTime);
        $sql = 'SELECT %s FROM %s WHERE %s GROUP BY %s';
        $sql = sprintf(
            $sql,
            implode(', ', $field),
            $this->getTable(),
            $where,
            'station_id'
        );
        return Db::query($sql);
    }


    /**
     * 填充每日空数据
     *
     * @param int $year 年
     * @param int $month 月
     * @param int $day 日
     * @param array $stationIds [default=[]] 排查的充电站ID集合
     * @return int
     */
    public function fillDailyEmptyData(int $year, int $month, int $day, array $stationIds = []): int
    {
        $insertField = [
            'corp_id', 'station_id', 'day', 'month', 'year',
            'sharp', 'peak', 'flat', 'valley', 'electricity',
            'amount', 'elec_amount', 'serv_amount', 'order_count',
            'charge_tick', 'utility_rate'
        ];
        $dayString = sprintf('%d-%d-%d 00:00:00', $year, $month, $day);
        $monthString = sprintf('%d-%d-01 00:00:00', $year, $month);
        $yearString = sprintf('%d-01-01 00:00:00', $year);
        $selectField = [
            'corp_id', 'id as station_id', '"' . $dayString . '" as day',
            '"' . $monthString . '" as month', '"' . $yearString . '" as year',
            '0 as sharp', '0 as peak', '0 as flat', '0 as valley', '0 as electricity',
            '0 as amount', '0 as elec_amount', '0 as serv_amount', '0 as order_count',
            '0 as charge_tick', '0 as utility_rate'
        ];
        $whereSQL = '';
        if (!empty($stationIds)) {
            $whereSQL = sprintf('WHERE id not in(%s)', implode(',', $stationIds));
        }
        $sql = 'INSERT INTO `%s`(%s) SELECT %s FROM %s %s';
        $StationsModel = app(Stations::class);
        $sql = sprintf(
            $sql, $this->getTable(), implode(', ', $insertField),
            implode(', ', $selectField), $StationsModel->getTable(), $whereSQL);

        return Db::execute($sql);
    }
}