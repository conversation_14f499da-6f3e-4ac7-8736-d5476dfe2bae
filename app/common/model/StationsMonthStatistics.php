<?php

namespace app\common\model;

use app\common\lib\TimeTool;
use think\facade\Db;
use think\Model;

/**
 * 充电站月统计表
 * cbj 2023.10.19 新增
 */
class StationsMonthStatistics extends Model
{
    protected $table = 'stations_month_statistics';


    /**
     * 今年统计概况
     *
     * @param int $corp_id 运营商ID
     * @param int|array $station_id 充电站ID
     * @param array $queryField [default=[]] 查询字段
     * @return array
     */
    public function thisYearStatistic(int $corp_id = 0, int|array $station_id = 0, array $queryField = []): array
    {
        if (!empty($queryField)) {
            $field = $queryField;
        } else {
            $field = [
                'sum(amount) as money',
                'sum(electricity) as electricity_total',
                'sum(serv_amount) as service_money',
                'sum(order_count) as order_count',
            ];
        }
        $startTime = TimeTool::getThisYearTimestamp();
        $where[] = sprintf('statistic_time >= %d', $startTime);
        if (!empty($corp_id)) {
            $where[] = sprintf('corp_id = %d', $corp_id);
        }
        if (!empty($station_id)) {
            if (is_int($station_id)) {
                $where[] = sprintf('station_id = %d', $station_id);
            } else {
                $where[] = sprintf('station_id in (%s)', implode(',', $station_id));
            }
        }
        $sql = 'SELECT %s FROM %s WHERE %s';
        $sql = sprintf(
            $sql,
            implode(', ', $field),
            $this->getTable(),
            implode(' and ', $where)
        );
        $result = Db::query($sql);
        $result = $result[0];
        $result['money'] = $result['money'] ?? 0;
        $result['electricity_total'] = $result['electricity_total'] ?? 0;
        $result['service_money'] = $result['service_money'] ?? 0;
        $result['order_count'] = $result['order_count'] ?? 0;

        return $result;
    }

    public function monthlyStatisticsData(int $year, int $corp_id = 0): array
    {
        $field = [
            'sum(amount) as money',
            'sum(electricity) as electricity_total',
            'sum(order_count) as order_count',
            'statistic_time as month'
        ];
        [$startTime, $endTime] = TimeTool::yearTimeFrame($year);
        $where[] = sprintf('statistic_time >= %s AND statistic_time < %s', $startTime, $endTime);
        if (!empty($corp_id)) {
            $where[] = sprintf('corp_id = %d', $corp_id);
        }
        $sql = 'SELECT %s FROM %s WHERE %s GROUP BY %s';
        $sql = sprintf(
            $sql,
            implode(', ', $field),
            $this->getTable(),
            implode(' AND ', $where),
            'statistic_time'
        );
        return Db::query($sql);
    }

    /**
     * 查询月统计数据
     *
     * @param int $year 年份
     * @return array
     */
    public function queryMonthStatisticsData(int $year): array
    {
        $field = [
            'corp_id',
            'station_id',
            'sum(sharp) as sharp',
            'sum(peak) as peak',
            'sum(flat) as flat',
            'sum(valley) as valley',
            'sum(electricity) as electricity',
            'sum(amount) as amount',
            'sum(elec_amount) as elec_amount',
            'sum(serv_amount) as serv_amount',
            'sum(order_count) as order_count',
            'sum(charge_tick) as charge_tick',
            'sum(utility_rate) as utility_rate',
            'count(id) as count'
        ];
        [$startTime, $endTime] = TimeTool::yearTimeFrame($year);
        $where = sprintf('statistic_time >= %s and statistic_time < %s', $startTime, $endTime);
        $sql = 'SELECT %s FROM %s WHERE %s GROUP BY %s';
        $sql = sprintf(
            $sql,
            implode(', ', $field),
            $this->getTable(),
            $where,
            'station_id'
        );
        return Db::query($sql);
    }

    /**
     * 填充每月空数据
     *
     * @param int $year 年
     * @param int $month 月
     * @param array $stationIds [default=[]] 排查的充电站ID集合
     * @return int
     */
    public function fillDailyEmptyData(int $year, int $month, array $stationIds = []): int
    {
        $insertField = [
            'corp_id', 'station_id', 'year', 'statistic_time',
            'sharp', 'peak', 'flat', 'valley', 'electricity',
            'amount', 'elec_amount', 'serv_amount', 'order_count',
            'charge_tick', 'utility_rate'
        ];
        $monthString = strtotime(sprintf('%d-%d-01 00:00:00', $year, $month));
        $yearString = strtotime(sprintf('%d-01-01 00:00:00', $year));
        $selectField = [
            'corp_id', 'id as station_id',
            '"' . $yearString . '" as year', '"' . $monthString . '" as statistic_time',
            '0 as sharp', '0 as peak', '0 as flat', '0 as valley', '0 as electricity',
            '0 as amount', '0 as elec_amount', '0 as serv_amount', '0 as order_count',
            '0 as charge_tick', '0 as utility_rate'
        ];
        $whereSQL = '';
        if (!empty($stationIds)) {
            $whereSQL = sprintf('WHERE id not in(%s)', implode(',', $stationIds));
        }
        $sql = 'INSERT INTO `%s`(%s) SELECT %s FROM %s %s';
        $StationsModel = app(Stations::class);
        $sql = sprintf(
            $sql, $this->getTable(), implode(', ', $insertField),
            implode(', ', $selectField), $StationsModel->getTable(), $whereSQL);

        return Db::execute($sql);
    }
}