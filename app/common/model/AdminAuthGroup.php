<?php
/** @noinspection PhpUnused */

namespace app\common\model;

use app\common\lib\ExceptionLogCollector;
use app\common\log\LogCollector;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Model;

//lwj 2023.7.17 新增
class AdminAuthGroup extends Model
{
    protected $table = 'admin_auth_group';

    // 状态
    public const StateNormal = 1; // 正常
    public const StateDisable = 2; // 禁用

    // 超级管理员的ID
    public const SuperAdministratorID = 1;

    public function verifyAuthority(int $group_id, string $url): bool
    {
        /**
         * @var Query $query
         */
        $query = $this;
        try {
            $count = $query->alias('aag')
                ->join('admin_auth_group_rules aagr', 'aag.id=aagr.group_id')
                ->join('sys_menu m', 'm.rule_name = aagr.rule_name')
                ->where('aag.id', $group_id)
                ->where('m.api_url', $url)
                ->count();
            LogCollector::collectorRunLog('$count = ' . $count);
            return $count > 0;
        } catch (DbException $e) {
            ExceptionLogCollector::collect($e);
            return false;
        }
    }


    /**
     * 选项列表
     *
     * @return array
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function getOptionsData(): array
    {
        $where = [
            ['state', '=', self::StateNormal],
            ['id', '>', self::SuperAdministratorID]
        ];
        return $this->where($where)->field(['id', 'group_name'])->select()->toArray();
    }


    /**
     * 是否存在
     *
     * @param int $id 管理员分组ID
     * @return bool true: 存在 false: 不存在
     */
    public function isAlreadyExists(int $id): bool
    {
        return $this->where('id', '=', $id)->count('id') > 0;
    }
}