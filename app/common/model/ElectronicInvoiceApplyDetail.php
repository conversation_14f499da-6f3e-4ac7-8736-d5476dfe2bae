<?php
/** @noinspection PhpUnused */

namespace app\common\model;

use think\db\exception\DbException;
use think\db\Query;
use think\Model;

class ElectronicInvoiceApplyDetail extends Model
{
    protected $table = 'electronic_invoice_apply_detail';

    // 状态
    public const StatusNotEffective = 0; // 未生效
    public const StatusEffective = 1; // 已生效

    /**
     * 用户已开票的充电订单ID
     *
     * @param int $userId 用户ID
     * @param int $page [default=1] 页码
     * @param int $pageSize [default=100] 每页最大显示条数
     * @return array
     * @throws DbException
     */
    public function userDetailedRecords(int $userId, int $page = 1, int $pageSize = 100): array
    {
        $field = ['order_id'];
        /**
         * @var Query $query
         */
        $query = $this;
        return $query->where('user_id', '=', $userId)
            ->where('status', '=', self::StatusEffective)
            ->field($field)
            ->paginate(['list_rows' => $pageSize, 'page' => $page])->toArray();
    }


    public function addDetails(string $applyRecordId, int $userId, array $orderIds): int
    {
        $insertData = [];
        foreach ($orderIds as $orderId) {
            $insertData[] = [
                'order_id' => $orderId,
                'user_id' => $userId,
                'status' => self::StatusNotEffective,
                'apply_record_id' => $applyRecordId
            ];
        }
        return $this->insertAll($insertData);
    }

    /**
     * 是否存在已开发票的订单
     *
     * @param array $orderIds
     * @return bool
     */
    public function isExistEffective(array $orderIds): bool
    {
        if (empty($orderIds)) return false;

        $row = $this->where('order_id', 'in', $orderIds)->where('status', self::StatusEffective)->count('id');

        return $row > 0;
    }

    /**
     * 过滤出已开发票的订单
     *
     * @param array $orderIds
     * @return array
     */
    public function filterExistEffectiveOrderIds(array $orderIds): array
    {
        if (count($orderIds) === 0) return [];
        $where = [
            ['order_id', 'in', $orderIds],
            ['status', '=', self::StatusEffective]
        ];
        return $this->where($where)->column('order_id');
    }

    /**
     * 生效电子发票申请明细
     *
     * @param string $applyRecordId
     * @return int
     */
    public function takeEffect(string $applyRecordId): int
    {
        if (empty($applyRecordId)) return 0;

        return (int)$this->where('apply_record_id', $applyRecordId)->update([
            'status' => self::StatusEffective
        ]);
    }
}