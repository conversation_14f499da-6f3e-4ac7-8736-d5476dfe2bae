<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\facade\Db;
use think\Model;

class Piles extends Model
{
    protected $table = 'piles';

    // 是否已删除
    public const IsDelYes = 1; // 已删除
    public const IsDelNot = 0; // 未删除
//    // 状态
//    public const StatusOnline = 1; // 在线
//    public const StatusOffline = 2; // 离线

//    /**
//     * 获取充电量充电桩与充电枪数量
//     *
//     * @param int $id 充电场ID
//     * @return array
//     * @throws DataNotFoundException
//     * @throws DbException
//     * @throws ModelNotFoundException
//     */
//    public function getPilesAndShotCount(int $id): array
//    {
//        /**
//         * @var Query $query
//         */
//        $query = $this->alias('p');
//        return $query->where('p.station_id', $id)
//            ->leftJoin('shots s', 's.piles_id = p.id')
//            ->group(['p.type', 's.status'])
//            ->field([
//                'p.type', 's.status', 'count(s.id) as count',
//            ])
//            ->select()
//            ->toArray();
//    }

//    /**
//     * !!! 已经放弃使用这个字段
//     * @param int|array $id
//     * @param int $status
//     * @return int
//     */
//    public function updateStatus(int|array $id, int $status): int
//    {
//        if (is_int($id)) {
//            return (int)$this->where('id', $id)->update([
//                'status' => $status,
//            ]);
//        } else {
//            return (int)$this->whereIn('id', $id)->update([
//                'status' => $status,
//            ]);
//        }
//    }

//    /**
//     * !!! 已经放弃使用这个字段
//     *
//     * @param int $id
//     * @return int
//     */
//    public function getStatus(int $id): int
//    {
//        return (int)$this->where('id', $id)->value('status');
//    }

    /**
     * 获取充电桩数据
     *
     * @param int $id 充电桩ID
     * @param array|null $field
     * @return ?array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getPilesData(int $id, ?array $field = null): ?array
    {
        if (is_null($field)) $field = [
            'ps.is_online',
            'p.corp_id',
            'p.station_id'
        ];
        /**
         * @var Query $query
         */
        $query = $this;
        $data = $query->alias('p')
            ->join('piles_status ps', 'ps.id = p.id')
            ->where('p.id', '=', $id)
            ->field($field)->find();
        if (empty($data)) {
            return null;
        }

        return $data->toArray();
    }

    /**
     * 查询费率组ID
     *
     * @param int $piles_id 充电桩ID
     * @return ?int
     */
    public function getTariffGroupId(int $piles_id): ?int
    {
        /**
         * @var Query $query
         */
        $query = $this->alias('p');
        $tariff_group_id = $query->where('p.id', '=', $piles_id)
            ->leftJoin('stations_extra_info sei', 'sei.id = p.station_id')
            ->value('sei.tariff_group_id');
        if (empty($tariff_group_id)) return null;
        return $tariff_group_id;
    }

    /**
     * @return int
     * @throws DbException
     */
    public function deleteAll(): int
    {
        return Db::name($this->table)->delete(true);
    }

    /**
     * 删除场站所有充电桩数据
     *
     * @param int $station_id
     * @return int
     * @throws DbException
     */
    public function deleteStationAllPiles(int $station_id): int
    {
        return Db::name($this->table)->where('station_id', '=', $station_id)->delete(true);
    }

    /**
     * 批量添加充电枪
     *
     * @param array $piles_list
     * @return bool
     */
    public function batchAddPiles(array $piles_list): bool
    {
        $inserts = [];
        $piles_ids = [];
        foreach ($piles_list as $piles) {
            $inserts[] = [
                'id' => $piles['id'],
                'name' => $piles['name'],
                'corp_id' => $piles['corp_id'],
                'station_id' => $piles['station_id'],
                'ac_dc' => $piles['ac_dc'],
                'type' => $piles['type'],
                'model' => $piles['model'],
                'power' => $piles['power'],
                'comm_id' => $piles['comm_id'],
                'opt_id' => 0,
                'phase_name' => $piles['phase_name']
            ];
            $piles_ids[] = $piles['id'];
        }
        $this->insertAll($inserts);
        (new PilesStatus())->batchAddShotsStatus($piles_ids);

        return true;
    }

    public function getStationPilesIds(int $station_id): array
    {
        $where = [
            ['p.station_id', '=', $station_id],
            ['p.is_del', '=', Piles::IsDelNot]
        ];
        return $this->alias('p')
            ->where($where)
            ->column('p.id');
    }

    public function getStationPilesCount(int $station_id): int
    {
        $where = [
            ['p.station_id', '=', $station_id],
            ['p.is_del', '=', Piles::IsDelNot]
        ];

        /**
         * @var Query $this
         */
        return $this->alias('p')
            ->where($where)
            ->count();
    }

    public function getCorpPiles(int $corp_id, int $piles_id, ?array $field = null): ?array
    {
        $where = [
            ['id', '=', $piles_id],
            ['corp_id', '=', $corp_id],
            ['is_del', '=', self::IsDelNot]
        ];
        if (empty($field)) {
            $field = '*';
        }

        $data = $this->where($where)->field($field)->find();
        if (empty($data)) {
            return null;
        }
        return $data->toArray();
    }

    public function isExistent(int $piles_id): bool
    {
        return $this->where([
                ['id', '=', $piles_id],
                ['is_del', '=', Piles::IsDelNot]
            ])->count() > 0;
    }

    public function corpPilesIsExistent(int $corp_id, int $piles_id): bool
    {
        return $this->where([
                ['is_del', '=', self::IsDelNot],
                ['id', '=', $piles_id],
                ['corp_id', '=', $corp_id]
            ])->count() > 0;
    }
}
