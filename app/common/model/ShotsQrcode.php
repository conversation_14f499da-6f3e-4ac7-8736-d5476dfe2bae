<?php
/** @noinspection PhpRedundantOptionalArgumentInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpUnused */

namespace app\common\model;

use app\common\lib\QrCode;
use think\Model;

class ShotsQrcode extends Model
{
    protected $table = 'shots_qrcode';

    public function updateQrcode(int $shots_id, string $qrcode): bool
    {
        $this->where('id', '=', $shots_id)->update([
            'qrcode' => $qrcode
        ]);
        return true;
    }

    public function batchAddShotsQrcode(array $shots_ids): bool
    {
        $db_shots_ids = $this->where('id', 'in', $shots_ids)->column('id');
        $inserts = [];
        foreach ($shots_ids as $shots_id) {
            if (!in_array($shots_id, $db_shots_ids)) {
                $inserts[] = [
                    'id' => $shots_id,
                    'qrcode' => QrCode::create_wechat_qrcode((string)$shots_id),
                ];
            }
        }
        if (!empty($inserts)) {
            $this->insertAll($inserts);
        }
        return true;
    }

    public function getShotsQrcode(int $shots_id): ?string
    {
        return $this->where('id', '=', $shots_id)->value('qrcode');
    }

    public function insertShotsQrcode(int $shots_id, string $qrcode): bool
    {
        return $this->insert([
                'id' => $shots_id,
                'qrcode' => $qrcode
            ]) > 0;
    }
}
