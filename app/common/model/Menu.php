<?php /** @noinspection PhpUnused */

namespace app\common\model;

use app\common\lib\ExceptionLogCollector;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;
use util\Tree;

/**
 * 节点模型
 * @package app\admin\model
 */
class Menu extends Model
{
    protected $table = 'sys_menu';

    // 状态
    public const StateAvailable = 1; // 可用
    public const StateDisable = 2; // 禁用

    // 公开状态，1不公开 2公开
    public const AuthOpenNotPublic = 1; // 不公开
    public const AuthOpenPublic = 2; // 公开

    // 显示菜单 1:显示 2:隐藏
    public const MenuStateShow = 1; // 显示
    public const MenuStateHidden = 2; // 隐藏

    /**
     * 获取所有子节点id
     * lwj 2023.10.9 修改
     * @param int $pid 父级id
     * @return array
     */
    public function getChildsId(int $pid = 0): array
    {
        $ids = $this->where('pid', $pid)->column('id');
        foreach ($ids as $value) {
            $ids = array_merge($ids, $this->getChildsId($value));
        }
        return $ids ?: [];
    }

    public function verifyApiIsPublic(string $url): bool
    {
        return $this->where('api_url', $url)
                ->where('state', self::StateAvailable)
                ->where('auth_open', '=', self::AuthOpenPublic)
                ->field('rule_name,auth_open')
                ->count() > 0;
    }

    public function getMenuData(string $url): ?array
    {

        try {
            $data = $this->where('api_url', $url)
                ->where('state', self::StateAvailable)
                ->field('rule_name,auth_open')
                ->find();
            if (empty($data)) return null;
            return $data->toArray();
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            ExceptionLogCollector::collect($e);
            return null;
        }
    }

    public function getAllMenuData(): array
    {
        return Tree::toLayer(
            $this->order('pid,id')
                ->column('id,pid,title,rule_name,url_value,api_url,icon_url,sort,menu_state,state,auth_open,level')
        );
    }

    public function userAuthList(int $group_id): array
    {
        $list = $this->where('state', 1)
            ->where(function ($query) use ($group_id) {
                $query->where('auth_open', 2)
                    ->whereOr(function ($query) use ($group_id) {
                        $query->where('auth_open', 1)
                            ->whereExists(function ($query) use ($group_id) {
                                $query->table('admin_auth_group_rules')
                                    ->where('admin_auth_group_rules.rule_name=sys_menu.rule_name')
                                    ->where('group_id', $group_id);
                            });
                    });
            })
            ->field(['id', 'menu_state', 'url_value', 'api_url'])
            ->select()
            ->toArray();

        $map = [];
        foreach ($list as $value) {
            if ($value['menu_state'] === self::MenuStateShow) {
                $map[$value['url_value']] = $value['id'];
            } else {
                $map[$value['api_url']] = $value['id'];
            }
        }
        return $map;
    }


}
