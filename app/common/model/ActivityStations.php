<?php

namespace app\common\model;

use think\Model;

class ActivityStations extends Model
{
    protected $table = 'activity_stations';

    public function addStations(int $activity_id, array $station_ids): int
    {
        $inserts = [];
        foreach ($station_ids as $station_id) {
            $inserts[] = [
                'activity_id' => $activity_id,
                'station_id' => $station_id,
                'create_time' => date("Y-m-d H:i:s")
            ];
        }

        return $this->insertAll($inserts);
    }


    public function removeActivityRelationAllStations(int $activity_id): bool
    {
        $where = [
            ['activity_id', '=', $activity_id]
        ];
        return $this->where($where)->delete();
    }


}
