<?php /** @noinspection PhpUnused */

namespace app\common\model;

use think\db\exception\DbException;
use think\Model;

class WorkOrderTemplateRelation extends Model
{
    protected $table = 'work_order_template_relation';

    /**
     * 验证是否存在
     *
     * @param int $template_id
     * @param int $corp_id
     * @param int $station_id
     * @return bool
     */
    public function isRelationExists(
        int $template_id,
        int $corp_id,
        int $station_id
    ): bool
    {
        $where = [];
        $where[] = ['template_id', '=', $template_id];
        $where[] = ['corp_id', '=', $corp_id];
        $where[] = ['station_id', '=', $station_id];
        return $this->where($where)->count() > 0;
    }

    /**
     * @param int $template_id
     * @param int $corp_id
     * @param int $station_id
     * @param int $notice_admin_user_id
     * @return bool
     * @throws DbException
     */
    public function setDefaultNoticeAdminUserID(
        int $template_id,
        int $corp_id,
        int $station_id,
        int $notice_admin_user_id
    ): bool
    {
        $is_exists = $this->isRelationExists($template_id, $corp_id, $station_id);
        if ($is_exists === true) {
            $where = [];
            $where[] = ['template_id', '=', $template_id];
            $where[] = ['corp_id', '=', $corp_id];
            $where[] = ['station_id', '=', $station_id];
            $result = $this->where($where)->update([
                'notice_admin_user_id' => $notice_admin_user_id
            ]);
        } else {
            $result = $this->insert([
                'template_id' => $template_id,
                'corp_id' => $corp_id,
                'station_id' => $station_id,
                'notice_admin_user_id' => $notice_admin_user_id
            ]);
        }
        return $result > 0;
    }

    public function getDefaultNoticeAdminUserID(
        int $template_id,
        int $corp_id,
        int $station_id,
    ): ?int
    {
        $where = [];
        $where[] = ['template_id', '=', $template_id];
        $where[] = ['corp_id', '=', $corp_id];
        $where[] = ['station_id', '=', $station_id];
        $user_id = $this->where($where)->value('notice_admin_user_id');
        if (empty($user_id)) {
            return null;
        }
        return $user_id;
    }
}
