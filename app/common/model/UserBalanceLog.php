<?php
/** @noinspection PhpUnused */

namespace app\common\model;

use think\Model;

class UserBalanceLog extends Model
{
    protected $table = 'user_balance_log';

    // 类型
    public const TypePayChargeOrder = '支付充电订单';

    /**
     * 添加用户余额日志
     *
     * @param int $user_id 用户ID
     * @param int $amount 变更金额
     * @param string $memo 描述
     * @param string $type 类型
     * @return int 新增的条数
     */
    public function add(int $user_id, int $amount, string $memo, string $type): int
    {
        $add_data = [
            'user_id' => $user_id,
            'amount' => $amount,
            'memo' => $memo,
            'type' => $type,
        ];
        return $this->insert($add_data);
    }
}
