<?php
/** @noinspection PhpRedundantOptionalArgumentInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpUnused */

namespace app\common\model;

use think\Model;

class StationDataAuthority extends Model
{
    protected $table = 'station_data_authority';

    public function getStationIds(int $admin_user_id): array
    {
        return $this->where('admin_user_id', '=', $admin_user_id)->column('station_id');
    }

    /**
     * 增加数据权限
     *
     * @param int $admin_user_id 管理员ID
     * @param array $station_ids 场站ID集合
     * @return int
     */
    public function addDataAuthority(int $admin_user_id, array $station_ids): int
    {
        $inserts = [];
        foreach ($station_ids as $station_id) {
            $inserts[] = [
                'station_id' => $station_id,
                'admin_user_id' => $admin_user_id,
            ];
        }

        $this->insertAll($inserts);
        return $this->getNumRows();
    }

    public function removeUserAllDataAuthority(int $admin_user_id): int
    {
        $this->where('admin_user_id', '=', $admin_user_id)->delete();
        return $this->getNumRows();
    }
}
