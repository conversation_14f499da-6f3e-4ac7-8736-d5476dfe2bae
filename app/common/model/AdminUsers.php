<?php


namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Model;
use think\model\relation\HasManyThrough;
use think\model\relation\HasOne;

//lwj 2023.7.17 新增
//lwj 2023.10.17 修改
class AdminUsers extends Model
{
    protected $table = 'admin_users';

    public const AdminGroupSuperAdministrator = 1; //  超级管理员
    public const AdminGroupCorpAdministrator = 3; // 运营商管理员


    // 状态，1正常，2禁用
    public const StateNormal = 1; // 正常
    public const StateDisabled = 2; // 禁用


    public function group(): HasOne
    {
        return $this->hasOne(AdminAuthGroup::class, 'id', 'perm_group_id');
    }

    public function rules(): HasManyThrough
    {
        return $this->hasManyThrough(AdminAuthGroupRules::class, AdminAuthGroup::class, 'id', 'group_id', 'perm_group_id', 'id');
    }

    public function getUserData(int $user_id, ?array $field = null): ?array
    {
        if (is_null($field)) {
            $data = $this->where('id', $user_id)->find();
        } else {
            $data = $this->where('id', $user_id)->field($field)->find();
        }

        if (empty($data)) {
            return null;
        }
        return $data->toArray();
    }

    /**
     * 用户名是否已经存在
     *
     * @param string $username
     * @param int|null $exclude_user_id
     * @return bool true: 已经存在 false: 不存在
     */
    public function isUsernameAlreadyExists(string $username, ?int $exclude_user_id = null): bool
    {
        $where = [
            ['name', '=', $username]
        ];
        if (!is_null($exclude_user_id)) {
            $where[] = ['id', '<>', $exclude_user_id];
        }
        return $this->where($where)->count('id') > 0;
    }

    /**
     * 手机号是否已经存在
     *
     * @param string $phone
     * @param int|null $exclude_user_id
     * @return bool true: 已经存在 false: 不存在
     */
    public function isPhoneAlreadyExists(string $phone, ?int $exclude_user_id = null): bool
    {
        $where = [
            ['phone', '=', $phone]
        ];
        if (!is_null($exclude_user_id)) {
            $where[] = ['id', '<>', $exclude_user_id];
        }
        return $this->where($where)->count('id') > 0;
    }

    /**
     * 电子邮箱是否已经存在
     *
     * @param string $email 电子邮箱
     * @param int|null $exclude_user_id
     * @return bool true: 已经存在 false: 不存在
     */
    public function isEmailAlreadyExists(string $email, ?int $exclude_user_id = null): bool
    {
        $where = [
            ['email', '=', $email]
        ];
        if (!is_null($exclude_user_id)) {
            $where[] = ['id', '<>', $exclude_user_id];
        }
        return $this->where($where)->count('id') > 0;
    }


    public function usePhoneGetData(int $phone, ?array $field = null): ?array
    {
        $where = [
            ['phone', '=', $phone]
        ];
        if (!is_null($field)) {
            $data = $this->where($where)->field($field)->find();
        } else {
            $data = $this->where($where)->find();
        }

        if (empty($data)) return null;
        return $data->toArray();
    }

    /**
     * 加密密码
     *
     * @param string $password 原始密码
     * @return string 加密后的密码
     */
    public static function encryptedPassword(string $password): string
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    public function createAdminUser(string $name, string $phone, string $email, int $perm_group_id, string $password, int $corp_id, int $pid = 0): false|array
    {
        $password = self::encryptedPassword($password);

        $insert = [
            'name' => $name,
            'phone' => $phone,
            'email' => $email,
            'perm_group_id' => $perm_group_id,
            'avatar' => '',
            'password' => $password,
            'pid' => $pid,
            'corp_id' => $corp_id,
            'create_time' => date('Y-m-d H:i:s')
        ];
        $insert['id'] = $this->insertGetId($insert);
        if (empty($insert['id'])) return false;
        return $insert;
    }

    public function deleteCorpAdminUser(int $corp_id): int
    {
        $this->where('corp_id', '=', $corp_id)->delete();
        return $this->getNumRows();
    }

    /**
     * 查询管理员详情数据
     *
     * @param int $admin_user_id
     * @param int|null $admin_user_pid [default = null]
     * @return array|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDetailsData(int $admin_user_id, ?int $admin_user_pid = null): ?array
    {
        /**
         * @var Query $query
         */
        $query = $this;

        // u.id,u.name,u.avatar,u.phone,u.email,u.state,u.perm_group_id, g.group_name
        $field = [
            'au.id', 'au.name', 'au.avatar', 'au.phone', 'au.email',
            'au.state', 'au.perm_group_id', 'g.group_name as group_name', 'au.corp_id', 'c.name as corp_name'
        ];

        $where = [];
        $where[] = ['au.id', '=', $admin_user_id];
        if (!is_null($admin_user_pid)) {
            $where[] = ['au.pid', '=', $admin_user_pid];
        }

        $data = $query->alias('au')
            ->leftJoin('corp c', 'au.corp_id = c.id')
            ->leftJoin('admin_auth_group g', 'au.perm_group_id = g.id')
            ->field($field)
            ->where($where)
            ->find();
        if (empty($data)) return null;
        return $data->toArray();
    }

    public function getCorpId(int $admin_user_id): ?int
    {
        $corp_id = $this->where('id', '=', $admin_user_id)->value('corp_id');
        if (empty($corp_id)) return null;
        return $corp_id;
    }

    public function getAdminName(int $admin_user_id): ?string
    {
        $name = $this->where('id', '=', $admin_user_id)->value('name');
        if (empty($name)) return null;
        return $name;
    }

    public function getAdminNamesMap(array $admin_user_ids): array
    {
        return $this->where('id', 'in', $admin_user_ids)->column('name', 'id');
    }

//    public function createCorpAdministrator(int ): int
//    {
//
//    }

//    public function setPasswordAttr($value): string
//    {
//        return password_hash($value,PASSWORD_DEFAULT);
//    }
}