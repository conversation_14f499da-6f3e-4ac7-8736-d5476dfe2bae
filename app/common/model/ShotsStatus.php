<?php
/** @noinspection PhpRedundantOptionalArgumentInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpUnused */

namespace app\common\model;

use app\common\lib\exception\RuntimeException;
use app\common\lib\ExceptionLogCollector;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Model;
use Throwable;

class ShotsStatus extends Model
{
    protected $table = 'shots_status';

    // 在线状态
    public const IsOnlineYes = 2; // 在线
    public const IsOnlineNot = 1; // 离线

    // 工作状态
    public const WorkStatusIdle = 1; // 空闲
    public const WorkStatusCharging = 2; // 充电


    // 是否故障
    public const IsFaultNot = 1; // 正常
    public const IsFaultYes = 2; // 故障

    // 是否已删除
    public const IsDeleteYes = 1; // 已删除
    public const IsDeleteNot = 0; // 未删除


    // 选项状态
    public const StatusOptionOffline = 0; // 离线
    public const StatusOptionFault = 1; // 故障
    public const StatusOptionIdle = 2; // 空闲
    public const StatusOptionCharging = 3; // 充电中


    /**
     * 批量添加充电枪状态
     *
     * @param array $shots_ids
     * @return bool
     */
    public function batchAddShotsStatus(array $shots_ids): bool
    {
        $dbShotsIds = $this->where('id', 'in', $shots_ids)->column('id');
        $inserts = [];
        foreach ($shots_ids as $shots_id) {
            if (!in_array($shots_id, $dbShotsIds)) {
                $inserts[] = [
                    'id' => $shots_id,
                    'is_online' => self::IsOnlineNot,
                    'is_online_update_time' => date('Y-m-d H:i:s'),
                    'work_status' => self::WorkStatusIdle,
                    'work_status_update_time' => date('Y-m-d H:i:s'),
                    'is_fault' => self::IsFaultNot,
                    'is_fault_update_time' => date('Y-m-d H:i:s')
                ];
            }
        }

        if (empty($inserts)) return true;
        $this->insertAll($inserts);
        return true;
    }

    public function updateIsFault(int|array $id, int $is_fault): void
    {
        $where = [];
        if (is_int($id)) {
            $where[] = ['id', '=', $id];
        } else {
            $where[] = ['id', 'in', $id];
        }
        if ($is_fault === self::IsFaultNot) {
            $where[] = ['is_fault', '=', self::IsFaultYes];
        } else if ($is_fault === self::IsFaultYes) {
            $where[] = ['is_fault', '=', self::IsFaultNot];
        } else {
            throw new RuntimeException('无效故障状态', [], RuntimeException::CodeServiceException);
        }

        $this->where($where)->update([
            'is_fault' => $is_fault,
            'is_fault_update_time' => date('Y-m-d H:i:s')
        ]);
    }

    public function getWorkStatus(int $id): ?int
    {
        return $this->where('id', '=', $id)->value('work_status');
    }

    public function updateWorkStatus(int|array $id, int $work_status): void
    {
        $where = [];
        if (is_int($id)) {
            $where[] = ['id', '=', $id];
        } else {
            $where[] = ['id', 'in', $id];
        }
        if ($work_status === self::WorkStatusIdle) {
            $where[] = ['work_status', '=', self::WorkStatusCharging];
        } else if ($work_status === self::WorkStatusCharging) {
            $where[] = ['work_status', '=', self::WorkStatusIdle];
        } else {
            throw new RuntimeException('无效工作状态', [], RuntimeException::CodeServiceException);
        }

        $this->where($where)->update([
            'work_status' => $work_status,
            'work_status_update_time' => date('Y-m-d H:i:s')
        ]);
    }


    /**
     * 充电枪状态统计
     *
     * @param int $corp_id [default=0] 运营商ID 不传递或传递0则表示查询所有运营商的
     * @param array|int $station_id [default=0] 充电站ID 不传递或传递0则表示查询所有充电站的
     * @return array
     * @throws Throwable
     */
    public function statusStatistics(int $corp_id = 0, int|array $station_id = 0): array
    {
        try {
            // 构建基础查询条件
            $where = [
                ['s.is_del', '=', Shots::IsDelNot]
            ];
            if (!empty($corp_id)) {
                $where[] = ['s.corp_id', '=', $corp_id];
            }
            if (!empty($station_id)) {
                if (is_int($station_id)) {
                    $where[] = ['s.station_id', '=', $station_id];
                } else {
                    $where[] = ['s.station_id', 'in', $station_id];
                }
            }

            /**
             * @var Query $query
             */
            $query = $this;

            // 一次性查询所有需要的数据
            $data = $query->alias('ss')
                ->join('shots s', 's.id = ss.id', 'INNER')
                ->join('piles_status ps', 'ps.id = s.piles_id', 'LEFT') // 使用LEFT JOIN确保即使没有piles_status记录也能获取到shots记录
                ->where($where)
                ->field([
                    'DISTINCT s.id', // 使用DISTINCT避免重复计数
                    'ss.work_status',
                    'ss.is_fault',
                    'ps.is_online'
                ])
                ->select()
                ->toArray();

            // 在PHP中统计各种状态的数量
            $totalCount = count($data); // 总数量就是不重复的充电枪数量
            $chargingCount = 0; // 充电中的枪数量
            $idleCount = 0;     // 空闲的枪数量
            $faultCount = 0;    // 故障的枪数量
            $offlineCount = 0;  // 离线的枪数量

            foreach ($data as $item) {
                // 统计故障数量
                if ($item['is_fault'] === self::IsFaultYes) {
                    $faultCount++;
                }

                // 统计充电中和空闲数量
                if ($item['work_status'] === self::WorkStatusCharging) {
                    $chargingCount++;
                } elseif ($item['work_status'] === self::WorkStatusIdle) {
                    $idleCount++;
                }

                // 统计离线数量
                if (isset($item['is_online']) && $item['is_online'] === PilesStatus::IsOnlineNot) {
                    $offlineCount++;
                }
            }

            return [
                [
                    'status' => -1,
                    'count' => $totalCount
                ],
                // 离线数量
                [
                    'status' => self::StatusOptionOffline,
                    'count' => $offlineCount
                ],
                // 故障数量
                [
                    'status' => self::StatusOptionFault,
                    'count' => $faultCount
                ],
                // 空闲数量(减去离线数量和故障数量才是实际空闲的桩)
                [
                    'status' => self::StatusOptionIdle,
                    'count' => $idleCount-$offlineCount-$faultCount
                ],
                // 充电中数量
                [
                    'status' => self::StatusOptionCharging,
                    'count' => $chargingCount
                ],
            ];

        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            throw $e;
        }
    }

    /**
     * 查询充电枪在线数量
     *
     * @param int $corp_id [default=0] 运营商ID
     * @return int
     */
    public function onlineCount(int $corp_id = 0): int
    {
        /**
         * @var Query $query
         */
        $query = $this;
        $query = $query->alias('ss')
            ->join('shots s', 's.id = ss.id', 'INNER')
            ->join('piles_status ps', 'ps.id = s.piles_id', 'INNER')
            ->join('piles p', 'p.id = s.piles_id', 'INNER');
        $where = [];
        $where[] = ['ps.is_online', '=', PilesStatus::IsOnlineYes];
        $where[] = ['p.is_del', '=', Piles::IsDelNot];
        if (!empty($corp_id)) {
            $where[] = ['s.corp_id', '=', $corp_id];
        }

        return $query->where($where)->count();
    }

    /**
     * 查询离线超过1小时的充电枪
     *
     * @return array
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function queryOfflineMoreThanOneHour(): array
    {
        /**
         * @var Query $query
         */
        $query = $this;
        $query = $query->alias('ss')->join('shots s', 's.id = ss.id', 'INNER')
            ->join('piles_status ps', 'ps.id = s.piles_id', 'INNER');

        return $query->where('ps.is_online', '=', PilesStatus::IsOnlineNot)
            ->where('ps.is_online_update_time', '<=', date('Y-m-d H:i:s', strtotime('-1 hour')))
            ->field(['s.id', 's.piles_id', 'ps.is_online_update_time as status_update_time'])
            ->select()
            ->toArray();
    }

    /**
     * 获取充电量充电桩与充电枪数量
     *
     * @param int $station_id 充电场ID
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getShotsIdleAndTotalCount(int $station_id): array
    {
        /**
         * @var Query $query
         */
        $query = $this->alias('ss');
        $data = $query
            ->join('shots s', 's.id = ss.id', 'INNER')
            ->join('piles p', 's.piles_id = p.id', 'INNER')
            ->join('piles_status ps', 'ps.id = p.id', 'LEFT') // 使用LEFT JOIN确保即使没有piles_status记录也能获取到shots记录
            ->where('p.station_id', '=', $station_id)
            ->where('s.is_del', '=', Shots::IsDelNot)
            ->field([
                'DISTINCT s.id', // 使用DISTINCT避免重复计数
                'p.type',
                'ss.work_status',
                'ps.is_online'
            ])
            ->select()
            ->toArray();

        // 在PHP中处理数据并统计
        $list = [];
        foreach ($data as $item) {
            $type = $item['type'];

            // 初始化该类型的计数器
            if (!isset($list[$type])) {
                $list[$type] = [
                    'sum_num' => 0,
                    'idle_num' => 0
                ];
            }

            // 总数量增加
            $list[$type]['sum_num']++;

            // 空闲且在线的充电枪数量
            if ($item['work_status'] === self::WorkStatusIdle &&
                isset($item['is_online']) && $item['is_online'] === PilesStatus::IsOnlineYes) {
                $list[$type]['idle_num']++;
            }
        }

        return $list;
    }
}
