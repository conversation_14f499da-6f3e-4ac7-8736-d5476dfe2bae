<?php

namespace app\common\model;

use app\common\cache\redis\entity\AdminLoginUser;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Model;

/**
 * 电表功率记录模型
 * @package app\admin\model
 */
class ElectricityMeterPowerRecord extends Model
{
    protected $table = 'electricity_meter_power_record';

    public function addRecord(int $station_id, string $centralized_controller_id, int $a_active_power,
                              int $b_active_power, int $c_active_power, int $total_active_power, int $record_time): bool
    {
        return $this->insert([
                'station_id' => $station_id,
                'centralized_controller_id' => $centralized_controller_id,
                'a_active_power' => $a_active_power,
                'b_active_power' => $b_active_power,
                'c_active_power' => $c_active_power,
                'total_active_power' => $total_active_power,
                'record_time' => date('Y-m-d H:i:s', $record_time),
            ]) > 0;
    }

    public function delete_expire_record(): bool
    {
        $expire_date = date('Y-m-d H:i:s', strtotime(sprintf('-%d day', 180)));

        return $this->where('record_time', '<=', $expire_date)->delete();
    }

    /**
     * 记录列表
     *
     * @param AdminLoginUser $loginUser
     * @param int $station_id 记录ID
     * @param string $centralized_controller_id 能源路由器ID
     * @param int $start_time 起始时间
     * @param int $end_time 结尾时间
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function record_list(AdminLoginUser $loginUser, int $station_id, string $centralized_controller_id, int $start_time, int $end_time): array
    {
        $fields = [
            'empr.a_active_power as a', 'empr.b_active_power as b', 'empr.c_active_power as c',
            'empr.total_active_power as total', 'empr.record_time as time'
        ];

        // 数据权限
        $where = [];
        $where[] = ['s.is_del', '=', Stations::IsDelNot];
        $where[] = ['empr.station_id', '=', $station_id];
        $where[] = ['empr.centralized_controller_id', '=', $centralized_controller_id];
        $where[] = ['empr.record_time', '>=', date('Y-m-d H:i:s', $start_time)];
        $where[] = ['empr.record_time', '<=', date('Y-m-d H:i:s', $end_time)];
        if ($loginUser->corp_id > 0 && $loginUser->pid === 0) {
            $where[] = ['s.corp_id', '=', $loginUser->corp_id];
        } else if ($loginUser->corp_id > 0) {
            $where[] = ['s.corp_id', '=', $loginUser->corp_id];
            $station_ids = (new StationDataAuthority())->getStationIds($loginUser->id);
            $where[] = ['empr.station_id', 'in', $station_ids];
        } else {
            if (!empty($data['corp_id'])) {
                $where[] = ['s.corp_id', '=', $data['corp_id']];
            }
        }

        /**
         * @var Query $this
         */
        return $this->alias('empr')
            ->join('stations s', 's.id = empr.station_id', 'LEFT')
            ->field($fields)
            ->where($where)
            ->select()
            ->toArray();
    }
}
