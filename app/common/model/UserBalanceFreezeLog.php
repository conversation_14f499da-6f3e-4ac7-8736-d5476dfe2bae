<?php
/** @noinspection PhpUnused */

namespace app\common\model;

use think\Model;

class UserBalanceFreezeLog extends Model
{
    protected $table = 'user_balance_freeze_log';

    // 状态
    public const StateFreezing = 1; // 冻结中
    public const StateUnFreeze = 2; // 完成

    /**
     * 解冻
     *
     * @param string $order_id 交易流水号
     * @return int 更新的条数
     */
    public function unfreeze(string $order_id): int
    {
        if (empty($order_id)) return 0;

        return (int)$this->where('order_id', $order_id)
            ->update([
                'state' => UserBalanceFreezeLog::StateUnFreeze,
                'update_time' => date('Y-m-d H:i:s'),
            ]);
    }
}
