<?php

namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Model;

class WorkOrderResponsible extends Model
{
    protected $table = 'work_order_responsible';

    // 是否激活
    public const IsActivateYes = 1; // 是
    public const IsActivateNot = 2; // 否

    public function setResponsible(
        int    $admin_user_id,
        string $work_order_id
    ): bool
    {
        return $this->insert([
                'admin_user_id' => $admin_user_id,
                'work_order_id' => $work_order_id,
                'is_activate' => self::IsActivateYes
            ]) > 0;
    }

    public function updateWorkOrderActivateStatus(string $work_order_id, int $admin_user_id, int $is_activate): bool
    {
        $where = [
            ['admin_user_id', '=', $admin_user_id],
            ['work_order_id', '=', $work_order_id]
        ];
        return $this->where($where)->update([
                'is_activate' => $is_activate
            ]) > 0;
    }

    public function getWorkOrderActivateResponsibleId(string $work_order_id): ?int
    {
        $where = [
            ['work_order_id', '=', $work_order_id],
            ['is_activate', '=', self::IsActivateYes]
        ];
        $admin_user_id = $this->where($where)->value('admin_user_id');
        if (empty($admin_user_id)) {
            return null;
        }
        return $admin_user_id;
    }

    /**
     * 是否存在激活状态的负责人
     *
     * @param string $work_order_id
     * @return bool true: 存在 false: 不存在
     */
    public function isExistsActivateResponsible(string $work_order_id): bool
    {
        $where = [
            ['work_order_id', '=', $work_order_id],
            ['is_activate', '=', self::IsActivateYes]
        ];
        return $this->where($where)->count() > 0;
    }

    /**
     * 获取激活状态的负责人
     *
     * @param string $work_order_id
     * @return ?int
     */
    public function getActivateResponsible(string $work_order_id): ?int
    {
        $where = [
            ['work_order_id', '=', $work_order_id],
            ['is_activate', '=', self::IsActivateYes]
        ];
        $admin_user_id = $this->where($where)->value('admin_user_id');
        if (empty($admin_user_id)) return null;
        return $admin_user_id;
    }


    /**
     *
     * @param int $admin_user_id
     * @param string $id
     * @param array|null $field
     * @return array|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getInfo(
        int    $admin_user_id,
        string $id,
        ?array $field = null
    ): ?array
    {
        /**
         * @var Query $query
         */
        $query = $this;
        if (is_null($field)) {
            $field = [
                'wo.id', 'wo.title', 'wo.priority', 'wo.status', 'wo.source', 'wo.template_id', 'wot.name as template_name',
                'wo.create_user_type', 'wo.create_user_id', 'wo.create_time', 'wo.update_time',
                'wor.corp_id', 'wor.station_id', 'wor.device_type', 'wor.device_id'
            ];
        }

        $where = [
            ['wo.id', '=', $id],
            ['wo_responsible.admin_user_id', '=', $admin_user_id],
            ['wo_responsible.is_activate', '=', self::IsActivateYes]
        ];

        $data = $query->alias('wo_responsible')
            ->leftJoin('work_order wo', 'wo.id = wo_responsible.work_order_id')
            ->leftJoin('work_order_template wot', 'wot.id = wo.template_id')
            ->leftJoin('work_order_relation wor', 'wor.work_order_id = wo.id')
            ->where($where)
            ->field($field)
            ->find();
        if (empty($data)) {
            return null;
        }

        return $data->toArray();
    }
}
