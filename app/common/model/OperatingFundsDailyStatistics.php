<?php

namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

// 运营资金每日统计表
class OperatingFundsDailyStatistics extends Model
{
    protected $table = 'operating_funds_daily_statistics';


    /**
     * @param string $start_time
     * @param string $end_time
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function get_time_range_data(string $start_time, string $end_time): array
    {
        $where = [
            ['date', '>=', $start_time],
            ['date', '<=', $end_time]
        ];
        return $this->where($where)
            ->field(['date', 'recharge_money', 'recharge_count', 'charging_money', 'charging_count'])
            ->order('date', 'desc')
            ->withAttr('date', function ($value) {
                return date('Y-m-d', strtotime($value));
            })
            ->select()
            ->toArray();
    }

    /**
     * @param string $start_time
     * @param string $end_time
     * @param int $page
     * @param int $limit
     * @return array
     * @throws DbException
     */
    public function get_list(string $start_time, string $end_time, int $page, int $limit): array
    {
        $where = [
            ['date', '>=', $start_time],
            ['date', '<=', $end_time]
        ];
        return $this->where($where)
            ->field(['date', 'recharge_money', 'recharge_count', 'charging_money', 'charging_count'])
            ->order('date', 'desc')
            ->withAttr('date', function ($value) {
                return date('Y-m-d', strtotime($value));
            })
            ->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();
    }

}