<?php

namespace app\common\model;

use think\Model;

class WorkOrderProcessingRecords extends Model
{
    protected $table = 'work_order_processing_records';

    public const UserTypeAdminUser = 1; // 后台用户
    public const UserTypeNormalUser = 2; // 普通用户
    public const UserTypeSystem = 3; // 系统

    public function addRecord(
        string $work_order_id,
        int    $user_type,
        int    $user_id,
        string $message,
        string $attachment = ''
    ): bool
    {
        return $this->insert([
                'work_order_id' => $work_order_id,
                'user_type' => $user_type,
                'user_id' => $user_id,
                'message' => $message,
                'attachment' => $attachment,
                'create_time' => date('Y-m-d H:i:s')
            ]) > 0;
    }


    public static function completeMessage(int $user_id): string
    {
        return sprintf('【系统】用户标识为：%s的工作人员完成对工单问题的处理。', $user_id);
    }

    public static function exitWorkOrderMessage(int $user_id): string
    {
        return sprintf('【系统】用户标识为：%s的工作人员退出此工单。', $user_id);
    }

    public static function assignWorkOrderMessage(string $operationUsername, int $assignPhone): string
    {
        return sprintf('【系统】管理员%s将工单指派给手机号为%s的工作人员。', $operationUsername, $assignPhone);
    }

    public static function createWorkOrderMessage(string $operationUsername): string
    {
        return sprintf('【系统】管理员%s新建工单。', $operationUsername);
    }

    public static function alarmAutoCreateWorkOrderMessage(): string
    {
        return '【系统】由主动告警生成。';
    }

    public static function commonUserCreateWorkOrderMessage(string $user_id): string
    {
        return sprintf('【系统】标识为%s的用户新建工单。', $user_id);
    }

    public static function systemCheckFaultRecovery(): string
    {
        return '【系统】检测到工单所顶绑定故障已恢复！';
    }


}
