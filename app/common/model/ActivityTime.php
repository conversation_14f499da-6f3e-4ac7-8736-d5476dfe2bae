<?php

namespace app\common\model;

use think\Model;

class ActivityTime extends Model
{
    protected $table = 'activity_time';

    public function addTimes(int $activity_id, array $times): int
    {
        $inserts = [];
        foreach ($times as $time) {
            $inserts[] = [
                'activity_id' => $activity_id,
                'start_time' => $time['start_time'],
                'end_time' => $time['end_time'],
                'create_time' => date('Y-m-d H:i:s')
            ];
        }

        return $this->insertAll($inserts);
    }

    public function removeActivityRelationAllTime(int $activity_id): bool
    {
        $where = [
            ['activity_id', '=', $activity_id]
        ];
        return $this->where($where)->delete();
    }
}