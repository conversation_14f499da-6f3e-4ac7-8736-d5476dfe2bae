<?php

namespace app\common\model;

use think\Model;

class CorpAdminGroup extends Model
{
    protected $table = 'corp_admin_group';

    public function getCorpAdminGroupIds(int $corp_id): array
    {
        return $this->where('corp_id', '=', $corp_id)->column('admin_group_id');
    }

    public function bindCorpAdminGroupIds(int $corp_id, array $admin_group_ids): int
    {
        if (count($admin_group_ids) === 0) return 0;

        $inserts = [];
        foreach ($admin_group_ids as $admin_group_id) {
            $inserts[] = [
                'admin_group_id' => $admin_group_id,
                'corp_id' => $corp_id
            ];
        }
        return $this->insertAll($inserts);
    }


    public function removeCorpAllAdminGroupIds(int $corp_id): int
    {
        $this->where('corp_id', '=', $corp_id)->delete();
        return $this->getNumRows();
    }
}