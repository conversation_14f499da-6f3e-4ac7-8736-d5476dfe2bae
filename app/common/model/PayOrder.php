<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

class PayOrder extends Model
{
    protected $table = 'pay_order';


    public const StateCreate = 1;
    public const StatePaying = 2; // 支付中
    public const StatePayComplete = 3; // 支付完成
    public const StateFailed = 4; // 失败或其他问题
    public const StateClose = 5; // 关闭支付

    public const AddBalanceSuccess = 1; // 增加余额成功
    public const AddBalanceFailed = 2; // 增加余额失败
    public const AddBalanceWait = 3; // 未加余额

    public function updateState(string $id, int $state, string $trade_no, string $msg = '', ?int $add_balance = null): int
    {
        $updateData = [
            'trade_no' => $trade_no,
            'state' => $state,
            'msg' => $msg,
        ];
        if (is_int($add_balance)) {
            $updateData['add_balance'] = $add_balance;
        }
        return (int)$this->where('id', $id)->update($updateData);
    }


    /**
     * 查询用户最近一次充值金额与时间
     *
     * @param int $user_id 用户ID
     * @return ?array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getUserLastRechargeAmountAndTime(int $user_id): ?array
    {
        $data = $this->where('user_id', $user_id)
            ->order('update_time', 'DESC')
            ->field(['price', 'create_time'])->find();
        if (empty($data)) {
            return null;
        }
        return $data->toArray();
    }

    /**
     * @param string $date
     * @return int[]
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function dailyStatistics(string $date): array
    {
        $startTime = $date;
        $endTime = strtotime($date) + 24 * 3600;
        $where = [
            ['create_time', '>=', $startTime],
            ['create_time', '<=', $endTime],
            ['state', '=', self::StatePayComplete]
        ];
        $data = $this->where($where)
            ->field(['ifnull(sum(price), 0) as money', 'ifnull(count(id), 0) as count'])
            ->find();
        if (empty($data)) {
            return [
                'money' => 0,
                'count' => 0
            ];
        }
        return $data->toArray();
    }
}
