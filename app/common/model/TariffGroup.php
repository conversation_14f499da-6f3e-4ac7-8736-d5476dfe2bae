<?php

namespace app\common\model;

use app\common\cache\redis\entity\AdminLoginUser;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;
use think\db\Query;

/**
 * 费率组模型
 * @package app\admin\model
 */
class TariffGroup extends Model
{
    protected $table = 'tariff_group';
    protected $json = ['period_json_sum', 'period_json'];
    protected $jsonAssoc = true;

    public const PeriodTypeSharp = '00'; // 尖时段
    public const PeriodTypePeak = '01'; // 峰时段
    public const PeriodTypeFlat = '02'; // 平时段
    public const PeriodTypeValley = '03'; // 谷时段


    /**
     * 获取列表数据
     *
     * @param AdminLoginUser $loginUser
     * @param array $params
     * @return array
     * @throws DbException
     */
    public function getListData(AdminLoginUser $loginUser, array $params): array
    {
        $page = ($params['page'] ?? false) ?: 1;
        $pageSize = ($params['limit'] ?? false) ?: 10;
        $order_name = ($params['order_name'] ?? false) ?: 'tg.id';
        $order_type = ($params['order_type'] ?? false) ?: 'desc';


        // 数据权限
        if ($loginUser->corp_id > 0) {
            $params['corp_id'] = $loginUser->corp_id;
        }

        // 搜索框搜索
        $where = function ($query) use ($params) {
            if (isset($params['id']) && $params['id']) {
                $query->where('tg.id', $params['id']);
            }
            if (isset($params['name']) && $params['name']) {
                $query->where('tg.name', 'like', '%' . $params['name'] . '%');
            }
            if (isset($params['corp_id']) && $params['corp_id']) {
                $query->where('tg.corp_id', '=', $params['corp_id']);
            }
            return $query;
        };


        $fields = [
            'tg.id', 'tg.name', 'tg.sharp_fee', 'tg.peak_fee', 'tg.flat_fee',
            'tg.valley_fee', 'tg.sharp_ser_fee', 'tg.peak_ser_fee',
            'tg.flat_ser_fee', 'tg.valley_ser_fee', 'tg.loss_rate', 'tg.period_codes',
            'tg.surcharge',
            'c.name as corp_name'
        ];


        /**
         * @var Query $query
         */
        $query = $this;

        return $query->alias('tg')
            ->join('corp c', 'c.id = tg.corp_id')
            ->field($fields)
            ->withAttr('period_codes', function ($value) {
                return period_codes_to_period_rate_list($value);
            })
            ->withAttr('sharp_fee', function ($value) {
                return int_to_decimal($value);
            })
            ->withAttr('peak_fee', function ($value) {
                return int_to_decimal($value);
            })
            ->withAttr('flat_fee', function ($value) {
                return int_to_decimal($value);
            })
            ->withAttr('valley_fee', function ($value) {
                return int_to_decimal($value);
            })
            ->withAttr('sharp_ser_fee', function ($value) {
                return int_to_decimal($value);
            })
            ->withAttr('peak_ser_fee', function ($value) {
                return int_to_decimal($value);
            })
            ->withAttr('flat_ser_fee', function ($value) {
                return int_to_decimal($value);
            })
            ->withAttr('valley_ser_fee', function ($value) {
                return int_to_decimal($value);
            })
            ->withAttr('surcharge', function ($value) {
                return int_to_decimal($value);
            })
            ->where($where)
            ->order($order_name, $order_type)
            ->paginate(['list_rows' => $pageSize, 'page' => $page])
            ->toArray();
    }


    public function getRelationPilesIds(int $id): array
    {
        $where = [
            ['tg.id', '=', $id]
        ];
        /**
         * @var Query $this
         */
        return $this->alias('tg')
            ->join('stations_extra_info sei', 'sei.tariff_group_id = tg.id')
            ->join('piles p', 'p.station_id = sei.id')
            ->where($where)
            ->column('p.id');
    }

    /**
     * @param int $tariff_group_id
     * @return array|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getTariffGroup(int $tariff_group_id): ?array
    {
        $where = [
            ['id', '=', $tariff_group_id]
        ];
        /**
         * @var Query $this
         */
        $tariff_group = $this->where($where)->find();
        if (empty($tariff_group)) return null;
        return $tariff_group->toArray();
    }
}
