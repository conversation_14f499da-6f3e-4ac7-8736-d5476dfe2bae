<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\common\model;

use think\Model;

class ChargingQrcodeToShots extends Model
{
    protected $table = 'charging_qrcode_to_shots';

    public function verifyShotsHasItBeenBound(int $shots_id): bool
    {
        return $this->where([
                ['shots_id', '=', $shots_id]
            ])->count() > 0;
    }

    public function verifyQrcodeHasItBeenBound(int $charging_qrcode_id): bool
    {
        return $this->where([
                ['id', '=', $charging_qrcode_id]
            ])->count() > 0;
    }

    public function verifyIfItExists(int $charging_qrcode_id, int $shots_id): bool
    {
        $where = [
            ['id', '=', $charging_qrcode_id],
            ['shots_id', '=', $shots_id]
        ];
        return $this->where($where)->count() > 0;
    }

    public function unbindQrcode(int $charging_qrcode_id, int $shots_id): bool
    {
        return $this->where([
            ['id', '=', $charging_qrcode_id],
            ['shots_id', '=', $shots_id]
        ])->delete();
    }

    public function bindQrcode(int $charging_qrcode_id, int $shots_id): bool
    {
        $this->insert([
            'id' => $charging_qrcode_id,
            'shots_id' => $shots_id,
            'create_time' => date('Y-m-d H:i:s')
        ]);
        return $this->getNumRows() > 0;
    }

    public function unbindShots(int $shots_id): bool
    {
        return $this->where([
            ['shots_id', '=', $shots_id]
        ])->delete();
    }

    public function unbindChargingQrcode(int $charging_qrcode_id): bool
    {
        return $this->where([
            ['id', '=', $charging_qrcode_id]
        ])->delete();
    }

    public function getBindShotsId(int $id): ?int
    {
        return $this->where('id', '=', $id)->value('shots_id');
    }
}