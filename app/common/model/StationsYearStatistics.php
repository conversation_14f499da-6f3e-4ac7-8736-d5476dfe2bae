<?php

namespace app\common\model;

use think\facade\Db;
use think\Model;

/**
 * 充电站年统计表
 * cbj 2023.10.19 新增
 */
class StationsYearStatistics extends Model
{
    protected $table = 'stations_year_statistics';

    /**
     * 累计统计概况
     *
     * @param int $corp_id 运营商ID
     * @param int|array $station_id 充电站ID
     * @return array
     */
    public function accumulateStatistic(int $corp_id = 0, int|array $station_id = 0): array
    {
        $field = [
            'sum(amount) as money',
            'sum(electricity) as electricity_total',
            'sum(serv_amount) as service_money',
            'sum(order_count) as order_count',
        ];
        $where = [];
        if (!empty($corp_id)) {
            $where[] = sprintf('corp_id = %d', $corp_id);
        }
        if (!empty($station_id)) {
            if (is_int($station_id)) {
                $where[] = sprintf('station_id = %d', $station_id);
            } else {
                $where[] = sprintf('station_id in (%s)', implode(',', $station_id));
            }
        }
        if (!empty($where)) {
            $where = 'WHERE ' . implode(' AND ', $where);
        } else {
            $where = '';
        }
        $sql = 'SELECT %s FROM %s %s';
        $sql = sprintf(
            $sql,
            implode(', ', $field),
            $this->getTable(),
            $where
        );

        $result = Db::query($sql);
        $result = $result[0];
        $result['money'] = $result['money'] ?? 0;
        $result['electricity_total'] = $result['electricity_total'] ?? 0;
        $result['service_money'] = $result['service_money'] ?? 0;
        $result['order_count'] = $result['order_count'] ?? 0;

        return $result;
    }

    /**
     * 填充每年空数据
     *
     * @param int $year 年
     * @param array $stationIds [default=[]] 排查的充电站ID集合
     * @return int
     */
    public function fillDailyEmptyData(int $year, array $stationIds = []): int
    {
        $insertField = [
            'corp_id', 'station_id', 'statistic_time',
            'sharp', 'peak', 'flat', 'valley', 'electricity',
            'amount', 'elec_amount', 'serv_amount', 'order_count',
            'charge_tick', 'utility_rate'
        ];
        $yearString = strtotime(sprintf('%d-01-01 00:00:00', $year));
        $selectField = [
            'corp_id', 'id as station_id',
            '"' . $yearString . '" as statistic_time',
            '0 as sharp', '0 as peak', '0 as flat', '0 as valley', '0 as electricity',
            '0 as amount', '0 as elec_amount', '0 as serv_amount', '0 as order_count',
            '0 as charge_tick', '0 as utility_rate'
        ];
        $whereSQL = '';
        if (!empty($stationIds)) {
            $whereSQL = sprintf('WHERE id not in(%s)', implode(',', $stationIds));
        }
        $sql = 'INSERT INTO `%s`(%s) SELECT %s FROM %s %s';
        $StationsModel = app(Stations::class);
        $sql = sprintf(
            $sql, $this->getTable(), implode(', ', $insertField),
            implode(', ', $selectField), $StationsModel->getTable(), $whereSQL);

        return Db::execute($sql);
    }
}