<?php
declare (strict_types=1);

namespace app\listener\event_distribute;

use app\common\queue\AdminSocketQueue;
use app\common\queue\MessagePushQueue;
use app\event\ChargeNormalEndEvent;
use RedisException;

class ChargeNormalEnd
{
    /**
     * 事件监听处理
     *
     * @param ChargeNormalEndEvent $event
     * @return bool
     * @throws RedisException
     */
    public function handle(ChargeNormalEndEvent $event): bool
    {

        $message = [
            'type' => AdminSocketQueue::TypeChargeNormalEnd,
            'order_id' => $event->orderId,
            'corp_id' => $event->corpId,
            'station_id' => $event->stationId,
            'pay_money' => $event->payMoney,
            'electricity_total' => $event->electricityTotal,
            'valley_electricity' => $event->valleyElectricity,
            'flat_electricity' => $event->flatElectricity,
            'peak_electricity' => $event->peakElectricity,
            'sharp_electricity' => $event->sharpElectricity,
            'service_money' => $event->serviceMoney,
            'charge_duration' => $event->chargeDuration,
            'start_time' => $event->startTime,
            'end_time' => $event->endTime,
            'user_id' => $event->user_id,
            'stop_charge_reason' => $event->stop_charge_reason,
            'stop_charge_status' => $event->stop_charge_status
        ];
        (new AdminSocketQueue())->push(json_encode_cn($message));
//        $message['type'] = RankingListQueue::TypeChargeNormalEnd;
//        (new RankingListQueue())->push(json_encode_cn($message));
        $message['type'] = MessagePushQueue::TypeChargeNormalEnd;
        (new MessagePushQueue())->push(json_encode_cn($message));

        return true;
    }
}
