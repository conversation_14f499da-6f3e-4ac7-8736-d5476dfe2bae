<?php
declare (strict_types=1);

namespace app\listener\event_distribute;

use app\common\queue\AdminSocketQueue;
use app\common\queue\RankingListQueue;
use app\event\ChargeAbnormalEndEvent;

class ChargeAbnormalEnd
{
    /**
     * 事件监听处理
     *
     * @param ChargeAbnormalEndEvent $event
     * @return bool
     */
    public function handle(ChargeAbnormalEndEvent $event): bool
    {
        $message = [
            'type' => AdminSocketQueue::TypeChargeAbnormalEnd,
            'abnormal_reason' => $event->abnormalReason,
            'abnormal_sub_reason' => $event->abnormalSubReason,
            'order_id' => $event->orderId,
            'corp_id' => $event->corpId,
            'station_id' => $event->stationId,
            'pay_money' => $event->payMoney,
            'electricity_total' => $event->electricityTotal,
            'charge_duration' => $event->chargeDuration
        ];

        app(AdminSocketQueue::class)->push(json_encode($message));

//        $message['type'] = RankingListQueue::TypeChargeAbnormalEnd;
//        app(RankingListQueue::class)->push(json_encode($message));

        return true;
    }
}
