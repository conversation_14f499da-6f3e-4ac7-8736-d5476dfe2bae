<?php
declare (strict_types=1);

namespace app\listener\event_distribute;

use app\common\queue\AdminSocketQueue;
use app\event\ShotsStatusChangeEvent;

class ShotsStatusChange
{
    /**
     * 事件监听处理
     *
     * @param ShotsStatusChangeEvent $event
     * @return bool
     */
    public function handle(ShotsStatusChangeEvent $event): bool
    {
        $message = [
            'type' => AdminSocketQueue::TypeShotsStatusChange,
            'corp_id' => $event->corpId,
            'station_id' => $event->stationId,
            'pile_id' => $event->pileId,
            'shot_id' => $event->shotId,
            'before_status' => $event->beforeStatus,
            'after_status' => $event->afterStatus
        ];
        (new AdminSocketQueue())->push(json_encode_cn($message));
//        $message['type'] = ShotsStatusQueue::TypeStatusChange;
//        (new ShotsStatusQueue())->push(json_encode($message));


        return true;
    }
}
