<?php

namespace app\listener\event_distribute;


use app\common\queue\AlarmMonitoringQueue;
use app\event\DeviceAbnormalityEvent;

// 设备异常事件监听器
class DeviceAbnormalityListener
{
    /**
     * 事件监听处理
     *
     * @param DeviceAbnormalityEvent $event
     * @return bool
     */
    public function handle(DeviceAbnormalityEvent $event): bool
    {
        $data = array_merge($event->toArray(), [
            'type' => AlarmMonitoringQueue::TypeAutomaticDetection
        ]);
        (new AlarmMonitoringQueue())->push(json_encode($data));

        return true;
    }
}