<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\listener\event_distribute;

use app\common\queue\package\user_balance_change\BasePackage;
use app\common\queue\package\user_balance_change\UpdateChargingStationBalancePackage;
use app\common\queue\UserBalanceChangeQueue;
use app\event\UserBalanceChangeEvent;

class UserBalanceChangeListener
{
    /**
     * 事件监听处理
     *
     * @param UserBalanceChangeEvent $event
     * @return bool
     * @throws \RedisException
     */
    public function handle(UserBalanceChangeEvent $event): bool
    {
        $package = new UpdateChargingStationBalancePackage([
            'type' => BasePackage::TypeUpdateChargingStationBalance,
            'user_id' => $event->user_id,
            'trigger_time' => $event->trigger_time
        ]);
        (new UserBalanceChangeQueue())->push($package->encode());

        return true;
    }
}