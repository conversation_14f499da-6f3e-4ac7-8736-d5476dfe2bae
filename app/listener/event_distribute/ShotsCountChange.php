<?php
declare (strict_types=1);

namespace app\listener\event_distribute;

use app\common\queue\AdminSocketQueue;
use app\common\queue\ShotsStatusQueue;
use app\event\ShotsCountChangeEvent;
use RedisException;

class ShotsCountChange
{
    /**
     * 事件监听处理
     *
     * @param ShotsCountChangeEvent $event
     * @return bool
     * @throws RedisException
     */
    public function handle(ShotsCountChangeEvent $event): bool
    {

        $message = [
            'type' => ShotsStatusQueue::TypeCountChange,
            'corp_id' => $event->corpId,
            'station_id' => $event->stationId,
            'pile_id' => $event->pileId,
            'shot_id' => $event->shotId,
            'change_type' => $event->changeType,
            'shot_status' => $event->shotStatus
        ];
        (new ShotsStatusQueue())->push(json_encode($message));
        $message['type'] = AdminSocketQueue::TypeShotsCountChange;
        (new AdminSocketQueue())->push(json_encode($message));

        return true;
    }
}
