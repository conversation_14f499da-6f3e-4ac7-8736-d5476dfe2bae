<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\listener\event_distribute;

use app\common\queue\MessagePushQueue;
use app\event\ChargeNormalStartEvent;

class ChargeNormalStart
{
    /**
     * 事件监听处理
     *
     * @param ChargeNormalStartEvent $event
     * @return bool
     * @throws \RedisException
     */
    public function handle(ChargeNormalStartEvent $event): bool
    {
        $message = $event->toArray();
        $message['type'] = MessagePushQueue::TypeChargeNormalStart;
        (new MessagePushQueue())->push(json_encode_cn($message));

        return true;
    }
}
