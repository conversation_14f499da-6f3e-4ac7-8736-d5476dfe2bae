<?php
declare (strict_types=1);

namespace app\listener\log;


use app\common\log\LogCollector;
use think\DbManager;

class HttpRunListener
{
    protected function listenerSql(): void
    {
        $DbManager = app(DbManager::class);
        $DbManager->listen(function ($sql, $time, $master) {
            if (str_starts_with($sql, 'CONNECT:')) {
                LogCollector::collectorRunLog($sql, LogCollector::LevelSql);

                return;
            }

            // 记录SQL
            if (is_bool($master)) {
                // 分布式记录当前操作的主从
                $master = $master ? 'master|' : 'slave|';
            } else {
                $master = '';
            }

            LogCollector::collectorRunLog($sql . ' [ ' . $master . 'RunTime:' . $time . 's ]', LogCollector::LevelSql);
        });
    }

    public function handle(): bool
    {
        $this->listenerSql();

        return true;
    }
}
