<?php
declare (strict_types=1);

namespace app\listener\log;


use app\common\log\LogCollector;
use think\App;
use think\Request;
use think\Response;

class HttpEndListener
{
    public function handle(Response $response): bool
    {
        $this->recordRequestLog();
        $this->recordResponseLog($response);

        return true;
    }

    protected function recordResponseLog(Response $response): void
    {
        LogCollector::collectorResponseLog(sprintf('响应状态=%d', $response->getCode()), LogCollector::LevelResponse);
        LogCollector::collectorResponseLog(sprintf('响应头=%s', print_r($response->getHeader(), true)), LogCollector::LevelResponse);
        LogCollector::collectorResponseLog(sprintf('响应参数=%s', $response->getContent()), LogCollector::LevelResponse);

        LogCollector::write();
    }

    protected function recordRequestLog(): void
    {
        $app = app(App::class);

        /**
         * @var Request $request
         */
        $request = app('request');


        $log = [];
        $log[] = '---------------------------------------------------------------------------------';
        $log[] = sprintf('[运行时间=%fs | 客户端IP=%s | 链接=%s | 请求方法=%s]',
            (microtime(true) - $app->getBeginTime()),
            $request->ip(),
            $request->domain() . $request->url(),
            $request->method()
        );

        $log[] = sprintf('请求头=%s', print_r($request->header(), true));
        $log[] = sprintf('主体参数=%s', print_r($request->post(), true));
        $log[] = sprintf('请求参数=%s', print_r($request->get(), true));

        foreach ($log as $value) {
            LogCollector::collectorRequestLog($value, LogCollector::LevelRequest);
        }
    }
}
