<?php
declare (strict_types=1);

namespace app\listener\electronic_invoice;

use app\common\lib\wechat\entity\answer\FapiaoInformation;
use app\common\lib\wechat\event\FapiaoCompleteEvent;
use app\common\model\ElectronicInvoiceApplyDetail;
use app\common\model\ElectronicInvoiceApplyRecord;
use app\common\model\ElectronicInvoiceRecord;

class WechatFapiaoCompleteEvent
{
    /**
     * 事件监听处理
     *
     * @param FapiaoCompleteEvent $event
     * @return bool
     */
    public function handle(FapiaoCompleteEvent $event): bool
    {

        // 查询用户数据
        $ElectronicInvoiceApplyRecord = app(ElectronicInvoiceApplyRecord::class);
        $userData = $ElectronicInvoiceApplyRecord->getUserData($event->answer->fapiao_apply_id);


        // 更新发票申请记录
        $ElectronicInvoiceApplyRecord->completeApply($event->answer->fapiao_apply_id);
        $totalAmount = $ElectronicInvoiceApplyRecord->getTotalAmount($event->answer->fapiao_apply_id);


        $ElectronicInvoiceApplyDetail = app(ElectronicInvoiceApplyDetail::class);
        $ElectronicInvoiceApplyDetail->takeEffect($event->answer->fapiao_apply_id);

        // 更新发票记录
        $ElectronicInvoiceRecord = app(ElectronicInvoiceRecord::class);
        $insertData = [];
        /**
         * @var FapiaoInformation $value
         */
        foreach ($event->answer->fapiao_information as $value) {
            $status = 0;
            if ($value->fapiao_status === 'ISSUE_ACCEPTED') {
                $status = 1;
            } else if ($value->fapiao_status === 'ISSUED') {
                $status = 2;
            }
            $card_status = 0;
            if ($value->card_status === 'INSERT_ACCEPTED') {
                $card_status = 2;
            } else if ($value->card_status === 'INSERTED') {
                $card_status = 1;
            }
            $insertData[] = [
                'id' => $value->fapiao_id,
                'user_id' => $userData['user_id'],
                'apply_record_id' => $event->answer->fapiao_apply_id,
                'total_amount' => $totalAmount,
                'status' => $status,
                'card_status' => $card_status,
                'create_time' => time(),
                'update_time' => time()
            ];
        }
        $ElectronicInvoiceRecord->addRecords($insertData);

        return true;
    }
}