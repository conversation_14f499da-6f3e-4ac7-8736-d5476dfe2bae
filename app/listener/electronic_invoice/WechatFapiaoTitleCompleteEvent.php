<?php
declare (strict_types=1);

namespace app\listener\electronic_invoice;

use app\common\lib\ExceptionLogCollector;
use app\common\lib\wechat\event\TitleCompleteEvent;
use \app\common\lib\wechat\entity\answer\UserTitleAnswer;
use app\common\model\ElectronicInvoiceApplyRecord;

class WechatFapiaoTitleCompleteEvent
{
    /**
     * 事件监听处理
     *
     * @param TitleCompleteEvent $event
     * @return bool
     */
    public function handle(TitleCompleteEvent $event): bool
    {
        $ElectronicInvoiceApplyRecord = app(ElectronicInvoiceApplyRecord::class);
        // 个人
        if ($event->userTitleAnswer->type == UserTitleAnswer::TypeIndividual) {
            $ElectronicInvoiceApplyRecord->updateTitleInfo(
                $event->titleCompleteAnswer->fapiao_apply_id,
                ElectronicInvoiceApplyRecord::TitleTypeIndividual,
                $event->userTitleAnswer->name
            );
        } else if ($event->userTitleAnswer->type === UserTitleAnswer::TypeOrganization) {
            $ElectronicInvoiceApplyRecord->updateTitleInfo(
                $event->titleCompleteAnswer->fapiao_apply_id,
                ElectronicInvoiceApplyRecord::TitleTypeOrganization,
                $event->userTitleAnswer->name,
                $event->userTitleAnswer->taxpayer_id
            );
        } else {
            ExceptionLogCollector::recordErrorLog('未知抬头类型', debug_backtrace());
        }

        return true;
    }
}