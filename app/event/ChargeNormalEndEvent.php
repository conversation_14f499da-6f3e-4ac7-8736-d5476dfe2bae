<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\event;

use app\common\lib\charging\Settlement;

class ChargeNormalEndEvent
{
    public string $orderId;
    public int $corpId;
    public int $stationId;
    public int $payMoney;
    public int $electricityTotal;
    public int $valleyElectricity;
    public int $flatElectricity;
    public int $peakElectricity;
    public int $sharpElectricity;
    public int $serviceMoney;
    public int $chargeDuration;
    public int $startTime;
    public int $endTime;

    public string $stop_charge_reason;
    public int $stop_charge_status;
    public int $user_id;

    public function __construct(array $data)
    {
        $this->orderId = $data['order_id'] ?? '';
        $this->corpId = $data['corp_id'] ?? 0;
        $this->stationId = $data['station_id'] ?? 0;
        $this->payMoney = $data['pay_money'] ?? 0;
        $this->electricityTotal = $data['electricity_total'] ?? 0;
        $this->valleyElectricity = $data['valley_electricity'] ?? 0;
        $this->flatElectricity = $data['flat_electricity'] ?? 0;
        $this->peakElectricity = $data['peak_electricity'] ?? 0;
        $this->sharpElectricity = $data['sharp_electricity'] ?? 0;
        $this->serviceMoney = $data['service_money'] ?? 0;
        $this->chargeDuration = $data['charge_duration'] ?? 0;
        $this->startTime = $data['start_time'] ?? 0;
        $this->endTime = $data['end_time'] ?? 0;
        $this->stop_charge_reason = $data['stop_charge_reason'] ?? '';
        $this->stop_charge_status = $data['stop_charge_status'] ?? Settlement::StopChargeStatusUnknown;
        $this->user_id = $data['user_id'] ?? 0;
    }
}