<?php

namespace app\event;

class ShotsStatusChangeEvent
{
    public int $corpId;
    public int $stationId;
    public int $pileId;
    public int $shotId;
    public int $beforeStatus;
    public int $afterStatus;

    public int $isPutBack;

    public function __construct(array $data)
    {
        $this->corpId = $data['corp_id'] ?? 0;
        $this->stationId = $data['station_id'] ?? 0;
        $this->pileId = $data['pile_id'] ?? 0;
        $this->shotId = $data['shot_id'] ?? 0;
        $this->beforeStatus = $data['before_status'] ?? 0;
        $this->afterStatus = $data['after_status'] ?? 0;
        $this->isPutBack = $data['is_put_back'] ?? 2;
    }
}