<?php

namespace app\event;

class ShotsCountChangeEvent
{
    public const CHANGE_TYPE_REDUCE = 1;
    public const CHANGE_TYPE_INCREASE = 2;

    public int $corpId;
    public int $stationId;
    public int $pileId;
    public int $shotId;
    public int $changeType; // 变更类型 0:减少 1:增加
    public int $shotStatus; // 充电枪增加或减少的状态 0:离线 1:故障 2:空闲 3:充电

    public function __construct(array $data)
    {
        $this->corpId = $data['corp_id'] ?? 0;
        $this->stationId = $data['station_id'] ?? 0;
        $this->pileId = $data['pile_id'] ?? 0;
        $this->shotId = $data['shot_id'] ?? 0;
        $this->changeType = $data['change_type'] ?? 0;
        $this->shotStatus = $data['shot_status'] ?? 0;
    }
}