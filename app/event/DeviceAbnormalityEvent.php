<?php

namespace app\event;

// 设备异常
class DeviceAbnormalityEvent extends BaseEvent
{

    // 异常类型
    public const AbnormalityTypeUploadRealtimeMonitoringData = 3; // 上传实时监测数据
    public const AbnormalityTypeRemoteStartChargingCommandResponse = 2; // 远程启动充电命令回复
    public const AbnormalityTypeTransactionRecord = 1; // 交易记录

    public int $abnormality_type;

    public int $corp_id;
    public int $station_id;
    public int $pile_id;
    public int $shot_id;

    public string $abnormal_cause;

    public int $start_time;


    public function __construct(array $data)
    {
        $this->analysis($data);
    }

}