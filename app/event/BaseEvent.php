<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\event;

class BaseEvent
{
    public function __construct(array $data)
    {
        $this->analysis($data);
    }

    public function encode(): string
    {
        return json_encode($this);
    }

    public function toArray(): array
    {
        return (array)$this;
    }

    public function decode(string $data): static
    {
        $data = json_decode($data, true);

        return $this->analysis($data);
    }

    protected function analysis(array $data): static
    {
        foreach ($data as $key => $value) {
            if (property_exists($this, $key) === true) {
                $this->{$key} = $value;
            }
        }

        return $this;
    }
}