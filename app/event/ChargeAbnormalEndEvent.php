<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\event;

class ChargeAbnormalEndEvent
{
    public const ABNORMAL_REASON_GET_PILE_POWER_API_FAILED = 1; // 获取充电桩开启功率: 接口调用失败
    public const ABNORMAL_REASON_NOT_ALLOW_CHARGING = 2; // 获取充电桩开启功率: 应答结果表示充电桩不允许充电
    public const ABNORMAL_REASON_GET_PILE_POWER_TIMEOUT = 3; // 获取充电桩开启功率: 应答超时
    public const ABNORMAL_REASON_SET_PILE_OUTPUT_POWER_API_FAILED = 4; // 设置充电桩输出功率: 接口调用失败
    public const ABNORMAL_REASON_SET_PILE_OUTPUT_POWER_FAILED = 5; // 设置充电桩输出功率: 应答结果表示设置失败
    public const ABNORMAL_REASON_SET_PILE_OUTPUT_POWER_TIMEOUT = 6; // 设置充电桩输出功率: 应答超时
    public const ABNORMAL_REASON_START_CHARGE_API_FAILED = 7; // 运营平台远程控制启机: 接口调用失败
    public const ABNORMAL_REASON_START_CHARGE_FAILED = 8; // 运营平台远程控制启机: 应答结果表示启机失败
    public const ABNORMAL_REASON_START_CHARGE_TIMEOUT = 9; // 运营平台远程控制启机: 应答超时
    public const ABNORMAL_REASON_UNKNOWN = 10; // 未知原因

    public int $abnormalReason;
    // 当异常原因为 ABNORMAL_REASON_START_CHARGE_FAILED 时，这里记录失败的原因。
    public int $abnormalSubReason = 0;
    public string $orderId;
    public int $corpId;
    public int $stationId;
    public int $payMoney;
    public int $electricityTotal;

    public int $chargeDuration;

    public function __construct(array $data)
    {
        $this->abnormalReason = $data['abnormal_reason'] ?? 0;
        $this->orderId = $data['order_id'] ?? '';
        $this->corpId = $data['corp_id'] ?? 0;
        $this->stationId = $data['station_id'] ?? 0;
        $this->payMoney = $data['pay_money'] ?? 0;
        $this->electricityTotal = $data['electricity_total'] ?? 0;
        $this->abnormalSubReason = $data['abnormal_sub_reason'] ?? 0;
        $this->chargeDuration = $data['charge_duration'] ?? 0;
    }
}