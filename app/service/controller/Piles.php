<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\service\controller;

use app\common\controller\ApiBase;
use app\common\lib\QrCode;
use app\common\lib\VerifyData;
use app\common\model\ShotsQrcode;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\response\Json;

#[Apidoc\Title("充电桩管理")]
class Piles extends ApiBase
{
    #[
        Apidoc\Title("获取桩二维码的PCX图片"),
        Apidoc\Author("cbj 2024.07.12 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/service/piles/get_charging_qrcode"),
        Apidoc\Param(name: "piles_id", type: "int", require: true, desc: "充电桩ID"),
        Apidoc\Param(name: "shots_count", type: "int", require: true, desc: "充电枪数量"),
        Apid<PERSON>\Returned(name: "qrcode", type: "array", require: true, desc: "二维码集合"),
    ]
    public function get_charging_qrcode(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::service_piles([
                'piles_id', 'shots_count'
            ]));

            $pcx_uris = [];


            $shotsQrcodeModel = new ShotsQrcode();

            for ($i = 1; $i <= $verify_data['shots_count']; $i++) {
                $shots_id = $verify_data['piles_id'] * 100 + $i;

                $path = $shotsQrcodeModel->getShotsQrcode($shots_id);
                if (is_null($path)) {
                    // 生成二维码
                    $path = QrCode::create_wechat_qrcode((string)$shots_id);
                    $shotsQrcodeModel->insertShotsQrcode($shots_id, $path);
                }
                $path = sprintf('%s%s', public_path(), $path);
                $pcx_dir = 'static/shots/qrcode-pcx/';
                if (!is_dir(public_path() . $pcx_dir)) {
                    mkdir(public_path() . $pcx_dir, 0777, true);
                }
                $pcx_uri = sprintf('%s%s.pcx', $pcx_dir, $shots_id);
                $pcx_path = sprintf("%s%s", public_path(), $pcx_uri);
                if (!is_file($pcx_path)) {
                    // 生成 PCX 格式的二维码
                    exec(sprintf("convert %s -negate -colors 2 -units PixelsPerInch -density 203 -depth 1 %s", $path, $pcx_path));
                }
                $pcx_uris[] = config('my.host') . $pcx_uri;
            }

            return [
                'qrcode' => $pcx_uris
            ];
        });
    }
}