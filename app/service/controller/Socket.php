<?php
///** @noinspection PhpDynamicAsStaticMethodCallInspection */
//
///** @noinspection PhpUnused */
//
//namespace app\service\controller;
//
//use app\common\cache\redis\ShotsStatusCache;
//use app\common\controller\ApiBase;
//use app\common\lib\charging\RemoteAccountBalanceUpdateAnswer;
//use app\common\lib\charging\ReportCurrentBatteryLevel;
//use app\common\lib\charging\request\RemoteAccountBalanceUpdateAnswerRequest;
//use app\common\lib\charging\request\ReportCurrentBatteryLevelRequest;
//use app\common\lib\charging\request\StartChargeAnswerRequest;
//use app\common\lib\charging\request\StopChargeAnswerRequest;
//use app\common\lib\charging\request\TransactionRecordRequest;
//use app\common\lib\charging\request\UploadRealtimeMonitoringDataRequest;
//use app\common\lib\charging\Settlement;
//use app\common\lib\charging\StartChargeAnswer;
//use app\common\lib\charging\StopChargeAnswer;
//use app\common\lib\charging\UploadRealtimeMonitoringData;
//use app\common\lib\ExceptionLogCollector;
//use app\common\lib\SendAdmin;
//use app\common\log\LogCollector;
//use app\common\model\Piles as PilesModel;
//use app\common\model\Shots;
//use app\common\traits\Curd;
//use app\event\ShotsStatusChangeEvent;
//use hg\apidoc\annotation as Apidoc;
//use RuntimeException;
//use think\facade\Db;
//use think\response\Json;
//use Throwable;
//
//#[Apidoc\Title("服务/充电桩Socket")]
//class Socket extends ApiBase
//{
//    use Curd;
//
//    public function initialize(): void
//    {
//        $this->modelClass = new PilesModel();
//    }
//
//    public function piles_heartbeat(): Json
//    {
//        $data = $this->request->post();
//
//        return $this->openExceptionCatch(function () use ($data) {
//
//            // {"msg_type":"heartbeat","piles_id":"20230105000380","shots":[{"shots_id":"01","shots_status":0}],"sequence_number":"c152",
//            //"hex":"680dc15200032023010500038001000206","res_time":1702656023,"client_id":"7f0000010b54000004f9"}
////            $ShotsStatusChangeList = [];
//
//            $pilesData = (new PilesModel())->getPilesData($data['piles_id']);
//            if (!$pilesData) {
//                throw new RuntimeException('找不到充电桩不允许登录');
//            }
////
////
////            Db::name('piles')
////                ->where('id', $data['piles_id'])
////                ->update([
////                    'status' => PilesModel::StatusOnline
////                ]);
//
////            $ShotsStatusCache = app(ShotsStatusCache::class);
//
//            foreach ($data['shots'] as $shot) {
//                $shots_id = $data['piles_id'] . $shot['shots_id'];
////                $beforeStatus = $ShotsStatusCache->getStatus($shots_id);
//
//                if ($shot['shots_status'] === 0) {
//                    (new Shots())->updateShotIsOnline($shots_id, Shots::IsOnlineYes);
//                }
////                $ShotsStatusCache->setStatus($shots_id, $shot['shots_status']);
//
//                // TODO: 后续修复
////                $ShotsStatusChangeList[] = new ShotsStatusChangeEvent([
////                    'corp_id' => $pilesData['corp_id'],
////                    'station_id' => $pilesData['station_id'],
////                    'pile_id' => $data['piles_id'],
////                    'shot_id' => $shots_id,
////                    'before_status' => $beforeStatus,
////                    'after_status' => Shots::StatusIdle
////                ]);
//            }
//
////            foreach ($ShotsStatusChangeList as $event) {
////                try {
////                    event('ShotsStatusChange', $event);
////                } catch (Throwable $e) {
////                    // 在事件中运行的业务都是次要业务，若是出现异常情况，
////                    // 只记录日志，不做其他处理，避免影响充电主流程。
////                    ExceptionLogCollector::collect($e);
////                }
////            }
//
//
//            return [];
//        });
//    }
//
//    #[
//        Apidoc\Title("充电桩下线"),
//        Apidoc\Author("cbj 2023-11-16 新增"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/service/Socket/piles_offline"),
//        Apidoc\Param(name: "data", type: "array", desc: "数据", children: [
//            ['name' => 'piles_id', 'type' => 'string', 'desc' => '充电桩ID'],
//        ]),
//    ]
//    public function piles_offline(): Json
//    {
//        $data = $this->request->post();
//        trace('充电桩上线通知=》' . json_encode_cn($data), '充电桩Socket');
//
//        return $this->openExceptionCatch(function () use ($data) {
//
//            $ShotsStatusChangeList = [];
//
//            $pilesData = (new PilesModel())->getPilesData($data['piles_id']);
//            (new PilesModel())->updateStatus($data['piles_id'], PilesModel::StatusOffline);
//
//            $shotIds = app(Shots::class)->getPilesShotIds(
//                $data['piles_id']
//            );
//
//            $ShotsModel = app(Shots::class);
//            foreach ($shotIds as $shotId) {
//                $beforeStatus = $ShotsModel->getIsOnline($shotId);
//
//                if ($beforeStatus !== Shots::IsOnlineNot) {
//                    $ShotsModel->updateIsOnline($shotId, Shots::IsOnlineNot);
//
//                    $ShotsStatusChangeList[] = new ShotsStatusChangeEvent([
//                        'corp_id' => $pilesData['corp_id'],
//                        'station_id' => $pilesData['station_id'],
//                        'pile_id' => $data['piles_id'],
//                        'shot_id' => $shotId,
//                        'before_status' => $beforeStatus,
//                        'after_status' => Shots::IsOnlineNot
//                    ]);
//                }
//            }
//
//            foreach ($ShotsStatusChangeList as $event) {
//                try {
//                    event('ShotsStatusChange', $event);
//                } catch (Throwable $e) {
//                    // 在事件中运行的业务都是次要业务，若是出现异常情况，
//                    // 只记录日志，不做其他处理，避免影响充电主流程。
//                    ExceptionLogCollector::collect($e);
//                }
//            }
//
//            return [];
//
//        }, true, sprintf('lock:charge_socket_login_or_logout_%s', $data['piles_id']));
//    }
//
//    #[
//        Apidoc\Title("充电桩登录"),
//        Apidoc\Author("cbj 2023-11-15 新增"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/service/Socket/piles_login_auth"),
//        Apidoc\Param(name: "data", type: "array", desc: "数据", children: [
//            ['name' => 'msg_type', 'type' => 'string', 'desc' => '消息类型'],
//            ['name' => 'piles_id', 'type' => 'string', 'desc' => '充电桩ID'],
//            ['name' => 'piles_type', 'type' => 'int', 'desc' => '充电桩类型 0:表示直流桩 1:表示交流桩'],
//            ['name' => 'shot_num', 'type' => 'int', 'desc' => '充电枪数量'],
//            ['name' => 'comm_ver', 'type' => 'int', 'desc' => '通信协议版本 版本号乘 10，v1.0 表示 0x0A'],
//            ['name' => 'version', 'type' => 'string', 'desc' => '程序版本'],
//            ['name' => 'network_type', 'type' => 'int', 'desc' => '网络链接类型 0:SIM卡 1:LAN 2:WAN 3:其他'],
//            ['name' => 'network_type_text', 'type' => 'string', 'desc' => '网络链接类型文本描述'],
//            ['name' => 'sim', 'type' => 'string', 'desc' => 'Sim卡'],
//            ['name' => 'comm_corp', 'type' => 'int', 'desc' => '运营商 0:移动 2:电信 3:联通 4:其他'],
//            ['name' => 'comm_corp_text', 'type' => 'string', 'desc' => '运营商文字描述'],
//            ['name' => 'sequence_number', 'type' => 'string', 'desc' => '序列号'],
//            ['name' => 'hex', 'type' => 'string', 'desc' => '协议包'],
//            ['name' => 'res_time', 'type' => 'int', 'desc' => '请求时间'],
//            ['name' => 'client_id', 'type' => 'string', 'desc' => '客户端ID'],
//        ]),
//    ]
//    public function piles_login_auth(): Json
//    {
//        $data = $this->request->post();
//        trace('充电桩上线通知=》' . json_encode_cn($data), '充电桩Socket');
//
//        return $this->openExceptionCatch(function () use ($data) {
//            $ShotsStatusChangeList = [];
//
//            $pilesData = (new PilesModel())->getPilesData($data['piles_id']);
//            if (!$pilesData) {
//                return res_error([], '找不到充电桩不允许登录');
//            }
//
//
//            Db::name('piles')
//                ->where('id', $data['piles_id'])
//                ->update([
//                    'shot_num' => $data['shot_num'],
//                    'comm_ver' => $data['comm_ver'],
//                    'version' => $data['version'],
//                    'comm_type' => $data['network_type'],
//                    'comm_corp' => $data['comm_corp'],
//                    'simid' => $data['sim'],
//                    'status' => PilesModel::StatusOnline
//                ]);
//
//
//            $shotIds = app(Shots::class)->getPilesShotIds(
//                $data['piles_id']
//            );
//
//            LogCollector::collectorRunLog('$shotIds = ' . json_encode_cn($shotIds));
//
//            $ShotsModel = app(Shots::class);
//
//            foreach ($shotIds as $shotId) {
//                $beforeStatus = $ShotsModel->getIsOnline($shotId);
//
//                LogCollector::collectorRunLog($shotId . ' - $beforeStatus = ' . $beforeStatus);
//
//                if ($beforeStatus !== Shots::IsOnlineYes) {
//                    $ShotsModel->updateIsOnline($shotId, Shots::IsOnlineYes);
//
//                    $ShotsStatusChangeList[] = new ShotsStatusChangeEvent([
//                        'corp_id' => $pilesData['corp_id'],
//                        'station_id' => $pilesData['station_id'],
//                        'pile_id' => $data['piles_id'],
//                        'shot_id' => $shotId,
//                        'before_status' => $beforeStatus,
//                        'after_status' => Shots::IsOnlineYes
//                    ]);
//                }
//            }
//
//            foreach ($ShotsStatusChangeList as $event) {
//                try {
//                    event('ShotsStatusChange', $event);
//                } catch (Throwable $e) {
//                    // 在事件中运行的业务都是次要业务，若是出现异常情况，
//                    // 只记录日志，不做其他处理，避免影响充电主流程。
//                    ExceptionLogCollector::collect($e);
//                }
//            }
//
//            return [
//                'shots_list' => $shotIds
//            ];
//        }, true, sprintf('lock:charge_socket_login_or_logout_%s', $data['piles_id']));
//    }
//
//    #[
//        Apidoc\Title("下发费率回复 0x57"),
//        Apidoc\Author("lwj 2023.8.17 新增，lwj 2023.8.21 修改, cbj 2023.11.8 修改"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/service/Socket/issue_tariff_group_response"),
//        Apidoc\Param(name: "request_id", type: "int", require: true, desc: "请求ID"),
//        Apidoc\Param(name: "data", type: "array", desc: "数据", children: [
//            ['name' => 'msg_type', 'type' => 'string', 'desc' => '消息类型'],
//            ['name' => 'transaction_serial_number', 'type' => 'string', 'desc' => '订单流水号'],
//            ['name' => 'piles_id', 'type' => 'int', 'desc' => '充电桩编码'],
//            ['name' => 'shots_id', 'type' => 'int', 'desc' => '充电枪编码(不是充电枪ID)'],
//            ['name' => 'start_result', 'type' => 'int', 'desc' => '启动结果 0:失败 1:成功'],
//            ['name' => 'failure_reason', 'type' => 'int', 'desc' => '失败原因'],
//            ['name' => 'failure_reason_text', 'type' => 'string', 'desc' => '失败原因文本'],
//        ]),
//    ]
//    public function issue_tariff_group_response(): Json
//    {
//        try {
//            $data = $this->request->post();
//            trace('下发费率回复=》' . json_encode_cn($data), '充电桩Socket');
//            $user_data = cache('充电桩服务_' . $data['request_id']);
//            SendAdmin::send_group($user_data['token'], [
//                'type' => 'issue_tariff_group_response',
//                'msg' => '下发费率回复',
//                'data' => $data['data']
//            ]);
//            return $this->res_success();
//        } catch (Throwable $e) {
//            ExceptionLogCollector::collect($e);
//            trace('下发费率回复:处理异常=》' . $e->getMessage(), '充电桩Socket');
//            return $this->res_error([], '处理异常：' . $e->getMessage());
//        }
//    }
//
//    #[
//        Apidoc\Title("远程启动充电命令回复"),
//        Apidoc\Author("lwj 2023.8.22 新增，lwj 2023.9.27 修改"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/service/Socket/remote_start_charging_command_response"),
//        Apidoc\Param(name: "request_id", type: "int", require: true, desc: "请求ID"),
//        Apidoc\Param(name: "data", type: "array", desc: "数据", children: [
//            ['name' => 'msg_type', 'type' => 'string', 'desc' => '消息类型'],
//            ['name' => 'piles_id', 'type' => 'int', 'desc' => '充电桩编码'],
//            ['name' => 'shots_id', 'type' => 'int', 'desc' => '充电枪编码(不是充电枪ID)'],
//            ['name' => 'start_result', 'type' => 'int', 'desc' => '停止结果 0:失败 1:成功'],
//            ['name' => 'failure_reason', 'type' => 'int', 'desc' => '失败原因'],
//            ['name' => 'failure_reason_text', 'type' => 'string', 'desc' => '失败原因文本'],
//        ]),
//    ]
//    public function remote_start_charging_command_response(): Json
//    {
//        try {
//            $data = $this->request->post();
//            trace('远程启动充电命令回复=》' . json_encode_cn($data), '充电桩Socket');
//
//            $requestParams = array_merge($data['data'], [
//                'request_id' => $data['request_id']
//            ]);
//            $request = new StartChargeAnswerRequest($requestParams);
//
//            (new StartChargeAnswer($request))->handler();
//
//            return $this->res_success();
//        } catch (Throwable $e) {
//            ExceptionLogCollector::collect($e);
//            trace('远程启动充电命令回复:处理异常=》' . $e->getMessage(), '充电桩Socket');
//            return $this->res_error([], '处理异常：' . $e->getMessage());
//        }
//    }
//
//    #[
//        Apidoc\Title("远程停机命令回复 0x35"),
//        Apidoc\Author("lwj 2023.8.22 新增，lwj 2023.9.15 修改"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/service/Socket/remote_shutdown_command_response"),
//        Apidoc\Param(name: "request_id", type: "int", require: true, desc: "请求ID"),
//        Apidoc\Param(name: "data", type: "array", desc: "数据", children: [
//            ['name' => 'msg_type', 'type' => 'string', 'desc' => '消息类型'],
//            ['name' => 'piles_id', 'type' => 'int', 'desc' => '充电桩编码'],
//            ['name' => 'shots_id', 'type' => 'string', 'desc' => '充电枪编码(不是充电枪ID)'],
//            ['name' => 'stop_result', 'type' => 'int', 'desc' => '停止结果 0:失败 1:成功'],
//            ['name' => 'failure_reason', 'type' => 'int', 'desc' => '失败原因'],
//            ['name' => 'failure_reason_text', 'type' => 'string', 'desc' => '失败原因文本'],
//        ]),
//    ]
//    public function remote_shutdown_command_response(): Json
//    {
//        try {
//            $data = $this->request->post();
//            trace('远程停机命令回复=》' . json_encode_cn($data), '充电桩Socket');
//
//            $request = new StopChargeAnswerRequest(array_merge($data['data'], [
//                'request_id' => $data['request_id']
//            ]));
//
//            Db::startTrans();
//            (new StopChargeAnswer($request))->handler();
//            Db::commit();
//            return $this->res_success();
//        } catch (Throwable $e) {
//            Db::rollback();
//            ExceptionLogCollector::collect($e);
//            trace('远程启动充电命令回复:处理异常=》' . $e->getMessage(), '充电桩Socket');
//            return $this->res_error([], '处理异常：' . $e->getMessage());
//        }
//    }
//
//    #[
//        Apidoc\Title("交易记录 0x3B"),
//        Apidoc\Author("lwj 2023.8.22 新增，lwj 2023.10.16 修改, cbj 2023.11.8 修改"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/service/Socket/transaction_record_confirmation"),
//        Apidoc\Param(name: "request_id", type: "int", require: true, desc: "请求ID"),
//        Apidoc\Param(name: "data", type: "array", desc: "数据", children: [
//            ['name' => 'msg_type', 'type' => 'string', 'desc' => '消息类型'],
//            ['name' => 'transaction_serial_number', 'type' => 'string', 'desc' => '交易流水号'],
//            ['name' => 'piles_id', 'type' => 'int', 'desc' => '充电桩编码'],
//            ['name' => 'shots_id', 'type' => 'int', 'desc' => '充电枪编码(不是充电枪ID)'],
//            ['name' => 'start_time', 'type' => 'string', 'desc' => '开始时间'],
//            ['name' => 'end_time', 'type' => 'string', 'desc' => '结束时间'],
//            ['name' => 'sharp_price', 'type' => 'int', 'desc' => '尖单价'],
//            ['name' => 'sharp_charge', 'type' => 'int', 'desc' => '尖电量'],
//            ['name' => 'loss_sharp_charge', 'type' => 'int', 'desc' => '计损尖电量'],
//            ['name' => 'sharp_amount', 'type' => 'int', 'desc' => '尖金额'],
//            ['name' => 'peak_price', 'type' => 'int', 'desc' => '峰单价'],
//            ['name' => 'peak_charge', 'type' => 'int', 'desc' => '峰电量'],
//            ['name' => 'loss_peak_charge', 'type' => 'int', 'desc' => '计损峰电量'],
//            ['name' => 'peak_amount', 'type' => 'int', 'desc' => '峰金额'],
//            ['name' => 'flat_price', 'type' => 'int', 'desc' => '平单价'],
//            ['name' => 'flat_charge', 'type' => 'int', 'desc' => '平电量'],
//            ['name' => 'loss_flat_charge', 'type' => 'int', 'desc' => '计损平电量'],
//            ['name' => 'flat_amount', 'type' => 'int', 'desc' => '平金额'],
//            ['name' => 'valley_price', 'type' => 'int', 'desc' => '谷单价'],
//            ['name' => 'valley_charge', 'type' => 'int', 'desc' => '谷电量'],
//            ['name' => 'loss_valley_charge', 'type' => 'int', 'desc' => '计损谷电量'],
//            ['name' => 'valley_amount', 'type' => 'int', 'desc' => '谷金额'],
//            ['name' => 'electricity_meter_total_start', 'type' => 'int', 'desc' => '电表总起值'],
//            ['name' => 'electricity_meter_total_end', 'type' => 'int', 'desc' => '电表总止值'],
//            ['name' => 'total_charge', 'type' => 'int', 'desc' => '总电量'],
//            ['name' => 'loss_total_charge', 'type' => 'int', 'desc' => '计损总电量'],
//            ['name' => 'consumption_amount', 'type' => 'int', 'desc' => '消费金额'],
//            ['name' => 'unique_identifier_of_electric_vehicle', 'type' => 'int', 'desc' => '电动汽车唯一标识'],
//            ['name' => 'transaction_identifier', 'type' => 'int', 'desc' => '交易标识'],
//            ['name' => 'transaction_identifier_text', 'type' => 'int', 'desc' => '交易标识文本'],
//            ['name' => 'transaction_datetime', 'type' => 'string', 'desc' => '交易日期、时间'],
//            ['name' => 'reason_for_stop', 'type' => 'int', 'desc' => '停止原因'],
//            ['name' => 'reason_for_stop_text', 'type' => 'string', 'desc' => '停止原因文本'],
//            ['name' => 'physical_card_number', 'type' => 'string', 'desc' => '物理卡号'],
//        ]),
//    ]
//    public function transaction_record_confirmation(): Json
//    {
//        try {
//            $data = $this->request->post();
//            trace('交易记录确认=》' . json_encode_cn($data), '充电桩Socket');
//
//            $request = new TransactionRecordRequest($data);
//
//            $Settlement = new Settlement($request);
//            $Settlement->run();
//
//            return $this->res_success();
//        } catch (Throwable $e) {
//            if ($e->getCode() === Settlement::ReturnCodeBusinessAbnormal) {
//                return $this->res_error([], $e->getMessage());
//            }
//            ExceptionLogCollector::collect($e);
//            trace('交易记录确认:处理异常=》' . $e->getMessage(), '充电桩Socket');
//            return $this->res_error([], '处理异常：' . $e->getMessage());
//        }
//    }
//
//    #[
//        Apidoc\Title("上传实时监测数据 0x13"),
//        Apidoc\Author("lwj 2023.8.23 新增,lwj 2023.10.16 修改"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/service/Socket/upload_realtime_monitoring_data"),
//        Apidoc\Param(name: "request_id", type: "int", require: true, desc: "请求ID"),
//        Apidoc\Param(name: "order_id", type: "string", require: true, desc: "订单ID"),
//        Apidoc\Param(name: "data", type: "array", desc: "数据", children: [
//            ['name' => 'msg_type', 'type' => 'string', 'desc' => '消息类型'],
//            ['name' => 'transaction_serial_number', 'type' => 'string', 'desc' => '交易流水号'],
//            ['name' => 'piles_id', 'type' => 'int', 'desc' => '充电桩编码'],
//            ['name' => 'shots_id', 'type' => 'int', 'desc' => '充电枪编码(不是充电枪ID)'],
//            ['name' => 'status', 'type' => 'int', 'desc' => '状态'],
//            ['name' => 'is_reset', 'type' => 'int', 'desc' => '是否归位'],
//            ['name' => 'is_plugged_in', 'type' => 'int', 'desc' => '是否插枪'],
//            ['name' => 'output_voltage', 'type' => 'int', 'desc' => '输出电压'],
//            ['name' => 'output_current', 'type' => 'int', 'desc' => '输出电流'],
//            ['name' => 'gun_wire_temperature', 'type' => 'int', 'desc' => '枪线温度'],
//            ['name' => 'gun_wire_code', 'type' => 'int', 'desc' => '枪线编码'],
//            ['name' => 'soc', 'type' => 'int', 'desc' => 'SOC'],
//            ['name' => 'maximum_battery_temperature', 'type' => 'int', 'desc' => '电池组最高温度'],
//            ['name' => 'cumulative_charging_time', 'type' => 'int', 'desc' => '累计充电时间'],
//            ['name' => 'remaining_time', 'type' => 'int', 'desc' => '剩余时间'],
//            ['name' => 'charging_percentage', 'type' => 'int', 'desc' => '充电度数'],
//            ['name' => 'calculated_loss_charging_percentage', 'type' => 'int', 'desc' => '计损充电度数'],
//            ['name' => 'amount_charged', 'type' => 'int', 'desc' => '已充金额'],
//            ['name' => 'hardware_failure', 'type' => 'int', 'desc' => '硬件故障'],
//            ['name' => 'hardware_failure_arr', 'type' => 'string', 'desc' => '硬件故障文本'],
//        ]),
//    ]
//    public function upload_realtime_monitoring_data(): Json
//    {
//        try {
//            Db::startTrans();
//            $data = $this->request->post();
//
//            $request = new UploadRealtimeMonitoringDataRequest($data);
////            $package = Kernel::create(BasePackage::TypeUploadRealtimeMonitoringData, $data);
////
////            /**
////             * @var UploadRealtimeMonitoringDataPackage $uploadRealtimeMonitoringDataRequest
////             */
////            $uploadRealtimeMonitoringDataRequest = $package->getBody();
//
//            $result = (new UploadRealtimeMonitoringData($request))->run();
//            Db::commit();
//            return $this->res_success($result);
//        } catch (Throwable $e) {
//            Db::rollback();
//            if ($e->getCode() === UploadRealtimeMonitoringData::ReturnCodeBusinessAbnormal) {
//                return $this->res_error([], $e->getMessage());
//            }
//            ExceptionLogCollector::collect($e);
//            trace('上传实时监测数据:处理异常=》' . $e->getMessage(), '充电桩Socket');
//            return $this->res_error([], '处理异常：' . $e->getMessage());
//        }
//    }
//
//    #[
//        Apidoc\Title("集中控制器充电桩开启功率应答 0xDA"),
//        Apidoc\Author("lwj 2023.9.23 新增，lwj 2023.9.29 修改"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/service/Socket/charging_pile_power_on_response"),
//        Apidoc\Param(name: "request_id", type: "int", require: true, desc: "请求ID"),
//        Apidoc\Param(name: "data", type: "array", desc: "数据", children: [
//            ['name' => 'msg_type', 'type' => 'string', 'desc' => '消息类型'],
//            ['name' => 'centralized_controller_id', 'type' => 'string', 'desc' => '集中控制器ID'],
//            ['name' => 'transaction_serial_number', 'type' => 'string', 'desc' => '交易流水号'],
//            ['name' => 'shots_ids', 'type' => 'int', 'desc' => '充电枪ID'],
//            ['name' => 'power_output', 'type' => 'int', 'desc' => '输出功率'],
//            ['name' => 'sequence_number', 'type' => 'string', 'desc' => '序列号'],
//            ['name' => 'hex', 'type' => 'string', 'desc' => '报文'],
//        ]),
//    ]
//    public function charging_pile_power_on_response(): Json
//    {
//        try {
//            $data = $this->request->post();
//            trace('集中控制器充电桩开启功率应答=》' . json_encode_cn($data), '充电桩Socket');
//            $user_data = cache('集中控制器服务_' . $data['request_id']);
//            $order_id = $data['data']['transaction_serial_number'];
//            cache('集中控制器充电桩开启功率应答-' . $order_id, $data['data'], 300);
//
////            send_start_charging_timer([
////                'type'=>'删除-接收集中控制器',
////                'order_id'=>$order_id,
////            ]);
//
//            send_start_charging_job([
//                'msg_type' => '集中控制器充电桩开启功率应答',
//                'order_id' => $order_id,
//                'user_data' => $user_data,
//                'data' => $data['data'],
//            ]);
//
//            return $this->res_success();
//        } catch (Throwable $e) {
//            ExceptionLogCollector::collect($e);
//            trace('集中控制器充电桩开启功率应答:处理异常=》' . $e->getMessage(), '充电桩Socket');
//            return $this->res_error([], '处理异常：' . $e->getMessage());
//        }
//    }
//
//    #[
//        Apidoc\Title("充电桩工作参数设置应答 0x51"),
//        Apidoc\Author("lwj 2023.9.23 新增"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/service/Socket/charging_station_parameter_setting_response"),
//        Apidoc\Param(name: "request_id", type: "int", require: true, desc: "请求ID"),
//        Apidoc\Param(name: "data", type: "array", desc: "数据", children: [
//            ['name' => 'msg_type', 'type' => 'string', 'desc' => '消息类型'],
//            ['name' => 'piles_id', 'type' => 'int', 'desc' => '充电桩ID'],
//            ['name' => 'setting_result', 'type' => 'int', 'desc' => '设置结果'],
//            ['name' => 'setting_result_text', 'type' => 'string', 'desc' => '设置结果文本'],
//            ['name' => 'sequence_number', 'type' => 'string', 'desc' => '序列号'],
//            ['name' => 'hex', 'type' => 'string', 'desc' => '报文'],
//            ['name' => 'res_time', 'type' => 'int', 'desc' => ''],
//            ['name' => 'client_id', 'type' => 'string', 'desc' => '客户端ID'],
//        ]),
//    ]
//    public function charging_station_parameter_setting_response(): Json
//    {
//        try {
//            $data = $this->request->post();
//            trace('充电桩工作参数设置应答=》' . json_encode_cn($data), '充电桩Socket');
//            $user_data = cache('充电桩服务_' . $data['request_id']);
//
////            send_start_charging_timer([
////                'type'=>'删除-接收下发功率',
////                'request_id'=>$data['request_id'],
////            ]);
//
//            send_start_charging_job([
//                'msg_type' => '充电桩工作参数设置应答',
//                'order_id' => $user_data['order_id'],
//                'user_data' => $user_data,
//                'data' => $data['data'],
//                'request_id' => $data['request_id'],
//            ]);
//
//            cache('充电桩工作参数设置应答-' . $data['request_id'], $data['data'], 300);
//            return $this->res_success();
//        } catch (Throwable $e) {
//            ExceptionLogCollector::collect($e);
//            trace('充电桩工作参数设置应答:处理异常=》' . $e->getMessage(), '充电桩Socket');
//            return $this->res_error([], '处理异常：' . $e->getMessage());
//        }
//    }
//
//    #[
//        Apidoc\Title("余额更新应答 0x41"),
//        Apidoc\Author("cbj 2023.11.23 新增"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/service/socket/remote_update_response"),
//        Apidoc\Param(name: "data", type: "array", desc: "数据", children: [
//            ['name' => 'msg_type', 'type' => 'string', 'desc' => '消息类型'],
//            ['name' => 'piles_id', 'type' => 'string', 'desc' => '充电桩ID'],
//            ['name' => 'physical_card_number', 'type' => 'string', 'desc' => '物理卡号'],
//            ['name' => 'edit_result', 'type' => 'int', 'desc' => '修改结果 0:修改成功 1:设备编号错误 2:卡号错误'],
//            ['name' => 'edit_result_text', 'type' => 'string', 'desc' => '修改结果描述'],
//            ['name' => 'hex', 'type' => 'string', 'desc' => '报文'],
//            ['name' => 'res_time', 'type' => 'int', 'desc' => '应答时间'],
//            ['name' => 'client_id', 'type' => 'string', 'desc' => '客户端ID'],
//        ]),
//    ]
//    public function remote_update_response(): Json
//    {
//        try {
//            $request = new RemoteAccountBalanceUpdateAnswerRequest($this->request->post());
//
//            (new RemoteAccountBalanceUpdateAnswer($request))->handler();
//
//            return $this->res_success();
//        } catch (Throwable $e) {
//            ExceptionLogCollector::collect($e);
//            return $this->res_error();
//        }
//    }
//
//
//    #[
//        Apidoc\Title("上报当前电量 0xD4"),
//        Apidoc\Author("cbj 2024.01.04 新增"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/service/socket/report_current_battery_level_handle"),
//        Apidoc\Param(name: "centralized_controller_id", type: "string", desc: "能源路由器ID"),
//        Apidoc\Param(name: "a_config_power", type: "int", default: 0, desc: "A相配置功率"),
//        Apidoc\Param(name: "b_config_power", type: "int", default: 0, desc: "B相配置功率"),
//        Apidoc\Param(name: "c_config_power", type: "int", default: 0, desc: "C相配置功率"),
//        Apidoc\Param(name: "total_config_power", type: "int", default: 0, desc: "总配置功率"),
//        Apidoc\Param(name: "a_active_power", type: "int", default: 0, desc: "A相有功功率"),
//        Apidoc\Param(name: "b_active_power", type: "int", default: 0, desc: "B相有功功率"),
//        Apidoc\Param(name: "c_active_power", type: "int", default: 0, desc: "C相有功功率"),
//        Apidoc\Param(name: "total_active_power", type: "int", default: 0, desc: "总有功功率"),
//        Apidoc\Param(name: "record_time", type: "int", desc: "记录时间(时间戳)"),
//    ]
//    public function report_current_battery_level_handle(): Json
//    {
//        try {
//            $request = new ReportCurrentBatteryLevelRequest($this->request->post());
//
//            (new ReportCurrentBatteryLevel($request))->run();
//
//            return $this->res_success();
//        } catch (Throwable $e) {
//            ExceptionLogCollector::collect($e);
//            return $this->res_error();
//        }
//    }
//}