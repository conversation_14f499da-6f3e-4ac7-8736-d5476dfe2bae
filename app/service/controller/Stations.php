<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\service\controller;

use app\common\controller\ApiBase;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\StationExtraInfo;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\response\Json;
use app\common\model\StationsCorpClearing as StationsCorpClearingModel;

#[Apidoc\Title("运营商")]
class Stations extends ApiBase
{
    #[
        Apidoc\Title("添加场站"),
        Apidoc\Author("cbj 2024.04.24 新增,lwj 2024.5.11 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/service/stations/add"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "场站ID"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "场站名称"),
        Apid<PERSON>\Param(name: "corp_id", type: "int", require: true, desc: "运营商ID"),
        Apidoc\Param(name: "city_id", type: "array", require: true, desc: "地址ID"),
        Apidoc\Param(name: "address", type: "string", require: true, desc: "场站地址"),
        Apidoc\Param(name: "lonlat", type: "string", require: true, desc: "场站经纬度(格式: 经度,纬度)"),
        Apidoc\Param(name: "type", type: "int", require: true, desc: "场站类型 1:公共 2:自营"),
    ]
    public function add(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::service_stations([
                'id', 'corp_id', 'name', 'address', 'city_id', 'lonlat', 'type'
            ]));

            $city = get_city_for_city_id($verify_data['city_id']);
            if (!$city) throw new RuntimeException("获取省市区失败", [], RuntimeException::CodeBusinessException);


            $all_address = $city['province'] . $city['city'] . $city['district'] . $verify_data['address'];

            $inserts[] = [
                'id' => $verify_data['id'],
                'name' => $verify_data['name'],
                'corp_id' => $verify_data['corp_id'],
                'all_address' => $all_address,
                'address' => $verify_data['address'],
                'province' => $city['province'],
                'city' => $city['city'],
                'district' => $city['district'],
                'city_id' => $verify_data['city_id'],
                'type' => $verify_data['type'],
                'lonlat' => $verify_data['lonlat'],
                'pile_num' => 0,
                'create_time' => date('Y-m-d H:i:s')
            ];
            $stations_extra_info_ids[] = $verify_data['id'];
            (new \app\common\model\Stations())->insertAll($inserts);
            (new StationExtraInfo())->insertDefaultDataAll($stations_extra_info_ids);
            (new StationsCorpClearingModel())->insert([
                'station_id'              => $verify_data['id'],
                'corp_id'                 => $verify_data['corp_id'],
                'ratio_electricity_price' => 100,
                'ratio_ser_price'         => 100,
            ]);
            return [];

        }, true);
    }

    #[
        Apidoc\Title("更新场站"),
        Apidoc\Author("cbj 2024.04.24 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/service/stations/update"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "场站ID"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "场站名称"),
        Apidoc\Param(name: "address", type: "string", require: true, desc: "场站地址"),
        Apidoc\Param(name: "city_id", type: "array", require: true, desc: "地址ID"),
        Apidoc\Param(name: "lonlat", type: "string", require: true, desc: "场站经纬度(格式: 经度,纬度)"),
        Apidoc\Param(name: "type", type: "int", require: true, desc: "场站类型 1:公共 2:自营"),
    ]
    public function update(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::service_stations([
                'id', 'name', 'address', 'city_id', 'lonlat', 'type'
            ]));

            $city = get_city_for_city_id($verify_data['city_id']);
            if (!$city) throw new RuntimeException("获取省市区失败", [], RuntimeException::CodeBusinessException);

            $all_address = $city['province'] . $city['city'] . $city['district'] . $verify_data['address'];

            $stationsModel = new \app\common\model\Stations();
            $stationsModel->where('id', '=', $verify_data['id'])->update([
                'name' => $verify_data['name'],
                'all_address' => $all_address,
                'address' => $verify_data['address'],
                'province' => $city['province'],
                'city' => $city['city'],
                'district' => $city['district'],
                'city_id' => $verify_data['city_id'],
                'type' => $verify_data['type'],
                'lonlat' => $verify_data['lonlat'],
            ]);

            return [];

        }, true);
    }

    #[
        Apidoc\Title("删除场站"),
        Apidoc\Author("cbj 2024.04.24 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/service/stations/delete"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "场站ID"),
    ]
    public function delete(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::service_stations([
                'id'
            ]));

            (new \app\common\model\Stations())->where('id', $verify_data['id'])->delete();

            return [];

        }, true);
    }
}