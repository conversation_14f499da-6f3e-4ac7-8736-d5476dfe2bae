<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\service\controller;

use app\common\controller\ApiBase;
use hg\apidoc\annotation as Apidoc;
use think\response\Json;
use app\common\model\AdminAuthGroup as AdminAuthGroupModel;

#[Apidoc\Title("管理员分组")]
class AdminAuthGroup extends ApiBase
{
    #[
        Apidoc\Title("管理员分组选项"),
        Apidoc\Author("cbj 2024.04.24 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/service/admin_auth_group/options"),
        Apidoc\Returned(name: "options", type: "array", require: true, desc: "选项集合", children: [
            ['name' => 'id', 'type' => 'int', 'require' => true, 'desc' => '管理员分组ID'],
            ['name' => 'group_name', 'type' => 'string', 'require' => true, 'desc' => '管理员分组名称'],
        ]),
    ]
    public function options(): Json
    {
        return $this->openExceptionCatch(function () {

            return ['options' => (new AdminAuthGroupModel())->getOptionsData()];

        });
    }
}