<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\service\controller;

use app\common\controller\ApiBase;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\log\LogCollector;
use app\common\model\CorpAdminGroup;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\response\Json;
use app\common\model\AdminUsers as AdminUsersModel;
use app\common\model\Corp as CorpModel;

#[Apidoc\Title("运营商")]
class Corp extends ApiBase
{
    #[
        Apidoc\Title("添加运营商"),
        Apidoc\Author("cbj 2024.04.24 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/service/corp/add"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "运营商ID"),
        Apid<PERSON>\Param(name: "name", type: "string", require: true, desc: "运营商名称"),
        Apid<PERSON>\Param(name: "icon_url", type: "string", require: true, desc: "图标地址"),
        Apidoc\Param(name: "city_id", type: "array", require: true, desc: "地址ID"),
        Apidoc\Param(name: "contact", type: "string", require: true, desc: "联系人"),
        Apidoc\Param(name: "phone", type: "string", require: true, desc: "联系电话"),
        Apidoc\Param(name: "create_time", type: "string", require: true, desc: "创建时间"),
        Apidoc\Param(name: "admin_group_ids", type: "string", require: true, desc: "添加管理员时能够选择的管理员分组(多个以','分隔)"),
        Apidoc\Param(name: "admin_username", type: "string", require: true, desc: "管理员用户名"),
        Apidoc\Param(name: "admin_password", type: "string", require: true, desc: "管理员密码"),
    ]
    public function add(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::service_corp([
                'id', 'name', 'icon_url', 'city_id', 'contact', 'phone', 'create_time',
                'admin_group_ids', 'admin_username', 'admin_password'
            ]));

            LogCollector::collectorRunLog('$verify_data-city_id' . json_encode($verify_data['city_id']));
            $city = get_city_for_city_id($verify_data['city_id']);
            LogCollector::collectorRunLog('$city' . json_encode($city));
            if (!$city) throw new RuntimeException("获取省市区失败", [], RuntimeException::CodeBusinessException);
            $verify_data = array_merge($verify_data, $city);

            $AdminUsersModel = new AdminUsersModel();
            if ($AdminUsersModel->isUsernameAlreadyExists($verify_data['admin_username']) === true) {
                throw new RuntimeException('用户名已经被使用', [], RuntimeException::CodeBusinessException);
            }

            $CorpModel = new CorpModel();
            //   int    $id, string $name, string $icon_url, string $province, string $city,
            //        string $district, string $contact, string $phone, string $city_id, string $create_time
            $result = $CorpModel->addCorpData(
                $verify_data['id'], $verify_data['name'], $verify_data['icon_url'], $verify_data['province'],
                $verify_data['city'], $verify_data['district'], $verify_data['contact'], $verify_data['phone'],
                $verify_data['city_id'], $verify_data['create_time']
            );
            if (!empty($verify_data['admin_group_ids'])) {
                (new CorpAdminGroup())->bindCorpAdminGroupIds($verify_data['id'], explode(',', $verify_data['admin_group_ids']));
            }


            if ($result === false) throw new RuntimeException('添加运营商失败', [], RuntimeException::CodeBusinessException);

            // 添加关联的管理员
            $AdminUsersModel->createAdminUser(
                $verify_data['admin_username'],
                '',
                '',
                3,
                $verify_data['admin_password'],
                $verify_data['id']
            );

            return [];

        }, true);
    }

    #[
        Apidoc\Title("更新运营商"),
        Apidoc\Author("cbj 2024.04.24 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/service/corp/update"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "运营商ID"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "运营商名称"),
        Apidoc\Param(name: "icon_url", type: "string", require: true, desc: "图标地址"),
        Apidoc\Param(name: "city_id", type: "array", require: true, desc: "地址ID"),
        Apidoc\Param(name: "contact", type: "string", require: true, desc: "联系人"),
        Apidoc\Param(name: "phone", type: "string", require: true, desc: "联系电话"),
        Apidoc\Param(name: "admin_group_ids", type: "string", require: true, desc: "添加管理员时能够选择的管理员分组(多个以','分隔)"),
    ]
    public function update(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::service_corp([
                'id', 'name', 'icon_url', 'city_id', 'contact', 'phone', 'admin_group_ids'
            ]));

            $city = get_city_for_city_id($verify_data['city_id']);
            if (!$city) throw new RuntimeException("获取省市区失败", [], RuntimeException::CodeBusinessException);
            $verify_data = array_merge($verify_data, $city);

            $CorpModel = new CorpModel();
            $result = $CorpModel->updateCorpData(
                $verify_data['id'], $verify_data['name'], $verify_data['icon_url'], $verify_data['province'],
                $verify_data['city'], $verify_data['district'], $verify_data['contact'], $verify_data['phone'],
                $verify_data['city_id']
            );

            $CorpAdminGroupModel = new CorpAdminGroup();
            $CorpAdminGroupModel->removeCorpAllAdminGroupIds($verify_data['id']);
            if (!empty($verify_data['admin_group_ids'])) {
                $CorpAdminGroupModel->bindCorpAdminGroupIds($verify_data['id'], explode(',', $verify_data['admin_group_ids']));
            }


            LogCollector::collectorRunLog('$result= ' . $result);
            if ($result === false) throw new RuntimeException('更新运营商失败', [], RuntimeException::CodeBusinessException);

            return [];
        });
    }

    #[
        Apidoc\Title("删除运营商"),
        Apidoc\Author("cbj 2024.04.24 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/service/corp/delete"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "运营商ID"),
    ]
    public function delete(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::service_corp([
                'id',
            ]));

            $CorpModel = new CorpModel();
            $result = $CorpModel->deleteCorpData($verify_data['id']);
            if ($result === false) throw new RuntimeException('删除运营商失败', [], RuntimeException::CodeBusinessException);

            // 删除与之关联的管理员账号
            $AdminUsersModel = new AdminUsersModel();
            $delRow = $AdminUsersModel->deleteCorpAdminUser($verify_data['id']);
            LogCollector::collectorRunLog(sprintf('$delRow = %s', $delRow));

        }, true);
    }
}