<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\service\controller;

use app\common\controller\ApiBase;
use app\common\lib\VerifyData;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\response\Json;
use app\common\model\AdminUsers as AdminUsersModel;

#[Apidoc\Title("管理员")]
class AdminUsers extends ApiBase
{
    #[
        Apidoc\Title("验证用户名是否已经被使用"),
        Apidoc\Author("cbj 2024.04.24 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/service/admin_users/verify_username"),
        Apidoc\Param(name: "username", type: "string", require: true, desc: "管理员用户名"),
        Apidoc\Returned(name: "result", type: "bool", require: true, desc: "验证结果 false:用户名可以使用 true:用户名已经被使用"),
    ]
    public function verify_username(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::service_admin_user([
                'username',
            ]));

            if ((new AdminUsersModel())->isUsernameAlreadyExists($verify_data['username']) === false) {
                return $this->res_success(data: ['result' => false], msg: '用户名可以使用', is_open_exception_catch: true);
            }
            return $this->res_success(data: ['result' => true], msg: '用户名已经被使用', is_open_exception_catch: true);
        });
    }
}