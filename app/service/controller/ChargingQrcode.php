<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\service\controller;

use app\common\controller\ApiBase;
use app\common\logic\admin\charging_qrcode\Bind;
use app\common\logic\admin\charging_qrcode\Unbind;
use hg\apidoc\annotation as Apidoc;
use think\response\Json;

#[Apidoc\Title("充电二维码")]
class ChargingQrcode extends ApiBase
{
    #[
        Apidoc\Title("绑定二维码"),
        Apidoc\Author("cbj 2024.11.08 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/service/charging_qrcode/bind"),
        Apidoc\Param(name: "charging_qrcode_id", type: "int", require: true, desc: "二维码ID", mock: 10000001),
        Apidoc\Param(name: "shots_id", type: "int", require: true, desc: "充电枪ID", mock: 1000000000001501),
        Apid<PERSON>\Param(name: "is_force_bind", type: "bool", require: true, desc: "是否强行绑定 true:是 false:否", mock: false),
        Apidoc\ResponseError(name: 'code', desc: "异常码", value: 40001),
    ]
    public function bind(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Bind($this->request))->run();
        });
    }

    #[
        Apidoc\Title("解绑二维码"),
        Apidoc\Author("cbj 2024.11.08 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/service/charging_qrcode/unbind"),
        Apidoc\Param(name: "charging_qrcode_id", type: "int", require: true, desc: "二维码ID", mock: 10000001),
        Apidoc\Param(name: "shots_id", type: "int", require: true, desc: "充电枪ID", mock: 1000000000001501),
    ]
    public function unbind(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new Unbind($this->request))->run();
        });
    }
}