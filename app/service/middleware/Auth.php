<?php
/** @noinspection PhpUnused */

namespace app\service\middleware;

use app\common\lib\ExceptionLogCollector;
use Closure;
use think\Container;
use think\Request;
use think\Response;
use think\response\Html;
use Throwable;
use Chenbingji\Tool\common\Encryption;

class Auth
{
    public const UsedCipherAlgo = 'aes-256-cbc';
    public const AUTH_TAG_LENGTH_BYTE = 16;

    public function handle(Request $request, Closure $next): Response
    {
        try {
            $postData = $request->post();
            if (empty($postData['ciphertext']) || empty($postData['iv']) || empty($postData['algorithm'])) {
                return $this->verifyFailedResponse();
            }

            $config = config('my.transfer_service');
            $plaintext = Encryption::decryption($postData['ciphertext'], $postData['iv'], $config['app_secret']);
            if ($plaintext === false) {
                return $this->verifyFailedResponse();
            }

            $data = json_decode($plaintext, true);
            if (isset($data['app_id']) === false || $data['app_id'] !== $config['app_id']) {
                return $this->verifyFailedResponse();
            }
            $postData = array_merge($postData, $data);
            $request->withPost($postData);
            return $next($request);
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            return res_error([], '服务器异常');
        }
    }

    protected function verifyFailedResponse(): Response
    {
        return Container::getInstance()->invokeClass(Html::class, ['', 404]);
    }
}