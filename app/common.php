<?php
/** @noinspection PhpUnused */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

// 应用公共文件

use app\common\log\LogCollector;
use think\facade\Db;
use think\response\Json;

use yzh52521\EasyHttp\Http;
use yzh52521\EasyHttp\RequestException;
use yzh52521\EasyHttp\Response;
use app\common\context\Order as ContextOrder;

//use GuzzleHttp\Client;


/**
 * 批量发送异步请求
 * lwj 2024.8.13 新增
 * @param array $urls 网址数组
 * @param string $method 方法
 * @param array|null $data 数据
 * @return array
 */
function sendMultiRequest(array $urls, string $method = 'get', array|null $data = null): array
{
    $conn = [];
    $res = [];
    //创建批处理curl句柄
    $mh = curl_multi_init();

    foreach ($urls as $k => $item) {
        $conn[$k] = curl_init();  //初始化各个子连接
        //设置url和相应的选项
        curl_setopt($conn[$k], CURLOPT_URL, $item['url']);
        curl_setopt($conn[$k], CURLOPT_HEADER, 0);
        curl_setopt($conn[$k], CURLOPT_RETURNTRANSFER, 1); //不直接输出到浏览器，而是返回字符串
        curl_setopt($conn[$k], CURLOPT_TIMEOUT, 10);
        curl_setopt($conn[$k], CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($conn[$k], CURLOPT_SSL_VERIFYHOST, false);
        if (isset($item['method']) && $item['method'] == 'post') {
            curl_setopt($conn[$k], CURLOPT_POST, true);
            $params = $item['params'];
            if (is_array($item['params'])) {
                $flag = true;
                foreach ($params as $val) {
                    if (str_starts_with($val, '@')) {
                        $flag = false;
                        break;
                    }
                }
                if ($flag) {
                    $params = http_build_query($params);
                }
            }
            curl_setopt($conn[$k], CURLOPT_POSTFIELDS, $params);
        } elseif ($method == 'post') {
            curl_setopt($conn[$k], CURLOPT_POST, true);
            $params = $data;
            if (is_array($data)) {
                $flag = true;
                foreach ($params as $val) {
                    if (str_starts_with($val, '@')) {
                        $flag = false;
                        break;
                    }
                }
                if ($flag) {
                    $params = http_build_query($params);
                }
            }
            curl_setopt($conn[$k], CURLOPT_POSTFIELDS, $params);
        } else {
            curl_setopt($conn[$k], CURLOPT_POST, false);
        }
        //处理302跳转
        curl_setopt($conn[$k], CURLOPT_FOLLOWLOCATION, 1);

        //增加句柄
        curl_multi_add_handle($mh, $conn[$k]);   //加入多处理句柄
    }

    $active = 0;     //连接数

    //防卡死写法:执行批处理句柄
    do {
        $mrc = curl_multi_exec($mh, $active);
        //这个循环的目的是尽可能地读写，直到无法继续读写为止
        //返回 CURLM_CALL_MULTI_PERFORM 表示还能继续向网络读写

    } while ($mrc == CURLM_CALL_MULTI_PERFORM);

    // var_dump($mrc);
    // echo '<hr/>';
    // var_dump($active);

    while ($active && $mrc == CURLM_OK) {
        if (curl_multi_select($mh) != -1) {
            do {
                $mrc = curl_multi_exec($mh, $active);

            } while ($mrc == CURLM_CALL_MULTI_PERFORM);
        }
    }

    foreach ($urls as $k => $url) {
//        $info = curl_multi_info_read($mh);
//         var_dump($info);

//        $headers = curl_getinfo($conn[$k]);
//         var_dump($headers);

        $res[$k] = json_to_array(curl_multi_getcontent($conn[$k]));
//        $res[$k] = curl_multi_getcontent($conn[$k]);
//        var_dump($res[$k]);

        //移除curl批处理句柄资源中的某一个句柄资源
        curl_multi_remove_handle($mh, $conn[$k]);

        //关闭curl会话
        curl_close($conn[$k]);
    }

    //关闭全部句柄
    curl_multi_close($mh);
    return $res;
}

/**
 * json转数组
 * lwj 2024.8.13 新增
 * @param string $json
 * @return array|string
 */
function json_to_array(string $json): array|string
{
    try {
        return json_decode($json, true) ?: $json;
    } catch (Throwable) {
        return $json;
    }
}

///**
// * 发送到开始充电任务
// * lwj 2023.8.23 新增
// * @param array $data
// * @return bool|Json
// */
//function send_start_charging_job2222(array $data=[]): bool|Json
//{
//    try {
//        $jsonStr=json_encode_cn($data);
//        trace('发送到开始充电任务=>参数：==》'.$jsonStr,'开始充电任务');
//        $url=config('my.StartChargingJobApi');
//        $ch = curl_init();
//        curl_setopt($ch, CURLOPT_POST, 1);
//        curl_setopt($ch, CURLOPT_URL, $url);
//        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
//        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
//        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证证书
//        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证主机名
//        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
//                'Content-Type: application/json; charset=utf-8',
//                'Content-Length: ' . strlen($jsonStr)
//            )
//        );
//        $response = curl_exec($ch);
//        curl_close($ch);
//        return json($response);
//    } catch (Throwable $e) {
//        trace('发送到开始充电任务：失败结果==》》' . $e->getMessage(),'开始充电任务');
//        return false;
//    }
//}

/**
 * 发送到开始充电任务
 * lwj 2023.8.23 新增
 * lwj 2023.8.28 修改
 * @param array $data
 * @return bool
 */
function send_start_charging_job(array $data = []): bool
{
    try {
        $jsonStr = json_encode_cn($data);
        trace('发送到开始充电任务=>参数：==》' . $jsonStr, '开始充电任务');
        $url = config('my.StartChargingJobApi');
        Http::asJson()->timeout(5)->postAsync($url, $data, function (Response $response) {
            trace('发送到开始充电定时器=>响应结果：==》' . $response->body(), '开始充电任务');
        }, function (RequestException $e) {
            trace('发送到开始充电定时器=>错误结果：==》' . $e->getMessage(), '开始充电任务');
        });
        return true;
    } catch (Throwable $e) {
        trace('发送到开始充电任务：失败结果==》》' . $e->getMessage(), '开始充电任务');
        return false;
    }
}


///**
// * 发送到开始充电定时器
// * lwj 2023.9.25 新增
// * @param array $task_data 数据
// * @return bool
// */
//function send_start_charging_timer(array $task_data): bool
//{
//    try {
//        // 与远程task服务建立异步连接，ip为远程task服务的ip，如果是本机就是127.0.0.1，如果是集群就是lvs的ip
//        $task_connection = new AsyncTcpConnection(config('my.StartChargingTimerApi'));
//        $send_data=json_encode_cn($task_data);
//        trace('发送到开始充电定时器=>参数：==》'.$send_data,'开始充电任务');
//        $task_connection->send($send_data);
//        // 异步获得结果
//        $task_connection->onMessage = function(AsyncTcpConnection $task_connection, $task_result)
//        {
//            trace('发送到开始充电定时器=>结果：==》'.json_encode_cn($task_result),'开始充电任务');
//            // 获得结果后记得关闭异步连接
//            $task_connection->close();
//        };
//        // 执行异步连接
//        $task_connection->connect();
//        return true;
//    } catch (Throwable $e) {
//        trace('发送到开始充电定时器=>异常：==》'.$e->getMessage(),'开始充电任务');
//        return false;
//    }
//}
//
///**
// * 发送到开始充电任务
// * lwj 2023.9.25 新增
// * @param array $task_data 数据
// * @return bool
// */
//function send_start_charging_job22222(array $task_data): bool
//{
//    try {
//        // 与远程task服务建立异步连接，ip为远程task服务的ip，如果是本机就是127.0.0.1，如果是集群就是lvs的ip
//        $task_connection = new AsyncTcpConnection(config('my.StartChargingJobApi'));
//        trace('发送到充电桩定时器=>参数：==》'.json_encode_cn($task_data),'开始充电任务');
//        $task_connection->send(json_encode_cn($task_data));
//        // 异步获得结果
//        $task_connection->onMessage = function(AsyncTcpConnection $task_connection, $task_result)
//        {
//            trace('发送到充电桩定时器=>结果：==》'.json_encode_cn($task_result),'开始充电任务');
//            // 获得结果后记得关闭异步连接
//            $task_connection->close();
//        };
//        // 执行异步连接
//        $task_connection->connect();
//        return true;
//    } catch (Throwable $e) {
//        trace('发送到充电桩定时器=>异常：==》'.$e->getMessage(),'开始充电任务');
//        return false;
//    }
//}

/**
 * 发送post请求到集中控制器中间api
 * lwj 2023.9.22 新增
 * @param string $api 请求地址
 * @param array $data 数据
 * @param array $user_data 用户数据
 * @return array|bool
 */
function send_http_post_to_centralized_api(string $api, array $data = [], array $user_data = []): array|bool
{
    try {
        $jsonStr = json_encode_cn($data);
        $url = config('my.charge_service_webman_centralized_api') . $api;
        $token = config('my.charge_service_webman_token');
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证主机名
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json; charset=utf-8',
                'Content-Length: ' . strlen($jsonStr),
                'Authorization: ' . $token
            )
        );
        $response = curl_exec($ch);
        curl_close($ch);
//        if(!is_json($response)) return res_error([], '返回不是json');
        $res_array = json_decode($response, true);
//        if($res_array['code'] !== 200 || !isset($res_array['data']['request_id'])) return json($res_array);
        if ($res_array['code'] !== 200) return false;
        cache('集中控制器服务_' . $res_array['data']['request_id'], $user_data, 864000); //暂时保存10天
        return $res_array;
    } catch (Throwable $e) {
        trace('发送post请求到集中控制器中间api=>异常：==》' . $e->getMessage(), '开始充电任务');
        return false;
    }
}

/**
 * 数组根据某个字段分组
 * lwj 2023.9.13 新增
 * @param array $arr 需要分组的数据
 * @param string $key 分组依据
 * @return array
 */
function arr_group(array $arr, string $key): array
{
    $new_arr = [];
    foreach ($arr as $v) {
        $new_arr[$v[$key]][] = $v;
    }
    return $new_arr;
}

/**
 * 发送post请求到下发指令处理定时器
 * lwj 2023.9.12 新增
 * @param array $data 数据
 * @return Json
 */
function send_http_post_timer(array $data = []): Json
{
    try {
        $jsonStr = json_encode_cn($data);
        $url = config('my.SendHandleTimerApi');
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证主机名
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json; charset=utf-8',
                'Content-Length: ' . strlen($jsonStr)
            )
        );
        $response = curl_exec($ch);
        curl_close($ch);
        return json($response);
    } catch (Throwable $e) {
        return res_error([], '发送异常：' . $e->getMessage());
    }
}

/**
 * 失败返回
 * @param array|string $data
 * @param string $msg
 * @param int $code
 * @return bool|string
 */
function res_error_text(array|string $data = [], string $msg = '查询失败', int $code = 200): bool|string
{
    return json_encode_cn(['code' => $code, 'msg' => $msg, 'data' => $data]);
}

/**
 * 成功返回
 * @param array|string $data
 * @param string $msg
 * @param int $code
 * @return bool|string
 */
function res_success_text(array|string $data = [], string $msg = '查询成功', int $code = 100): bool|string
{
    return json_encode_cn(['code' => $code, 'msg' => $msg, 'data' => $data]);
}

/**
 * 警告返回
 * @param array|string $data
 * @param string $msg
 * @param int $code
 * @return bool|string
 */
function res_warning_text(array|string $data = [], string $msg = '警告', int $code = 300): bool|string
{
    return json_encode_cn(['code' => $code, 'msg' => $msg, 'data' => $data]);
}


/**
 * 获取日期
 * lwj 2023.2.3 新增
 * @param int $type
 * @return string
 */
function get_date(int $type = 0): string
{
    $date = new DateTime();
    return match ($type) {
        1 => $date->format('Y-m-d H:i:s u'),
        2 => '[' . $date->format('Y-m-d H:i:s u') . '] ',
        default => $date->format('Y-m-d H:i:s'),
    };
}

/**
 * 获取日期差格式化输出
 * lwj 2023.8.31 新增
 * @param string $date1
 * @param string $date2
 * @return string
 */
function get_date_diff_format(string $date1, string $date2): string
{
    try {
        $date1 = new DateTime($date1);
        $date2 = new DateTime($date2);
        $interval = $date1->diff($date2);

        $years = $interval->y;
        $months = $interval->m;
        $days = $interval->d;
        $hours = $interval->h;
        $minutes = $interval->i;
        $seconds = $interval->s;

        if ($years) {
            $res = $years . "年" . $months . "月" . $days . "天" . $hours . "时" . $minutes . "分" . $seconds . "秒";
        } elseif ($months) {
            $res = $months . "月" . $days . "天" . $hours . "时" . $minutes . "分" . $seconds . "秒";
        } elseif ($days) {
            $res = $days . "天" . $hours . "时" . $minutes . "分" . $seconds . "秒";
        } elseif ($hours) {
            $res = $hours . "时" . $minutes . "分" . $seconds . "秒";
        } elseif ($minutes) {
            $res = $minutes . "分" . $seconds . "秒";
        } else {
            $res = $seconds . "秒";
        }
        return $res;
    } catch (Throwable) {
        return "0秒";
    }
}

/**
 * 获取当前时间段费率费率
 * lwj 2023.8.28 新增
 * @param array $periods
 * @return array|bool|string
 */
function get_current_period_rate(array $periods): array|bool|string
{
    $current_time = date('H:i'); // 获取当前时间，格式为小时:分钟

    foreach ($periods as $period) {
        $start_time = $period['start_time'];
        $end_time = $period['end_time'];
        if ($end_time == '0:00' || $end_time == '00:00') $end_time = '24:00';
        if ($current_time >= $start_time && $current_time < $end_time) {
            return $period;
        }
    }
    return false;
}

/**
 * 时间费率json添加费率
 * lwj 2023.8.28 新增
 * @param array $fee
 * @param array $period
 * @return array
 */
function period_json_add_fee(array $fee, array $period): array
{
    foreach ($period as &$v) {
        $v['start_time'] = explode("～", $v['period'])[0];
        $v['end_time'] = explode("～", $v['period'])[1];
        switch ($v['rate']) {
            case '00':
                $v['fee'] = [
                    'fee' => $fee['sharp_fee'],
                    'ser_fee' => $fee['sharp_ser_fee'],
                ];
                $v['sum_fee'] = $fee['sharp_fee'] + $fee['sharp_ser_fee'];
                break;
            case '01':
                $v['fee'] = [
                    'fee' => $fee['peak_fee'],
                    'ser_fee' => $fee['peak_ser_fee'],
                ];
                $v['sum_fee'] = $fee['peak_fee'] + $fee['peak_ser_fee'];
                break;
            case '02':
                $v['fee'] = [
                    'fee' => $fee['flat_fee'],
                    'ser_fee' => $fee['flat_ser_fee'],
                ];
                $v['sum_fee'] = $fee['flat_fee'] + $fee['flat_ser_fee'];
                break;
            case '03':
                $v['fee'] = [
                    'fee' => $fee['valley_fee'],
                    'ser_fee' => $fee['valley_ser_fee'],
                ];
                $v['sum_fee'] = $fee['valley_fee'] + $fee['valley_ser_fee'];
                break;
        }
    }
    return $period;
}


/**
 * 获取订单id+序列号
 * lwj 2023.8.21 新增
 * @param string|int $shots_id 充电枪编号
 * @return string|int
 */
function get_order_id_sn(string|int $shots_id): string|int
{
    $num = cache('order_id_sn' . $shots_id);

    if ($num && $num < 9998) {
        $new_num = $num + 1;
    } else {
        $new_num = 1;
    }
    $new_num_str = sprintf("%04d", $new_num);

    $order_id = $shots_id . date('ymdHis') . $new_num_str;
    cache('order_id_sn' . $shots_id, $new_num_str);
    return $order_id;
}

/**
 * http数据请求
 * lwj 2023.8.14 新增
 * @param $url
 * @param array $header 获取头部
 * @param string $post_data POST数据，不填写默认以GET方式请求
 * @return bool|string
 */
function get_http_data($url, array $header = [], string $post_data = ""): bool|string
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 2);
    if ($post_data != "") {
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data); //设置post提交数据
    }
    //判断当前是不是有post数据的发
    $output = curl_exec($ch);
    if ($output === FALSE) {
        $output = "curl 错误信息: " . curl_error($ch);
    }
    curl_close($ch);
    return $output;
}


/**
 * 获取ip
 * lwj 2023.8.12 新增
 * @return string
 */
function get_ip(): string
{
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return $ip;
}

/**
 * 发送get请求json转数组
 * lwj 2023.8.12 新增
 * lwj 2023.9.13 修改
 * @param string $url
 * @return array|bool
 */
function send_get_json_to_shuzu(string $url): array|bool
{
    try {
        $res_str = file_get_contents($url);
        $data = json_decode($res_str, true);
        LogCollector::collectorRunLog("url = " . $url);
        LogCollector::collectorRunLog("send_get_json_to_shuzu = " . print_r($data, true));
        return $data;
    } catch (Throwable) {
        return false;
    }
}

/**
 * 判断是否为json
 * lwj 2023.8.10 新增
 * @param string $string
 * @return bool
 */
function is_json(string $string): bool
{
    json_decode($string);
    return (json_last_error() == JSON_ERROR_NONE);
}

/**
 * 发送post请求到充电桩中间api
 * lwj 2023.7.29 新增
 * lwj 2023.8.21 修改
 * @param string $api 请求地址
 * @param array $data 数据
 * @param array $user_data 用户数据
 * @return Json
 */
function send_http_post_charge(string $api, array $data = [], array $user_data = []): Json
{
    try {
        $jsonStr = json_encode_cn($data);
        $url = config('my.charge_service_webman_api') . $api;
        $token = config('my.charge_service_webman_token');
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证主机名
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json; charset=utf-8',
                'Content-Length: ' . strlen($jsonStr),
                'Authorization: ' . $token
            )
        );
        $response = curl_exec($ch);
        curl_close($ch);
//        if(!is_json($response)) return res_error([], '返回不是json');
        $res_array = json_decode($response, true);
//        if($res_array['code'] !== 200 || !isset($res_array['data']['request_id'])) return json($res_array);
        if ($res_array['code'] !== 200) return json($res_array);
//        cache('充电桩服务_' . $res_array['data']['request_id'], $user_data, 864000); //暂时保存10天
        app(ContextOrder::class)->saveSequenceNumberToContextMap($res_array['data']['request_id'], $user_data);
        return json($res_array);
    } catch (Throwable $e) {
        return res_error([], '发送异常：' . $e->getMessage());
    }
}

/**
 * PHP发送表单数据
 * lwj 2023.8.12 修改
 * @param string $url
 * @param array $data 数据
 * @param bool $res_array
 * @return string|array|Json
 */
function http_post_form(string $url, array $data = [], bool $res_array = true): string|array|Json
{
    try {
        $queryStr = http_build_query($data);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $queryStr);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证主机名
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/x-www-form-urlencoded; charset=utf-8',
                'Content-Length: ' . strlen($queryStr)
            )
        );
        $response = curl_exec($ch);
//        curl_close($ch);
        if ($res_array) return json_decode($response, true);
        return $response;
    } catch (Throwable $e) {
        return res_error([], '处理异常：' . $e->getMessage());
    }
}

/**
 * PHP发送Json对象数据
 * lwj 2023.8.12 修改
 * @param string $url
 * @param array $data 数据
 * @param bool $res_array
 * @return string|array|Json
 */
function http_post_json(string $url, array $data = [], bool $res_array = true): string|array|Json
{
    try {
        $jsonStr = json_encode_cn($data);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证主机名
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json; charset=utf-8',
                'Content-Length: ' . strlen($jsonStr)
            )
        );
        $response = curl_exec($ch);
        curl_close($ch);
        if ($res_array) return json_decode($response, true);
        return $response;
    } catch (Throwable $e) {
        return res_error([], '处理异常：' . $e->getMessage());
    }
}

/**
 * 十六进制字符串自动补0
 * lwj 2023.7.19 新增
 * @param string|int $hexString 十六进制字符串
 * @param int $length 要生成十六进制字符串长度
 * @return string
 */
function hex_auto_fill_0(string|int $hexString, int $length): string
{
    $format = "%0" . $length . "s"; // 动态生成格式化字符串
    return sprintf($format, $hexString);
}

/**
 * 费率列表转费率代码
 * lwj 2023.8.7 新增
 * @param array $period_rate_list
 * @return string|bool
 */
function period_rate_list_to_period_codes(array $period_rate_list): string|bool
{
    try {
        if (count($period_rate_list) !== 48) return false;
        $y = ['00', '01', '02', '03'];
        $period_codes = '';
        foreach ($period_rate_list as $v) {
            if (!in_array($v['rate'], $y)) return false;
            $period_codes .= $v['rate'];
        }
        if (strlen($period_codes) !== 96) return false;
        return $period_codes;
    } catch (Throwable $e) {
        trace('费率列表转费率代码：异常=》' . json_encode_cn($e->getMessage()), '错误');
        return false;
    }
}

/**
 * 小数转int
 * lwj 2023.8.7 新增
 * @param float $decimal
 * @param int $divisor
 * @return int
 */
function decimal_to_int(float $decimal, int $divisor = 100000): int
{
    return round($decimal * $divisor);
}

/**
 * int转小数
 * lwj 2023.8.3 新增
 * @param int $int
 * @param int $divisor
 * @param int $length
 * @return float
 */
function int_to_decimal(int $int, int $divisor = 100000, int $length = 5): float
{
    return round($int / $divisor, $length);
}

/**
 * 费率代码转费率列表
 * lwj 2023.8.3 新增
 * @param string $period_codes
 * @return array|bool
 */
function period_codes_to_period_rate_list(string $period_codes): array|bool
{
    try {
        if (strlen($period_codes) !== 96) return false;
        $hex_arr = str_split($period_codes, 2);

        $period = config('my.period');

        $res_data = [];
        $start = null;
        $end = null;
        $rate = null;
        foreach ($period as $k => $v) {
            if ($rate !== $hex_arr[$k]) {
                if ($start !== null && $end !== null) {
                    $res_data[] = [
                        'period' => $start . '～' . $end,
                        'rate' => $rate,
                    ];
                }
                $start = $v['start'];
                $end = $v['end'];
                $rate = $hex_arr[$k];
            } else {
                $end = $v['end'];
            }
        }

        $res_data[] = [
            'period' => $start . '～' . $end,
            'rate' => $rate,
        ];

        return $res_data;
    } catch (Throwable $e) {
        trace('费率代码转费率列表：异常=》' . json_encode_cn($e->getMessage()), '错误');
        return false;
    }
}

/**
 * 费率代码转费率列表(全部输出)
 * lwj 2023.8.3 新增
 * @param string $period_codes
 * @return array|bool
 */
function period_codes_to_period_rate_list_all(string $period_codes): array|bool
{
    try {
        if (strlen($period_codes) !== 96) return false;
        $hex_arr = str_split($period_codes, 2);
        $period = config('my.period');

        foreach ($period as $k => &$v) {
            $v['period'] = $v['start'] . '～' . $v['end'];
            $v['rate'] = $hex_arr[$k];
            unset($v['start']);
            unset($v['end']);
        }

        return $period;
    } catch (Throwable $e) {
        trace('费率代码转费率列表：异常=》' . json_encode_cn($e->getMessage()), '错误');
        return false;
    }
}

/**
 * 从省市区id获取省市区
 * lwj 2023.8.3 新增
 * @param array $city_id
 * @return array|bool
 */
function get_city_for_city_id(array $city_id = []): array|bool
{
    try {
        if (count($city_id) !== 3) return false;
        $res_data = [];
        $city_list = Db::table('city')
            ->whereIn('id', $city_id)
            ->column('name', 'id');
        $res_data['province'] = $city_list[$city_id[0]];
        $res_data['city'] = $city_list[$city_id[1]];
        $res_data['district'] = $city_list[$city_id[2]];
        return $res_data;
    } catch (Throwable $e) {
        trace('从省市区id获取省市区：异常=》' . json_encode_cn($e->getMessage()), '错误');
        return false;
    }
}

///**
// * 发送post请求到充电桩中间api
// * lwj 2023.7.29 新增
// * @param string $api 请求地址
// * @param array $data
// * @param string $res_type
// * @return Response|array|bool
// */
//function send_http_post_charge(string $api,array $data=[],string $res_type='json'): Response|array|bool
//{
//    try {
//        $url=config('my.charge_service_webman_api').$api;
//        $token=config('my.charge_service_webman_token');
//        $response = Http::withHeaders([
//            'authorization' => $token
//        ])->post($url,$data);
//        trace('发送post请求到充电桩：=》' . json_encode_cn($response),'错误');
//        return match ($res_type) {
//            'array' => json_decode($response, true),
//            default => $response,
//        };
//    } catch (Throwable $e) {
//        trace('发送post请求到充电桩：异常=》' . json_encode_cn($e->getMessage()),'错误');
//        return false;
//    }
//}

/**
 * 成功返回
 * @param mixed $data
 * @param string $msg
 * @param int $code
 * @return Json
 */
function res_success(mixed $data = [], string $msg = '成功', int $code = 200): Json
{
    return json(['code' => $code, 'msg' => $msg, 'data' => $data]);
}

/**
 * 失败返回
 * @param mixed $data
 * @param string $msg
 * @param int $code
 * @return Json
 */
function res_error(mixed $data = [], string $msg = '失败', int $code = 100): Json
{
    return json(['code' => $code, 'msg' => $msg, 'data' => $data]);
}

/**
 * 返回无权访问
 * lwj 2023.7.27 新增
 * @param mixed $data 请求地址
 * @param string $msg 请求地址
 * @param int $code 请求地址
 * @return Json
 */
function res_error_auth(mixed $data = [], string $msg = '无权访问', int $code = 102): Json
{
    return json(['code' => $code, 'msg' => $msg, 'data' => $data]);
}

/**
 * 返回需要登录
 * lwj 2023.7.27 新增
 * @param mixed $data 请求地址
 * @param string $msg 请求地址
 * @param int $code 请求地址
 * @return Json
 */
function res_error_login(mixed $data = [], string $msg = '请登录', int $code = 101): Json
{
    return json(['code' => $code, 'msg' => $msg, 'data' => $data]);
}

/**
 * 直接发送请求转数组
 * @param string $url 请求地址
 * @return array
 */
function send_json_to_arr(string $url): array
{
    $result = file_get_contents($url);
//    return $result;
    return json_decode($result, true);
}

/**
 * json编码，中文处理，json_encode
 * lwj 2021.9.15 新增
 */
function json_encode_cn($data): bool|string
{
    return json_encode($data, JSON_UNESCAPED_UNICODE);
}

/**
 * 返回当前的毫秒时间戳
 * lwj 2021.10.27 新增
 * 注意：sprintf('%.0f', $num) 是输出不含小数部分的浮点数
 */
function get_msec_time(): float
{
    list($msec, $sec) = explode(' ', microtime());
    return (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
}

/**
 * 获取微秒时间戳
 * lwj 2023.11.20 新增
 * @return int
 */
function get_microtime(): int
{
    // 获取当前的微秒时间戳
    $microtime = microtime(true);
    // 将微秒时间戳转换为整数格式
    return intval($microtime * 1000000);
}

/**
 * 获取充电停止原因
 * lwj 2023.7.19 新增
 * @param int $code 代码
 * @return string
 */
function get_reason_for_charging_stop(int $code): string
{
    return match ($code) {
        0x40 => "充电完成，APP远程停止",
        0x41 => "充电完成，SOC达到100%",
        0x42 => "充电完成，充电电量满足设定条件",
        0x43 => "充电完成，充电金额满足设定条件",
        0x44 => "充电完成，充电时间满足设定条件",
        0x45 => "充电完成，手动停止充电",
        0x46, 0x47, 0x48, 0x49 => "充电完成，其他方式（预留）",
        0x4A => "充电启动失败，充电桩控制系统故障(需要重启或自动恢复)",
        0x4B => "充电启动失败，控制导引断开",
        0x4C => "充电启动失败，断路器跳位",
        0x4D => "充电启动失败，电表通信中断",
        0x4E => "充电启动失败，余额不足",
        0x4F => "充电启动失败，充电模块故障",
        0x50 => "充电启动失败，急停开入",
        0x51 => "充电启动失败，防雷器异常",
        0x52 => "充电启动失败，BMS未就绪",
        0x53 => "充电启动失败，温度异常",
        0x54 => "充电启动失败，电池反接故障",
        0x55 => "充电启动失败，电子锁异常",
        0x56 => "充电启动失败，合闸失败",
        0x57 => "充电启动失败，绝缘异常",
        0x58 => "充电启动失败，预留",
        0x59 => "充电启动失败，接收 BMS 握手报文 BHM 超时",
        0x5A => "充电启动失败，接收 BMS 和车辆的辨识报文超时 BRM",
        0x5B => "充电启动失败，接收电池充电参数报文超时 BCP",
        0x5C => "充电启动失败，接收 BMS 完成充电准备报文超时 BRO AA",
        0x5D => "充电启动失败，接收电池充电总状态报文超时 BCS",
        0x5E => "充电启动失败，接收电池充电要求报文超时 BCL",
        0x5F => "充电启动失败，接收电池充电电流报文超时 BCC",
        0x60 => "充电启动失败，GB2015 电池在 BHM 阶段有电压不允许充电",
        0x61 => "充电启动失败，GB2015 辨识阶段在 BRO_AA 时候电池实际电压与 BCP 报文电池电压差距大于 5%",
        0x62 => "充电启动失败，B2015 充电机在预充电阶段从 BRO_AA 变成 BRO_00 状态",
        0x63 => "充电启动失败，接收主机配置报文超时",
        0x64 => "充电启动失败，充电机未准备就绪,我们没有回 CRO AA，对应老国标",
        0x65, 0x66, 0x67, 0x68, 0x69 => "充电启动失败，（其他原因）预留",
        0x6A => "充电异常中止，系统闭锁",
        0x6B => "充电异常中止，导引断开",
        0x6C => "充电异常中止，断路器跳位",
        0x6D => "充电异常中止，电表通信中断",
        0x6E => "充电异常中止，余额不足",
        0x6F => "充电异常中止，交流保护动作",
        0x70 => "充电异常中止，直流保护动作",
        0x71 => "充电异常中止，充电模块故障",
        0x72 => "充电异常中止，急停开入",
        0x73 => "充电异常中止，防雷器异常",
        0x74 => "充电异常中止，温度异常",
        0x75 => "充电异常中止，输出异常",
        0x76 => "充电异常中止，充电无流",
        0x77 => "充电异常中止，电子锁异常",
        0x78 => "充电异常中止，预留",
        0x79 => "充电异常中止，总充电电压异常",
        0x7A => "充电异常中止，总充电电流异常",
        0x7B => "充电异常中止，单体充电电压异常",
        0x7C => "充电异常中止，电池组过温",
        0x7D => "充电异常中止，最高单体充电电压异常",
        0x7E => "充电异常中止，最高电池组过温",
        0x7F => "充电异常中止，BMV 单体充电电压异常",
        0x80 => "充电异常中止，BMT 电池组过温",
        0x81 => "充电异常中止，电池状态异常停止充电",
        0x82 => "充电异常中止，车辆发报文禁止充电",
        0x83 => "充电异常中止，充电桩断电",
        0x84 => "充电异常中止，接收电池充电总状态报文超时",
        0x85 => "充电异常中止，接收电池充电要求报文超时",
        0x86 => "充电异常中止，接收电池状态信息报文超时",
        0x87 => "充电异常中止，接收 BMS 中止充电报文超时",
        0x88 => "充电异常中止，接收 BMS 充电统计报文超时",
        0x89 => "充电异常中止，接收对侧 CCS 报文超时",
        0x8A, 0x8B, 0x8C, 0x8D, 0x8E, 0x8F => "充电异常中止，（其他原因）预留",
        0x90 => "未知原因停止",
        default => "未知原因停止(默认)",
    };
}
