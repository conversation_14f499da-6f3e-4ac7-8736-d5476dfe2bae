<?php
/** @noinspection PhpUnused */
/** @noinspection PhpObjectFieldsAreOnlyWrittenInspection */
declare (strict_types=1);

namespace app\command;

use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use Workerman\Worker;
use app\common\new_queue\EnterpriseWechatMessageQueue;

/**
 * 企业微信消息队列消费者
 * cbj 2023.10.31 新增
 */
class EnterpriseWechatMessageQueueConsumer extends BaseCommand
{
    public const COMMAND = 'enterprise-wechat-message-queue-consumer';

    protected function configure(): void
    {
        // 指令配置
        $this->setName(self::COMMAND)
            ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|status|connections", 'start')
            ->addOption('mode', 'm', Option::VALUE_OPTIONAL, '以守护程序模式运行workerman服务器')
            ->setDescription('企业微信消息队列消费者');
    }

    protected function execute(Input $input, Output $output): void
    {
        // 指令输出
        $output->writeln('开始');

        $this->initArgv($input);


        $worker = new Worker();

        $worker->name = $this->getDescription();
        $worker->count = 1;


        $worker->onWorkerStart = function () {

            app(EnterpriseWechatMessageQueue::class)->startConsumer();

        };

        Worker::runAll();
    }
}
