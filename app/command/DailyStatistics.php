<?php
declare (strict_types=1);

namespace app\command;

use app\common\lib\ExceptionLogCollector;
use app\common\model\StationsMonthStatistics;
use app\common\model\StationsStats;
use app\common\model\StationsYearStatistics;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\common\model\Order;
use Throwable;

/**
 * 日统计
 * 针对充电订单做的每日统计
 * cbj 2023.10.19 新增
 */
class DailyStatistics extends Command
{
    /**
     * @var int 统计范围起始时间
     */
    protected int $statisticTime;

    /**
     * @var int 日统计数据写入条数
     */
    protected int $dailyStatisticsInsertCount = 0;

    /**
     * @var int 月统计数据写入条数
     */
    protected int $monthlyStatisticsInsertCount = 0;

    /**
     * @var int 年统计数据写入条数
     */
    protected int $yearlyStatisticsInsertCount = 0;


    protected function configure(): void
    {
        // 指令配置
        $this->setName('DailyStatistics')
            ->setDescription('每日统计');
    }

    protected function execute(Input $input, Output $output): void
    {
        $this->statisticTime = strtotime('-1 day');
        $runStartTime = microtime(true);

        $dailyStatisticsResult = $this->statisticsDailyData();
        if ($dailyStatisticsResult === true) {
            echo sprintf("每日统计成功，完成时间: %s，统计耗时: %d，写入数据条数: %d\n",
                date('Y-m-d H:i:s'), microtime(true) - $runStartTime, $this->dailyStatisticsInsertCount);
        } else {
            echo sprintf("每日统计失败，失败时间: %s，统计耗时: %d\n",
                date('Y-m-d H:i:s'), microtime(true) - $runStartTime);
        }

        // 如果到月初了
        if (date('d', strtotime('+1 day', $this->statisticTime)) === '01' && $dailyStatisticsResult === true) {
            $runStartTime = microtime(true);
            $monthlyStatisticsResult = $this->statisticsMonthlyData();
            if ($monthlyStatisticsResult === true) {
                echo sprintf("每月统计成功，完成时间: %s，统计耗时: %d，写入数据条数: %d\n",
                    date('Y-m-d H:i:s'), microtime(true) - $runStartTime, $this->monthlyStatisticsInsertCount);
            } else {
                echo sprintf("每月统计失败，失败时间: %s，统计耗时: %d\n",
                    date('Y-m-d H:i:s'), microtime(true) - $runStartTime);
            }
        } else {
            $monthlyStatisticsResult = false;
        }

        // 年初
        if ($monthlyStatisticsResult === true && date('m-d', strtotime('+1 day', $this->statisticTime)) === '01-01') {
            $runStartTime = microtime(true);
            $YearlyStatisticsResult = $this->statisticsYearlyData();
            if ($YearlyStatisticsResult === true) {
                echo sprintf("每年统计成功，完成时间: %s，统计耗时: %d，写入数据条数: %d\n",
                    date('Y-m-d H:i:s'), microtime(true) - $runStartTime, $this->yearlyStatisticsInsertCount);
            } else {
                echo sprintf("每年统计失败，失败时间: %s，统计耗时: %d\n",
                    date('Y-m-d H:i:s'), microtime(true) - $runStartTime);
            }
        }
    }

    protected function statisticsYearlyData(): bool
    {
        try {
            $year = (int)date('Y', $this->statisticTime);
            $StationsMonthStatistics = app(StationsMonthStatistics::class);
            $statisticsResult = $StationsMonthStatistics->queryMonthStatisticsData($year);
            // 收集要排除的充电站ID，因为它已经是统计数据的，所以不用填充。
            $stationIds = [];
            foreach ($statisticsResult as $value) {
                $stationIds[] = $value['station_id'];
            }
            $insertsData = $this->getYearlyInsertsData($statisticsResult);
            $StationsYearStatistics = app(StationsYearStatistics::class);
            $fillRows = $StationsYearStatistics->fillDailyEmptyData($year, $stationIds);
            $this->yearlyStatisticsInsertCount = $StationsYearStatistics->insertAll($insertsData);
            $this->yearlyStatisticsInsertCount += $fillRows;
            return true;
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            return false;
        }
    }

    protected function getYearlyInsertsData(array $statisticsResult): array
    {
        $monthTime = strtotime(date('Y-01-01', $this->statisticTime));
        foreach ($statisticsResult as &$value) {
            $value['statistic_time'] = $monthTime;
            if ($value['utility_rate'] > 0) {
                $value['utility_rate'] = $value['utility_rate'] / $value['count'];
            }
            unset($value['count']);

        }
        return $statisticsResult;
    }

    protected function statisticsMonthlyData(): bool
    {
        try {
            $year = (int)date('Y', $this->statisticTime);
            $month = (int)date('m', $this->statisticTime);

            $StationsStats = app(StationsStats::class);
            $statisticsResult = $StationsStats->queryMonthStatisticsData($year, $month);
            // 收集要排除的充电站ID，因为它已经是统计数据的，所以不用填充。
            $stationIds = [];
            foreach ($statisticsResult as $value) {
                $stationIds[] = $value['station_id'];
            }
            $insertsData = $this->getMonthlyInsertsData($statisticsResult);
            $StationsMonthStatistics = app(StationsMonthStatistics::class);
            $fillRows = $StationsMonthStatistics->fillDailyEmptyData($year, $month, $stationIds);
            $this->monthlyStatisticsInsertCount = $StationsMonthStatistics->insertAll($insertsData);
            $this->monthlyStatisticsInsertCount += $fillRows;
            return true;
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            return false;
        }
    }

    protected function getMonthlyInsertsData(array $statisticsResult): array
    {
        $monthTime = strtotime(date('Y-m-01', $this->statisticTime));
        $year = (int)date('Y', $this->statisticTime);
        foreach ($statisticsResult as &$value) {
            $value['statistic_time'] = $monthTime;
            $value['year'] = $year;
            if ($value['utility_rate'] > 0) {
                $value['utility_rate'] = $value['utility_rate'] / $value['count'];
            }
            unset($value['count']);

        }
        return $statisticsResult;
    }

    protected function statisticsDailyData(): bool
    {
        try {
            $year = (int)date('Y', $this->statisticTime);
            $month = (int)date('m', $this->statisticTime);
            $day = (int)date('d', $this->statisticTime);
            $Order = app(Order::class);
            $statisticsResult = $Order->queryDayStatisticsData($year, $month, $day);
            // 收集要排除的充电站ID，因为它已经是统计数据的，所以不用填充。
            $stationIds = [];
            foreach ($statisticsResult as $value) {
                $stationIds[] = $value['station_id'];
            }
            $insertsData = $this->getDailyInsertsData($statisticsResult);
            $StationsStats = new StationsStats();
            $fillRows = $StationsStats->fillDailyEmptyData($year, $month, $day, $stationIds);
            $this->dailyStatisticsInsertCount = $StationsStats->insertAll($insertsData);
            $this->dailyStatisticsInsertCount += $fillRows;
            return true;
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            return false;
        }
    }

    protected function getDailyInsertsData(array $statisticsResult): array
    {
        $day = date('Y-m-d 00:00:00', $this->statisticTime);
        $month = date('Y-m-01 00:00:00', $this->statisticTime);
        $year = date('Y-01-01 00:00:00', $this->statisticTime);
        foreach ($statisticsResult as &$value) {
            $value['day'] = $day;
            $value['month'] = $month;
            $value['year'] = $year;
            $value['utility_rate'] = 0;
        }
        return $statisticsResult;
    }
}
