<?php
/** @noinspection PhpUnused */
/** @noinspection PhpObjectFieldsAreOnlyWrittenInspection */
declare (strict_types=1);

namespace app\command;

use app\common\queue\AdminSocketQueue;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use Workerman\Worker;

/**
 * 后台Socket队列消费者
 * cbj 2023.10.31 新增
 */
class AdminSocketQueueConsumer extends BaseCommand
{
    protected function configure(): void
    {
        // 指令配置
        $this->setName('admin-socket-queue-consumer')
            ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|status|connections", 'start')
            ->addOption('mode', 'm', Option::VALUE_OPTIONAL, '以守护程序模式运行workerman服务器')
            ->setDescription('后台Socket消息队列消费者');
    }

    protected function execute(Input $input, Output $output): void
    {
        // 指令输出
        $output->writeln('开始');

        $this->initArgv($input);


        $worker = new Worker();

        $worker->name = '后台Socket消息队列消费者';
        $worker->count = 1;


        $worker->onWorkerStart = function () {

            $AdminSocketQueue = app(AdminSocketQueue::class);
            $AdminSocketQueue->consumer();

        };

        Worker::runAll();
    }
}
