<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
declare (strict_types=1);

namespace app\command;

use app\common\email\ActiveAlarmSummaryEmail;
use app\common\log\SocketLogCollector;
use app\common\repositories\AlarmRecord;
use app\common\repositories\StationsActiveAlarmConfig;
use think\console\Input;
use think\console\Output;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 主动告警汇总
 * cbj 2024.05.20
 */
class ActiveAlarmSummary extends BaseCommand
{

    public const COMMAND = 'active-alarm-summary';

    protected function configure(): void
    {
        // 指令配置
        $this->setName(self::COMMAND)
            ->setDescription('主动告警汇总');
    }


    /**
     * @param Input $input
     * @param Output $output
     * @param SocketLogCollector $socketLogCollector
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function handler(Input $input, Output $output, SocketLogCollector $socketLogCollector): void
    {
        $stationToAlarmCount = AlarmRecord::getGroupByStationNotRecoveryAlarmCount();
        foreach ($stationToAlarmCount as $station_id => $data) {

            // 查询邮箱地址
            $email = StationsActiveAlarmConfig::getEmail($station_id);
            if (!$email) continue;


            $records = AlarmRecord::queryStationNotRecoveryAlarmRecords($station_id);

            if (count($records) > 0) {
                $body = '';
                foreach ($records as $record) {
                    $body .= sprintf('<p>设备类型:%s</p>', AlarmRecord::DeviceTypeToText[$record['device_type']] ?? '未知设备类型');
                    $body .= sprintf('<p>设备编号:%s</p>', $record['device_id']);
                    $body .= sprintf('<p>告警类型:%s</p>', AlarmRecord::TypeToText[$record['type']] ?? '未知类型');
                    $body .= sprintf('<p>告警代码:%s</p>', $record['code']);
                    $body .= sprintf('<p>告警代码说明:%s</p>', AlarmRecord::CodeToMessageMap[$record['code']] ?? '未知');
                    $body .= sprintf('<p>告警时间:%s</p>', date('Y-m-d H:i:s', $record['alarm_time']));
                    $body .= '<br />';
                }

                $result = (new ActiveAlarmSummaryEmail())->send(
                    $email,
                    $data['corp_name'],
                    $data['station_name'],
                    $body
                );
                $output->writeln("运行结果:" . $result);
            }
        }

        $output->writeln('运行成功');


// 1. 查询出哪些运营商存在未恢复的告警记录
// 2. 依次查询每个运营商的数据
// 2.1. 告警记录
// 2.2. 主动告警配置(企业微信机器人密钥)
// 2.3. 将告警记录整理成MD格式
// 2.4. 发送企业微信消息
    }
}