<?php
/** @noinspection PhpSameParameterValueInspection */
/** @noinspection PhpUnusedLocalVariableInspection */
/** @noinspection PhpUnusedParameterInspection */
/** @noinspection PhpObjectFieldsAreOnlyWrittenInspection */

/**
 * 接收消息
 * lwj 2023.8.23 新增
 * lwj 2023.8.24 修改
 */

namespace app\command;

use app\common\lib\SendAdmin;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\console\input\Argument;
use think\console\input\Option;
use Throwable;
use Workerman\Lib\Timer;
use Workerman\Worker;
use Workerman\Connection\AsyncTcpConnection;
use Workerman\Http\Client as WorkermanHttp;

class receive_msg extends Command
{
    protected function configure(): void
    {
        // 指令配置
        $this->setName('receive_msg')
            ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|status|connections", 'start')
            ->addOption('mode', 'm', Option::VALUE_OPTIONAL, 'Run the workerman server in daemon mode.')
            ->setDescription('接收消息');
    }

    protected function execute(Input $input, Output $output): void
    {
        // 指令输出
        $output->writeln('开始');

        $action = $input->getArgument('action');
        $mode = $input->getOption('mode');


        // 重新构造命令行参数,以便兼容workerman的命令
        global $argv;

        $argv = [];

        array_unshift($argv, 'think', $action);
        if ($mode == 'd') {
            $argv[] = '-d';
        } else if ($mode == 'g') {
            $argv[] = '-g';
        }

        $worker = new Worker();
        $isConnected = false;
        $heartbeatTimer = null;

        $worker->onWorkerStart = function($worker) use (&$heartbeatTimer) {

            $con = new AsyncTcpConnection(config('my.ChargeNoticeSocketRemoteAddress'));

            $con->onConnect = function(AsyncTcpConnection $con) use (&$isConnected, &$heartbeatTimer) {
                echo "开始连接" . PHP_EOL;
                $con->send('{"type":"msg"}');
                $isConnected = true;
                // 取消之前的心跳定时器
                if ($heartbeatTimer !== null) {
                    Timer::del($heartbeatTimer);
                }
                // 心跳，例如每10秒发送一次
                $heartbeatTimer = Timer::add(10, function() use ($con, &$isConnected) {
                    if ($isConnected) {
                        echo "发送心跳" . PHP_EOL;
                        $con->send('{"type":"ping"}');
                    }
                });
            };

            $con->onMessage = function(AsyncTcpConnection $con, $message) {
                try {
                    echo '接收到的消息：' . $message . PHP_EOL;
                    $msg = json_decode($message,true);
                    if(!isset($msg['type'])) return;
                    switch($msg['type'])
                    {
                        case 'login':
                            $this->send_http_post_charge_notice_asyn(
                                'login_notice_web_socket',
                                [
                                    'client_id'=>$msg['client_id'],
                                    'ws_group'=>'后台',
                                ]
                            );
                            break;
                        case 'login_success':
                            $con->send(json_encode_cn([
                                'type'=>'login_success',
                                'login_id'=>$msg['login_id'],
                            ]));
                            break;
                        case 'upload_realtime_monitoring_data_notice':
                            SendAdmin::send_group('后台',$msg);
                            break;
                        default:
                            break;
                    }
                } catch (Throwable $e) {
                    echo '接收消息异常：' . $e->getMessage() . PHP_EOL;
                }
            };

            $con->onError = function(AsyncTcpConnection $con, $code, $msg) {
                echo "Error: " . $msg . "\n";
            };

            $con->onClose = function(AsyncTcpConnection $con) use (&$isConnected) {
                // 如果连接断开，则在1秒后重连
                $isConnected = false;
                $con->reConnect(1);
            };

            $con->connect();
        };

        Worker::runAll();

    }

    /**
     * 发送post请求到充电桩中间通知api（异步）
     * lwj 2023.8.23 新增
     * lwj 2023.8.24 修改
     * @param string $api
     * @param array $data
     * @return void
     */
    private function send_http_post_charge_notice_asyn(string $api,array $data=[]): void
    {
        $url=config('my.charge_service_webman_notice').$api;
        $token=config('my.charge_service_webman_token');
        $options = [
            'max_conn_per_addr' => 1280, // 每个域名最多维持多少并发连接
            'keepalive_timeout' => 15,  // 连接多长时间不通讯就关闭
            'connect_timeout'   => 30,  // 连接超时时间
            'timeout'           => 60,  // 请求发出后等待响应的超时时间
        ];
        $http = new WorkermanHttp($options);
        $jsonData = json_encode($data);
        $http->request($url, [
            'method' => 'POST',
            'version' => '1.1',
            'headers' => ['authorization' => $token,'Content-type'=>'application/json'],
            'data' => $jsonData,
            'success' => function ($response)use ($url) {
                trace('发送post请求到充电桩中间通知api（异步）：'.$url.'，成功结果==》》' . $response->getBody(),'信息');
//            echo $response->getBody();
            },
            'error' => function ($exception)use ($url) {
                trace('发送post请求到充电桩中间通知api（异步）：'.$url.'，失败结果==》》' . json_encode_cn($exception),'信息');
//            echo $exception;
            }
        ]);
    }

}

