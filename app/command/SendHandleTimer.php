<?php /** @noinspection PhpUnused */
/** @noinspection PhpUnusedParameterInspection */
/** @noinspection PhpObjectFieldsAreOnlyWrittenInspection */
declare (strict_types=1);

namespace app\command;

use app\common\lib\send_handle_timer\RemoveRemoteAccountBalanceUpdate;
use app\common\lib\send_handle_timer\request\RemoteAccountBalanceUpdateRequest;
use app\common\lib\send_handle_timer\request\RemoveRemoteAccountBalanceUpdateRequest;
use app\common\lib\timeout_handle_job\request\RemoteAccountBalanceUpdateRequest as RemoteAccountBalanceUpdateRequestTimeout;
use app\common\lib\timeout_handle_job\request\BaseRequest;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use Throwable;
use Workerman\Connection\TcpConnection;
use Workerman\Protocols\Http\Request;
use Workerman\Worker;
use app\common\lib\send_handle_timer\TimerManager;

/**
 * 下发指令处理定时器进程
 * lwj 2023.9.11 新增
 * lwj 2023.9.27 修改
 */
class SendHandleTimer extends BaseCommand
{

    protected function configure(): void
    {
        // 指令配置
        $this->setName('SendHandleTimer')
            ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|status|connections", 'start')
            ->addOption('mode', 'm', Option::VALUE_OPTIONAL, '以守护程序模式运行workerman服务器')
            ->setDescription('下发指令处理定时器');
    }

    protected function execute(Input $input, Output $output): void
    {
        // 指令输出
        $output->writeln('开始');

        $this->initArgv($input);

        $this->LoadTimer();

        // 在这里放心的实例化worker,
        // 就像参照workerman文档写一样,
        // 无非在workerman的文档里,代码是新建纯php文件,但在这里,写到了一个方法里.
        $worker = new Worker(config('my.SendHandleTimerHost'));

        $worker->name = '下发指令处理定时器';

        $worker->onMessage = function (TcpConnection $connection, Request $request) {
            $logPrefix = '下发指令处理定时器 - ';
            try {
                $data = $request->post();

                trace(str_repeat('-', 40), 'request');
                trace($logPrefix . '参数 = ' . print_r($data, true), 'request');

                if (empty($data['type'])) {
                    $result = res_error_text([], '参数错误');
                } else {
                    $type = $data['type'];

                    $result = match ($type) {
                        '运营平台远程控制启机' => $this->addStartChargeAnswerTimeoutTimer($connection, $data),
                        '删除-运营平台远程控制启机' => $this->removeStartChargeAnswerTimeoutTimer($connection, $data),
                        '运营平台远程停机' => $this->addStopChargeAnswerTimeoutTimer($connection, $data),
                        '删除-运营平台远程停机-指令' => $this->removeStopChargeAnswerTimeoutTimer($connection, $data),
                        '删除-运营平台远程停机-账单' => $this->removeTransactionRecordTimeoutTimer($connection, $data),
                        '上传实时监测数据' => $this->addUploadRealtimeMonitoringDataTimeoutTimer($connection, $data),
                        '删除-上传实时监测数据' => $this->removeUploadRealtimeMonitoringDataTimeoutTimer($connection, $data),
                        '自动结算' => $this->addAutoSettlement($connection, $data),
                        '删除-自动结算' => $this->removeAutoSettlement($connection, $data),
                        '远程账户余额更新' => $this->remoteAccountBalanceUpdate($connection, $data),
                        '删除-远程账户余额更新' => $this->removeRemoteAccountBalanceUpdate($connection, $data),
                        default => res_error_text([], '未知消息'),
                    };
                }

            } catch (Throwable $e) {
                trace('下发指令处理定时器 - ' . print_r([
                        'msg' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'code' => $e->getCode()
                    ], true), 'error');

                trace($logPrefix . print_r($e->getTrace(), true), 'error');
                $result = res_error_text([], '消息处理出现异常');
            }

            trace($logPrefix . print_r($result, true), 'response');
            return $connection->send($result);
        };

        Worker::runAll();
    }

    protected function messageHandler()
    {

    }

    protected function removeRemoteAccountBalanceUpdate(TcpConnection $connection, array $data): string
    {
        $request = new RemoveRemoteAccountBalanceUpdateRequest($data);
        (new RemoveRemoteAccountBalanceUpdate($request))->handler();

        return res_success_text([], '发送成功');
    }

    protected function remoteAccountBalanceUpdate(TcpConnection $connection, array $data): string
    {
        $request = new RemoteAccountBalanceUpdateRequest($data);
        try {
            if (empty($request->request_id)) return res_error_text([], '参数错误');
            $timer_key = '远程账户余额更新' . $request->request_id;
            $timer_time = 10;
            $timeoutRequest = new RemoteAccountBalanceUpdateRequestTimeout([
                'type' => BaseRequest::TypeRemoteAccountBalanceUpdate,
                'timer_key' => $timer_key,
                'time' => time(),
                'user_id' => $request->user_id,
                'trigger_time' => $request->trigger_time,
                'retried_count' => $request->retried_count,
                'retry_count' => $request->retry_count,
                'timer_time' => $timer_time,
            ]);
            $this->AddTimer($timer_key, $timeoutRequest->toArray(), $timer_time);
            trace('远程账户余额更新==>添加定时器完成：' . $request->request_id, '下发指令定时器');
            return res_success_text([], '发送成功');
        } catch (Throwable $e) {
            trace(print_r([
                'msg' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'code' => $e->getCode()
            ], true), '下发指令定时器');
            trace(print_r($e->getTrace(), true), '下发指令定时器');
            trace('远程账户余额更新==>添加定时器失败：' . $request->request_id, '下发指令定时器');
            return res_success_text([], '发送失败');
        }
    }

    protected function removeAutoSettlement(TcpConnection $connection, array $data): string
    {
        trace('删除-自动结算', '下发指令定时器');
        if (empty($data['order_id'])) return res_error_text([], '参数错误');
        $order_id = $data['order_id'];
        $timer_key = '自动结算' . $order_id;
        $this->DelTimer($timer_key);
        trace('删除-自动结算==>完成：' . $order_id, '下发指令定时器');
        return res_success_text([], '发送成功');
    }

    protected function addAutoSettlement(TcpConnection $connection, array $data): string
    {
//        trace('自动结算', '下发指令定时器');
//        if (empty($data['order_id'])) return res_error_text([], '参数错误');
//        $order_id = $data['order_id'];
//        $timer_key = '自动结算' . $order_id;
//        $timer_time = 1200;
//        $add_data = [
//            'type' => '自动结算',
//            'timer_key' => $timer_key,
//            'time' => time(),
//            'order_id' => $order_id,
//            'timer_time' => $timer_time,
//        ];
//        $this->AddTimer($timer_key, $add_data, $timer_time);
//        trace('自动结算==>完成：' . $order_id, '下发指令定时器');
        return res_success_text([], '发送成功');
    }

    protected function addStartChargeAnswerTimeoutTimer(TcpConnection $connection, array $data): string
    {
        trace('运营平台远程控制启机', '下发指令定时器');
        if (empty($data['order_id'])) return res_error_text([], '参数错误');
        $order_id = $data['order_id'];
        $timer_key = '运营平台远程控制启机' . $order_id;
        $timer_time = 60;
        $add_data = [
            'type' => '运营平台远程控制启机',
            'timer_key' => $timer_key,
            'time' => time(),
            'order_id' => $order_id,
            'timer_time' => $timer_time,
        ];
        $this->AddTimer($timer_key, $add_data, $timer_time);
        trace('运营平台远程控制启机==>完成：' . $order_id, '下发指令定时器');
        return res_success_text([], '发送成功');
    }

    protected function removeStartChargeAnswerTimeoutTimer(TcpConnection $connection, array $data): string
    {
        trace('删除-运营平台远程控制启机', '下发指令定时器');
        if (empty($data['order_id'])) return res_error_text([], '参数错误');
        $order_id = $data['order_id'];
        $timer_key = '运营平台远程控制启机' . $order_id;
        $this->DelTimer($timer_key);
        trace('删除-运营平台远程控制启机==>完成：' . $order_id, '下发指令定时器');
        return res_success_text([], '发送成功');
    }

    protected function addStopChargeAnswerTimeoutTimer(TcpConnection $connection, array $data): string
    {
        trace('运营平台远程停机', '下发指令定时器');
        if (empty($data['order_id'])) return res_error_text([], '参数错误');
        $order_id = $data['order_id'];
        $timer_key = '运营平台远程停机-指令' . $order_id;
        $timer_time = 15;
        $add_data = [
            'type' => '运营平台远程停机-指令',
            'timer_key' => $timer_key,
            'time' => time(),
            'order_id' => $order_id,
            'timer_time' => $timer_time,
        ];
        $this->AddTimer($timer_key, $add_data, $timer_time);

        $timer_key2 = '运营平台远程停机-账单' . $order_id;
        $timer_time2 = 40;
        $add_data2 = [
            'type' => '运营平台远程停机-账单',
            'timer_key' => $timer_key,
            'time' => time(),
            'order_id' => $order_id,
            'timer_time' => $timer_time2,
        ];
        $this->AddTimer($timer_key2, $add_data2, $timer_time2);
        trace('运营平台远程停机==>完成：' . $order_id, '下发指令定时器');
        return res_success_text([], '发送成功');
    }

    protected function removeStopChargeAnswerTimeoutTimer(TcpConnection $connection, array $data): string
    {
        trace('删除-运营平台远程停机-指令', '下发指令定时器');
        if (empty($data['order_id'])) return res_error_text([], '参数错误');
        $order_id = $data['order_id'];
        $timer_key = '运营平台远程停机-指令' . $order_id;
        $this->DelTimer($timer_key);
        trace('删除-运营平台远程停机-指令==>完成：' . $order_id, '下发指令定时器');
        return res_success_text([], '发送成功');
    }

    protected function removeTransactionRecordTimeoutTimer(TcpConnection $connection, array $data): string
    {
        trace('删除-运营平台远程停机-账单', '下发指令定时器');
        if (empty($data['order_id'])) return res_error_text([], '参数错误');
        $order_id = $data['order_id'];
        $timer_key = '运营平台远程停机-账单' . $order_id;
        $this->DelTimer($timer_key);
        trace('删除-运营平台远程停机-账单==>完成：' . $order_id, '下发指令定时器');
        return res_success_text([], '发送成功');
    }

    protected function addUploadRealtimeMonitoringDataTimeoutTimer(TcpConnection $connection, array $data): string
    {
        trace('上传实时监测数据', '下发指令定时器');
        if (empty($data['order_id'])) return res_error_text([], '参数错误');
        $order_id = $data['order_id'];
        $timer_key = '上传实时监测数据' . $order_id;
        $timer_time = 40;
        $add_data = [
            'type' => '上传实时监测数据',
            'timer_key' => $timer_key,
            'time' => time(),
            'order_id' => $order_id,
            'timer_time' => $timer_time,
        ];
        $this->AddTimer($timer_key, $add_data, $timer_time);
        trace('上传实时监测数据==>完成：' . $order_id, '下发指令定时器');
        return res_success_text([], '发送成功');
    }

    protected function removeUploadRealtimeMonitoringDataTimeoutTimer(TcpConnection $connection, array $data): string
    {
        trace('删除-上传实时监测数据', '下发指令定时器');
        if (empty($data['order_id'])) return res_error_text([], '参数错误');
        $order_id = $data['order_id'];
        $timer_key = '上传实时监测数据' . $order_id;
        $this->DelTimer($timer_key);
        trace('删除-上传实时监测数据==>完成：' . $order_id, '下发指令定时器');
        return res_success_text([], '发送成功');
    }

    /**
     * 加载下发指令处理定时器
     * lwj 2023.9.12 新增
     * lwj 2023.9.27 修改
     * @return void
     */
    private function LoadTimer(): void
    {
        TimerManager::getInstance()->loadTimer();
    }

    /**
     * 添加下发指令处理定时器
     * lwj 2023.9.12 新增
     * lwj 2023.9.27 修改
     * @param string|int $timer_key
     * @param array $data
     * @param int|null $timeout
     * @return void
     */
    private function AddTimer(string|int $timer_key, array $data, int|null $timeout = null): void
    {
        TimerManager::getInstance()->addTimer($timer_key, $data, $timeout);
    }

    /**
     * 删除下发指令处理定时器
     * lwj 2023.9.12 新增
     * lwj 2023.9.27 修改
     * @param string|int $timer_key
     * @return void
     */
    private function DelTimer(string|int $timer_key): void
    {
        TimerManager::getInstance()->delTimer($timer_key);
    }


}
