<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
declare (strict_types=1);

namespace app\command;

use app\common\model\ElectricityMeterPowerRecord;
use think\console\Input;
use think\console\Output;

/**
 * 删除过期的电表功率记录
 * cbj 2024.01.04 新增
 */
class DeleteExpireElectricityMeterPowerRecord extends BaseCommand
{

    protected function configure(): void
    {
        // 指令配置
        $this->setName('delete-expire-empr')
            ->setDescription('删除过期的电表功率记录');
    }


    protected function execute(Input $input, Output $output): void
    {
        echo (new ElectricityMeterPowerRecord())->delete_expire_record();
    }
}