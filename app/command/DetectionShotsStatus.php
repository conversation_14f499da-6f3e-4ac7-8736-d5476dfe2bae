<?php
/** @noinspection PhpUnused */
/** @noinspection PhpObjectFieldsAreOnlyWrittenInspection */
declare (strict_types=1);

namespace app\command;

use app\common\model\ShotsStatus;
use app\common\queue\MessagePushQueue;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use Throwable;

/**
 * 检测充电枪状态
 * cbj 2023.12.12 新增
 */
class DetectionShotsStatus extends BaseCommand
{
    protected function configure(): void
    {
        // 指令配置
        $this->setName('detection-shots-status')
            ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|status|connections", 'start')
            ->addOption('mode', 'm', Option::VALUE_OPTIONAL, '以守护程序模式运行workerman服务器')
            ->setDescription('检测充电枪状态');
    }

    protected function execute(Input $input, Output $output): void
    {
        try {
            $shotsData = app(ShotsStatus::class)->queryOfflineMoreThanOneHour();

            foreach ($shotsData as $shotData) {
                (app(MessagePushQueue::class))->push(json_encode_cn([
                    'type' => MessagePushQueue::TypeShotsOffline,
                    'shot_id' => $shotData['id'],
                    'status_update_time' => $shotData['status_update_time']
                ]));
            }

        } catch (Throwable $e) {
            trace(print_r([
                'command' => $this->getName(),
                'msg' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ], true), 'error');
            trace(print_r($e->getTrace(), true), 'error');
        }
    }
}
