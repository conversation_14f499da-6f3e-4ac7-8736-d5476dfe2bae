<?php
/** @noinspection PhpObjectFieldsAreOnlyWrittenInspection */
declare (strict_types=1);

namespace app\command;

use Predis\Client as Predis;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

//use think\facade\Db;
use Throwable;
use Workerman\Connection\TcpConnection;

//use Workerman\Protocols\Http\Request;
use Workerman\Http\Client as WorkermanHttp;
use Workerman\Worker;
use Workerman\Lib\Timer;

/**
 * 开始充电定时器进程
 * lwj 2023.9.25 新增
 */
class StartChargingTimer extends BaseCommand
{
    private mixed $redis;

    private array $timer = [];

    protected function configure(): void
    {
        // 指令配置
        $this->setName('StartChargingTimer')
            ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|status|connections", 'start')
            ->addOption('mode', 'm', Option::VALUE_OPTIONAL, '以守护程序模式运行workerman服务器')
            ->setDescription('开始充电定时器');
    }

    protected function execute(Input $input, Output $output): void
    {
        // 指令输出
        $output->writeln('开始');

        $this->initArgv($input);

        $this->redis = new Predis(config('my.redis2'));

        $this->LoadTimer();

        // 在这里放心的实例化worker,
        // 就像参照workerman文档写一样,
        // 无非在workerman的文档里,代码是新建纯php文件,但在这里,写到了一个方法里.
        $worker = new Worker(config('my.StartChargingTimerHost'));

        $worker->name = '开始充电定时器';

        $worker->onMessage = function (TcpConnection $connection, $task_data) {
            try {
                trace('开始充电定时器==>开始', '开始充电定时器');
                trace('开始充电定时器==>数据》》' . $task_data, '开始充电定时器');
                // 假设发来的是json数据
                $data = json_decode($task_data, true);
                if (empty($data['type'])) {
                    trace('开始充电定时器==>参数错误', '开始充电定时器');
                    return $connection->send(res_error_text([], '参数错误'));
                }
                $type = $data['type'];

                switch ($type) {
                    case '接收集中控制器':
                        trace('接收集中控制器', '开始充电定时器');
                        if (empty($data['order_id'])) return $connection->send(res_error_text([], '参数错误'));
                        $order_id = $data['order_id'];
                        $timer_key = '接收集中控制器' . $order_id;
                        $timer_time = 10;
                        $add_data = [
                            'msg_type' => '接收集中控制器-失败',
                            'timer_key' => $timer_key,
                            'time' => time(),
                            'timer_time' => $timer_time,
                            'order_id' => $order_id,
//                            'data' => $data['data'],
//                            'order' => $data['order'],
                            'user_data' => $data['user_data'],
                        ];
                        $this->AddTimer($timer_key, $add_data, $timer_time);
                        trace('接收集中控制器==>完成：' . $order_id, '开始充电定时器');
                        return $connection->send(res_success_text([], '发送成功'));

                    case '删除-接收集中控制器':
                        trace('删除-接收集中控制器', '开始充电定时器');
                        if (empty($data['order_id'])) return $connection->send(res_error_text([], '参数错误'));
                        $order_id = $data['order_id'];
                        $timer_key = '接收集中控制器' . $order_id;
                        $this->DelTimer($timer_key);
                        trace('删除-接收集中控制器==>完成：' . $order_id, '开始充电定时器');
                        return $connection->send(res_success_text([], '发送成功'));

                    case '接收下发功率':
                        trace('接收下发功率', '开始充电定时器');
                        if (empty($data['request_id']) || empty($data['order_id'])) return $connection->send(res_error_text([], '参数错误'));
                        $order_id = $data['order_id'];
                        $request_id = $data['request_id'];
                        $timer_key = '接收下发功率' . $request_id;
                        $timer_time = 10;
                        $add_data = [
                            'msg_type' => '接收下发功率-失败',
                            'order_id' => $order_id,
                            'timer_key' => $timer_key,
                            'time' => time(),
                            'timer_time' => $timer_time,
                            'data' => $data['data'],
                            'order' => $data['order'],
                            'user_data' => $data['user_data'],
                        ];
                        $this->AddTimer($timer_key, $add_data, $timer_time);
                        trace('接收下发功率==>完成：' . $order_id, '开始充电定时器');
                        return $connection->send(res_success_text([], '发送成功'));

                    case '删除-接收下发功率':
                        trace('删除-接收下发功率', '开始充电定时器');
                        if (empty($data['request_id'])) return $connection->send(res_error_text([], '参数错误'));
                        $request_id = $data['request_id'];
                        $timer_key = '接收下发功率' . $request_id;
                        $this->DelTimer($timer_key);
                        trace('删除-接收下发功率==>完成：' . $request_id, '开始充电定时器');
                        return $connection->send(res_success_text([], '发送成功'));

                    default:
                        return $connection->send(res_error_text([], '未知消息'));
                }

            } catch (Throwable $e) {
                trace('消息处理出现异常=》' . json_encode_cn($e->getMessage()), '开始充电定时器');
                return $connection->send(res_error_text([], '消息处理出现异常'));
            }


        };

        Worker::runAll();
    }

    /**
     * 加载下发指令处理定时器
     * lwj 2023.9.12 新增
     * lwj 2023.9.19 修改
     * @return void
     */
    private function LoadTimer(): void
    {
        $timer_list = $this->redis->hgetall('开始充电定时器-列表');
        if (!$timer_list) {
            echo '开始充电定时器列表不存在' . PHP_EOL;
            return;
        }

        foreach ($timer_list as $k => $v) {
            $vv = json_decode($v, true);
            $timer_time = $vv['timer_time'] ?? config('my.send_handle_timer_timeout');
            $remainder = time() - $vv['time'] - $timer_time;
            if ($remainder < 0) {
                $this->AddTimer($k, $vv, abs($remainder));
            } else {
                $this->redis->hdel('开始充电定时器-列表', $k);
            }
        }
    }

    /**
     * 添加开始充电定时器
     * lwj 2023.9.25 新增
     * @param string|int $timer_key
     * @param array $data
     * @param int|null $timeout
     * @return void
     */
    private function AddTimer(string|int $timer_key, array $data, int|null $timeout = null): void
    {
        trace('添加定时器：开始：' . $timer_key, '开始充电定时器');
        if (isset($this->timer[$timer_key])) {
            Timer::del($this->timer[$timer_key]);
            unset($this->timer[$timer_key]);
            trace('存在定时器，先删除：' . $timer_key, '开始充电定时器');
        }

        if (!$timeout) $timeout = config('my.send_handle_timer_timeout');
        trace('定时器时间：' . $timeout, '开始充电定时器');
        $this->timer[$timer_key] = Timer::add($timeout, function () use ($data, $timer_key) {
            //Todo 待写 超时要做什么
            trace('执行定时器:数据==>' . json_encode_cn($data), '开始充电定时器');
            $this->send_start_charging_job_asyn($data);
//            send_start_charging_job($data);
            $this->redis->hdel('开始充电定时器-列表', $timer_key);
            unset($this->timer[$timer_key]);
            trace('定时器执行：完==>' . $timer_key, '开始充电定时器');
        }, '', false);

        $data['timeout'] = $timeout;
        $this->redis->hset('开始充电定时器-列表', $timer_key, json_encode_cn($data));
        trace('添加定时器：结束==>' . $timer_key, '开始充电定时器');
    }

    /**
     * 删除开始充电定时器
     * lwj 2023.9.25 新增
     * @param string|int $timer_key
     * @return void
     */
    private function DelTimer(string|int $timer_key): void
    {
        if (isset($this->timer[$timer_key])) {
            Timer::del($this->timer[$timer_key]);
            unset($this->timer[$timer_key]);
            trace('删除定时器==>' . $timer_key, '开始充电定时器');
        }
        $this->redis->hdel('开始充电定时器-列表', $timer_key);
    }

    /**
     * 发送到开始充电任务（异步）
     * lwj 2023.10.7 新增
     * @param array $data
     * @return void
     */
    private function send_start_charging_job_asyn(array $data = []): void
    {
        try {
            $url = config('my.StartChargingJobApi');
            $options = [
                'max_conn_per_addr' => 1280, // 每个域名最多维持多少并发连接
                'keepalive_timeout' => 15,  // 连接多长时间不通讯就关闭
                'connect_timeout' => 15,  // 连接超时时间
                'timeout' => 120,  // 请求发出后等待响应的超时时间
            ];
            $http = new WorkermanHttp($options);
            $jsonData = json_encode($data);
            $http->request($url, [
                'method' => 'POST',
                'version' => '1.1',
                'headers' => ['Content-type' => 'application/json'],
                'data' => $jsonData,
                'success' => function ($response) use ($url) {
                    trace('发送到开始充电任务（异步）：' . $url . '，成功结果==》》' . $response->getBody(), '开始充电定时器');
                },
                'error' => function ($exception) use ($url) {
                    trace('发送到开始充电任务（异步）：' . $url . '，失败结果==》》' . json_encode_cn($exception), '开始充电定时器');
                }
            ]);
        } catch (Throwable $e) {
            trace('发送到开始充电任务：失败结果==》》' . $e->getMessage(), '开始充电定时器');
        }
    }


}
