<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
declare (strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\common\model\OrderClearing;
use app\common\model\Order;
use think\facade\Db;
use Throwable;

/**
 * 统计分成订单
 * 每天 0:10 执行
 * lwj 2024.5.11 新增
 */
class StatisticsClearingOrder extends Command
{

    protected function configure(): void
    {
        // 指令配置
        $this->setName('StatisticsClearingOrder')
            ->setDescription('统计分成订单');
    }

    protected function execute(Input $input, Output $output): void
    {
        try {
            $bill_date = date('Y-m-d',strtotime('-1 days'));
            $order = new Order();
            $order_clearing = new OrderClearing();

            $order_list=$order->with(['clearing' => function($query) {
                    $query->field('id,station_id,corp_id,ratio_electricity_price,ratio_ser_price');
                }])
//                ->where('station_id',1013)
                ->where('clearing_status',1)
                ->whereIn('status',[5,10])
//                ->clearing()
                ->where('trans_end_time >= "' . $bill_date . ' 00:00:00"')
                ->where('trans_end_time <= "' . $bill_date . ' 23:59:59"')
//                ->limit(10)
                ->field('id,station_id,corp_id,clearing_electricity_price,clearing_ser_price,trans_end_time')
                ->select()
                ->toArray();
            echo '开始统计分成订单:'.json_encode($order_list).PHP_EOL;

            $clearing_order=[];

            Db::startTrans();
            try {
                foreach ($order_list as &$v) {
                    if(empty($v['clearing'])){
                        trace('统计分成订单=》无分成信息，跳过：' . $v['id'], '信息');
                        continue;
                    }
                    foreach ($v['clearing'] as &$vv) {
                        $vv['clearing_electricity_price']=intval($v['clearing_electricity_price']*$vv['ratio_electricity_price']/100);
                        $vv['clearing_ser_price']=intval($v['clearing_ser_price']*$vv['ratio_ser_price']/100);
                    }

                    $clearing_electricity_price = array_column($v['clearing'], 'clearing_electricity_price');
                    $clearing_electricity_price_sum = array_sum($clearing_electricity_price);
                    $electricity_price_diff = $v['clearing_electricity_price']-$clearing_electricity_price_sum;
                    if($electricity_price_diff !== 0){
                        $clearing_electricity_price_max = max($clearing_electricity_price);
                        $clearing_electricity_price_keys = array_keys($clearing_electricity_price, $clearing_electricity_price_max);
                        $v['clearing'][$clearing_electricity_price_keys[0]]['clearing_electricity_price'] += $electricity_price_diff;
                    }

                    $clearing_ser_price = array_column($v['clearing'], 'clearing_ser_price');
                    $clearing_ser_price_sum = array_sum($clearing_ser_price);
                    $ser_price_diff = $v['clearing_ser_price']-$clearing_ser_price_sum;
                    if($ser_price_diff !== 0){
                        $clearing_ser_price_max = max($clearing_ser_price);
                        $clearing_ser_price_keys = array_keys($clearing_ser_price, $clearing_ser_price_max);
                        $v['clearing'][$clearing_ser_price_keys[0]]['clearing_ser_price'] += $ser_price_diff;
                    }

                    foreach ($v['clearing'] as $vvv) {
                        $clearing_order[]=[
                            'order_id'=>$v['id'],
                            'station_id'=>$vvv['station_id'],
                            'corp_id'=>$vvv['corp_id'],
                            'clearing_electricity_price'=>$vvv['clearing_electricity_price'],
                            'clearing_ser_price'=>$vvv['clearing_ser_price'],
//                            'clearing_time'=>date('Y-m-d H:i:s'),
                            'order_time'=>$v['trans_end_time'],
                            'bill_date'=>$bill_date,
                            'order_clearing_electricity_price'=>$v['clearing_electricity_price'],
                            'order_clearing_ser_price'=>$v['clearing_ser_price'],
                            'ratio_electricity_price'=>$vvv['ratio_electricity_price'],
                            'ratio_ser_price'=>$vvv['ratio_ser_price'],
                        ];
                    }
                    $order->where('id',$v['id'])->update(['clearing_status'=>2]);
                }

                $res=$order_clearing->insertAll($clearing_order);
                Db::commit();
                trace('统计分成订单=》完成：' . $res, '信息');
                echo '统计分成订单=》完成：' . $res.PHP_EOL;
                return ;
            } catch (Throwable $e) {
                Db::rollback();
                trace('统计分成订单，回滚数据=》异常：' . $e->getMessage(), '信息');
                return;
            }

        } catch (Throwable $e) {
            trace('统计分成订单=》异常：' . $e->getMessage(), '信息');
            return ;
        }
    }

}
