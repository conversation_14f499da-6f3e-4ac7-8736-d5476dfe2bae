<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
declare (strict_types=1);

namespace app\command;

use think\console\Input;
use think\console\Output;
use app\common\model\Order;
use think\facade\Db;

/**
 * 打印充电中的订单
 * cbj 2023.11.14 新增
 */
class PrintChargingOrders extends BaseCommand
{

    protected function configure(): void
    {
        // 指令配置
        $this->setName('print-charging-orders')
            ->setDescription('打印充电中的订单');
    }


    protected function execute(Input $input, Output $output): void
    {
        $field = [
            'o.id', 'o.user_id', 'u.phone', 'c.name as corp_name', 's.name as station_name',
            'p.name as piles_name', 'sh.name as shot_name',
            'o.amount_charged', 'o.electricity_charged', 'o.create_time',
            'o.sharp_electricity', 'o.peak_electricity', 'o.flat_electricity',
            'o.valley_electricity'
        ];
        $cursor = Db::table('order')->alias('o')
            ->leftJoin('corp c', 'c.id = o.corp_id')
            ->leftJoin('stations s', 's.id = o.station_id')
            ->leftJoin('piles p', 'p.id = o.piles_id')
            ->leftJoin('shots sh', 'sh.id = o.shot_id')
            ->leftJoin('users u', 'u.id = o.user_id')
            ->field($field)
            ->where('o.status', Order::StatusCharging)
            ->cursor();
        echo PHP_EOL;
        echo '---------------------------------------------------------' . PHP_EOL;
        echo Date("Y-m-d H:i:s") . PHP_EOL;
        foreach ($cursor as $order) {
            echo sprintf("订单号: %s, 用户ID: %s, 用户手机号: %s",
                    $order['id'], $order['user_id'], $order['phone']) . PHP_EOL;
            echo sprintf("运营商: %s, 充电站: %s, 充电桩: %s, 充电枪: %s",
                    $order['corp_name'], $order['station_name'], $order['piles_name'], $order['shot_name']) . PHP_EOL;
            echo sprintf('充电已产生费用: %s, 充电已消耗电量: %s, 创建时间: %s',
                    $order['amount_charged'], $order['electricity_charged'], $order['create_time']) . PHP_EOL;
            echo sprintf('尖时电量: %s, 峰时电量: %s, 平时电量: %s, 谷时电量: %s',
                    $order['sharp_electricity'], $order['peak_electricity'], $order['flat_electricity'], $order['valley_electricity']) . PHP_EOL;
        }
    }
}