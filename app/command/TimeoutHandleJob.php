<?php
/** @noinspection PhpUnusedParameterInspection */
/** @noinspection PhpObjectFieldsAreOnlyWrittenInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpConditionAlreadyCheckedInspection */
declare (strict_types=1);

namespace app\command;

use app\common\lib\charging\request\TRATimeoutSettlementRequest;
use app\common\lib\charging\TRATimeoutSettlement;
use app\common\lib\ExceptionLogCollector;
use app\common\lib\SendApplet;
use app\common\lib\timeout_handle_job\request\RemoteAccountBalanceUpdateRequest;
use app\common\model\Order;
use app\common\queue\package\user_balance_change\BasePackage;
use app\common\queue\package\user_balance_change\UpdateChargingStationBalanceRetryPackage;
use app\common\queue\UserBalanceChangeQueue;
use app\event\ChargeAbnormalEndEvent;
use Predis\Client as Predis;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\db\exception\DbException;
use think\facade\Db;
use Throwable;
use Workerman\Connection\TcpConnection;
use Workerman\Worker;

/**
 * 异步执行超时处理任务进程
 * lwj 2023.9.12 新增
 * lwj 2023.9.27 修改
 */
class TimeoutHandleJob extends BaseCommand
{

    private mixed $redis;

//    private mixed $redis2;

    protected function configure(): void
    {
        // 指令配置
        $this->setName('TimeoutHandleJob')
            ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|status|connections", 'start')
            ->addOption('mode', 'm', Option::VALUE_OPTIONAL, '以守护程序模式运行workerman服务器')
            ->setDescription('超时处理任务');
    }

    protected function execute(Input $input, Output $output): void
    {
        // 指令输出
        $output->writeln('开始');

        $this->initArgv($input);

//        $this->redis = new Predis(config('my.redis5'));
//        $this->redis2 = new Predis(config('my.redis2'));

        // 在这里放心的实例化worker,
        // 就像参照workerman文档写一样,
        // 无非在workerman的文档里,代码是新建纯php文件,但在这里,写到了一个方法里.
        $worker = new Worker(config('my.TimeoutHandleJobHost'));

        // task进程数可以根据需要多开一些
        $worker->name = '超时处理任务';

        $worker->onMessage = function (TcpConnection $connection, $task_data) {
            $logPrefix = '超时处理任务 - ';
            try {
                trace(str_repeat('-', 40), 'request');
                trace($logPrefix . '参数 = ' . print_r($task_data, true), 'request');

                // 假设发来的是json数据
                $data = json_decode($task_data, true);
                if (empty($data['type'])) {
                    $result = res_error_text([], '参数错误');
                } else {
                    $type = $data['type'];

                    $result = match ($type) {
                        '运营平台远程控制启机' => $this->startChargeCommand($connection, $data),
                        '运营平台远程停机-指令' => $this->stopChargeCommand($connection, $data),
                        '运营平台远程停机-账单' => $this->transactionRecordAnswerTimeout($connection, $data),
                        '上传实时监测数据' => $this->uploadRealtimeMonitoringData($connection, $data),
                        '自动结算' => $this->autoSettlement($connection, $data),
                        '远程账户余额更新' => $this->remoteAccountBalanceUpdate($connection, $data),
                        default => res_error_text([], '未知消息'),
                    };
                }
            } catch (Throwable $e) {
                trace($logPrefix . print_r([
                        'msg' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'code' => $e->getCode()
                    ], true), 'error');

                trace($logPrefix . print_r($e->getTrace(), true), 'error');
                return res_error_text([], '消息处理出现异常');
            }

            trace($logPrefix . print_r($result, true), 'response');

            return $connection->send($result);

        };

        Worker::runAll();
    }

    protected function remoteAccountBalanceUpdate(TcpConnection $connection, array $data): string
    {
        $request = new RemoteAccountBalanceUpdateRequest($data);

        if ($request->retried_count >= $request->retry_count) return res_success_text([], '重试结束');

        $request->retried_count++;
        $package = new UpdateChargingStationBalanceRetryPackage([
            'type' => BasePackage::TypeUpdateChargingStationBalanceRetry
            , 'user_id' => $request->user_id
            , 'trigger_time' => $request->trigger_time
            , 'retry_count' => $request->retry_count
            , 'retried_count' => $request->retried_count
        ]);
        try {
            app(UserBalanceChangeQueue::class)->push($package->encode());
            return res_success_text([], '发送成功');
        } catch (Throwable $e) {
            trace(print_r([
                'msg' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'code' => $e->getCode()
            ], true), '超时处理任务');
            trace(print_r($e->getTrace(), true), '超时处理任务');
            return res_success_text([], '异常 - ' . $e->getMessage());
        }
    }

    protected function autoSettlement(TcpConnection $connection, array $data): string
    {
        try {
//            $TRATimeoutSettlement = new TRATimeoutSettlement(
//                new TRATimeoutSettlementRequest($data)
//            );
//            $TRATimeoutSettlement->run();

            return res_success_text([], '发送成功');
        } catch (DbException $e) {
            ExceptionLogCollector::collect($e);
            return res_success_text([], '异常 - ' . $e->getMessage());
        }
    }

    protected function startChargeCommand(TcpConnection $connection, array $data): string
    {
        trace('运营平台远程控制启机==>开始', '超时处理任务');
        if (empty($data['order_id'])) return res_error_text([], '参数错误');
        $order_id = $data['order_id'];

        $order = Db::name('order')
            ->where('id', $order_id)
            ->find();
        if (empty($order)) return res_error_text([], '找不到订单');
        if ($order['status'] >= 3 && $order['status'] !== Order::StatusReservation) {
            SendApplet::send_uid($order['user_id'], [
                'type' => '订单状态-运营平台远程控制启机',
                'result' => 'error',
                'msg' => '设备已启动',
                'order_id' => $order_id,
                'status' => $order['status'],
            ]);
            return res_error_text([], '设备已启动');
        }

        $orderContext = app(\app\common\context\Order::class);
        $status = $orderContext->getOrderStatus($order_id);
//        $status = $this->redis->get('运营平台远程控制启机-状态-' . $order_id);
        if ($status != '创建订单') {
            SendApplet::send_uid($order['user_id'], [
                'type' => '运营平台远程控制启机-状态',
                'result' => 'error',
                'msg' => '状态已经不是创建订单',
                'order_id' => $order_id,
                'status' => $status,
            ]);
            return (res_error_text([], '状态已经不是创建订单'));
        }

        $res = app(Order::class)->updateStatus($order_id, Order::StatusAbnormal, '【运营平台远程控制启机】指令超时');

        try {
            event('ChargeAbnormalEnd', new ChargeAbnormalEndEvent([
                'abnormal_reason' => ChargeAbnormalEndEvent::ABNORMAL_REASON_START_CHARGE_TIMEOUT,
                'order_id' => $order_id,
                'corp_id' => $order['corp_id'],
                'station_id' => $order['station_id'],
                'pay_money' => 0,
                'electricity_total' => 0
            ]));
        } catch (Throwable $e) {
            // 在事件中运行的业务都是次要业务，若是出现异常情况，
            // 只记录日志，不做其他处理，避免影响充电主流程。
            ExceptionLogCollector::collect($e);
        }


        SendApplet::send_uid($order['user_id'], [
            'type' => '运营平台远程控制启机-失败',
            'result' => 'error',
            'msg' => '指令超时,已设置订单状态为6，订单结束',
            'order_id' => $order_id,
        ]);
        trace('运营平台远程控制启机==>结束：' . $order_id . '，更新结果' . json_encode_cn($res), '超时处理任务');
        return res_success_text([], '发送成功');
    }

    protected function stopChargeCommand(TcpConnection $connection, array $data): string
    {
        trace('运营平台远程停机-指令==>开始', '超时处理任务');
//        if (empty($data['order_id'])) return $connection->send(res_error_text([], '参数错误'));
//        $order_id = $data['order_id'];
//
//        $order = Db::name('order')
//            ->where('id', $order_id)
//            ->find();
//        if (empty($order)) return $connection->send(res_error_text([], '找不到订单'));
//        if ($order['status'] >= 5) {
//            SendApplet::send_uid($order['user_id'], [
//                'type' => '订单已结束-运营平台远程停机-指令',
//                'result' => 'error',
//                'msg' => '订单已结束',
//                'order_id' => $order_id,
//                'status' => $order['status'],
//            ]);
//            return $connection->send(res_error_text([], '订单已结束'));
//        }
//
//        SendApplet::send_uid($order['user_id'], [
//            'type' => '指令超时-运营平台远程停机-指令',
//            'result' => 'error',
//            'msg' => '指令超时-运营平台远程停机-指令',
//            'order_id' => $order_id,
//        ]);
//        trace('运营平台远程停机-指令==>结束：' . $order_id, '超时处理任务');
        return res_success_text([], '发送成功');
    }

    /**
     * 交易记录超时应答
     *
     * @param TcpConnection $connection
     * @param array $data
     * @return string
     */
    protected function transactionRecordAnswerTimeout(TcpConnection $connection, array $data): string
    {
        trace('运营平台远程停机-账单==>开始', '超时处理任务');
        if (empty($data['order_id'])) return res_error_text([], '参数错误');

        try {
            $TRATimeoutSettlement = new TRATimeoutSettlement(
                new TRATimeoutSettlementRequest($data)
            );
            $TRATimeoutSettlement->run();

            return res_success_text([], '发送成功');
        } catch (DbException $e) {
            ExceptionLogCollector::collect($e);
            return res_success_text([], '异常 - ' . $e->getMessage());
        }
    }

    protected function uploadRealtimeMonitoringData(TcpConnection $connection, array $data): string
    {
//        trace('上传实时监测数据==>开始：', '超时处理任务');
//        if (empty($data['order_id'])) return $connection->send(res_error_text([], '参数错误'));
//        $order_id = $data['order_id'];
//
//        $order = Db::name('order')
//            ->where('id', $order_id)
//            ->find();
//        if (empty($order)) return $connection->send(res_error_text([], '找不到订单'));
//        if ($order['status'] >= 5) {
//            SendApplet::send_uid($order['user_id'], [
//                'type' => '订单已结束-上传实时监测数据',
//                'result' => 'error',
//                'msg' => '订单已结束',
//                'order_id' => $order_id,
//                'status' => $order['status'],
//            ]);
//            return $connection->send(res_error_text([], '订单已结束'));
//        }
//
//        SendApplet::send_uid($order['user_id'], [
//            'type' => '回传超时-上传实时监测数据',
//            'result' => 'error',
//            'msg' => '回传超时-上传实时监测数据',
//            'order_id' => $order_id,
//        ]);
//        trace('上传实时监测数据==>结束：' . $order_id, '超时处理任务');
        return res_success_text([], '发送成功');
    }
}
