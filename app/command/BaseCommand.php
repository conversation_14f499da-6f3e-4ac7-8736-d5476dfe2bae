<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpObjectFieldsAreOnlyWrittenInspection */
declare (strict_types=1);

namespace app\command;

use app\common\log\SocketLogCollector;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\DbManager;
use Workerman\Worker;

/**
 * 基础命令类
 * cbj 2023.10.31 新增
 */
class BaseCommand extends Command
{
    protected function execute(Input $input, Output $output): void
    {
        $beginTime = microtime(true);
        $socketLogCollector = new SocketLogCollector();
        $this->listenerSql($socketLogCollector);

        try {
            $this->handler($input, $output, $socketLogCollector);
        } catch (\Throwable $exception) {
            $socketLogCollector->collect($exception);
        }

        $this->recordRequestLog($input, $output, $beginTime, $socketLogCollector);
    }

    protected function handler(Input $input, Output $output, SocketLogCollector $socketLogCollector): void
    {

    }

    protected function listenerSql(SocketLogCollector $socketLogCollector): void
    {
        $DbManager = app(DbManager::class);
        $DbManager->listen(function ($sql, $time, $master) use ($socketLogCollector) {
            if (str_starts_with($sql, 'CONNECT:')) {
                $socketLogCollector->collectorRunLog($sql, SocketLogCollector::LevelSql);

                return;
            }

            // 记录SQL
            if (is_bool($master)) {
                // 分布式记录当前操作的主从
                $master = $master ? 'master|' : 'slave|';
            } else {
                $master = '';
            }

            $socketLogCollector->collectorRunLog($sql . ' [ ' . $master . 'RunTime:' . $time . 's ]', SocketLogCollector::LevelSql);
        });


    }


    protected function recordRequestLog(
        Input              $input,
        Output             $output,
        float              $beginTime,
        SocketLogCollector $socketLogCollector
    ): void
    {
        $log = [];
        $log[] = '---------------------------------------------------------------------------------';
        $log[] = sprintf('[运行时间=%fs | 指令=%s]',
            (microtime(true) - $beginTime),
            $this->getName(),
        );

        $log[] = sprintf('指令参数=%s', print_r($input->getArguments(), true));
        $log[] = sprintf('指令选项=%s', print_r($input->getOptions(), true));

        foreach ($log as $value) {
            $socketLogCollector->collectorRequestLog($value, SocketLogCollector::LevelRequest);
        }

        $socketLogCollector->write();
    }

    public function initArgv(Input $input): void
    {
        Worker::$pidFile = app()->getRuntimePath() . 'pid' . DIRECTORY_SEPARATOR . $this->getName() . '.pid';

        $action = $input->getArgument('action');
        $mode = $input->getOption('mode');

        // 重新构造命令行参数,以便兼容workerman的命令
        global $argv;

        $argv = [];

        array_unshift($argv, 'think', $action);
        if ($mode == 'd') {
            $argv[] = '-d';
        } else if ($mode == 'g') {
            $argv[] = '-g';
        }
    }
}
