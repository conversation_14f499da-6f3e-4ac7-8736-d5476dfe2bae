<?php
/** @noinspection PhpUnused */
/** @noinspection PhpObjectFieldsAreOnlyWrittenInspection */
declare (strict_types=1);

namespace app\command;

use app\common\lib\transfer_service\TransferServiceClientWrite;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use Workerman\Worker;

/**
 * 发送协议包到中转服务的消费者
 * cbj 2023.12.26 新增
 */
class TransferServiceWriteQueueConsumer extends BaseCommand
{
    protected function configure(): void
    {
        // 指令配置
        $this->setName('transfer-service-write-queue-consumer')
            ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|status|connections", 'start')
            ->addOption('mode', 'm', Option::VALUE_OPTIONAL, '以守护程序模式运行workerman服务器')
            ->setDescription('发送协议包到中转服务的消费者');
    }

    protected function execute(Input $input, Output $output): void
    {
        // 指令输出
        $output->writeln('开始');

        $this->initArgv($input);


        $worker = new Worker();

        $worker->name = '发送协议包到中转服务的消费者';
        $worker->count = 1;


        $worker->onWorkerStart = function () {

            $transferServiceQueue = app(TransferServiceClientWrite::class);
            $transferServiceQueue->run();

        };

        Worker::runAll();
    }
}
