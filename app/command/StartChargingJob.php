<?php
/** @noinspection PhpUnusedParameterInspection */
/** @noinspection PhpObjectFieldsAreOnlyWrittenInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpConditionAlreadyCheckedInspection */
declare (strict_types=1);

namespace app\command;

use app\common\lib\charging\command\request\StartChargeCommandRequest;
use app\common\lib\charging\command\StartChargeCommand;
use app\common\lib\charging\request\UserData;
use app\common\lib\ExceptionLogCollector;
use app\common\lib\SendAdmin;
use app\common\lib\SendApplet;
use app\common\model\Order;
use app\event\ChargeAbnormalEndEvent;
use send\SendGetApi;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;
use Throwable;
use Workerman\Connection\AsyncTcpConnection;
use Workerman\Connection\TcpConnection;
use Workerman\Protocols\Http\Request;
use Workerman\Worker;

/**
 * 开始充电任务进程
 * lwj 2023.9.25 新增
 * lwj 2023.10.16 修改
 */
class StartChargingJob extends BaseCommand
{

    protected string $logPrefix = '开始充电任务 - ';

    protected function configure(): void
    {
        // 指令配置
        $this->setName('StartChargingJob')
            ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|status|connections", 'start')
            ->addOption('mode', 'm', Option::VALUE_OPTIONAL, '以守护程序模式运行workerman服务器')
            ->setDescription('开始充电任务');
    }

    protected function execute(Input $input, Output $output): void
    {
        // 指令输出
        $output->writeln('开始');

        $this->initArgv($input);

        // 在这里放心的实例化worker,
        // 就像参照workerman文档写一样,
        // 无非在workerman的文档里,代码是新建纯php文件,但在这里,写到了一个方法里.
        $worker = new Worker(config('my.StartChargingJobHost'));

        // task进程数可以根据需要多开一些
        $worker->count = 4;
        $worker->name = '开始充电任务';

        $worker->onMessage = function (TcpConnection $connection, Request $request) {
            try {
                $data = $request->post();
                trace(str_repeat('-', 40), 'request');
                trace($this->logPrefix . '参数 = ' . print_r($data, true), 'request');

                if (empty($data['order_id']) || empty($data['msg_type']) || empty($data['user_data'])) {
                    $result = res_error_text([], '参数错误');
                } else {
                    $order_id = $data['order_id'];
                    $user_data = $data['user_data'];

                    $field = [
                        'o.id'
                        , 'o.type'
                        , 'o.corp_id'
                        , 'o.station_id'
                        , 'o.piles_id'
                        , 'o.sequence'
                        , 'o.shot_id'
                        , 'o.freeze_money'
                        , 'o.user_id'
                        , 'o.freeze_status'
                        , 'o.status'
                        , 'u.balance'
                        , 'u.credit_limit'
                        , 'u.amount_charged'
                        , 'o.reservation_time'
                        , 'o.discount'
                    ];

                    $order = Db::name('order')->alias('o')
                        ->leftJoin('users u', 'u.id = o.user_id')
                        ->where('o.id', $order_id)
                        ->field($field)
                        ->find();
                    if (empty($order)) {
                        $result = res_error_text([], '找不到订单');
                    } else {
                        // , '(u.balance + u.credit_limit - u.amount_charged) as balance'
                        $order['balance'] += $order['credit_limit'];
                        $order['balance'] -= (int)($order['amount_charged'] / 100);

                        $result = match ($data['msg_type']) {
                            '开始充电-开始' => $this->startCharge($data, $user_data, $order),
                            '集中控制器充电桩开启功率应答' => $this->chargingPilePowerOnResponse($data, $user_data, $order),
                            '充电桩工作参数设置应答' => $this->chargingStationParameterSettingResponse($data, $user_data, $order),
                            '接收集中控制器-失败' => $this->chargingPilePowerOnResponseTimeout($data, $user_data, $order),
                            '接收下发功率-失败' => $this->chargingStationParameterSettingResponseTimeout($data, $user_data, $order),
                            default => res_error_text([], '参数错误'),
                        };
                    }
                }
            } catch (Throwable $e) {
                trace($this->logPrefix . print_r([
                        'msg' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'code' => $e->getCode()
                    ], true), 'error');
                trace($this->logPrefix . print_r($e->getTrace(), true), 'error');
                $result = res_error_text([], '开始充电任务：异常');
            }

            trace($this->logPrefix . print_r($result, true), 'response');

            return $connection->send($result);
        };

        Worker::runAll();
    }

    protected function chargingStationParameterSettingResponseTimeout(array $data, array $user_data, array $order): string
    {
        $order_id = $data['order_id'];
        $res3 = Db::name('order')
            ->where('id', $order_id)
            ->update([
                'status' => Order::StatusAbnormal,
                'msg' => '接收下发功率-失败',
                'update_time' => date('Y-m-d H:i:s'),
            ]);
//                        $update = new Update();
//                        $res4 = $update->unfreeze_user_balance_and_pay_order($order['user_id'], 0, $order['id'], $order['freeze_money']);
        trace('接收下发功率-失败：写入订单结果=》' . json_encode_cn($res3), '开始充电任务');

        echo get_date(2) . $order_id . '==》接收下发功率-失败==>写入订单结果：' . json_encode_cn($res3) . PHP_EOL;

        try {
            event('ChargeAbnormalEnd', new ChargeAbnormalEndEvent([
                'abnormal_reason' => ChargeAbnormalEndEvent::ABNORMAL_REASON_SET_PILE_OUTPUT_POWER_TIMEOUT,
                'order_id' => $order_id,
                'corp_id' => $order['corp_id'],
                'station_id' => $order['station_id'],
                'pay_money' => 0,
                'electricity_total' => 0
            ]));
        } catch (Throwable $e) {
            // 在事件中运行的业务都是次要业务，若是出现异常情况，
            // 只记录日志，不做其他处理，避免影响充电主流程。
            ExceptionLogCollector::collect($e);
        }

        $msg_data = [
            'type' => '运营平台远程控制启机-失败',
            'result' => 'error',
            'msg' => '接收下发功率-失败',
            'order_id' => $order_id,
        ];
        switch ($user_data['type']) {
            case '后台':
                SendAdmin::send_group($user_data['token'], $msg_data);
                break;
            case '小程序':
                SendApplet::send_uid($order['user_id'], $msg_data);
                break;
        }
        return res_success_text([], '接收下发功率-失败-完成');
    }

    protected function chargingPilePowerOnResponseTimeout(array $data, array $user_data, array $order): string
    {
        $order_id = $data['order_id'];
        $res3 = Db::name('order')
            ->where('id', $order_id)
            ->update([
                'status' => Order::StatusAbnormal,
                'msg' => '接收集中控制器-失败',
                'update_time' => date('Y-m-d H:i:s'),
            ]);
//                        $update = new Update();
//                        $res4 = $update->unfreeze_user_balance_and_pay_order($order['user_id'], 0, $order['id'], $order['freeze_money']);
        trace('接收集中控制器-失败：写入订单结果=》' . json_encode_cn($res3), '开始充电任务');
        echo get_date(2) . $order_id . '==》接收集中控制器-失败==>写入订单结果：' . json_encode_cn($res3) . PHP_EOL;

        try {
            event('ChargeAbnormalEnd', new ChargeAbnormalEndEvent([
                'abnormal_reason' => ChargeAbnormalEndEvent::ABNORMAL_REASON_GET_PILE_POWER_TIMEOUT,
                'order_id' => $order_id,
                'corp_id' => $order['corp_id'],
                'station_id' => $order['station_id'],
                'pay_money' => 0,
                'electricity_total' => 0
            ]));
        } catch (Throwable $e) {
            // 在事件中运行的业务都是次要业务，若是出现异常情况，
            // 只记录日志，不做其他处理，避免影响充电主流程。
            ExceptionLogCollector::collect($e);
        }

        $msg_data = [
            'type' => '运营平台远程控制启机-失败',
            'result' => 'error',
            'msg' => '接收集中控制器-失败',
            'order_id' => $order_id,
        ];
        switch ($user_data['type']) {
            case '后台':
                SendAdmin::send_group($user_data['token'], $msg_data);
                break;
            case '小程序':
                SendApplet::send_uid($order['user_id'], $msg_data);
                break;
        }
        return res_success_text([], '接收集中控制器-失败-完成');
    }

    protected function chargingStationParameterSettingResponse(array $data, array $user_data, array $order): string
    {
        $order_id = $data['order_id'];
        // 向 开始充电定时器进程 发送任务：删除-接收下发功率
        $this->send_start_charging_timer([
            'type' => '删除-接收下发功率',
            'request_id' => $data['request_id'],
        ]);

        // 如果 充电桩工作参数设置 设置成功
        if (!empty($data['data']['setting_result']) && $data['data']['setting_result'] === 1) {

            // 发送开始充电指令
            // 这里返回的状态只是对端是否接收成功指令，执行实际执行结果会有额外的回调。
            $res3 = $this->send_start_charging($order, $user_data);

            if ($res3) {
                // 向 小程序Socket客户端 发送消息：运营平台远程控制启机-成功，发起充电成功
                $msg_data = [
                    'type' => '运营平台远程控制启机-成功',
                    'result' => 'success',
                    'msg' => '发起充电成功',
                    'order_id' => $order_id,
                ];
            } else {
                // 更新订单数据：订单状态=90，描述=发起充电失败
                $res2 = Db::name('order')
                    ->where('id', $order_id)
                    ->update([
                        'status' => Order::StatusAbnormal,
                        'msg' => '发起充电失败',
                        'update_time' => date('Y-m-d H:i:s'),
                    ]);

//                                $update = new Update();
//                                $res3 = $update->unfreeze_user_balance_and_pay_order($order['user_id'], 0, $order['id'], $order['freeze_money']);
                trace('发起充电失败：写入订单结果=》' . json_encode_cn($res2), '开始充电任务');
                echo get_date(2) . $order_id . '==》发起充电失败==>写入订单结果：' . json_encode_cn($res2) . PHP_EOL;

                try {
                    event('ChargeAbnormalEnd', new ChargeAbnormalEndEvent([
                        'abnormal_reason' => ChargeAbnormalEndEvent::ABNORMAL_REASON_START_CHARGE_API_FAILED,
                        'order_id' => $order_id,
                        'corp_id' => $order['corp_id'],
                        'station_id' => $order['station_id'],
                        'pay_money' => 0,
                        'electricity_total' => 0
                    ]));
                } catch (Throwable $e) {
                    // 在事件中运行的业务都是次要业务，若是出现异常情况，
                    // 只记录日志，不做其他处理，避免影响充电主流程。
                    ExceptionLogCollector::collect($e);
                }

                // 向 小程序Socket客户端 发送消息：运营平台远程控制启机-失败，发起充电失败
                $msg_data = [
                    'type' => '运营平台远程控制启机-失败',
                    'result' => 'error',
                    'msg' => '发起充电失败',
                    'order_id' => $order_id,
                ];
            }
            switch ($user_data['type']) {
                case '后台':
                    SendAdmin::send_group($user_data['token'], $msg_data);
                    break;
                case '小程序':
                    SendApplet::send_uid($order['user_id'], $msg_data);
                    break;
            }
            // 返回响应
            return res_success_text([], '发起充电-完成');
        }

        //Todo 设置失败，结束订单
        $res3 = Db::name('order')
            ->where('id', $order_id)
            ->update([
                'status' => Order::StatusAbnormal,
                'msg' => '设置功率失败',
                'update_time' => date('Y-m-d H:i:s'),
            ]);

//                        $update = new Update();
//                        $res4 = $update->unfreeze_user_balance_and_pay_order($order['user_id'], 0, $order['id'], $order['freeze_money']);
        trace('设置功率失败：写入订单结果=》' . json_encode_cn($res3), '开始充电任务');
        echo get_date(2) . $order_id . '==》设置功率失败==>写入订单结果：' . json_encode_cn($res3) . PHP_EOL;


        try {
            event('ChargeAbnormalEnd', new ChargeAbnormalEndEvent([
                'abnormal_reason' => ChargeAbnormalEndEvent::ABNORMAL_REASON_SET_PILE_OUTPUT_POWER_FAILED,
                'order_id' => $order_id,
                'corp_id' => $order['corp_id'],
                'station_id' => $order['station_id'],
                'pay_money' => 0,
                'electricity_total' => 0
            ]));
        } catch (Throwable $e) {
            // 在事件中运行的业务都是次要业务，若是出现异常情况，
            // 只记录日志，不做其他处理，避免影响充电主流程。
            ExceptionLogCollector::collect($e);
        }


        $msg_data = [
            'type' => '运营平台远程控制启机-失败',
            'result' => 'error',
            'msg' => '设置功率失败',
            'order_id' => $order_id,
        ];
        switch ($user_data['type']) {
            case '后台':
                SendAdmin::send_group($user_data['token'], $msg_data);
                break;
            case '小程序':
                SendApplet::send_uid($order['user_id'], $msg_data);
                break;
        }
        return res_success_text([], '充电桩工作参数设置应答-设置功率失败');
    }

    protected function chargingPilePowerOnResponse(array $data, array $user_data, array $order): string
    {
        $order_id = $data['order_id'];

        // 删除定时器
        $this->send_start_charging_timer([
            'type' => '删除-接收集中控制器',
            'order_id' => $order_id,
        ]);

        if (empty($data['data']['power_output']) || $data['data']['power_output'] < 30 || $data['data']['power_output'] > 100) {
            $res3 = Db::name('order')
                ->where('id', $order_id)
                ->update([
                    'status' => Order::StatusAbnormal,
                    'msg' => '该桩不允许充电',
                    'update_time' => date('Y-m-d H:i:s'),
                ]);
//                            $update = new Update();
//                            $res4 = $update->unfreeze_user_balance_and_pay_order($order['user_id'], 0, $order['id'], $order['freeze_money']);
            echo get_date(2) . $order_id . '==》该桩不允许充电==>写入订单结果：' . json_encode_cn($res3) . PHP_EOL;

            try {
                event('ChargeAbnormalEnd', new ChargeAbnormalEndEvent([
                    'abnormal_reason' => ChargeAbnormalEndEvent::ABNORMAL_REASON_NOT_ALLOW_CHARGING,
                    'order_id' => $order_id,
                    'corp_id' => $order['corp_id'],
                    'station_id' => $order['station_id'],
                    'pay_money' => 0,
                    'electricity_total' => 0
                ]));
            } catch (Throwable $e) {
                // 在事件中运行的业务都是次要业务，若是出现异常情况，
                // 只记录日志，不做其他处理，避免影响充电主流程。
                ExceptionLogCollector::collect($e);
            }


            $msg_data = [
                'type' => '运营平台远程控制启机-失败',
                'result' => 'error',
                'msg' => '该桩不允许充电',
                'order_id' => $order_id,
            ];
            switch ($user_data['type']) {
                case '后台':
                    SendAdmin::send_group($user_data['token'], $msg_data);
                    break;
                case '小程序':
                    SendApplet::send_uid($order['user_id'], $msg_data);
                    break;
            }
            return res_success_text([], '集中控制器充电桩开启功率应答-该桩不允许充电');
        }

        $msg_data = [
            'type' => '运营平台远程控制启机-成功',
            'result' => 'success',
            'msg' => '收到开启功率',
            'order_id' => $order_id,
        ];

        switch ($user_data['type']) {
            case '后台':
                SendAdmin::send_group($user_data['token'], $msg_data);
                break;
            case '小程序':
                SendApplet::send_uid($order['user_id'], $msg_data);
                break;
        }

        // 充电桩工作参数设置 - 下发功率
        $res4 = SendGetApi::charging_station_parameter_setting(
            [
                'piles_id' => (string)$order['piles_id'],
                'allow_work' => 1,
                'maximum_allowed_output_power' => $data['data']['power_output'],
            ],
            $user_data
        );
        $res5 = json_decode($res4->getContent(), true);
        trace('下发功率：结果=》' . json_encode_cn($res5), '开始充电任务');
        if (!empty($res5['code']) && !empty($res5['data']['request_id']) && $res5['code'] == 200) {
            $msg_data2 = [
                'type' => '运营平台远程控制启机-成功',
                'result' => 'success',
                'msg' => '下发功率成功',
                'order_id' => $order_id,
            ];

            $this->send_start_charging_timer([
                'type' => '接收下发功率',
                'order_id' => $order_id,
                'data' => $data,
                'order' => $order,
                'user_data' => $user_data,
                'request_id' => $res5['data']['request_id'],
            ]);

        } else {
            $msg_data2 = [
                'type' => '运营平台远程控制启机-失败',
                'result' => 'error',
                'msg' => '下发功率失败',
                'order_id' => $order_id,
            ];

            $res6 = Db::name('order')
                ->where('id', $order_id)
                ->update([
                    'status' => Order::StatusAbnormal,
                    'msg' => '下发功率失败',
                    'update_time' => date('Y-m-d H:i:s'),
                ]);

            trace('下发功率：失败=》' . json_encode_cn($res6), '开始充电任务');

            try {
                event('ChargeAbnormalEnd', new ChargeAbnormalEndEvent([
                    'abnormal_reason' => ChargeAbnormalEndEvent::ABNORMAL_REASON_SET_PILE_OUTPUT_POWER_API_FAILED,
                    'order_id' => $order_id,
                    'corp_id' => $order['corp_id'],
                    'station_id' => $order['station_id'],
                    'pay_money' => 0,
                    'electricity_total' => 0
                ]));
            } catch (Throwable $e) {
                // 在事件中运行的业务都是次要业务，若是出现异常情况，
                // 只记录日志，不做其他处理，避免影响充电主流程。
                ExceptionLogCollector::collect($e);
            }
        }


        switch ($user_data['type']) {
            case '后台':
                SendAdmin::send_group($user_data['token'], $msg_data2);
                break;
            case '小程序':
                SendApplet::send_uid($order['user_id'], $msg_data2);
                break;
        }
        return res_success_text([], '集中控制器充电桩开启功率应答-完成');
    }

    protected function startCharge(array $data, array $user_data, array $order): string
    {
        $data['user_data'] = new UserData($data['user_data']);
        $request = new StartChargeCommandRequest($data);
        return (new StartChargeCommand($request, $order))->run();
    }

    /**
     * 发送开始充电
     * lwj 2023.9.25 新增
     * @param array $order
     * @param array $user_data
     * @return bool
     */
    private function send_start_charging(array $order, array $user_data): bool
    {
        try {
            // 向 下发指令处理定时器进程 发送请求：运营平台远程控制启机
            $res3 = send_http_post_timer([
                'type' => '运营平台远程控制启机',
                'order_id' => $order['id'],
            ]);
            trace('send_http_post_timer：开始充电=》' . json_encode_cn($res3), '开始充电任务');

            // 向 充电桩中间服务 发送请求：运营平台远程控制启机 0x34
            $send_data = [
                'transaction_serial_number' => $order['id'],
                'piles_id' => hex_auto_fill_0($order['piles_id'], 14),
                'shots_id' => hex_auto_fill_0($order['sequence'], 2),
                'logical_card_number' => hex_auto_fill_0($order['user_id'], 16),
                'physical_card_number' => hex_auto_fill_0($order['user_id'], 16),
//                'physical_card_number'=>hex_auto_fill_0('0',16),
//                'logical_card_number'=>hex_auto_fill_0('0',16),
                'account_balance' => $order['balance'],
            ];

            $res = SendGetApi::remote_control_startup_of_operating_platform($send_data, $user_data);
            $res2 = json_decode($res->getContent(), true);
            if (!empty($res2['code']) && $res2['code'] == 200) {
                return true;
            }
            return false;
        } catch (Throwable $e) {
            trace($this->logPrefix . print_r([
                    'msg' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'code' => $e->getCode()
                ], true), 'error');
            trace($this->logPrefix . print_r($e->getTrace(), true), 'error');
            return false;
        }
    }

    /**
     * 发送到开始充电定时器
     * lwj 2023.10.7 新增
     * @param array $task_data 数据
     * @return void
     */
    private function send_start_charging_timer(array $task_data): void
    {
        try {
            // 与远程task服务建立异步连接，ip为远程task服务的ip，如果是本机就是127.0.0.1，如果是集群就是lvs的ip
            $task_connection = new AsyncTcpConnection(config('my.StartChargingTimerApi'));
            $send_data = json_encode_cn($task_data);
            trace($this->logPrefix . '发送到开始充电定时器=>参数：==》' . $send_data, 'debug');
            $task_connection->send($send_data);
            // 异步获得结果
            $task_connection->onMessage = function (AsyncTcpConnection $task_connection, $task_result) {

                trace($this->logPrefix . '发送到开始充电定时器=>结果：==》' . json_encode_cn($task_result), 'debug');
                // 获得结果后记得关闭异步连接
                $task_connection->close();
            };
            // 执行异步连接
            $task_connection->connect();

        } catch (Throwable $e) {
            trace($this->logPrefix . '发送到开始充电定时器失败', 'error');
            trace($this->logPrefix . print_r([
                    'msg' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'code' => $e->getCode()
                ], true), 'error');
            trace($this->logPrefix . print_r($e->getTrace(), true), 'error');
        }
    }

}
