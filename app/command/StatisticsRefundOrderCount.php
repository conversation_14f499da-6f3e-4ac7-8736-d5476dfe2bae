<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

declare (strict_types=1);

namespace app\command;

use app\common\lib\enterprise_wechat\PendingRefundApplyAlertMessage;
use app\common\lib\ExceptionLogCollector;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use Throwable;
use app\common\model\RefundOrder as RefundOrderModel;

/**
 * 统计退款订单数量
 * lwj 2024.4.9 新增
 */
class StatisticsRefundOrderCount extends Command
{

    protected function configure(): void
    {
        // 指令配置
        $this->setName('StatisticsRefundOrderCount')
            ->setDescription('统计退款订单数量');
    }

    protected function execute(Input $input, Output $output): void
    {
        try {
            $output->writeln('开始');

            $count = RefundOrderModel::where('state', 1)->count();
            if ($count <= 0) {
                $output->writeln('没有需要统计的订单');
                return;
            }

//            $corp_name=CorpModel::where('id', 100000)->value('name');
            $corp_name = config('app.name');
            if (!$corp_name) {
                $output->writeln('没有找到配置文件app.name');
                trace('统计退款订单数量 - 没有找到配置文件app.name', 'error');
                return;
            }

            $send_data = [
                'corp_name' => $corp_name,
                'pending_count' => $count
            ];
            $output->writeln('发送数据：' . json_encode_cn($send_data));

            $res = (new PendingRefundApplyAlertMessage($corp_name, $count))->send();

            if (!$res->isFailed && $res->httpCode == 200) {
                $output->writeln('发送通知成功');
            } else {
                trace('统计退款订单数量 - 发送通知失败-' . json_encode_cn($res), 'error');
                $output->writeln('发送通知失败');
            }
            $output->writeln('完成');
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            trace('统计退款订单数量 - ' . print_r([
                    'msg' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'code' => $e->getCode()
                ], true), 'error');
            $output->writeln('异常：' . $e->getMessage());
        }
    }

}
