<?php
/** @noinspection PhpUnused */
/** @noinspection PhpObjectFieldsAreOnlyWrittenInspection */
declare (strict_types=1);

namespace app\command;

use app\common\model\PilesStatus;
use app\common\model\ShotsStatus;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use Workerman\Worker;
use app\common\lib\transfer_service\TransferServiceClientRead as TransferServiceClientReadLogic;

/**
 * 中转服务客户端只读
 * cbj 2023.12.26 新增
 */
class TransferServiceClientRead extends BaseCommand
{
    protected function configure(): void
    {
        // 指令配置
        $this->setName('transfer-service-client-read')
            ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|status|connections", 'start')
            ->addOption('mode', 'm', Option::VALUE_OPTIONAL, '以守护程序模式运行workerman服务器')
            ->setDescription('中转服务客户端只读');
    }

    protected function execute(Input $input, Output $output): void
    {
        // 指令输出
        $output->writeln('开始');

        $this->initArgv($input);

        $worker = new Worker();

        $worker->name = '中转服务客户端只读';
        $worker->count = 1;


        $worker->onWorkerStart = function () {

            (new TransferServiceClientReadLogic())->run();

        };

        $worker->onWorkerExit = function () {

            (new PilesStatus())->allPilesOffline();
//            (new ShotsStatus())->allShotsOffline();

        };

        Worker::runAll();
    }
}