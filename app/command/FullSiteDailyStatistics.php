<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
declare (strict_types=1);

namespace app\command;

use app\common\log\SocketLogCollector;
use app\common\model\Corp;
use app\common\model\CorpOperatingFundsDailyStatistics;
use app\common\model\Order;
use app\common\model\PayOrder;
use think\console\Input;
use think\console\Output;
use app\common\model\OperatingFundsDailyStatistics as RechargeDailyStatisticsModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 全站每日运营资金统计
 * 这里打算以充值订单的创建时间作为时间范围的筛选字段，这会存在一个问题：当天创建订单，第二天支付才支付，这种情况要是统计脚本是在凌晨零点，那么就很有可能错过统计。
 * 查看了微信支付API文档，上面有提到两小时内未支付的话，该订单会过期。
 * 所以将统计每日充值金额的脚本，设置在每天凌晨3点运行，这样就能避免这种情况了。
 * 微信支付API文档：https://pay.weixin.qq.com/docs/merchant/apis/jsapi-payment/direct-jsons/jsapi-prepay.html
 * cbj 2024.08.17
 */
class FullSiteDailyStatistics extends BaseCommand
{

    public const COMMAND = 'full-site-daily-statistics';

    protected function configure(): void
    {
        // 指令配置
        $this->setName(self::COMMAND)
            ->setDescription('全站每日统计');
    }


    /**
     * @param Input $input
     * @param Output $output
     * @param SocketLogCollector $socketLogCollector
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function handler(Input $input, Output $output, SocketLogCollector $socketLogCollector): void
    {
        $startTime = microtime(true);
        $date = date('Y-m-d 00:00:00', strtotime('-1 day'));
        $OrderModel = new Order();
        $payOrderStatistics = (new PayOrder())->dailyStatistics($date);
        $orderStatistics = $OrderModel->dailyStatistics($date);

        $result = (new RechargeDailyStatisticsModel())->insert([
            'recharge_money' => $payOrderStatistics['money'],
            'recharge_count' => $payOrderStatistics['count'],
            'charging_money' => $orderStatistics['money'],
            'charging_count' => $orderStatistics['count'],
            'date' => $date,
        ]);

        $all_corp_ids = (new Corp())->getAllCorpId();
        $inserts = [];
        foreach ($all_corp_ids as $corp_id) {
            $orderStatistics = $OrderModel->dailyStatistics($date, $corp_id);
            $inserts[] = [
                'corp_id' => $corp_id,
                'charging_money' => $orderStatistics['money'],
                'charging_count' => $orderStatistics['count'],
                'date' => $date,
            ];
        }
        $row = (new CorpOperatingFundsDailyStatistics())->insertAll($inserts);


        $output->writeln(sprintf('运行成功，统计耗时:%s，daily_statistics写入结果：%s，corp_daily_statistics写入条数：%s',
                microtime(true) - $startTime, $result, $row)
        );
    }
}