<?php
/** @noinspection PhpUnused */
declare(strict_types=1);

namespace app\command;

use app\common\queue\OrderPayConsumer;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use Throwable;

/**
 * 微信支付通知消费者命令
 *
 * 使用方法：
 * php think wechat:pay-notify-consumer
 */
class OrderPayCommand extends Command
{
    const FAILURE = 0;
    const SUCCESS = 1;

    protected function configure(): void
    {
        $this->setName('order_pay')
            ->addArgument('action', Argument::OPTIONAL, "start|stop|restart|reload|status|connections", 'start')
            ->addOption('mode', 'm', Option::VALUE_OPTIONAL, '以守护程序模式运行workerman服务器')
            ->setDescription('订单支付通知消费者');
    }

    protected function execute(Input $input, Output $output): int
    {
        $output->writeln('正在启动微信支付通知消费者...');

        try {
            $consumer = new OrderPayConsumer();

            $output->writeln('订单支付通知消费者已启动，等待消息...');
            $output->writeln('按 Ctrl+C 停止消费者');

            // 启动消费者
            $consumer->start();

        } catch (Throwable $e) {
            $output->writeln('<error>消费者启动失败: ' . $e->getMessage() . '</error>');
            return self::FAILURE;
        }

        return self::SUCCESS;
    }
}
