<?php
/** @noinspection PhpUnused */

/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */

namespace app\applet\controller;

use app\common\lib\VerifyData;
use app\common\lib\WeChat;
use app\common\model\Users as UsersModel;
use app\common\traits\Curd;
use app\ms\Api;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\Container;
use think\facade\Log;
use think\response\Json;
use app\common\lib\GatewayApplet;
use Throwable;
use app\common\cache\redis\AppletUserLogin;

#[Apidoc\Title("登录")]
class Login extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new UsersModel();
    }

    #[
        Apidoc\Title("登录websocket"),
        Apidoc\Author("lwj 2023.7.25 新增"),
        <PERSON>pid<PERSON>\Method("POST"),
        Apidoc\Url("/applet/Login/web_socket"),
        Apidoc\Param(name: "client_id", type: "string", require: true, desc: "客户端id"),
        Apidoc\Param(name: "ws_group", type: "string", require: true, desc: "组名"),
    ]
    public function web_socket(): Json
    {
        if ($this->request->isPost()) {
            $user = $this->loginUser;
            $client_id = $this->request->post('client_id');
            $ws_group = $this->request->post('ws_group');
            if (!$user || !$ws_group) return $this->res_error([], '参数错误');

            $login_id = session_create_id();
            $session_data = [
                'ws_group' => $ws_group,
                'user_id' => $user->id,
                'user_name' => $user->nickname,
                'token' => $user->token,
                'login_id' => $login_id,
            ];
            GatewayApplet::bindUid($client_id, $user->id);
            GatewayApplet::joinGroup($client_id, '小程序');
            GatewayApplet::joinGroup($client_id, $ws_group);
            GatewayApplet::joinGroup($client_id, $user->token);
            GatewayApplet::updateSession($client_id, $session_data);
            unset($session_data['token']);
            return $this->res_success($session_data, '登录成功');
        } else {
            return $this->res_error([], '错误请求');
        }

    }

    #[
        Apidoc\Title("微信小程序登录"),
        Apidoc\Author("lwj 2023.8.11 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Login/wechat_applet_login"),
        Apidoc\Param(name: "nickname", type: "string", require: true, desc: "用户昵称"),
        Apidoc\Param(name: "avatar", type: "string", require: true, desc: "用户头像url"),
        Apidoc\Param(name: "code", type: "string", require: true, desc: "code"),
        Apidoc\NotHeaders,
        Apidoc\Returned(name: "id", type: "int", desc: "用户id"),
        Apidoc\Returned(name: "nickname", type: "string", desc: "昵称"),
        Apidoc\Returned(name: "avatar", type: "string", desc: "用户头像url"),
        Apidoc\Returned(name: "gender", type: "string", desc: "用户性别  1 男 2 女 3 未知"),
        Apidoc\Returned(name: "phone", type: "string", desc: "用户手机号"),
        Apidoc\Returned(name: "freeze", type: "string", desc: "账号是否冻结，1-未冻结，2-冻结"),
        Apidoc\Returned(name: "balance", type: "string", desc: "余额，单位分"),
        Apidoc\Returned(name: "auth_status", type: "string", desc: "授权状态： 1 未授权 2 已授权"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "用户创建时间"),
        Apidoc\Returned(name: "token", type: "string", desc: "登录令牌"),
        Apidoc\Returned(name: "expire_time", type: "int", desc: "登录令牌有效时间(单位:秒)"),
    ]
    public function wechat_applet_login(): Json
    {
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::users([
                'nickname',
                'avatar',
                'code',
            ]));

            $WeChat = new WeChat;
            $res_session = $WeChat->get_open_id($verify_data['code']);

            if (!isset($res_session['openid'])) return $this->res_error([], '获取openid失败');

            $user = $this->modelClass
                ->where('open_id', $res_session['openid'])
                ->field('id,nickname,avatar,gender,phone,freeze,balance,auth_status,create_time')
                ->find()
                ->toArray();

            if ($user) {
                $tokenData = $this->get_token($user, $res_session['openid']);
                if (count($tokenData) === 0) {
                    return $this->res_error([], '登录失败');
                }

                $user['token'] = $tokenData['token'];
                $user['expire_time'] = $tokenData['expire_time'];
                return $this->res_success($user, '登录成功');
            }

            $verify_data['open_id'] = $res_session['openid'];
            unset($verify_data['code']);
            $res = $this->modelClass->create($verify_data);

            if ($res) {
                $user = $this->modelClass
                    ->where('open_id', $res_session['openid'])
                    ->field('id,nickname,avatar,gender,phone,freeze,balance,auth_status,create_time')
                    ->find()
                    ->toArray();
                $tokenData = $this->get_token($user, $res_session['openid']);
                if (count($tokenData) === 0) {
                    return $this->res_error([], '登录失败');
                }

                $user['token'] = $tokenData['token'];
                $user['expire_time'] = $tokenData['expire_time'];
                return $this->res_success($user, '登录成功');
            }
            return $this->res_error([], '登录失败');

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("更新手机号"),
        Apidoc\Author("lwj 2023.8.12 新增，lwj 2023.9.15 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Login/update_phone"),
        Apidoc\Param(name: "code", type: "string", require: true, desc: "code"),
        Apidoc\Returned(name: "id", type: "int", desc: "用户id"),
        Apidoc\Returned(name: "nickname", type: "string", desc: "昵称"),
        Apidoc\Returned(name: "avatar", type: "string", desc: "用户头像url"),
        Apidoc\Returned(name: "gender", type: "string", desc: "用户性别  1 男 2 女 3 未知"),
        Apidoc\Returned(name: "phone", type: "string", desc: "用户手机号"),
        Apidoc\Returned(name: "freeze", type: "string", desc: "账号是否冻结，1-未冻结，2-冻结"),
        Apidoc\Returned(name: "balance", type: "string", desc: "余额，单位分"),
        Apidoc\Returned(name: "auth_status", type: "string", desc: "授权状态： 1 未授权 2 已授权"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "用户创建时间"),
        Apidoc\Returned(name: "token", type: "string", desc: "登录令牌"),
        Apidoc\Returned(name: "expire_time", type: "int", desc: "登录令牌有效时间(单位:秒)"),
    ]
    public function update_phone(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::users([
                'code',
            ]));

            $WeChat = new WeChat;
            $phone = $WeChat->get_phone_number($verify_data['code']);

            if (!$phone) return $this->res_error([], '获取手机号失败');

            $res = $this->modelClass
                ->where('id', $user->id)
                ->update([
                    'phone' => $phone,
                    'update_time' => date('Y-m-d H:i:s'),
                ]);

            if (!$res) return $this->res_error([], '更新手机号失败');

            $user->phone = $phone;
            $AppletUserLogin = app(AppletUserLogin::class);
            $expireTime = $AppletUserLogin->saveUserLoginData($user->token, (array)$user);
            if (is_null($expireTime)) {
                return $this->res_error([], '登录失败');
            }

            $result = (array)$user;
            $result['expire_time'] = $expireTime;
            $user->expire_time = $expireTime;

            unset($result['open_id']);
            unset($result['last_time']);
            return $this->res_success($result);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("微信小程序通过手机号登录"),
        Apidoc\Author("lwj 2023.8.16 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Login/wechat_applet_phone_login"),
        Apidoc\Param(name: "openid_code", type: "string", require: true, desc: "openid_code"),
        Apidoc\Param(name: "phone_code", type: "string", require: true, desc: "phone_code"),
        Apidoc\NotHeaders,
        Apidoc\Returned(name: "id", type: "int", desc: "用户id"),
        Apidoc\Returned(name: "nickname", type: "string", desc: "昵称"),
        Apidoc\Returned(name: "avatar", type: "string", desc: "用户头像url"),
        Apidoc\Returned(name: "gender", type: "string", desc: "用户性别  1 男 2 女 3 未知"),
        Apidoc\Returned(name: "phone", type: "string", desc: "用户手机号"),
        Apidoc\Returned(name: "freeze", type: "string", desc: "账号是否冻结，1-未冻结，2-冻结"),
        Apidoc\Returned(name: "balance", type: "string", desc: "余额，单位分"),
        Apidoc\Returned(name: "auth_status", type: "string", desc: "授权状态： 1 未授权 2 已授权"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "用户创建时间"),
        Apidoc\Returned(name: "token", type: "string", desc: "登录令牌"),
        Apidoc\Returned(name: "expire_time", type: "int", desc: "登录令牌有效时间(单位:秒)"),
    ]
    public function wechat_applet_phone_login(): Json
    {
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::users([
                'openid_code',
                'phone_code',
            ]));

            $WeChat = new WeChat;
            $res_session = $WeChat->get_open_id($verify_data['openid_code']);
            if (!isset($res_session['openid'])) return $this->res_error([], '获取openid失败');

            $WeChat = new WeChat;
            $phone = $WeChat->get_phone_number($verify_data['phone_code']);
            if (!$phone) return $this->res_error([], '获取手机号失败');

            $user = $this->modelClass
                ->where('open_id', $res_session['openid'])
                ->field('id,nickname,avatar,gender,phone,freeze,balance,auth_status,create_time')
                ->find();

            if ($user) {
                $user = $user->toArray();
                $tokenData = $this->get_token($user, $res_session['openid']);
                if (count($tokenData) === 0) {
                    return $this->res_error([], '登录失败');
                }

                $user['token'] = $tokenData['token'];
                $user['expire_time'] = $tokenData['expire_time'];
                return $this->res_success($user, '登录成功');
            }

            $verify_data['open_id'] = $res_session['openid'];
            $verify_data['phone'] = $phone;
            unset($verify_data['openid_code']);
            unset($verify_data['phone_code']);
            $res = $this->modelClass->create($verify_data);
            if ($res) {
                $user = $this->modelClass
                    ->where('open_id', $res_session['openid'])
                    ->field('id,nickname,avatar,gender,phone,freeze,balance,auth_status,create_time')
                    ->find()
                    ->toArray();
                $tokenData = $this->get_token($user, $res_session['openid']);
                if (count($tokenData) === 0) {
                    return $this->res_error([], '登录失败');
                }

                $user['token'] = $tokenData['token'];
                $user['expire_time'] = $tokenData['expire_time'];

                // 调用微服务同步数据
                $user['user_id'] = $user['id'];
                $user['open_id'] = $verify_data['open_id'];
                Api::send('/auth/user/register/mini', 'POST', $user);
                return $this->res_success($user, '登录成功');
            }
            return $this->res_error([], '登录失败');

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("小程序自动登录"),
        Apidoc\Author("lwj 2023.9.6 新增"),
        Apidoc\Author("lwj 2023.9.13 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Login/auto_login"),
    ]
    public function auto_login(): Json
    {
        try {
            $user = (array)$this->loginUser;
            unset($user['last_time']);
            unset($user['open_id']);
            return $this->res_success($user, '小程序自动登录成功');
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("刷新令牌(Token)有效期(每隔1小时调用一次)"),
        Apidoc\Author("cbj 2024.07.18 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Login/refresh_expire_time"),
        Apidoc\Returned(name: "expire_time", type: "int", desc: "刷新后的登录令牌有效时间(单位:秒)"),
    ]
    public function refresh_expire_time(): Json
    {
        return $this->openExceptionCatch(function () {
            $AppletUserLogin = app(AppletUserLogin::class);
            $expireTime = $AppletUserLogin->extendLoginValidityTime($this->loginUser->token);

            return [
                'expire_time' => $expireTime
            ];
        });
    }


    #[
        Apidoc\Title("登录测试"),
        Apidoc\Author("lwj 2023.8.23 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Login/login_test"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "id"),
        Apidoc\Param(name: "password", type: "string", require: true, desc: "接口文档登陆密码"),
        Apidoc\NotHeaders,
    ]
    public function login_test(): Json
    {
        try {
            $password = $this->request->post('password');
            if ($password !== config('apidoc.auth.password')) {
                return Container::getInstance()->invokeClass(Json::class, ['404 Not Found', 404]);
            }

            $data = $this->request->post();
            $user = $this->modelClass
                ->where('id', $data['id'])
                ->field('id,nickname,avatar,gender,phone,freeze,balance,auth_status,create_time,open_id')
                ->find();
            Log::info('user:'.json_encode($user));

            $user = $user->toArray();
            $tokenData = $this->get_token($user, $user['open_id']);
            if (count($tokenData) === 0) {
                return $this->res_error([], '登录失败');
            }

            $user['token'] = $tokenData['token'];
            $user['expire_time'] = $tokenData['expire_time'];
            unset($user['open_id']);
            return $this->res_success($user, '登录成功');

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取二维码"),
        Apidoc\Author("lwj 2023.8.23 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Login/get_qr"),
        Apidoc\NotHeaders,
    ]
    public function get_qr(): Json
    {
        try {
//            $data = $this->request->post();
            $qr_data = [
                'scene' => '2023010500038001',
                'page' => 'pages/index/index',
                'check_path' => false,
                'env_version' => 'develop',
            ];
            $WeChat = new WeChat;
            $qr = $WeChat->get_qr_code($qr_data);
//            return $qr;
            return $this->res_success($qr, '登录成功');

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    /**
     * 登录获取token
     * lwj 2023.8.12 新增
     * lwj 2023.9.15 修改
     * @param array $user
     * @param string $open_id
     * @return array
     */
    private function get_token(array $user, string $open_id): array
    {
        $last_time = microtime();
        $token = sha1($last_time . $user['id'] . '-' . $open_id);

        $user['last_time'] = $last_time;
        $user['token'] = $token;
        $user['open_id'] = $open_id;
        $AppletUserLogin = app(AppletUserLogin::class);
        $user['expire_time'] = $AppletUserLogin->saveUserLoginData($token, $user);
        if (is_null($user['expire_time'])) return [];
        return $user;
    }

}