<?php
/** @noinspection PhpUnused */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */

namespace app\applet\controller;


use hg\apidoc\annotation as Apidoc;
use think\facade\Db;
use think\response\Json;
use app\common\model\City as CityModel;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use app\common\lib\VerifyData;
use Throwable;

#[Apidoc\Title("公共模块")]
class Common extends BaseController
{

    #[
        Apidoc\Title("获取主机域名"),
        Apidoc\Author("lwj 2023.9.9 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/applet/Common/get_host"),
    ]
    public function get_host(): Json
    {
        $res_data=['host'=>config('my.host')];
        return $this->res_success($res_data);
    }

    #[
        Apidoc\Title("获取城市列表"),
        Apidoc\Author("lwj 2023.9.13 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/applet/Common/city_list"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name:"data", type:"array", require:true, desc:"充电站列表", children:[
            ['name' => 'city_list', 'type' => 'array', 'desc' => '城市列表', 'children' => [
                ['name' => 'id', 'type' => 'int', 'desc' => '城市id' ],
                ['name' => 'name', 'type' => 'string', 'desc' => '城市名称'],
                ['name' => 's', 'type' => 'string', 'desc' => '城市首字母'],
            ]],
            ['name' => 'city_index', 'type' => 'array', 'desc' => '城市首字母列表'],
            ['name' => 'city_hot', 'type' => 'string', 'desc' => '热门城市列表', 'children' => [
                ['name' => 'id', 'type' => 'int', 'desc' => '城市id' ],
                ['name' => 'name', 'type' => 'string', 'desc' => '城市名称'],
            ]]
        ]),
    ]
    public function city_list(): Json
    {
        $CityModel=new CityModel();

        $city_hot=$CityModel
            ->where('type',2)
            ->where('hot',2)
            ->order('s','asc')
            ->column('id,name');

        $city=$CityModel
            ->where('type',2)
            ->order('s','asc')
            ->column('id,name,s');

        $city_list=arr_group($city,'s');
        $city_index=array_keys($city_list);

        return $this->res_success([
            'city_list'=>$city_list,
            'city_index'=>$city_index,
            'city_hot'=>$city_hot,
        ]);
    }

    #[
        Apidoc\Title("逆地址解析"),
        Apidoc\Author("lwj 2023.9.13 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Common/get_geocoder_location"),
        Apidoc\Param(name:"center_lon", type:"float", desc:"当前经度"),
        Apidoc\Param(name:"center_lat", type:"float", desc:"当前纬度"),

        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name:"data", type:"array", require:true, desc:"充电站列表", children:[
            ['name' => 'nation', 'type' => 'string', 'desc' => '国家'],
            ['name' => 'province', 'type' => 'string', 'desc' => '省'],
            ['name' => 'city', 'type' => 'string', 'desc' => '市，如果当前城市为省直辖县级区划，city与district字段均会返回此城市'],
            ['name' => 'district', 'type' => 'string', 'desc' => '区，可能为空字串'],
            ['name' => 'street', 'type' => 'string', 'desc' => '道路，可能为空字串'],
            ['name' => 'street_number', 'type' => 'string', 'desc' => '门牌，可能为空字串'],
        ]),
    ]
    public function get_geocoder_location(): Json
    {
        $data = $this->request->post();
        $center_lon  = ($data['center_lon'] ?? false) ?: 114.12772;
        $center_lat  = ($data['center_lat'] ?? false) ?: 22.54764;

        $query_params=[
            'location' => $center_lat.','.$center_lon,
            'key' => config('my.qq_map_key'),
        ];
        $queryString = http_build_query($query_params);
        $path='/ws/geocoder/v1/';
        $url = 'https://apis.map.qq.com' . $path . '?' . $queryString;
        $res_arr=send_get_json_to_shuzu($url);
        $region_list=[];
        if(!empty($res_arr['result']['address_component'])) $region_list=$res_arr['result']['address_component'];
        return $this->res_success($region_list);
    }

    #[
        Apidoc\Title("获取驾驶路线"),
        Apidoc\Author("lwj 2023.9.13 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Common/get_driving_line"),
        Apidoc\Param(name:"center_lon", type:"float", desc:"当前经度"),
        Apidoc\Param(name:"center_lat", type:"float", desc:"当前纬度"),

        Apidoc\Param(name:"to_lon", type:"float", desc:"目的地经度"),
        Apidoc\Param(name:"to_lat", type:"float", desc:"目的地纬度"),

        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name:"data", type:"array", require:true, desc:"充电站列表", children:[
            ['name' => 'nation', 'type' => 'string', 'desc' => '国家'],
            ['name' => 'province', 'type' => 'string', 'desc' => '省'],
            ['name' => 'city', 'type' => 'string', 'desc' => '市，如果当前城市为省直辖县级区划，city与district字段均会返回此城市'],
            ['name' => 'district', 'type' => 'string', 'desc' => '区，可能为空字串'],
            ['name' => 'street', 'type' => 'string', 'desc' => '道路，可能为空字串'],
            ['name' => 'street_number', 'type' => 'string', 'desc' => '门牌，可能为空字串'],
        ]),
    ]
    public function get_driving_line(): Json
    {
        $data = $this->request->post();
        $center_lon  = ($data['center_lon'] ?? false) ?: 114.12772;
        $center_lat  = ($data['center_lat'] ?? false) ?: 22.54764;

        $to_lon  = ($data['to_lon'] ?? false) ?: 113.949287;
        $to_lat  = ($data['to_lat'] ?? false) ?: 22.585732;

        $query_params=[
            'from' => $center_lat.','.$center_lon,
            'to' => $to_lat.','.$to_lon,
            'key' => config('my.qq_map_key'),
        ];
        $queryString = http_build_query($query_params);
        $path='/ws/direction/v1/driving/';
        $url = 'https://apis.map.qq.com' . $path . '?' . $queryString;
        $res_arr=send_get_json_to_shuzu($url);

        if(empty($res_arr['result']['routes'][0]['polyline'])) return $this->res_success();
        $polyline=$res_arr['result']['routes'][0]['polyline'];
        $polylineCount = count($polyline);

        for ($i = 2; $i < $polylineCount; $i++) {
            $polyline[$i] = $polyline[$i-2] + $polyline[$i]/1000000;
        }

        $result = [];
        for ($j = 0; $j < count($polyline); $j+=2) {
            $latitude = $polyline[$j];
            $longitude = $polyline[$j+1];
            $result[] = [
                'longitude'=>$longitude,
                'latitude'=>$latitude,
            ];
        }

        return $this->res_success($result);
    }

    #[
        Apidoc\Title("批量获取行车距离"),
        Apidoc\Author("lwj 2024.8.13 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Common/get_driving_distance_all"),
        Apidoc\Param(name:"center_lon", type:"float", desc:"当前经度"),
        Apidoc\Param(name:"center_lat", type:"float", desc:"当前纬度"),

        Apidoc\Param(name:"to", type:"array", desc:"场站位置数组", children:[
            ['name' => 'id', 'type' => 'number', 'desc' => '场站id，无场站是0，非必需'],
            ['name' => 'to_lon', 'type' => 'string', 'desc' => '经度'],
            ['name' => 'to_lat', 'type' => 'string', 'desc' => '纬度'],
        ]),

        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name:"data", type:"array", require:true, desc:"充电站距离列表", children:[
            ['name' => 'distance', 'type' => 'number', 'desc' => '方案总距离，单位：米'],
            ['name' => 'duration', 'type' => 'number', 'desc' => '方案估算时间（结合路况），单位：分钟'],
            ['name' => 'id', 'type' => 'number', 'desc' => '场站id，无场站是0'],
            ['name' => 'is_success', 'type' => 'boolean', 'desc' => '是否成功获取，true成功，false失败'],
        ]),
    ]
    public function get_driving_distance_all(): Json
    {
        try {
            $data = $this->request->post();
            $center_lon  = ($data['center_lon'] ?? false) ?: 114.12772;
            $center_lat  = ($data['center_lat'] ?? false) ?: 22.54764;

            $verify_data = v::input($data, VerifyData::stations([
                'to',
            ]));

            $urls = [];
            foreach ($verify_data['to'] as $v){
                $query_params=[
                    'from' => $center_lat.','.$center_lon,
                    'to' => $v['to_lat'].','.$v['to_lon'],
                    'key' => config('my.qq_map_key'),
                    'mode' => 'driving',
                    'policy' => 'LEAST_TIME',
                ];
                $queryString = http_build_query($query_params);
                $path='/ws/direction/v1/driving/';
                $urls[] = [
                    'url' => 'https://apis.map.qq.com' . $path . '?' . $queryString
                ];
            }
//            return $this->res_success($urls);
            $res_arr=sendMultiRequest($urls);
            if(empty($res_arr)) return $this->res_error();

            $res_data=[];
            foreach ($res_arr as $k=>$v){
                if(empty($v['result']['routes'][0])){
                    $res_data[]=[
                        'distance'=>0,
                        'duration'=>0,
                        'id'=>($verify_data['to'][$k]['id']) ?? 0,
                        'is_success'=>false,
                    ];
                }else{
                    $res_data[]=[
                        'distance'=>$v['result']['routes'][0]['distance'],
                        'duration'=>$v['result']['routes'][0]['duration'],
                        'id'=>($verify_data['to'][$k]['id']) ?? 0,
                        'is_success'=>true,
                    ];
                }
            }

            return $this->res_success($res_data);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：'.$e->getMessage());
        }
    }

    #[
        Apidoc\Title("批量获取行车距离（估算）"),
        Apidoc\Author("lwj 2024.8.13 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Common/get_driving_distance_matrix_all"),
        Apidoc\Param(name:"center_lon", type:"float", desc:"当前经度"),
        Apidoc\Param(name:"center_lat", type:"float", desc:"当前纬度"),

        Apidoc\Param(name:"to", type:"array", desc:"场站位置数组", children:[
            ['name' => 'id', 'type' => 'number', 'desc' => '场站id，无场站是0，非必需'],
            ['name' => 'to_lon', 'type' => 'string', 'desc' => '经度'],
            ['name' => 'to_lat', 'type' => 'string', 'desc' => '纬度'],
        ]),

        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name:"data", type:"array", require:true, desc:"充电站距离列表", children:[
            ['name' => 'distance', 'type' => 'number', 'desc' => '方案总距离，单位：米'],
            ['name' => 'duration', 'type' => 'number', 'desc' => '方案估算时间（结合路况），单位：分钟'],
            ['name' => 'id', 'type' => 'number', 'desc' => '场站id，无场站是0'],
        ]),
    ]
    public function get_driving_distance_matrix_all(): Json
    {
        try {
            $data = $this->request->post();
            $center_lon  = ($data['center_lon'] ?? false) ?: 114.12772;
            $center_lat  = ($data['center_lat'] ?? false) ?: 22.54764;

            $verify_data = v::input($data, VerifyData::stations([
                'to',
            ]));
            $to='';
            foreach ($verify_data['to'] as $v){
                $to.=$v['to_lat'].','.$v['to_lon'].';';
            }
            $to = rtrim($to, ';');

            $query_params=[
                'from' => $center_lat.','.$center_lon.',280',
                'to' => $to,
                'key' => config('my.qq_map_key'),
                'mode' => 'driving',
            ];
            $queryString = http_build_query($query_params);
            $path='/ws/distance/v1/matrix/';
            $url = 'https://apis.map.qq.com' . $path . '?' . $queryString;
            $res_arr=send_get_json_to_shuzu($url);

            if(empty($res_arr['result']['rows'][0]['elements'])){
                return $this->res_error();
            }
            $elements=$res_arr['result']['rows'][0]['elements'];
            if(!is_array($elements)) return $this->res_error();

            foreach ($elements as $k=>&$v){
                $v['id']=($verify_data['to'][$k]['id']) ?? 0;
            }

            return $this->res_success($elements);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：'.$e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取行车距离"),
        Apidoc\Author("lwj 2024.8.13 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Common/get_driving_distance"),
        Apidoc\Param(name:"center_lon", type:"float", desc:"当前经度"),
        Apidoc\Param(name:"center_lat", type:"float", desc:"当前纬度"),

        Apidoc\Param(name:"id", type:"number", desc:"场站id"),
        Apidoc\Param(name:"to_lon", type:"float", desc:"当前经度"),
        Apidoc\Param(name:"to_lat", type:"float", desc:"当前纬度"),

        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name:"data", type:"array", require:true, desc:"充电站距离", children:[
            ['name' => 'distance', 'type' => 'number', 'desc' => '起点到终点的距离，单位：米'],
            ['name' => 'duration', 'type' => 'number', 'desc' => '表示从起点到终点的结合路况的时间，秒为单位'],
            ['name' => 'id', 'type' => 'number', 'desc' => '场站id，无场站是0'],
        ]),
    ]
    public function get_driving_distance(): Json
    {
        try {
            $data = $this->request->post();
            $center_lon  = ($data['center_lon'] ?? false) ?: 114.12772;
            $center_lat  = ($data['center_lat'] ?? false) ?: 22.54764;

            $to_lon  = ($data['to_lon'] ?? false) ?: 113.949287;
            $to_lat  = ($data['to_lat'] ?? false) ?: 22.585732;
            $id  = ($data['id'] ?? false) ?: 0;

            $query_params=[
                'from' => $center_lat.','.$center_lon,
                'to' => $to_lat.','.$to_lon,
                'key' => config('my.qq_map_key'),
                'mode' => 'driving',
                'policy' => 'LEAST_TIME',
            ];
            $queryString = http_build_query($query_params);
            $path='/ws/direction/v1/driving/';
            $url = 'https://apis.map.qq.com' . $path . '?' . $queryString;
            $res_arr=send_get_json_to_shuzu($url);

            if(empty($res_arr['result']['routes'][0])){
                return $this->res_error();
            }
            $driving=$res_arr['result']['routes'][0];

            return $this->res_success([
                'distance' => $driving['distance'],
                'duration' => $driving['duration'],
                'id' => $id,
            ]);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：'.$e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取协议"),
        Apidoc\Author("lwj 2023.9.18 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Common/get_agreement"),
        Apidoc\Param(name:"name", type:"string", require: true, desc:"协议标题，分类"),
        Apidoc\Returned(name: "content", type: "string", require: true, desc: "协议内容"),
    ]
    public function get_agreement(): Json
    {
        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::agreement([
                'name',
            ]));

            $agreement=Db::name('agreement')
                ->where('status',1)
                ->where('name',$verify_data['name'])
                ->field('content')
                ->find();

            return $this->res_success($agreement);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：'.$e->getMessage());
        }
    }

}