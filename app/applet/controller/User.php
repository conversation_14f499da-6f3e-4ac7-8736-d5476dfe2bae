<?php
/** @noinspection PhpUnused */

namespace app\applet\controller;

use app\common\model\Users as UsersModel;
use app\common\model\UserBalanceLog as UserBalanceLogModel;
use app\common\traits\Curd;
use app\ms\Api;
use hg\apidoc\annotation as Apidoc;
use think\facade\Log;
use think\response\Json;
use Throwable;


#[Apidoc\Title("用户")]
class User extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new UsersModel();
    }

    #[
        Apidoc\Title("获取用户余额"),
        Apidoc\Author("lwj 2023.8.15 新增，lwj 2023.8.24 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/User/get_user_balance"),
        Apidoc\Returned(name: "balance", type: "int", desc: "余额(单位:分)"),
        Apidoc\Returned(name: "freeze_balance", type: "int", desc: "冻结余额(单位:分)"),
        Apid<PERSON>\Returned(name: "credit_limit", type: "int", desc: "信用额度(单位:分)"),
        Apidoc\Returned(name: "sum_balance", type: "int", desc: "可用余额(单位:分)"),
    ]
    public function get_user_balance(): Json
    {
        try {
            $user = $this->loginUser;

            $balance = $this->modelClass
                ->where('id', $user->id)
                ->field('balance,freeze_balance,credit_limit,sum(balance+freeze_balance+credit_limit) as sum_balance')
                ->find();
            return $this->res_success($balance);

        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取用户余额流水"),
        Apidoc\Author("lwj 2023.8.15 新增，lwj 2023.8.24 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/User/get_user_balance_log"),
        Apidoc\Returned(name: "amount", type: "int", desc: "余额流水，单位分"),
        Apidoc\Returned(name: "memo", type: "string", desc: "备注"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "时间"),
    ]
    public function get_user_balance_log(): Json
    {
        try {
            $user = $this->loginUser;
            $user_balance_log = new UserBalanceLogModel();

            $log = $user_balance_log
                ->where('user_id', $user->id)
                ->order('id', 'desc')
                ->column('amount,type,create_time');
            return $this->res_success($log);

        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
    Apidoc\Title("获取用户信息"),
    Apidoc\Author("cccq 2025.3.14 新增"),
    Apidoc\Method("GET"),
    Apidoc\Url("/applet/User/info"),
    Apidoc\Returned(name: "code", type: "int", desc: "状态码"),
    Apidoc\Returned(name: "msg", type: "string", desc: "提示信息"),
    Apidoc\Returned(name: "data", type: "array", desc: "用户信息", children: []),
    ]
    public function info(): Json
    {
        // 从微服务获取用户信息
        $response = Api::send('/auth/user/info','GET', ['user_id'=>$this->loginUser->id]);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }

    #[
        Apidoc\Title("获取管理员账户token"),
        Apidoc\Author("cccq 2025.3.29 新增"),
        Apidoc\Method("POST"),
        Apidoc\Param(name: "admin_id", type: "int", desc: "管理员ID"),
        Apidoc\Url("/applet/User/admin_token"),
        Apidoc\Returned(name: "code", type: "int", desc: "状态码"),
        Apidoc\Returned(name: "msg", type: "string", desc: "提示信息"),
        Apidoc\Returned(name: "data", type: "array", desc: "用户信息", children: []),
    ]
    public function admin_token(): Json
    {
        $admin_id = $this->request->post('admin_id');
//        if (!$admin_id) {
//            return $this->res_error([], '管理员ID不能为空');
//        }
        // 从微服务获取用户信息
        $response = Api::send('/auth/user/admin/create_token','POST', ['user_id'=>$this->loginUser->id,'admin_id' => $admin_id]);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }

    #[
    Apidoc\Title("修改用户信息"),
    Apidoc\Author("cccq 2025.3.15 新增"),
    Apidoc\Method("POST"),
    Apidoc\Url("/applet/User/update_info"),
    Apidoc\Param(name: "nickname", type: "string", desc: "昵称"),
    Apidoc\Param(name: "avatar", type: "string", desc: "头像URL"),
    Apidoc\Param(name: "gender", type: "性别", desc: "1:男,2:女,3:未知"),
    Apidoc\Param(name: "plate", type: "string", desc: "车牌"),
    Apidoc\Returned(name: "code", type: "int", desc: "状态码"),
    Apidoc\Returned(name: "msg", type: "string", desc: "提示信息"),
    Apidoc\Returned(name: "data", type: "array", desc: "更新后的用户信息"),
    ]
    public function update_info(): Json
    {
        $input = $this->request->post();
        $input['user_id'] = $this->loginUser->id;
        // 调用微服务更新用户信息
        $response = Api::send('/auth/user/info','PUT', $input);
        return $this->res_success($response['data'], $response['msg'], $response['code']);
    }


}