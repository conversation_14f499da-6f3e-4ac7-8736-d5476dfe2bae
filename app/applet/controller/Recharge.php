<?php
/** @noinspection PhpUnused */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */

namespace app\applet\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\lib\WeChatPay;
use app\common\model\RechargeList as RechargeListModel;
use app\common\traits\Curd;
use Exception;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use app\common\model\PayOrder as PayOrderModel;
use app\common\model\Order as ChargeOrderModel;
use app\common\model\Users as UsersModel;
use app\common\model\RefundOrder as RefundOrderModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\response\Json;
use Throwable;

#[Apidoc\Title("充值")]
class Recharge extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new PayOrderModel();
    }

    #[
        Apidoc\Title("充值列表"),
        Apidoc\Author("lwj 2023.8.14 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/applet/Recharge/list"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "运营商列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => 'id'],
            ['name' => 'title', 'type' => 'string', 'desc' => '标题'],
            ['name' => 'original_price', 'type' => 'string', 'desc' => '原价'],
            ['name' => 'price', 'type' => 'string', 'desc' => '支付金额'],
            ['name' => 'currency', 'type' => 'string', 'desc' => '币种：1:CNY,2:USD'],
        ]),
    ]
    public function list(): Json
    {
        $recharge_list = new RechargeListModel();
        $list = $recharge_list
            ->where('state', 1)
            ->order('sort', 'asc')
            ->column('id,title,original_price,price,currency');
        return $this->res_success($list);
    }

    #[
        Apidoc\Title("创建充值订单"),
        Apidoc\Author("lwj 2023.8.14 新增，lwj 2023.8.26 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/create_order"),
        Apidoc\Param(name: 'id', type: "int", require: true, desc: '充值列表id'),
        Apidoc\Param(name: 'price', type: "int", require: true, desc: '支付金额'),
    ]
    public function create_order(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::pay_order([
                'id',
                'price',
            ]));

            $recharge_list = new RechargeListModel();
            $list = $recharge_list->find($verify_data['id']);
            if (!$list) return $this->res_error([], '获取支付列表失败');

            $price = $list['price'];
            if ($list['id'] === 1) $price = $verify_data['price'];

            $order_id = $this->get_order_no();
            $title = $list['title'];
            $openid = $user->open_id;

            $order_data = [
                'id' => $order_id,
                'user_id' => $user->id,
                'list_id' => $list['id'],
                'name' => $list['title'],
                'price' => $price,
                'ip' => get_ip(),
            ];

            $res = $this->modelClass->create($order_data);
            if (!$res) return $this->res_error([], '创建订单失败');

            $WeChatPay = new WeChatPay;
            $res_pay = $WeChatPay->js_place_order($order_id, $price, $title, $openid);
            if (!$res_pay) return $this->res_error([], '下单失败');

            return $this->res_success($res_pay);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取充值订单列表"),
        Apidoc\Author("lwj 2023.8.28 新增，lwj 2024.2.20 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/get_pay_order_list"),
        Apidoc\Param(name: "id", type: "string", desc: "支付流水号"),
        Apidoc\Param(name: "trade_no", type: "string", desc: "外部订单编号"),
        Apidoc\Param(name: "status", type: "array|int", desc: "状态：1生成订单，2支付中，3完成支付，4失败或其他问题，5关闭支付"),
        Apidoc\Param(name: "start_time", type: "string", desc: "开始时间"),
        Apidoc\Param(name: "end_time", type: "string", desc: "结束时间"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '支付流水号'],
            ['name' => 'trade_no', 'type' => 'string', 'desc' => '外部订单编号'],
            ['name' => 'price', 'type' => 'int', 'desc' => '支付金额'],
            ['name' => 'currency', 'type' => 'int', 'desc' => '币种：1:CNY,2:USD'],
            ['name' => 'state', 'type' => 'int', 'desc' => '状态：1生成订单，2支付中，3完成支付，4失败或其他问题，5关闭支付'],
            ['name' => 'add_balance', 'type' => 'int', 'desc' => '加余额结果，1成功，2失败，3没有加币（默认3）'],
            ['name' => 'type', 'type' => 'int', 'desc' => '支付类型，1充值余额，2充卡'],
            ['name' => 'refund_state', 'type' => 'int', 'desc' => '退款状态，1无退款，2申请退款，3已退款，4退款失败，10已拒绝，11已取消'],
        ]),
    ]
    public function get_pay_order_list(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();
            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
            $order_name = ($data['order_name'] ?? false) ?: 'create_time';
            $order_type = ($data['order_type'] ?? false) ?: 'desc';

            // 搜索框搜索
            $where = function ($query) use ($data) {
                if (isset($data['id']) && $data['id']) {
                    $query->where('id', $data['id']);
                }
                if (isset($data['trade_no']) && $data['trade_no']) {
                    $query->where('trade_no', $data['trade_no']);
                }
                if (isset($data['state']) && $data['state']) {
                    if (is_array($data['state'])) {
                        $query->where('state', 'in', $data['state']);
                    } else {
                        $query->where('state', $data['state']);
                    }
                }

                if (isset($params['start_time']) && isset($params['end_time']) && $params['start_time'] && $params['end_time']) {
                    $query->where('create_time >= "' . $params['start_time'] . ' 00:00:00"');
                    $query->where('create_time <= "' . $params['end_time'] . ' 23:59:59"');
                }

                return $query;
            };

            $list = $this->modelClass
                ->field('id,trade_no,name,price,currency,state,add_balance,type,refund_state')
                ->where('user_id', $user->id)
                ->where($where)
                ->order($order_name, $order_type)
                ->paginate(['list_rows' => $pageSize, 'page' => $page]);

            return $this->res_success($list);

        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取充值详情"),
        Apidoc\Author("lwj 2024.8.26 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/get_pay_order_info"),
        Apidoc\Param(name: "order_id", type: "string", require: true, desc: "充值订单号"),

        Apidoc\Returned(name: "id", type: "string", desc: "退款流水号"),
        Apidoc\Returned(name: "trade_no", type: "string", desc: "外部订单编号"),
        Apidoc\Returned(name: "name", type: "string", desc: "支付项名称"),
        Apidoc\Returned(name: "price", type: "int", desc: "支付金额"),
        Apidoc\Returned(name: "currency", type: "int", desc: "币种：1:CNY,2:USD"),
        Apidoc\Returned(name: "state", type: "int", desc: "状态：1生成订单，2支付中，3完成支付，4失败或其他问题，5关闭支付"),
        Apidoc\Returned(name: "add_balance", type: "int", desc: "加余额结果，1成功，2失败，3没有加币（默认3）"),
        Apidoc\Returned(name: "type", type: "int", desc: "支付类型，1充值余额，2充卡"),
        Apidoc\Returned(name: "refund_state", type: "int", desc: "退款状态，1无退款，2申请退款，3已退款，4退款失败，10已拒绝，11已取消"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "充值时间"),

    ]
    public function get_pay_order_info(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();

            $verify_data = v::input($data, VerifyData::pay_order([
                'order_id',
            ]));

            $info = $this->modelClass
                ->where('id', $verify_data['order_id'])
                ->where('user_id', $user->id)
                ->field([
                    'id',
                    'trade_no',
                    'name',
                    'price',
                    'currency',
                    'state',
                    'add_balance',
                    'type',
                    'refund_state',
                    'create_time',
                ])
                ->find();
            if ($info) return $this->res_success($info);
            return $this->res_error([], '订单不存在');

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("退款"),
        Apidoc\Author("lwj 2024.2.19 新增，lwj 2024.7.15 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/refund"),
        Apidoc\Param(name: 'order_id', type: "string", require: true, desc: '支付订单id'),

//        Apidoc\Returned(name: "channel", type: "string", require: true, desc: "退款渠道，ORIGINAL: 原路退款，BALANCE: 退回到余额，OTHER_BALANCE: 原账户异常退到其他余额账户，OTHER_BANKCARD: 原银行卡异常退到其他银行卡"),
//        Apidoc\Returned(name: "user_received_account", type: "string", require: true, desc: "退款入账账户，1）退回银行卡：{银行名称}{卡类型}{卡尾号}，2）退回支付用户零钱:支付用户零钱，3）退还商户:商户基本账户商户结算银行账户，4）退回支付用户零钱通:支付用户零钱通"),
//        Apidoc\Returned(name: "out_status", type: "string", require: true, desc: "微信返回状态（仅代表业务的受理情况，不代表退款成功到账）：SUCCESS: 退款成功，CLOSED: 退款关闭，PROCESSING: 退款处理中，ABNORMAL: 退款异常"),
        Apidoc\Returned(name: "refund_price", type: "int", require: true, desc: "退款金额，单位分"),
    ]
    public function refund(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::pay_order([
                'order_id',
            ]));

            $user_cache = cache('用户申请退款_' . $user->id);
            if($user_cache) return $this->res_error([], '请勿频繁申请退款');
            cache('用户申请退款_' . $user->id, 1, 10);

            $UsersModel = new UsersModel;

            $user2 = $UsersModel
                ->where('id', $user->id)
                ->field('id,balance')
                ->find();
            if (!$user2) return $this->res_error([], '异常用户');
            if ($user2['balance']<=0) return $this->res_error([], '没有可退余额');

            $pay_order = $this->modelClass
                ->where('id', $verify_data['order_id'])
                ->where('user_id', $user->id)
                ->field('id,create_time,state,add_balance,refund_state,price,currency,trade_no')
                ->find();
            if (!$pay_order) return $this->res_error([], '订单不存在');

            if (strtotime($pay_order['create_time'])-time() >= 365*24*60*60) return $this->res_error([], '超过一年，无法退款');
            if ($pay_order['state'] !== 3) return $this->res_error([], '订单没支付');
            if ($pay_order['add_balance'] !== 1) return $this->res_error([], '订单没有加余额，请联系客服处理');
            if (!in_array($pay_order['refund_state'],[1,10,11])) return $this->res_error([], '订单已申请过退款，无法重复申请，如有疑问，请联系客服处理');

            $charge_order = (new ChargeOrderModel)
                ->where('user_id', $user->id)
                ->whereIn('status', [1,2,3,8])
                ->field('id')
                ->find();
            if ($charge_order) return $this->res_error([], '您还有充电中的订单，请结束充电后再退款');

            Db::startTrans();
            try {
                $refund_id=$this->get_refund_id();
                $RefundOrder = new RefundOrderModel;

                $is_refund_order = $RefundOrder
                    ->where('order_id', $verify_data['order_id'])
                    ->whereNotIn('state', [1,10,11])
                    ->field('id')
                    ->find();
                if ($is_refund_order) return $this->res_error([], '订单已申请过退款，无法重复申请，如有疑问，请联系客服处理');

                $refund_price = min($user2['balance'], $pay_order['price']);
                $deduction_balance_res = $UsersModel
                    ->where('id', $user->id)
                    ->limit(1)
                    ->update([
                        'balance' => Db::raw('balance-' . $refund_price),
                    ]);
                if(!$deduction_balance_res) throw new Exception('扣除余额失败');

                $user3 = $UsersModel
                    ->where('id', $user->id)
                    ->field('id,balance')
                    ->find();
                if(!$user3['balance']<0) throw new Exception('余额不足');

                $res_pay_order = $this->modelClass
                    ->where('id', $verify_data['order_id'])
                    ->where('user_id', $user->id)
                    ->limit(1)
                    ->whereIn('refund_state', [1,10,11])
                    ->update([
                        'refund_state' => 2,
                    ]);
                if(!$res_pay_order) throw new Exception('锁订单失败');

                $refund_order = $RefundOrder->create([
                    'id' => $refund_id,
                    'user_id' => $user->id,
                    'order_id' => $pay_order['id'],
                    'refund_price' => $refund_price,
                    'order_price' => $pay_order['price'],
                    'currency' => $pay_order['currency'],
                    'out_transaction_id' => $pay_order['trade_no'],
                    'msg' => '申请退款',
                    'ip' => get_ip(),
                ]);
                if(empty($refund_order->id)) throw new Exception('创建退款订单失败');
                // 提交事务
                Db::commit();
                return $this->res_success(
                    [
                        'refund_price'=>$refund_price
                    ],
                    '已提交退款申请，请耐心等待审核并退款，一般2-3个工作日处理，如有疑问，请联系客服处理'
                );
            } catch (Exception $e) {
                // 回滚事务
                Db::rollback();
                return $this->res_error([], $e->getMessage());
            }

//            $WeChatPay = new WeChatPay;
//            $res_refund = $WeChatPay->order_refund(
//                $refund_order->order_id,
//                $refund_order->id,
//                $refund_order->refund_price,
//                $refund_order->order_price,
//            );
//            if (empty($res_refund['status'])){
//                $refund_order = $RefundOrder
//                    ->where('id', $refund_id)
//                    ->limit(1)
//                    ->where('state','<>',20)
//                    ->update([
//                        'state' => 30,
//                ]);
//                trace('退款=》更新结果：' . json_encode_cn($refund_order), '信息');
//                return $this->res_error([], '退款失败');
//            }
//
//            $refund_order = $RefundOrder
//                ->where('id', $refund_id)
//                ->limit(1)
//                ->update([
//                    'out_refund_id' => $res_refund['refund_id'],
//                    'channel' => $res_refund['channel'],
//                    'user_received_account' => $res_refund['user_received_account'],
//                    'out_status' => $res_refund['status'],
//                ]);
//
//            trace('退款=》更新结果：' . json_encode_cn($refund_order), '信息');
//
//            if(in_array($res_refund['status'], ['SUCCESS','PROCESSING'])){
//                return $this->res_success([
//                    'channel'=>$res_refund['channel'],
//                    'user_received_account'=>$res_refund['user_received_account'],
//                    'out_status'=>$res_refund['status'],
//                    'refund_price'=>$refund_price,
//                ],'提交退款成功，请等待微信处理');
//            }
//
//            return $this->res_warning([
//                'channel'=>$res_refund['channel'],
//                'user_received_account'=>$res_refund['user_received_account'],
//                'out_status'=>$res_refund['status'],
//                'refund_price'=>$refund_price,
//            ],'退款异常');

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取退款详情(最后一笔)"),
        Apidoc\Author("lwj 2024.2.20 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/get_refund_order_info_last"),
        Apidoc\Param(name: "order_id", type: "string", require: true, desc: "交易流水号"),

        Apidoc\Returned(name: "id", type: "string", desc: "退款流水号"),
        Apidoc\Returned(name: "order_id", type: "string", desc: "充值订单号"),
        Apidoc\Returned(name: "out_refund_id", type: "string", desc: "外部退款号"),
        Apidoc\Returned(name: "out_transaction_id", type: "string", desc: "外部交易号"),
        Apidoc\Returned(name: "refund_price", type: "int", desc: "退款金额，单位为分"),
        Apidoc\Returned(name: "order_price", type: "int", desc: "订单总金额，单位为分"),
        Apidoc\Returned(name: "payer_refund", type: "int", desc: "【用户退款金额】 退款给用户的金额，单位为分，不包含所有优惠券金额"),
        Apidoc\Returned(name: "state", type: "int", desc: "状态：1已申请，5退款中，10已拒绝，11已取消，20已退款，30退款失败，40其它"),
        Apidoc\Returned(name: "state_arr", type: "array", desc: "状态数组"),
        Apidoc\Returned(name: "currency", type: "int", desc: "币种：1:CNY,2:USD"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "申请退款时间"),
        Apidoc\Returned(name: "channel", type: "string", require: true, desc: "退款渠道，ORIGINAL: 原路退款，BALANCE: 退回到余额，OTHER_BALANCE: 原账户异常退到其他余额账户，OTHER_BANKCARD: 原银行卡异常退到其他银行卡"),
        Apidoc\Returned(name: "user_received_account", type: "string", require: true, desc: "退款入账账户，1）退回银行卡：{银行名称}{卡类型}{卡尾号}，2）退回支付用户零钱:支付用户零钱，3）退还商户:商户基本账户商户结算银行账户，4）退回支付用户零钱通:支付用户零钱通"),
        Apidoc\Returned(name: "out_status", type: "string", require: true, desc: "微信返回状态（仅代表业务的受理情况，不代表退款成功到账）：SUCCESS: 退款成功，CLOSED: 退款关闭，PROCESSING: 退款处理中，ABNORMAL: 退款异常"),
        Apidoc\Returned(name: "out_notice_status", type: "string", desc: "外部通知状态，退款状态，枚举值：，SUCCESS：退款成功，CLOSED：退款关闭，ABNORMAL：退款异常，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往【商户平台—>交易中心】，手动处理此笔退款"),

    ]
    public function get_refund_order_info_last(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();

            $verify_data = v::input($data, VerifyData::pay_order([
                'order_id',
            ]));

            $res_pay_order = $this->modelClass
                ->where('id', $verify_data['order_id'])
                ->where('user_id', $user->id)
                ->field('id')
                ->find();
            if(!$res_pay_order) return $this->res_error([], '订单不存在');

            $field = [
                'id',
                'order_id',
                'out_refund_id',
                'out_transaction_id',
                'refund_price',
                'order_price',
                'payer_refund',
                'state',
                'currency',
                'create_time',
                'channel',
                'user_received_account',
                'out_status',
                'out_notice_status',
            ];

            $info = (new RefundOrderModel)
                ->field($field)
                ->append(['state_arr'])
                ->where('order_id', $verify_data['order_id'])
                ->order('create_time','desc')
                ->withAttr('state_arr', function ($value, $data) {
                    return match ($data['state']) {
                        1, 5, 20 => [
                            ['key' => 1, 'name' => '已申请'],
                            ['key' => 5, 'name' => '已审核'],
                            ['key' => 20, 'name' => '已退款'],
                        ],
                        10 => [
                            ['key' => 1, 'name' => '已申请'],
                            ['key' => 10, 'name' => '已拒绝'],
                        ],
                        11 => [
                            ['key' => 1, 'name' => '已申请'],
                            ['key' => 11, 'name' => '已取消'],
                        ],
                        default => [
                            ['key' => 1, 'name' => '已申请'],
                            ['key' => 5, 'name' => '退款中'],
                            ['key' => $data['state'], 'name' => '退款失败'],
                        ],
                    };
                })
                ->find();
            if (!$info) return $this->res_error([], '退款订单不存在');
            return $this->res_success($info);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("取消退款"),
        Apidoc\Author("lwj 2024.2.21 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/cancel_refund"),
        Apidoc\Param(name: 'refund_id', type: "string", require: true, desc: '退款流水号'),

        Apidoc\Returned(name: "refund_price", type: "int", require: true, desc: "退款金额，单位分"),
    ]
    public function cancel_refund(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::pay_order([
                'refund_id',
            ]));

            $user_cache = cache('用户申请退款_' . $user->id);
            if($user_cache) return $this->res_error([], '请勿频繁取消退款');
            cache('用户申请退款_' . $user->id, 1, 10);

            return $this->openExceptionCatch(function ()use($verify_data,$user) {

                $RefundOrder = new RefundOrderModel;

                $refund_order = $RefundOrder
                    ->where('id', $verify_data['refund_id'])
                    ->where('user_id', $user->id)
                    ->field('id,order_id,refund_price,state')
                    ->find();
//            if (!$refund_order) return $this->res_error([], '没有退款订单');
                if (!$refund_order) throw new RuntimeException(
                    '没有退款订单',
                    [],
                    RuntimeException::CodeBusinessException
                );
//            if ($refund_order['state'] !== 1) return $this->res_error([], '该笔退款正在处理或已处理，不能取消，如有疑问，请联系客服处理');
                if ($refund_order['state'] !== 1) throw new RuntimeException(
                    '该笔退款正在处理或已处理，不能取消，如有疑问，请联系客服处理',
                    [],
                    RuntimeException::CodeBusinessException
                );

                $pay_order = $this->modelClass
                    ->where('id', $refund_order['order_id'])
                    ->where('user_id', $user->id)
                    ->field('id,create_time,state,add_balance,refund_state,price,currency,trade_no')
                    ->find();
//            if (!$pay_order) return $this->res_error([], '充值订单不存在');
                if (!$pay_order) throw new RuntimeException(
                    '充值订单不存在',
                    [],
                    RuntimeException::CodeBusinessException
                );
//            if ($pay_order['refund_state'] !== 2) return $this->res_error([], '该笔退款正在处理或已处理，不能取消，如有疑问，请联系客服处理');
                if ($pay_order['refund_state'] !== 2) throw new RuntimeException(
                    '该笔退款正在处理或已处理，不能取消，如有疑问，请联系客服处理',
                    [],
                    RuntimeException::CodeBusinessException
                );

                $res_refund_order = $RefundOrder
                    ->where('id', $verify_data['refund_id'])
                    ->where('user_id', $user->id)
                    ->where('state', 1)
                    ->limit(1)
                    ->update([
                        'state' => 11,
                        'msg' => '用户取消退款',
                    ]);
                if (!$res_refund_order) throw new RuntimeException(
                    '取消退款失败',
                    [],
                    RuntimeException::CodeBusinessException
                );

                $res_pay_order = $this->modelClass
                    ->where('id', $refund_order['order_id'])
                    ->where('user_id', $user->id)
                    ->where('refund_state', 2)
                    ->limit(1)
                    ->update([
                        'refund_state' => 11,
                    ]);
                if (!$res_pay_order) throw new RuntimeException(
                    '取消退款失败2',
                    [],
                    RuntimeException::CodeBusinessException
                );

                $add_balance_res = (new UsersModel)
                    ->where('id', $user->id)
                    ->limit(1)
                    ->update([
                        'balance' => Db::raw('balance+' . $refund_order['refund_price']),
                    ]);
                if (!$add_balance_res) throw new RuntimeException(
                    '取消退款失败，加余额失败',
                    [],
                    RuntimeException::CodeBusinessException
                );
                return $this->res_success([
                    'refund_price' => $refund_order['refund_price'],
                ], '取消退款成功',is_open_exception_catch:true);

            }, true, sprintf("lock_key_%s", $verify_data['refund_id']));
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取退款订单列表"),
        Apidoc\Author("lwj 2024.2.21 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/get_refund_order_list"),
        Apidoc\Param(name: "id", type: "string", desc: "退款流水号"),
        Apidoc\Param(name: "order_id", type: "string", desc: "充值订单号"),
        Apidoc\Param(name: "state", type: "array|int", desc: "状态：1已申请，5退款中，10已拒绝，11已取消，20已退款，30退款失败，40其它"),
        Apidoc\Param(name: "start_time", type: "string", desc: "开始时间"),
        Apidoc\Param(name: "end_time", type: "string", desc: "结束时间"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '退款流水号'],
            ['name' => 'order_id', 'type' => 'string', 'desc' => '充值订单号'],
            ['name' => 'refund_price', 'type' => 'int', 'desc' => '退款金额'],
            ['name' => 'order_price', 'type' => 'int', 'desc' => '订单总金额'],
            ['name' => 'currency', 'type' => 'int', 'desc' => '币种：1:CNY,2:USD'],
            ['name' => 'state', 'type' => 'int', 'desc' => '状态：1已申请，5退款中，10已拒绝，11已取消，20已退款，30退款失败，40其它'],
            ['name' => 'create_time', 'type' => 'int', 'desc' => '申请时间'],
        ]),
    ]
    public function get_refund_order_list(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();
            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
            $order_name = ($data['order_name'] ?? false) ?: 'create_time';
            $order_type = ($data['order_type'] ?? false) ?: 'desc';

            // 搜索框搜索
            $where = function ($query) use ($data) {
                if (isset($data['id']) && $data['id']) {
                    $query->where('id', $data['id']);
                }
                if (isset($data['order_id']) && $data['order_id']) {
                    $query->where('order_id', $data['order_id']);
                }
                if (isset($data['state']) && $data['state']) {
                    if (is_array($data['state'])) {
                        $query->where('state', 'in', $data['state']);
                    } else {
                        $query->where('state', $data['state']);
                    }
                }

                if (isset($params['start_time']) && isset($params['end_time']) && $params['start_time'] && $params['end_time']) {
                    $query->where('create_time >= "' . $params['start_time'] . ' 00:00:00"');
                    $query->where('create_time <= "' . $params['end_time'] . ' 23:59:59"');
                }

                return $query;
            };

            $list = (new RefundOrderModel)
                ->field('id,order_id,refund_price,order_price,currency,state,create_time')
                ->where('user_id', $user->id)
                ->where($where)
                ->order($order_name, $order_type)
                ->paginate(['list_rows' => $pageSize, 'page' => $page]);

            return $this->res_success($list);

        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取退款详情"),
        Apidoc\Author("lwj 2024.2.21 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/get_refund_order_info"),
        Apidoc\Param(name: "refund_id", type: "string", require: true, desc: "退款流水号"),

        Apidoc\Returned(name: "id", type: "string", desc: "退款流水号"),
        Apidoc\Returned(name: "order_id", type: "string", desc: "充值订单号"),
        Apidoc\Returned(name: "out_refund_id", type: "string", desc: "外部退款号"),
        Apidoc\Returned(name: "out_transaction_id", type: "string", desc: "外部交易号"),
        Apidoc\Returned(name: "refund_price", type: "int", desc: "退款金额，单位为分"),
        Apidoc\Returned(name: "order_price", type: "int", desc: "订单总金额，单位为分"),
        Apidoc\Returned(name: "payer_refund", type: "int", desc: "【用户退款金额】 退款给用户的金额，单位为分，不包含所有优惠券金额"),
        Apidoc\Returned(name: "state", type: "int", desc: "状态：1已申请，5退款中，10已拒绝，11已取消，20已退款，30退款失败，40其它"),
        Apidoc\Returned(name: "state_arr", type: "array", desc: "状态数组"),
        Apidoc\Returned(name: "currency", type: "int", desc: "币种：1:CNY,2:USD"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "申请退款时间"),
        Apidoc\Returned(name: "channel", type: "string", require: true, desc: "退款渠道，ORIGINAL: 原路退款，BALANCE: 退回到余额，OTHER_BALANCE: 原账户异常退到其他余额账户，OTHER_BANKCARD: 原银行卡异常退到其他银行卡"),
        Apidoc\Returned(name: "user_received_account", type: "string", require: true, desc: "退款入账账户，1）退回银行卡：{银行名称}{卡类型}{卡尾号}，2）退回支付用户零钱:支付用户零钱，3）退还商户:商户基本账户商户结算银行账户，4）退回支付用户零钱通:支付用户零钱通"),
        Apidoc\Returned(name: "out_status", type: "string", require: true, desc: "微信返回状态（仅代表业务的受理情况，不代表退款成功到账）：SUCCESS: 退款成功，CLOSED: 退款关闭，PROCESSING: 退款处理中，ABNORMAL: 退款异常"),
        Apidoc\Returned(name: "out_notice_status", type: "string", desc: "外部通知状态，退款状态，枚举值：，SUCCESS：退款成功，CLOSED：退款关闭，ABNORMAL：退款异常，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往【商户平台—>交易中心】，手动处理此笔退款"),

    ]
    public function get_refund_order_info(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();

            $verify_data = v::input($data, VerifyData::pay_order([
                'refund_id',
            ]));

            $field = [
                'id',
                'order_id',
                'out_refund_id',
                'out_transaction_id',
                'refund_price',
                'order_price',
                'payer_refund',
                'state',
                'currency',
                'create_time',
                'channel',
                'user_received_account',
                'out_status',
                'out_notice_status',
            ];

            $info = (new RefundOrderModel)
                ->field($field)
                ->append(['state_arr'])
                ->where('id', $verify_data['refund_id'])
                ->where('user_id', $user->id)
                ->withAttr('state_arr', function ($value, $data) {
                    return match ($data['state']) {
                        1, 5, 20 => [
                            ['key' => 1, 'name' => '已申请'],
                            ['key' => 5, 'name' => '已审核'],
                            ['key' => 20, 'name' => '已退款'],
                        ],
                        10 => [
                            ['key' => 1, 'name' => '已申请'],
                            ['key' => 10, 'name' => '已拒绝'],
                        ],
                        11 => [
                            ['key' => 1, 'name' => '已申请'],
                            ['key' => 11, 'name' => '已取消'],
                        ],
                        default => [
                            ['key' => 1, 'name' => '已申请'],
                            ['key' => 5, 'name' => '退款中'],
                            ['key' => $data['state'], 'name' => '退款失败'],
                        ],
                    };
                })
                ->find();
            if (!$info) return $this->res_error([], '退款订单不存在');
            return $this->res_success($info);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }





//    /**
//     * 微信签名
//     * lwj 2021.8.28新增
//     */
//    public function get_Sign($url, $http_method, $body, $sign_key, $merchant_id, $serial_no)
//    {
//        $timestamp     = time();//时间戳
//        $nonce         = $timestamp . rand(10000, 99999);//随机字符串
//        $url_parts     = parse_url($url);
//        $canonical_url = ($url_parts['path'] . (!empty($url_parts['query']) ? "?${url_parts['query']}" : ""));
//        $message       =
//            $http_method . "\n" .
//            $canonical_url . "\n" .
//            $timestamp . "\n" .
//            $nonce . "\n" .
//            $body . "\n";
//        openssl_sign($message, $raw_sign, $sign_key, 'sha256WithRSAEncryption');
//        $sign  = base64_encode($raw_sign);
//        $token = sprintf('mchid="%s",nonce_str="%s",timestamp="%d",serial_no="%s",signature="%s"',
//            $merchant_id, $nonce, $timestamp, $serial_no, $sign);
//        return $token;
//    }
//
//    /**
//     * 获取平台证书内容
//     * lwj 2021.8.28新增
//     */
//    public function get_Certificates($merchant_id, $serial_no, $Key)
//    {
//        $sign     = $this->get_Sign("https://api.mch.weixin.qq.com/v3/certificates", "GET", "", $Key, $merchant_id, $serial_no);//$http_method要大写
//        $header[] = 'User-Agent:' . env('ua');
//        $header[] = 'Accept:application/json';
//        $header[] = 'Authorization:WECHATPAY2-SHA256-RSA2048 ' . $sign;
//        $back     = get_http_data("https://api.mch.weixin.qq.com/v3/certificates", $header);
//        return $back;
//    }
//
//
//    public function ttt()
//    {
//        return $this->get_Certificates(config('wechat.mch_id'), '37FAAC15E804AB9E03D3F69F658BC10CE4E3E389', config('wechat.apiclient_key'));
//    }

//    public function hdf()
//    {
//
//        $decrypter = new AesUtil($opts['key']);
//        $plain = $decrypter->decryptToString($encCert['associated_data'], $encCert['nonce'], $encCert['ciphertext']);
//        $plain = AesGcm::decrypt($encCert['ciphertext'], $opts['key'], $encCert['nonce'], $encCert['associated_data']);
//    }


//    #[
//        Apidoc\Title("充值记录"),
//        Apidoc\Author("swk 2023.7.25 新增"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/applet/recharge/recharge_record"),
//        Apidoc\Param(name: 'page', require: true, desc: '页数'),
//        Apidoc\Param(name: 'limit', require: true, desc: '记录数')
//    ]
//    public function recharge_record()
//    {
//        $user_id = $this->request->token_user['id'];
//        $page    = $this->request->post('page') ?? 1;
//        $limit   = $this->request->post('limit') ?? 15;
//        $res     = $this->recharge_order_model->where('user_id', $user_id)->where('is_pay', 1)->paginate(['list_rows' => $page, 'page' => $limit]);
//        $this->res_success($res);
//    }


    /**
     * 获取充值订单编号
     * lwj 2023.8.14 新增
     * @return string
     */
    public function get_order_no(): string
    {
        $maxRetries = 5;
        $retryCount = 0;
        $datePrefix = date('ymd'); // 生成6位日期前缀

        while ($retryCount < $maxRetries) {
            $randomPart = bin2hex(random_bytes(13)); // 26位随机字符串
            $order_no = $datePrefix . $randomPart; // 合并成32位订单号

            $exist_order_no = $this->modelClass->field('id')->find($order_no);
            if (!$exist_order_no) {
                return $order_no;
            }

            $retryCount++;
        }

        throw new RuntimeException('生成订单号失败，请重试');
    }

    /**
     * 获取退款编号
     * lwj 2024.2.19 新增
     * lwj 2024.2.20 修改
     * @return string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function get_refund_id(): string
    {
        $RefundOrder = new RefundOrderModel;
        while (true) {
            $refund_id = 'T'.date('Ymd').'M'.get_microtime().'S'.mt_rand(10000, 99999);
            if (strlen($refund_id) > 64) continue;
            $exist_refund_id = $RefundOrder->field('id')->find($refund_id);
            if (!$exist_refund_id) {
                break;
            }
        }
        return $refund_id;
    }
}