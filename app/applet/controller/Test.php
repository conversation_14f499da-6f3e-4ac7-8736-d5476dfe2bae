<?php
/** @noinspection PhpUnused */

namespace app\applet\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\ExceptionLogCollector;
use app\common\lib\recharge\request\WechatPayNoticeRequest;
use app\common\lib\recharge\WechatPayNotice;
use app\common\model\Users as UsersModel;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use think\response\Json;


#[Apidoc\Title("测试")]
class Test extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new UsersModel();
    }


    #[
        Apidoc\Title("微信支付回调接口"),
        Apidoc\Author("cbj 2024.01.17 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/test/weixin_callback"),
        Apidoc\Returned(name: "mchid", type: "string", desc: "mchid"),
        Apid<PERSON>\Returned(name: "appid", type: "int", desc: "冻结余额(单位:分)"),
        Apidoc\Returned(name: "credit_limit", type: "int", desc: "信用额度(单位:分)"),
        Apidoc\Returned(name: "sum_balance", type: "int", desc: "可用余额(单位:分)"),
    ]
    public function weixin_callback(): Json
    {
        $body = $this->request->post();

        // 前面的代码后续在整理
        // 业务逻辑
        try {
            $request = new WechatPayNoticeRequest($body);
            (new WechatPayNotice($request))->handler();
            return json(['code' => 'SUCCESS', 'message' => '成功']);
        } catch (RuntimeException $e) {
            ExceptionLogCollector::collect($e);
            return match ($e->getCode()) {
                RuntimeException::CodeBusinessException => json(['code' => 'FAIL', 'message' => $e->getMessage()]),
                default => json(['code' => 'FAIL', 'message' => '服务器异常']),
            };
        } catch (\Throwable $e) {
            ExceptionLogCollector::collect($e);
            return json(['code' => 'FAIL', 'message' => '服务器异常']);
        }
    }
}