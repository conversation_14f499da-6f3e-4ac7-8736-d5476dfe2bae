<?php
/** @noinspection PhpUnused */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\applet\controller;

use app\common\lib\electronic_invoice\Apply;
use app\common\lib\ExceptionLogCollector;
use app\common\lib\VerifyData;
use app\common\model\ElectronicInvoiceApplyDetail;
use app\common\model\ElectronicInvoiceApplyRecord;
use app\common\traits\Curd;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\facade\Db;
use think\response\Json;
use hg\apidoc\annotation as Apidoc;
use Throwable;

#[Apidoc\Title("电子发票")]
class ElectronicInvoice extends BaseController
{
    use Curd;

    #[
        Apidoc\Title("申请开电子发票"),
        Apidoc\Author("cbj 2023.10.26 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/ElectronicInvoice/apply"),
        Apidoc\Param(name: "order_ids", type: "array", require: true, desc: "充电订单流水号集合"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'miniprogram_appid', 'type' => 'string', 'desc' => '抬头填写小程序AppID'],
            ['name' => 'miniprogram_path', 'type' => 'string', 'desc' => '抬头填写小程序页面路径'],
        ])
    ]
    public function apply(): Json
    {
        $user = $this->loginUser;

        $data = $this->request->post();
        // 验证数据
        $data = v::input($data, VerifyData::electronicInvoice([
            'order_ids',
        ]));

        Db::startTrans();
        try {

            $Apply = app(Apply::class);
            $result = $Apply->handler($user->id, $user->open_id, $data['order_ids']);

            Db::commit();
            return $this->res_success($result);

        } catch (Throwable $e) {
            Db::rollback();
            if ($e->getCode() === 400) {
                return $this->res_error('', $e->getMessage());
            }
            ExceptionLogCollector::collect($e);
            return $this->res_error('服务器异常');
        }
    }

    #[
        Apidoc\Title("开票记录"),
        Apidoc\Author("cbj 2023.10.26 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/electronic_invoice/list"),
        Apidoc\Param(name: "id", type: "string", require: false, default: '', desc: "订单发票申请ID"),
        Apidoc\Param(name: 'title_type', type: 'int', require: false, desc: '抬头类型 0:个人 1:单位'),
        Apidoc\Param(name: 'title_name', type: 'string', require: false, default: '', desc: '抬头名称'),
        Apidoc\Param(name: 'taxpayer_id', type: 'string', require: false, default: '', desc: '纳税人识别号'),
        Apidoc\Param(name: 'stage', type: 'int', require: false, desc: '阶段 0:用户发起申请 1:用户填写抬头完成 2:开具发票完成'),
        Apidoc\Param(name: 'start_time', type: 'int', require: false, default: 0, desc: '起始时间(单位:秒)'),
        Apidoc\Param(name: 'end_time', type: 'int', require: false, default: 0, desc: '结束时间(单位:秒)'),

        Apidoc\Param(name: "page", type: "int", require: false, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: false, default: 10, desc: "每页显示"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", require: false, desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '订单发票申请ID'],
            ['name' => 'total_amount', 'type' => 'int', 'desc' => '发票总金额(单位:分)'],
            ['name' => 'title_type', 'type' => 'int', 'desc' => '抬头类型 0:个人 1:单位'],
            ['name' => 'title_name', 'type' => 'string', 'desc' => '抬头名称'],
            ['name' => 'taxpayer_id', 'type' => 'string', 'desc' => '纳税人识别号(抬头类型为单位时不为空)'],
            ['name' => 'stage', 'type' => 'int', 'desc' => '阶段 0:用户发起申请 1:用户填写抬头完成 2:开具发票完成'],
            ['name' => 'create_time', 'type' => 'int', 'desc' => '创建时间(单位:秒)'],
            ['name' => 'update_time', 'type' => 'int', 'desc' => '更新时间(单位:秒)'],
        ]),
    ]
    public function list(): Json
    {
        try {
            $user = $this->loginUser;

            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::electronicInvoice([
                'id',
                'title_type',
                'title_name',
                'taxpayer_id',
                'stage',
                'start_time',
                'end_time',
                'page',
                'limit',
            ]));
            $verifyData['user_id'] = $user->id;

            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;

            $ElectronicInvoiceApplyRecord = app(ElectronicInvoiceApplyRecord::class);
            $list = $ElectronicInvoiceApplyRecord->list($verifyData, $page, $pageSize);

            return $this->res_success($list);
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            return $this->res_error([], '服务器异常');
        }
    }

    #[
        Apidoc\Title("已开票的充电订单ID"),
        Apidoc\Author("cbj 2023.10.26 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/electronic_invoice/detailedRecords"),
        Apidoc\Param(name: "page", type: "int", require: false, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: false, default: 100, desc: "每页显示"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", require: false, desc: "数据", children: [
            ['name' => 'order_id', 'type' => 'string', 'desc' => '已开发票的充电订单ID'],
        ]),
    ]
    public function detailedRecords(): Json
    {
        try {
            $user = $this->loginUser;

            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::electronicInvoice([
                'page',
                'limit',
            ]));

            $page = ($verifyData['page'] ?? false) ?: 1;
            $pageSize = ($verifyData['limit'] ?? false) ?: 100;

            $ElectronicInvoiceApplyDetail = app(ElectronicInvoiceApplyDetail::class);
            $list = $ElectronicInvoiceApplyDetail->userDetailedRecords($user->id, $page, $pageSize);

            return $this->res_success($list);
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            return $this->res_error([], '服务器异常');
        }
    }
}