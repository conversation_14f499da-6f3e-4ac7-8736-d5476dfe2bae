<?php
/** @noinspection PhpUnused */

/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\applet\controller;

use app\common\lib\VerifyData;
use app\common\model\Activity;
use app\common\model\ShotsStatus;
use app\common\model\Stations as StationsModel;
use app\common\model\UsersCollectStations as UsersCollectStationsModel;
use app\common\traits\Curd;
use app\ms\Api;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\facade\Db;
use think\response\Json;
use Throwable;


#[Apidoc\Title("场站")]
class Stations extends BaseController
{

    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new StationsModel();
    }

    #[
        Apidoc\Title("获取充电站列表"),
        Apidoc\Author("lwj 2023.8.29 新增，lwj 2023.9.18 修改"),
        <PERSON>pid<PERSON>\Method("POST"),
        Apidoc\Url("/applet/Stations/get_list"),
        Apidoc\Param(name: "id", type: "int", desc: "场站id"),
        Apidoc\Param(name: "status", type: "array|int", desc: "运行状态：1-正常使用、2-维护中、3-未开放"),
        Apidoc\Param(name: "start_time", type: "string", desc: "开始时间"),
        Apidoc\Param(name: "end_time", type: "string", desc: "结束时间"),
        Apidoc\Param(name: "type", type: "int", desc: "场站类型：1-公共、2-自营"),
        Apidoc\Param(name: "tag_pos", type: "int", desc: "桩位置标示：1-地上,2-地库"),
        Apidoc\Param(name: "tag_toilet", type: "int", desc: "场站服务卫生间标识：1-无，2-有"),
        Apidoc\Param(name: "tag_canopy", type: "int", desc: "场站服务雨棚标识：1-无，2-有"),
        Apidoc\Param(name: "tag_rest", type: "int", desc: "场站服务休息室标识：1-无，2-有"),
        Apidoc\Param(name: "tag_pnp", type: "int", desc: "场站特色即插即用标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_insure", type: "int", desc: "场站特色充电保险标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_protect", type: "int", desc: "场站特色充电电池防护标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_ultrafast", type: "int", desc: "场站电桩超快充标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_fast", type: "int", desc: "场站电桩快充标识：1-不支持，2-支持"),
        Apidoc\Param(name: "tag_slow", type: "int", desc: "场站电桩慢充标识：1-不支持，2-支持"),

        Apidoc\Param(name: "center_lon", type: "float", desc: "当前经度"),
        Apidoc\Param(name: "center_lat", type: "float", desc: "当前纬度"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_class", type: "string", require: true, desc: "排序分类,可选值为【综合排序】【距离最近】"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '场站id'],
            ['name' => 'name', 'type' => 'string', 'desc' => '场站名称'],
            ['name' => 'all_address', 'type' => 'string', 'desc' => '场站地址'],
            ['name' => 'type', 'type' => 'int', 'desc' => '场站类型：1-公共、2-自营'],
            ['name' => 'status', 'type' => 'int', 'desc' => '运行状态：1-正常使用、2-维护中、3-未开放'],
            ['name' => 'place_rate', 'type' => 'int', 'desc' => '占位费（X分/分钟）,X*100'],
            ['name' => 'tag_pos', 'type' => 'int', 'desc' => '桩位置标示：1-地上,2-地库'],
            ['name' => 'tag_park', 'type' => 'string', 'desc' => '停车费说明'],
            ['name' => 'tag_toilet', 'type' => 'int', 'desc' => '场站服务卫生间标识：1-无，2-有'],
            ['name' => 'tag_canopy', 'type' => 'int', 'desc' => '场站服务雨棚标识：1-无，2-有'],
            ['name' => 'tag_rest', 'type' => 'int', 'desc' => '场站服务休息室标识：1-无，2-有'],
            ['name' => 'tag_pnp', 'type' => 'int', 'desc' => '场站特色即插即用标识：1-不支持，2-支持'],
            ['name' => 'tag_insure', 'type' => 'int', 'desc' => '场站特色充电保险标识：1-不支持，2-支持'],
            ['name' => 'tag_protect', 'type' => 'int', 'desc' => '场站特色充电电池防护标识：1-不支持，2-支持'],
            ['name' => 'tag_ultrafast', 'type' => 'int', 'desc' => '场站电桩超快充标识：1-不支持，2-支持'],
            ['name' => 'tag_fast', 'type' => 'int', 'desc' => '场站电桩快充标识：1-不支持，2-支持'],
            ['name' => 'tag_slow', 'type' => 'int', 'desc' => '场站电桩慢充标识：1-不支持，2-支持'],

            ['name' => 'lonlat', 'type' => 'string', 'desc' => '经纬度，前面纬度，后面经度'],
            ['name' => 'distance', 'type' => 'int', 'desc' => '距离'],
            ['name' => 'period_json_sum', 'type' => 'array', 'desc' => '当前时段费率', 'children' => [
                ['name' => 'fee', 'type' => 'string', 'desc' => '费用', 'children' => [
                    ['name' => 'fee', 'type' => 'int', 'desc' => '电费(精确到小数点后5位）'],
                    ['name' => 'ser_fee', 'type' => 'int', 'desc' => '服务费(精确到小数点后5位）'],
                ]],
                ['name' => 'rate', 'type' => 'int', 'desc' => '时段费率代码'],
                ['name' => 'period', 'type' => 'int', 'desc' => '时段'],
                ['name' => 'start_time', 'type' => 'int', 'desc' => '开始时间'],
                ['name' => 'end_time', 'type' => 'int', 'desc' => '结束时间'],
            ]],
            ['name' => 'shots_sum', 'type' => 'array', 'desc' => '枪数，key值是桩类型：1-慢充，2-快充，3超快充，', 'children' => [
                ['name' => 'sum_num', 'type' => 'int', 'desc' => '总枪数'],
                ['name' => 'idle_num', 'type' => 'int', 'desc' => '空闲数'],
            ]],
            ['name' => 'is_support_reservation', 'type' => 'int', 'desc' => '是否支持预约充电 0:不支持 1:支持'],
            ['name' => 'discount', 'type' => 'int', 'desc' => '[新增]优惠折扣(精确到小数点后1位)']
        ]),
    ]
    public function get_list(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
//            $order_name = ($data['order_name'] ?? false) ?: 'distance';
//            $order_type  = ($data['order_type'] ?? false) ?: 'asc';
            return app(StationsModel::class)->getListData($data, $page, $pageSize, $this->loginUser);
        });
    }

    #[
        Apidoc\Title("获取充电站详情"),
        Apidoc\Author("lwj 2023.8.29 新增，lwj 2023.9.18 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Stations/get_info"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "充电站id"),
        Apidoc\Param(name: "center_lon", type: "float", desc: "当前经度"),
        Apidoc\Param(name: "center_lat", type: "float", desc: "当前纬度"),

        Apidoc\Returned(name: "id", type: "int", desc: "充电站id"),
        Apidoc\Returned(name: "name", type: "string", desc: "充电站名称"),
        Apidoc\Returned(name: "all_address", type: "string", desc: "充电站地址"),
        Apidoc\Returned(name: "type", type: "int", desc: "场站类型：1-公共、2-自营"),
        Apidoc\Returned(name: "status", type: "int", desc: "运行状态：1-正常使用、2-维护中、3-未开放"),
        Apidoc\Returned(name: "place_rate", type: "int", desc: "占位费（X分/分钟）,X*100"),
        Apidoc\Returned(name: "tag_pos", type: "int", desc: "桩位置标示：1-地上,2-地库"),
        Apidoc\Returned(name: "tag_park", type: "string", desc: "停车费说明"),
        Apidoc\Returned(name: "tag_toilet", type: "int", desc: "场站服务卫生间标识：1-无，2-有"),
        Apidoc\Returned(name: "tag_canopy", type: "int", desc: "场站服务雨棚标识：1-无，2-有"),
        Apidoc\Returned(name: "tag_rest", type: "int", desc: "场站服务休息室标识：1-无，2-有"),
        Apidoc\Returned(name: "tag_pnp", type: "int", desc: "场站特色即插即用标识：1-不支持，2-支持"),
        Apidoc\Returned(name: "tag_insure", type: "int", desc: "场站特色充电保险标识：1-不支持，2-支持"),
        Apidoc\Returned(name: "tag_protect", type: "int", desc: "场站特色充电电池防护标识：1-不支持，2-支持"),
        Apidoc\Returned(name: "tag_ultrafast", type: "int", desc: "场站电桩超快充标识：1-不支持，2-支持"),
        Apidoc\Returned(name: "tag_fast", type: "int", desc: "场站电桩快充标识：1-不支持，2-支持"),
        Apidoc\Returned(name: "tag_slow", type: "int", desc: "场站电桩慢充标识：1-不支持，2-支持"),
        Apidoc\Returned(name: "lonlat", type: "string", desc: "经纬度，前面纬度，后面经度"),
        Apidoc\Returned(name: "distance", type: "int", desc: "距离"),
        Apidoc\Returned(name: "period_json_sum", type: "array", desc: "当前时段费率", children: [
            ['name' => 'fee', 'type' => 'string', 'desc' => '费用', 'children' => [
                ['name' => 'fee', 'type' => 'int', 'desc' => '电费(精确到小数点后5位）'],
                ['name' => 'ser_fee', 'type' => 'int', 'desc' => '服务费(精确到小数点后5位）'],
            ]],
            ['name' => 'rate', 'type' => 'int', 'desc' => '时段费率代码'],
            ['name' => 'period', 'type' => 'int', 'desc' => '时段'],
            ['name' => 'start_time', 'type' => 'int', 'desc' => '开始时间'],
            ['name' => 'end_time', 'type' => 'int', 'desc' => '结束时间'],
        ]),
        Apidoc\Returned(name: "shots_sum", type: "array", desc: "枪数，key值是桩类型：1-慢充，2-快充，3超快充", children: [
            ['name' => 'sum_num', 'type' => 'int', 'desc' => '总枪数'],
            ['name' => 'idle_num', 'type' => 'int', 'desc' => '空闲数'],
        ]),
        Apidoc\Returned(name: "work_time", type: "string", desc: "运营时段描述"),
        Apidoc\Returned(name: "pictures", type: "array", desc: "场站图片url集"),
        Apidoc\Returned(name: "is_collect", type: "bool", desc: "是否收藏"),
        Apidoc\Returned(name: "is_support_reservation", type: "int", desc: "是否支持预约充电 0:不支持 1:支持"),
        Apidoc\Returned(name: "discount", type: "int", desc: "[新增]优惠折扣(精确到小数点后1位)")
    ]
    public function get_info(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();

            //longitude 经度，浮点数，范围为-180~180，负数表示西经
            //latitude 纬度，浮点数，范围为-90~90，负数表示南纬
            $center_lon = ($data['center_lon'] ?? false) ?: 114.12772;
            $center_lat = ($data['center_lat'] ?? false) ?: 22.54764;

            $verify_data = v::input($data, VerifyData::stations([
                'id',
            ]));

            if ($user) {
                $field = [
                    "a.id", "a.name", "a.all_address", "a.type",
                    "sei.status", "sei.tag_pos", "sei.tag_park", "sei.place_rate",
                    "sei.tag_toilet", "sei.tag_canopy", "sei.tag_rest", "sei.tag_pnp", "sei.tag_insure",
                    "sei.tag_protect", "sei.tag_ultrafast", "sei.tag_fast", "sei.tag_slow", "a.lonlat",
                    "ST_Distance_Sphere(ST_PointFromText(CONCAT('POINT(', SUBSTRING_INDEX(a.lonlat, ',', -1) ,' ', SUBSTRING_INDEX(a.lonlat, ',', 1), ')')), ST_PointFromText('POINT($center_lon $center_lat)')) AS distance",
                    "sei.work_time", "sei.pictures", "f.period_json_sum","f.surcharge",
                    "group_concat(DISTINCT CONCAT(d.type, '-', e.id) SEPARATOR ',') as shots_ids",
                    "g.id as is_collect", "sei.is_support_reservation"
                ];
                $info = $this->modelClass->alias('a')
                    ->append(['shots_sum', 'test'])
                    ->json(['period_json_sum', 'pictures'])
                    ->join('piles d', 'a.id = d.station_id')
                    ->join('shots e', 'a.id = e.station_id and d.id=e.piles_id')
                    ->leftJoin('users_collect_stations g', 'a.id = g.station_id and g.user_id="' . $user->id . '"')
                    ->join('stations_extra_info sei', 'sei.id = a.id')
                    ->join('tariff_group f', 'sei.tariff_group_id = f.id')
                    ->field($field)
                    ->withAttr('period_json_sum', function ($value) {
//                    return $value;

                        $current_time = date('H:i'); // 获取当前时间，格式为小时:分钟

                        $sum_fee = [];
                        foreach ($value as &$v) {
                            $v['current'] = false;
                            $sum_fee[] = $v['sum_fee'];

                            $start_time = $v['start_time'];
                            $end_time = $v['end_time'];
                            if ($end_time == '0:00' || $end_time == '00:00') $end_time = '24:00';
                            if ($current_time >= $start_time && $current_time < $end_time) {
                                $v['current'] = true;
                            }
                        }
                        $max_fee = max($sum_fee);
                        $min_fee = min($sum_fee);

                        foreach ($value as &$v2) {
                            if ($v2['sum_fee'] === $min_fee) {
                                $v2['max_or_min'] = 'min';
                            } elseif ($v2['sum_fee'] === $max_fee) {
                                $v2['max_or_min'] = 'max';
                            } else {
                                $v2['max_or_min'] = '';
                            }
                        }

                        return $value;
                    })
                    ->withAttr('shots_sum', function ($value, $data) {
                        return (new ShotsStatus())->getShotsIdleAndTotalCount($data['id']);
                    })
                    ->withAttr('is_collect', function ($value) {
                        if ($value) return true;
                        return false;
                    })
                    ->where('a.id', $verify_data['id'])
                    ->group('a.id')
                    ->find()
                    ->toArray();

                    foreach ($info['period_json_sum'] as &$period) {
                        $surcharge = $info['surcharge'];
                        // 确保服务费不会小于0
                        $period['fee']['ser_fee'] = max(0, $period['fee']['ser_fee'] + $surcharge);
                        $period['sum_fee'] = $period['fee']['fee'] + $period['fee']['ser_fee'];
                    }
            } else {
                $field = [
                    "a.id", "a.name", "a.all_address", "a.type", "sei.status", "sei.tag_pos", "sei.tag_park", "sei.place_rate", "sei.tag_toilet", "sei.tag_canopy", "sei.tag_rest",
                    "sei.tag_pnp", "sei.tag_insure", "sei.tag_protect", "sei.tag_ultrafast", "sei.tag_fast", "sei.tag_slow", "a.lonlat",
                    "ST_Distance_Sphere(ST_PointFromText(CONCAT('POINT(', SUBSTRING_INDEX(a.lonlat, ',', -1) ,' ', SUBSTRING_INDEX(a.lonlat, ',', 1), ')')), ST_PointFromText('POINT($center_lon $center_lat)')) AS distance",
                    "sei.work_time", "sei.pictures", "group_concat(DISTINCT CONCAT(d.type, '-', e.id) SEPARATOR ',') as shots_ids",
                    "sei.is_support_reservation"
                ];
                $info = $this->modelClass->alias('a')
                    ->append(['shots_sum', 'period_json_sum', 'is_collect'])
                    ->json(['period_json_sum', 'pictures'])
                    ->join('piles d', 'a.id = d.station_id')
                    ->join('shots e', 'a.id = e.station_id and d.id = e.piles_id')
                    ->join('stations_extra_info sei', 'sei.id = a.id')
                    ->field($field)
                    ->withAttr('shots_sum', function ($value, $data) {
                        return (new ShotsStatus())->getShotsIdleAndTotalCount($data['id']);
                    })
                    ->where('a.id', $verify_data['id'])
                    ->group('a.id')
                    ->find()
                    ->toArray();
            }


            // ========== 从微服务查询充电站信息(是否需要用户输入车牌) ==========
            $result = Api::send("/device/stations/info",'GET',[
                'id' => $verify_data['id'],
            ])['data'];
            $info['is_require_plate'] = $result['is_require_plate'];
            $info['discount'] = (new Activity())->getStationDiscount($info['id']);

            if (!$info) return $this->res_error([], '充电站不存在');
            return $this->res_success($info);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }


    #[
        Apidoc\Title("收藏充电站"),
        Apidoc\Author("lwj 2023.9.9 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Stations/collect_stations"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "充电站id"),
    ]
    public function collect_stations(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::stations([
                'id',
            ]));

            $stations_id = $this->modelClass
                ->where('id', $verify_data['id'])
                ->value('id');
            if (!$stations_id) return $this->res_error([], '充电站不存在');

            $users_collect_stations_model = new UsersCollectStationsModel();

            $collect_id = $users_collect_stations_model
                ->where('user_id', $user->id)
                ->where('station_id', $stations_id)
                ->value('id');

            if ($collect_id) {
                $res = $users_collect_stations_model
                    ->where('id', $collect_id)
                    ->limit(1)
                    ->delete();
                if ($res) return $this->res_success([], '取消收藏成功');
                return $this->res_error([], '取消收藏失败');
            }

            $res = $users_collect_stations_model
                ->insert([
                    'user_id' => $user->id,
                    'station_id' => $stations_id,
                ]);
            if ($res) return $this->res_success([], '收藏成功');
            return $this->res_error([], '收藏失败');

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            trace('收藏充电站:处理异常=》' . $e->getMessage(), '错误');
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }


    #[
        Apidoc\Title("获取用户收藏充电站列表"),
        Apidoc\Author("lwj 2023.9.9 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Stations/get_user_collect_stations_list"),
        Apidoc\Param(name: "center_lon", type: "float", desc: "当前经度"),
        Apidoc\Param(name: "center_lat", type: "float", desc: "当前纬度"),
        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '场站id'],
            ['name' => 'name', 'type' => 'string', 'desc' => '场站名称'],
            ['name' => 'all_address', 'type' => 'string', 'desc' => '场站地址'],
            ['name' => 'type', 'type' => 'int', 'desc' => '场站类型：1-公共、2-自营'],
            ['name' => 'status', 'type' => 'int', 'desc' => '运行状态：1-正常使用、2-维护中、3-未开放'],
            ['name' => 'place_rate', 'type' => 'int', 'desc' => '占位费（X分/分钟）,X*100'],
            ['name' => 'tag_pos', 'type' => 'int', 'desc' => '桩位置标示：1-地上,2-地库'],
            ['name' => 'tag_park', 'type' => 'string', 'desc' => '停车费说明'],
            ['name' => 'tag_toilet', 'type' => 'int', 'desc' => '场站服务卫生间标识：1-无，2-有'],
            ['name' => 'tag_canopy', 'type' => 'int', 'desc' => '场站服务雨棚标识：1-无，2-有'],
            ['name' => 'tag_rest', 'type' => 'int', 'desc' => '场站服务休息室标识：1-无，2-有'],
            ['name' => 'tag_pnp', 'type' => 'int', 'desc' => '场站特色即插即用标识：1-不支持，2-支持'],
            ['name' => 'tag_insure', 'type' => 'int', 'desc' => '场站特色充电保险标识：1-不支持，2-支持'],
            ['name' => 'tag_protect', 'type' => 'int', 'desc' => '场站特色充电电池防护标识：1-不支持，2-支持'],
            ['name' => 'tag_ultrafast', 'type' => 'int', 'desc' => '场站电桩超快充标识：1-不支持，2-支持'],
            ['name' => 'tag_fast', 'type' => 'int', 'desc' => '场站电桩快充标识：1-不支持，2-支持'],
            ['name' => 'tag_slow', 'type' => 'int', 'desc' => '场站电桩慢充标识：1-不支持，2-支持'],

            ['name' => 'lonlat', 'type' => 'string', 'desc' => '经纬度，前面纬度，后面经度'],
            ['name' => 'distance', 'type' => 'int', 'desc' => '距离'],
            ['name' => 'period_json_sum', 'type' => 'array', 'desc' => '当前时段费率', 'children' => [
                ['name' => 'fee', 'type' => 'string', 'desc' => '费用', 'children' => [
                    ['name' => 'fee', 'type' => 'int', 'desc' => '电费(精确到小数点后5位）'],
                    ['name' => 'ser_fee', 'type' => 'int', 'desc' => '服务费(精确到小数点后5位）'],
                ]],
                ['name' => 'rate', 'type' => 'int', 'desc' => '时段费率代码'],
                ['name' => 'period', 'type' => 'int', 'desc' => '时段'],
                ['name' => 'start_time', 'type' => 'int', 'desc' => '开始时间'],
                ['name' => 'end_time', 'type' => 'int', 'desc' => '结束时间'],
            ]],
            ['name' => 'shots_sum', 'type' => 'array', 'desc' => '枪数，key值是桩类型：1-慢充，2-快充，3超快充，', 'children' => [
                ['name' => 'sum_num', 'type' => 'int', 'desc' => '总枪数'],
                ['name' => 'idle_num', 'type' => 'int', 'desc' => '空闲数'],
            ]],
            ['name' => 'is_support_reservation', 'type' => 'int', 'desc' => '是否支持预约充电 0:不支持 1:支持'],
            ['name' => 'discount', 'type' => 'int', 'desc' => '[新增]优惠折扣(精确到小数点后1位)'],
        ]),
    ]
    public function get_user_collect_stations_list(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;

            return (new StationsModel())->getUserCollectStationsList($data, $page, $pageSize, $this->loginUser);
        });
    }


    #[
        Apidoc\Title("搜索充电站"),
        Apidoc\Author("lwj 2023.9.13 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Stations/search"),
        Apidoc\Param(name: "s", type: "string", desc: "关键词"),
        Apidoc\Param(name: "region", type: "string", desc: "地区，默认深圳市"),

        Apidoc\Param(name: "center_lon", type: "float", desc: "当前经度"),
        Apidoc\Param(name: "center_lat", type: "float", desc: "当前纬度"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
//        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
//        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),
        Apidoc\Param(name: "order_class", type: "string", require: true, desc: "排序分类,可选值为【综合排序】【距离最近】"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '场站id'],
            ['name' => 'name', 'type' => 'string', 'desc' => '场站名称'],
            ['name' => 'all_address', 'type' => 'string', 'desc' => '场站地址'],
            ['name' => 'type', 'type' => 'int', 'desc' => '场站类型：1-公共、2-自营'],
            ['name' => 'status', 'type' => 'int', 'desc' => '运行状态：1-正常使用、2-维护中、3-未开放'],
            ['name' => 'place_rate', 'type' => 'int', 'desc' => '占位费（X分/分钟）,X*100'],
            ['name' => 'tag_pos', 'type' => 'int', 'desc' => '桩位置标示：1-地上,2-地库'],
            ['name' => 'tag_park', 'type' => 'string', 'desc' => '停车费说明'],
            ['name' => 'tag_toilet', 'type' => 'int', 'desc' => '场站服务卫生间标识：1-无，2-有'],
            ['name' => 'tag_canopy', 'type' => 'int', 'desc' => '场站服务雨棚标识：1-无，2-有'],
            ['name' => 'tag_rest', 'type' => 'int', 'desc' => '场站服务休息室标识：1-无，2-有'],
            ['name' => 'tag_pnp', 'type' => 'int', 'desc' => '场站特色即插即用标识：1-不支持，2-支持'],
            ['name' => 'tag_insure', 'type' => 'int', 'desc' => '场站特色充电保险标识：1-不支持，2-支持'],
            ['name' => 'tag_protect', 'type' => 'int', 'desc' => '场站特色充电电池防护标识：1-不支持，2-支持'],
            ['name' => 'tag_ultrafast', 'type' => 'int', 'desc' => '场站电桩超快充标识：1-不支持，2-支持'],
            ['name' => 'tag_fast', 'type' => 'int', 'desc' => '场站电桩快充标识：1-不支持，2-支持'],
            ['name' => 'tag_slow', 'type' => 'int', 'desc' => '场站电桩慢充标识：1-不支持，2-支持'],

            ['name' => 'lonlat', 'type' => 'string', 'desc' => '经纬度，前面纬度，后面经度'],
            ['name' => 'distance', 'type' => 'int', 'desc' => '距离'],
            ['name' => 'period_json_sum', 'type' => 'array', 'desc' => '当前时段费率', 'children' => [
                ['name' => 'fee', 'type' => 'string', 'desc' => '费用', 'children' => [
                    ['name' => 'fee', 'type' => 'int', 'desc' => '电费(精确到小数点后5位）'],
                    ['name' => 'ser_fee', 'type' => 'int', 'desc' => '服务费(精确到小数点后5位）'],
                ]],
                ['name' => 'rate', 'type' => 'int', 'desc' => '时段费率代码'],
                ['name' => 'period', 'type' => 'int', 'desc' => '时段'],
                ['name' => 'start_time', 'type' => 'int', 'desc' => '开始时间'],
                ['name' => 'end_time', 'type' => 'int', 'desc' => '结束时间'],
            ]],
            ['name' => 'shots_sum', 'type' => 'array', 'desc' => '枪数，key值是桩类型：1-慢充，2-快充，3超快充，', 'children' => [
                ['name' => 'sum_num', 'type' => 'int', 'desc' => '总枪数'],
                ['name' => 'idle_num', 'type' => 'int', 'desc' => '空闲数'],
            ]],
            ['name' => 'is_support_reservation', 'type' => 'int', 'desc' => '是否支持预约充电 0:不支持 1:支持'],
            ['name' => 'discount', 'type' => 'int', 'desc' => '[新增]优惠折扣(精确到小数点后1位)'],
        ]),
    ]
    public function search(): Json
    {
        try {
            $data = $this->request->post();
            $s = $this->request->post('s');

            //longitude 经度，浮点数，范围为-180~180，负数表示西经
            //latitude 纬度，浮点数，范围为-90~90，负数表示南纬
            $center_lon = ($data['center_lon'] ?? false) ?: 114.12772;
            $center_lat = ($data['center_lat'] ?? false) ?: 22.54764;
            $region = ($data['region'] ?? false) ?: '深圳市';

            $query_params = [
                'boundary' => 'region(' . $region . ',0)',
                'keyword' => $s,
                'page_size' => 5,
                'page_index' => 1,
                'key' => config('my.qq_map_key'),
            ];

            $queryString = http_build_query($query_params);
            $path = '/ws/place/v1/search';
            $url = 'https://apis.map.qq.com' . $path . '?' . $queryString;
            $res_arr = send_get_json_to_shuzu($url);
            $region_list = [];
            if (!empty($res_arr['data'])) $region_list = $res_arr['data'];


            // 搜索框搜索
            $where = function ($query) use ($s) {
                if ($s) {
                    $query->where('a.name|a.all_address', 'like', '%' . $s . '%');
                }
                return $query;
            };

            $field = [
                "a.id", "a.name", "a.all_address", "a.lonlat",
                "ST_Distance_Sphere(ST_PointFromText(CONCAT('POINT(', SUBSTRING_INDEX(a.lonlat, ',', -1) ,' ', SUBSTRING_INDEX(a.lonlat, ',', 1), ')')), ST_PointFromText('POINT($center_lon $center_lat)')) AS distance",
                "sei.is_support_reservation"
            ];
            $sql1 = $this->modelClass->alias('a')
                ->join('piles d', 'a.id = d.station_id')
                ->join('shots e', 'a.id = e.station_id and d.id=e.piles_id')
                ->join('stations_extra_info sei', 'sei.id = a.id')
                ->field($field)
                ->where($where)
                ->group('a.id')
                ->buildSql();

            $activityModel = new Activity();

            $list = Db::table($sql1 . ' s')
                ->append(['discount'])
                ->withAttr('shots_sum', function ($value, $data) {
                    return (new ShotsStatus())->getShotsIdleAndTotalCount($data['id']);
                })
                ->withAttr('lonlat', function ($value) {
                    if ($value) {
                        $arr = explode(',', $value);
                        return [
                            'lat' => (float)$arr[0],
                            'lng' => (float)$arr[1],
                        ];
                    }
                    return null;
                })
                ->withAttr('discount', function ($value, $data) use ($activityModel) {
                    return $activityModel->getStationDiscount($data['id']);
                })
                ->limit(5)
                ->select()
                ->toArray();

            return $this->res_success([
                'region_list' => $region_list,
                'station_list' => $list
            ]);

        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }


}