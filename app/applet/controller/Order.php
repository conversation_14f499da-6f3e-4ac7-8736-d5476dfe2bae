<?php
/** @noinspection PhpUnused */

/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */

namespace app\applet\controller;

use app\common\lib\VerifyData;
use app\common\model\ElectronicInvoiceApplyDetail;
use app\common\model\Order as OrderModel;
use app\common\traits\Curd;
use hg\apidoc\annotation as Apidoc;
use Predis\Client as Predis;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\response\Json;
use Throwable;


#[Apidoc\Title("充电订单")]
class Order extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new OrderModel();
    }

    #[
        Apidoc\Title("获取充电订单列表"),
        Apidoc\Author("lwj 2023.8.24 新增，lwj 2023.8.31 修改, cbj 2023.11.3 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Order/get_order_list"),
        Apidoc\Param(name: "id", type: "string", desc: "交易流水号"),
        Apidoc\Param(name: "status", type: "array|int", desc: "订单状态 1:下单 3:充电中 5:完成 6:异常结束 7:自动结算 8:预约"),
        Apidoc\Param(name: "start_time", type: "string", desc: "开始时间"),
        Apidoc\Param(name: "end_time", type: "string", desc: "结束时间"),
        Apidoc\Param(name: "scene", type: "int", require: false, default: 0, desc: "应用场景 0:默认 1:开具发票"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '交易流水号'],
            ['name' => 'sequence', 'type' => 'int', 'desc' => '枪号'],
            ['name' => 'type', 'type' => 'int', 'desc' => '[变更]订单类型 1:立即充电 2:预约充电'],
            ['name' => 'status', 'type' => 'int', 'desc' => '[变更]订单状态 1:下单 3:充电中 5:完成 6:异常结束 7:自动结算 8:预约'],
            ['name' => 'money', 'type' => 'int', 'desc' => '订单应付金额（分）'],
            ['name' => 'pay_money', 'type' => 'int', 'desc' => '订单实付金额（分）'],
            ['name' => 'coupon_money', 'type' => 'int', 'desc' => '订单优惠金额（分）'],
            ['name' => 'create_time', 'string' => 'int', 'desc' => '订单创建时间'],
            ['name' => 'trans_start_time', 'type' => 'string', 'desc' => '充电桩交易开始时间'],
            ['name' => 'trans_end_time', 'type' => 'string', 'desc' => '充电桩交易结束时间'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '充电站名称'],
            ['name' => 'trans_time_diff', 'type' => 'string', 'desc' => '交易时间差'],
            ['name' => 'is_invoiced', 'type' => 'int', 'require' => false, 'desc' => '是否已开票 0:否 1:是(该字段只有在场景[scene]为开具发票时才返回)'],
            ['name' => 'amount_charged', 'type' => 'int', 'require' => true, 'desc' => '充电已产生费用(精确到小数点后四位)'],
            ['name' => 'reservation_time', 'type' => 'null|string', 'require' => true, 'desc' => '预约的时间(格式:YYYY-mm-dd HH:ii:ss)，只有当订单类型为预约充电时，该字段才有值。'],
        ]),
    ]
    public function get_order_list(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();
            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
            $order_name = ($data['order_name'] ?? false) ?: 'a.create_time';
            $order_type = ($data['order_type'] ?? false) ?: 'desc';

            // 搜索框搜索
            $where = function ($query) use ($data) {
                if (isset($data['id']) && $data['id']) {
                    $query->where('a.id', $data['id']);
                }
                if (isset($data['status']) && $data['status']) {
                    if (is_array($data['status'])) {
                        $query->where('a.status', 'in', $data['status']);
                    } else {
                        $query->where('a.status', $data['status']);
                    }
                }

                if (isset($params['start_time']) && isset($params['end_time']) && $params['start_time'] && $params['end_time']) {
                    $query->where('a.create_time >= "' . $params['start_time'] . ' 00:00:00"');
                    $query->where('a.create_time <= "' . $params['end_time'] . ' 23:59:59"');
                }

                return $query;
            };

            $field = [
                'a.id', 'a.sequence', 'a.type', 'a.status', 'a.money', 'a.pay_money',
                'c.name as station_name', 'a.amount_charged', 'a.reservation_time',
                'a.coupon_money', 'a.create_time', 'a.trans_start_time', 'a.trans_end_time',
            ];

            $list = $this->modelClass->alias('a')
                ->append(['trans_time_diff'])
                ->join('stations c', 'a.station_id = c.id')
                ->field($field)
                ->withAttr('trans_time_diff', function ($value, $data) {
                    if ($data['status'] === OrderModel::StatusCharging) {
                        if ($data['trans_start_time']) {
                            return get_date_diff_format($data['trans_start_time'], date('Y-m-d H:i:s'));
                        }
                    } else {
                        if ($data['trans_start_time'] && $data['trans_end_time']) {
                            return get_date_diff_format($data['trans_start_time'], $data['trans_end_time']);
                        }
                    }
                    return '0秒';
                })
                ->where('a.user_id', $user->id)
                ->where('a.build_type', 1)
                ->where($where)
                ->order($order_name, $order_type)
                ->paginate(['list_rows' => $pageSize, 'page' => $page])
                ->toArray();

            // 如果应该场景是开具发票
            if (isset($data['scene']) && $data['scene'] === 1) {
                // 那么检测当前这些订单是否已经开具发票
                $orderIds = array_column($list['data'], 'id');
                $invoicedOrderIds = [];
                if (!empty($orderIds)) {
                    $ElectronicInvoiceApplyDetail = app(ElectronicInvoiceApplyDetail::class);
                    $invoicedOrderIds = $ElectronicInvoiceApplyDetail->filterExistEffectiveOrderIds($orderIds);
                }

                foreach ($list['data'] as &$value) {
                    if (in_array($value['id'], $invoicedOrderIds) === true) {
                        $value['is_invoiced'] = 1;
                    } else {
                        $value['is_invoiced'] = 0;
                    }
                }

            }

            return $this->res_success($list);

        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取充电订单排序信息"),
        Apidoc\Author("lwj 2023.8.25 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/applet/Order/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        $res = [
            'order_name' => 'a.create_time',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'a.id', 'label' => '交易流水号'],
                ['value' => 'a.create_time', 'label' => '订单创建时间'],
                ['value' => 'a.station_id', 'label' => '充电站'],
                ['value' => 'a.piles_id', 'label' => '充电桩'],
                ['value' => 'a.status', 'label' => '订单状态'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("获取充电中订单列表"),
        Apidoc\Author("lwj 2023.8.24 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/applet/Order/get_charging_order"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "订单列表", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '交易流水号'],
            ['name' => 'status', 'type' => 'int', 'desc' => '[变更]订单状态 1:下单 3:充电中 5:完成 6:异常结束 7:自动结算 8:预约'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '订单创建时间'],
        ]),
    ]
    public function get_charging_order(): Json
    {
        try {
            $user = $this->loginUser;

            $list = $this->modelClass
                ->field('id,status,create_time')
                ->where('user_id', $user->id)
                ->where('build_type', 1)
                ->where('status', '<', 4)
                ->order('create_time', 'desc')
                ->select()
                ->toArray();

            return $this->res_success($list);

        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取充电订单详情"),
        Apidoc\Author("lwj 2023.8.30 新增，lwj 2023.9.9 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Order/get_order_info"),
        Apidoc\Param(name: "id", type: "string", require: true, desc: "交易流水号"),

        Apidoc\Returned(name: "id", type: "string", desc: "交易流水号"),
        Apidoc\Returned(name: "type", type: "int", desc: "[变更]订单类型 1:立即充电 2:预约充电"),
        Apidoc\Returned(name: "pay_mode", type: "int", desc: "支付方式：1微信、2支付宝、3零钱、4会员卡"),
        Apidoc\Returned(name: "status", type: "int", desc: "[变更]订单状态 1:下单 3:充电中 5:完成 6:异常结束 7:自动结算 8:预约"),
        Apidoc\Returned(name: "money", type: "int", desc: "订单应付金额（分）"),
        Apidoc\Returned(name: "pay_money", type: "int", desc: "订单实付金额（分）"),
        Apidoc\Returned(name: "coupon_money", type: "int", desc: "订单优惠金额（分）"),
        Apidoc\Returned(name: "freeze_money", type: "int", desc: "订单冻结金额（分）"),
        Apidoc\Returned(name: "currency", type: "int", desc: "币种：1:CNY,2:USD"),
        Apidoc\Returned(name: "card_id", type: "int", desc: "会员卡id"),
        Apidoc\Returned(name: "user_id", type: "int", desc: "订单创建用户id"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "订单创建时间"),
        Apidoc\Returned(name: "trans_start_time", type: "string", desc: "充电桩交易开始时间"),
        Apidoc\Returned(name: "trans_end_time", type: "string", desc: "充电桩交易结束时间"),
        Apidoc\Returned(name: "electricity_price", type: "int", desc: "电价（分）"),
        Apidoc\Returned(name: "ser_price", type: "int", desc: "服务费（分）"),
        Apidoc\Returned(name: "refund", type: "int", desc: "退款（分）"),
        Apidoc\Returned(name: "stations_name", type: "string", desc: "场站名称"),
        Apidoc\Returned(name: "shot_id", type: "int", desc: "枪id"),
        Apidoc\Returned(name: "electricity_total", type: "int", desc: "总电量，精确小数点后4位"),
        Apidoc\Returned(name: "is_collect", type: "bool", desc: "是否收藏"),
        Apidoc\Returned(name: "reason_for_stop", type: "int", desc: "停止原因代码"),
        Apidoc\Returned(name: "reason_for_stop_text", type: "string", desc: "停止原因文本"),
        Apidoc\Returned(name: "msg", type: "string", desc: "信息"),
        Apidoc\Returned(name: "reservation_time", type: "null|string", desc: "[新增]预约的时间(格式:YYYY-mm-dd HH:ii:ss)，只有当订单类型为预约充电时，该字段才有值。"),

    ]
    public function get_order_info(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();

            $verify_data = v::input($data, VerifyData::charging_order([
                'id',
            ]));


            $field = [
                'a.id',
                'a.type',
                'a.pay_mode',
                'a.status',
                'a.money',
                'a.pay_money',
                'a.coupon_money',
                'a.freeze_money',
                'a.currency',
                'a.card_id',
                'a.user_id',
                'a.create_time',
                'a.trans_start_time',
                'a.trans_end_time',
                'a.electricity_price',
                'a.ser_price',
                'a.freeze_money-a.pay_money as refund',
                'b.name as stations_name',
                'a.shot_id',
                'a.electricity_total',
                'a.station_id',
                'g.id as is_collect',
                'a.reason_for_stop',
                'a.reason_for_stop_text',
                'a.msg',
                'a.reservation_time'
            ];
            $info = $this->modelClass->alias('a')
                ->append(['price', 'ser_price', 'refund'])
                ->join('stations b', 'a.station_id = b.id')
                ->leftJoin('users_collect_stations g', 'a.station_id = g.station_id and g.user_id="' . $user->id . '"')
                ->where('a.id', $verify_data['id'])
                ->field($field)
                ->withAttr('is_collect', function ($value) {
                    if ($value) return true;
                    return false;
                })
                ->find();

                // 长三院场站兼容代码
                if (str_contains($info['stations_name'],'长三院')){
                    $info['is_only_show_sum'] = true;
                }
            if (!$info) return $this->res_error([], '订单不存在');
            return $this->res_success($info);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取充电订单状态"),
        Apidoc\Author("lwj 2023.9.20 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Order/get_order_status"),
        Apidoc\Param(name: "id", type: "string", require: true, desc: "交易流水号"),

        Apidoc\Returned(name: "order_id", type: "string", desc: "交易流水号"),
        Apidoc\Returned(name: "order_status", type: "int", desc: "[变更]订单状态 1:下单 3:充电中 5:完成 6:异常结束 7:自动结算 8:预约"),
        Apidoc\Returned(name: "last_order", type: "string", desc: "充电枪最后订单号"),
        Apidoc\Returned(name: "last_status", type: "string", desc: "充电枪最后状态： 0离线，1故障，2空闲，3充电"),
        Apidoc\Returned(name: "startup_status", type: "string", desc: "启动状态： 创建订单、收到数据"),
        Apidoc\Returned(name: "order_last_time", type: "int", desc: "订单最后时间戳(秒)"),
        Apidoc\Returned(name: "shots_last_time", type: "int", desc: "充电枪最后时间戳(秒)"),

    ]
    public function get_order_status(): Json
    {
        try {
            $data = $this->request->post();

            $verify_data = v::input($data, VerifyData::charging_order([
                'id',
            ]));

            $order = $this->modelClass
                ->where('id', $verify_data['id'])
                ->find();
            if (!$order) return $this->res_error([], '订单不存在');
            $order_id = $order['id'];
            $shots_ids = $order['shot_id'];

            $redis5 = new Predis(config('my.redis5'));
//            $startup_status = $redis5->get('运营平台远程控制启机-状态-' . $order_id);
            $orderContext = app(\app\common\context\Order::class);
            $startup_status = $orderContext->getOrderStatus($order_id);
            $order_last_time = intval($redis5->get('上传实时监测数据处理-订单-最后时间-' . $order_id));
            $shots_last_time = intval($redis5->get('上传实时监测数据处理-充电枪-最后时间-' . $shots_ids));
            $last_status = $redis5->get('上传实时监测数据处理-充电枪-最后状态-' . $shots_ids);
            $last_order = $redis5->get('上传实时监测数据处理-充电枪-最后订单号-' . $shots_ids);

            return $this->res_success([
                'order_id' => $order_id,
                'order_status' => $order['status'],
                'startup_status' => $startup_status,
                'order_last_time' => $order_last_time,
                'shots_last_time' => $shots_last_time,
                'last_status' => $last_status,
                'last_order' => $last_order,
            ]);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }


}