<?php
/** @noinspection PhpUndefinedFieldInspection */


namespace app\applet\middleware;


use app\common\cache\redis\entity\AppletLoginUser;
use app\common\log\LogCollector;
use app\common\model\Users;
use Closure;
use think\Request;
use think\Response;
use Throwable;
use app\common\cache\redis\AppletUserLogin;

class Auth
{
    /**
     * 小程序验证权限中间件
     * lwj 2023.7.17 新增
     * lwj 2024.8.13 修改
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $url = strtolower($request->baseUrl());
            $allow_url = [
                '/applet/login/get_qr',
                '/applet/login/login_test',
                '/applet/login/wechat_applet_login',
                '/applet/login/wechat_applet_phone_login',
                '/applet/pay/weixin_callback',
                '/applet/pay/weixin_refund_callback',
                '/applet/common/get_host',
                '/applet/common/city_list',
                '/applet/common/get_agreement',
                '/applet/common/get_driving_distance',
                '/applet/common/get_driving_distance_all',
                '/applet/common/get_driving_distance_matrix_all',
                // 电子发票回调接口
                '/applet/wechat/electronic_invoice_callback',
                // 更新电子发票相关配置
                '/applet/wechat/update_config'
            ];
            if (in_array($url, $allow_url)) return $next($request);

            $token = $request->header('authorization');

            $no_login_url = [
                '/applet/stations/get_list',
                '/applet/stations/get_info',
            ];
            $AppletUserLogin = app(AppletUserLogin::class);
            if (in_array($url, $no_login_url)) {
                LogCollector::collectorRunLog('当前接口不需要登录权限');
                if (!$token) return $next($request);

                // 获取用户登录数据
                $token_user = $AppletUserLogin->getUserLoginData($token);
                if (!$token_user) return $next($request);

                $user = (new Users)->where('id', $token_user['id'])->find();
                if (!$user) return $next($request);
                $token2 = sha1($token_user['last_time'] . $user['id'] . '-' . $user['open_id']);
                if ($token !== $token2) return $next($request);
                // new AppletLoginUser($token_user);
                $request->appletLoginUser = new AppletLoginUser($token_user);

                // 延长登录有效期
//                $AppletUserLogin->extendLoginValidityTime($token);
                return $next($request);
            }

            if (!$token) {
                LogCollector::collectorRunLog('当前接口需要登录权限，但是没有传递令牌');
                return res_error_login();
            }

            // 获取用户登录数据
            $token_user = $AppletUserLogin->getUserLoginData($token);
            if (!$token_user) return res_error_login();

            $user = (new Users)->where('id', $token_user['id'])->find();
            if (!$user) {
                LogCollector::collectorRunLog('本次操作用户尚未登录');
                return res_error_login();
            }
            LogCollector::collectorRunLog(sprintf('小程序操作用户：%s', $user['phone']));
            $token2 = sha1($token_user['last_time'] . $user['id'] . '-' . $user['open_id']);
            if ($token !== $token2) return res_error_login();
            $request->appletLoginUser = new AppletLoginUser($token_user);

            return $next($request);
        } catch (Throwable $e) {
            LogCollector::collectorRunLog(
                sprintf('用户鉴权中间件异常：异常文件=%s, 异常代码行数=%d, 异常描述=%s, 异常状态码=%s',
                    $e->getFile(), $e->getLine(), $e->getMessage(), $e->getCode()
                ), LogCollector::LevelError);
            return res_error([], '中间件处理异常：' . $e->getMessage(), 500);
        }
    }
}