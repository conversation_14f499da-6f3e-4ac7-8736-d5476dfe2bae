<?php
declare (strict_types = 1);

namespace app\validate;

use think\Validate;

class AdminUser extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'name'  =>  'require|max:64|min:3',
        'password'  =>  'require|max:12|min:6',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'name.require' => '请输入用户名',
        'name.min'     => '用户名不能少于3个字符',
        'name.max'     => '用户名不能超过64个字符',
        'password.require' => '请输入密码',
        'password.min'     => '密码不能少于6个字符',
        'password.max'     => '密码不能超过64个字符',
    ];
}
