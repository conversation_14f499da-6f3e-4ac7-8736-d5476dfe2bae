更新桩相线和空开一致
```sql
UPDATE piles p
  INNER JOIN air_switch_to_piles asp ON p.id = asp.piles_id
  INNER JOIN air_switch a ON asp.air_switch_id = a.id
  SET p.phase_name = a.phase_name
WHERE
  p.is_del = 0   -- 仅更新未删除的桩
  AND a.is_del = 0; -- 仅关联未删除的空开
```


- 导入场站数据；
  - 补充对协议的处理："全量同步场站充电桩数据"；
  - 补充对协议的处理："全量同步场站充电桩数据应答"；
  - 补充对协议的处理："全量同步场站充电枪数据"；
  - 补充对协议的处理："全量同步场站充电枪数据应答"；
  - 补充对协议的处理："全量同步场站能源路由器数据"；
  - 补充对协议的处理："全量同步场站能源路由器数据应答"；
- 更新 Composer 组件：
```bash
composer update liehuohuyu/charge-transfer-service-protocol:v1.0.3
```