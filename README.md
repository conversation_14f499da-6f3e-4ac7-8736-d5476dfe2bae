# 更新日志

> 这里只记录这个分支更新的内容，需要合并到主分支时，在将这里内容与主分支的合并。

---


### v1.6.2

```
CREATE TABLE `operating_funds_daily_statistics`
(
    `id`             INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `recharge_money` INT          NOT NULL COMMENT '充值金额(精确到小数点后2位)',
    `recharge_count` INT          NOT NULL COMMENT '充值次数',
    `charging_money` INT          NOT NULL COMMENT '充电金额(精确到小数点后2位)',
    `charging_count` INT          NOT NULL COMMENT '充电次数',
    `date`           timestamp    NOT NULL COMMENT '日期',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT = "每日运营资金统计表";

CREATE TABLE `corp_operating_funds_daily_statistics`
(
    `id`             INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `corp_id`        INT UNSIGNED NOT NULL COMMENT '运营商ID',
    `charging_money` INT          NOT NULL COMMENT '充电金额(精确到小数点后2位)',
    `charging_count` INT          NOT NULL COMMENT '充电次数',
    `date`           timestamp    NOT NULL COMMENT '日期',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `corp_date` (`corp_id`, `date`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT = "运营商的每日运营资金统计表";
```
- 增加一个定时脚本：
```bash
# 全站每日运营资金统计
php think full-site-daily-statistics
```
- 需要为运营商管理员分配接口权限；

---

- 按照《中转服务 与 业务运营 的协议》 v1.0.12 版本，更新"开始订单应答"中失败原因的描述；

### v1.6.3

---

- 小程序增加获取充值详情接口

### v1.6.4

---

- 修改菜单排序

### v1.6.5

---

- v1.6.5 新增按区域汇总返回场站数据接口 /admin/monitoringCenter/getCityStations

### v1.6.6

---

- 增加充电二维码管理模块

数据库改动

```mysql
CREATE TABLE `charging_qrcode`
(
    `id`          INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '充电二维码ID',
    `url`         VARCHAR(255) NOT NULL COMMENT '二维码内容中的URL',
    `path`        VARCHAR(255) NOT NULL COMMENT '二维码保存的路径',
    `create_time` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  AUTO_INCREMENT = 10000000
  ROW_FORMAT = DYNAMIC COMMENT ='充电二维码';

CREATE TABLE `charging_qrcode_to_shots`
(
    `id`          INT UNSIGNED    NOT NULL COMMENT '充电二维码ID',
    `shots_id`    BIGINT UNSIGNED NOT NULL COMMENT '充电枪ID',
    `create_time` TIMESTAMP       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='充电二维码与充电枪的绑定关系';
```

nginx配置改动

```nginx
# 增加以下配置，用于解决二维码图片跨域问题。
location /charging_qrcode/ {
		add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type text/plain;
        add_header Content-Length 0;
        return 204;
    }
}
```

composer 改动

```bash
composer require endroid/qr-code
```

### v1.6.7

---

- 支持通过充电二维码ID来获取充电枪信息；
- 支持绑定与解绑充电二维码与枪的关系。

### v1.6.8

---

- 充电桩与枪增加软删除机制；

## 数据库改动

```
ALTER TABLE `piles_status` ADD COLUMN `is_delete` tinyint(3) NOT NULL DEFAULT 0 COMMENT '是否已删除 1:是 0:否';
ALTER TABLE `shots_status` ADD COLUMN `is_delete` tinyint(3) NOT NULL DEFAULT 0 COMMENT '是否已删除 1:是 0:否';
```

### v1.7.0

---

1. 恢复运营商、场站、充电桩、充电枪的管理（增删改）；
2. 部分主动告警因采用临时告警配置(.env文件配置)，例如：能源路由器服务上下线(后续可以迁移到场站平台)；
3. 去除从设备平台同步数据的机制；


##### 数据库更新：

```mysql
ALTER TABLE `stations`  
    ADD COLUMN `is_del` tinyint(3) NOT NULL DEFAULT 0 COMMENT '是否已删除 1:已删除 0:未删除';  
ALTER TABLE `piles`  
    ADD COLUMN `is_del` tinyint(3) NOT NULL DEFAULT 0 COMMENT '是否已删除 1:已删除 0:未删除';  
ALTER TABLE `shots`  
    ADD COLUMN `is_del` tinyint(3) NOT NULL DEFAULT 0 COMMENT '是否已删除 1:已删除 0:未删除';
```

##### 增加环境变量：

```env
[TRANSFER_SERVICE]
# 设备平台域名  
DOMAIN=http://example.com


# 临时的告警配置  
[TEMPORARY_ACTIVE_ALARM_CONFIG]  
ENTERPRISE_WECHAT_KEY=e4e44b30-f4f4-4b66-b7eb-b133e0eb7765  
SF_IS_GENERATE_WORK_ORDER=2  
SF_AUTO_DISPATCH_USER_ID=0  
CSO_IS_GENERATE_WORK_ORDER=2  
CSO_AUTO_DISPATCH_USER_ID=0  
REMP_IS_GENERATE_WORK_ORDER=2  
REMP_AUTO_DISPATCH_USER_ID=0  
PO_IS_GENERATE_WORK_ORDER=2  
PO_AUTO_DISPATCH_USER_ID=0  
CCSR_IS_GENERATE_WORK_ORDER=2  
CCSR_AUTO_DISPATCH_USER_ID=0  
EMAIL=
```

##### 数据迁移

```mysql
-- 更改主键字段，防止历史已删除数据与后续新增ID一样引起的冲突。
CREATE TABLE `piles_new` (
  `primary_key` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT "主键",
  `id` bigint(24) NOT NULL COMMENT '桩id(生产时写入）长度7Bytes',
  `name` varchar(128) NOT NULL COMMENT '桩名称',
  `corp_id` bigint(24) NOT NULL COMMENT '运营商id',
  `station_id` bigint(24) NOT NULL COMMENT '场站id',
  `ac_dc` int(2) NOT NULL DEFAULT '1' COMMENT '交直流：0表示直流桩，1表示交流桩',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '桩类型：1-慢充，2-快充，3超快充',
  `model` varchar(32) NOT NULL COMMENT '桩型号',
  `power` int(8) NOT NULL DEFAULT '0' COMMENT '桩功率（w）',
  `comm_id` varchar(16) NOT NULL COMMENT '认证id:4G模块IMEI号,mac地址，根据通讯类型区分',
  `shot_num` int(2) DEFAULT NULL COMMENT '枪数量',
  `comm_ver` int(2) DEFAULT NULL COMMENT '通讯协议版本，版本号乘10，v1.0 表示 0x0A',
  `version` varchar(32) DEFAULT NULL COMMENT '程序版本,如：23070101',
  `comm_type` int(2) DEFAULT NULL COMMENT '通讯类型：0-SIM卡，1-LAN，2-WAN，3-其他',
  `simid` varchar(32) DEFAULT NULL COMMENT 'SIM卡号',
  `comm_corp` int(2) DEFAULT NULL COMMENT '通讯运营商：0-移动，2-电信，3-联通，4-其他',
  `opt_id` bigint(24) NOT NULL COMMENT '创建人id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `line` varchar(1) DEFAULT NULL COMMENT 'ABC线',
  `centralized_controller_id` varchar(24) DEFAULT NULL COMMENT '集中器id，集中器mac地址（全大写）',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '在线状态 1:在线 2:离线',
  `phase_name` enum('a','b','c') NOT NULL DEFAULT 'a' COMMENT '所属相线',
  `is_del` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否已删除 1:已删除 0:未删除',
  PRIMARY KEY (`primary_key`) USING BTREE,
  INDEX `piles_id`(`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT="充电桩";

INSERT INTO `piles_new`(`id`, `name`, `corp_id`, `station_id`, `ac_dc`, `type`, `model`, `power`, `comm_id`, `shot_num`, `comm_ver`, `version`, `comm_type`, `simid`, `comm_corp`, `opt_id`, `create_time`, `line`, `centralized_controller_id`, `status`, `phase_name`, `is_del`) SELECT * FROM `piles`;

RENAME TABLE `piles` TO `piles_old_2021_12_10`;
RENAME TABLE `piles_new` TO `piles`;

CREATE TABLE `piles_status_new` (
  `primary_key` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT "主键",
  `id` bigint(20) unsigned NOT NULL COMMENT '充电桩ID',
  `is_online` tinyint(3) unsigned NOT NULL COMMENT '是否在线 1:在线 2:离线',
  `is_online_update_time` timestamp NOT NULL COMMENT '是否在线状态更新时间',
  `is_` tinyint(4) DEFAULT '0',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否已删除 1:是 0:否',
  PRIMARY KEY (`primary_key`) USING BTREE,
  INDEX `piles_id`(`id`) 
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='充电桩状态表';

INSERT INTO `piles_status_new`(`id`, `is_online`, `is_online_update_time`, `is_`, `is_delete`) SELECT * FROM `piles_status`;

RENAME TABLE `piles_status` TO `piles_status_old_2021_12_10`;
RENAME TABLE `piles_status_new` TO `piles_status`;


CREATE TABLE `shots_new` (
  `primary_key` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT "主键",
  `id` bigint(24) NOT NULL COMMENT '枪id，桩id+枪序号',
  `name` varchar(128) NOT NULL COMMENT '枪名称',
  `corp_id` bigint(24) NOT NULL COMMENT '运营商id',
  `station_id` bigint(24) NOT NULL COMMENT '场站id',
  `piles_id` bigint(24) NOT NULL COMMENT '桩id',
  `sequence` int(2) NOT NULL COMMENT '枪序号',
  `qrcode` text NOT NULL COMMENT '枪二维码url',
  `port_num` int(4) NOT NULL COMMENT '虚拟枪号，用于485访问的地址',
  `is_online` int(2) NOT NULL DEFAULT '1' COMMENT '枪在线状态：1-离线，2-在线',
  `work_status` int(2) NOT NULL DEFAULT '1' COMMENT '枪工作状态：1-空闲，2-充电中',
  `link_status` int(2) NOT NULL DEFAULT '1' COMMENT '枪连接状态：1-无连接，2-连接中',
  `opt_id` bigint(24) NOT NULL COMMENT '创建人id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `status` tinyint(4) unsigned NOT NULL DEFAULT '2' COMMENT '状态 0:离线 1:故障 2:空闲 3:充电',
  `is_put_back` tinyint(4) unsigned NOT NULL DEFAULT '1' COMMENT '充电枪是否放回原处(是否插枪) 0:否 1:是  2:未知',
  `is_fault` tinyint(3) unsigned NOT NULL COMMENT '是否故障 1:未故障 2:已故障',
  `status_update_time` datetime NOT NULL COMMENT '状态更新时间',
  `is_detection` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否检测 1:检测 0:不检测',
  `is_del` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否已删除 1:已删除 0:未删除',
  PRIMARY KEY (`primary_key`) USING BTREE,
  INDEX `shots_id`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT="充电枪";

INSERT INTO `shots_new`(`id`, `name`, `corp_id`, `station_id`, `piles_id`,`sequence`,`qrcode`, `port_num`,`is_online`,`work_status`,`link_status`,`opt_id`,`create_time`,`status`, `is_put_back`, `is_fault`, `status_update_time`, `is_detection`, `is_del`) SELECT * FROM `shots`;

RENAME TABLE `shots` TO `shots_old_2021_12_10`;
RENAME TABLE `shots_new` TO `shots`;


CREATE TABLE `shots_status_new` (
  `primary_key` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT "主键",
  `id` bigint(20) unsigned NOT NULL COMMENT '充电枪ID',
  `is_online` tinyint(3) unsigned NOT NULL COMMENT '在线在线 1:离线 2:在线',
  `is_online_update_time` timestamp NOT NULL COMMENT '在线状态更新时间',
  `work_status` tinyint(3) unsigned NOT NULL COMMENT '工作状态 1:空闲 2:充电',
  `work_status_update_time` timestamp NOT NULL COMMENT '工作状态更新时间',
  `is_fault` tinyint(3) unsigned NOT NULL COMMENT '故障状态 1:正常 2:故障',
  `is_fault_update_time` timestamp NOT NULL COMMENT '故障状态更新时间',
  `is_delete` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否已删除 1:是 0:否',
  PRIMARY KEY (`primary_key`) USING BTREE,
  INDEX `shots_id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='充电枪状态表';

INSERT INTO `shots_status_new`(`id`, `is_online`, `is_online_update_time`, `work_status`, `work_status_update_time`,`is_fault`,`is_fault_update_time`, `is_delete`) SELECT * FROM `shots_status`;

RENAME TABLE `shots_status` TO `shots_status_old_2021_12_10`;
RENAME TABLE `shots_status_new` TO `shots_status`;


CREATE TABLE `shots_qrcode_new` (
  `primary_key` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT "主键",
  `id` bigint(20) unsigned NOT NULL COMMENT '充电枪ID',
  `qrcode` varchar(60) NOT NULL COMMENT '枪二维码url',
  PRIMARY KEY (`primary_key`) USING BTREE,
  INDEX `shots_id`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='充电枪二维码';

INSERT INTO `shots_qrcode_new`(`id`, `qrcode`) SELECT * FROM `shots_qrcode`;

RENAME TABLE `shots_qrcode` TO `shots_qrcode_old_2021_12_10`;
RENAME TABLE `shots_qrcode_new` TO `shots_qrcode`;
```

### v1.7.1

---

1. 修复运营总览每月统计功能，12月份数据不正确的问题；
2. 修复系统管理 - 权限组，组名搜索无效的问题；
3. 修复系统管理 - 管理员列表，用户名搜索异常的问题；


### v1.7.2
---
1. 将服务放入 Docker 容器内运行。