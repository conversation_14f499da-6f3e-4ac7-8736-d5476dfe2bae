## resc-us-docker

docker 运行辅助项目

## 使用方法

1. 在别的项目中安装(已安装的项目请忽略)

```bash
cd 别的项目path
git submodule add -<NAME_EMAIL>:6769455a35730943ed494704/resc-us-docker-cmd.git docker/cmd
```
2. 第一次在别的项目拉取此项目

```bash
cd 别的项目path
git submodule init
git submodule update
# 或者第一次拉取别的项目代码的时候同时拉取
git clone --recurse-submodules xxx
```
3. 以后在别的项目更新此项目

```bash
cd 别的项目path
git submodule update
# 或者更新别的项目代码的时候同时更新
git pull --recurse-submodules
```
4. 需要使用最新的子模块代码, 并提交，这样别人就可以在2，3项目时pull到最新的
```bash
cd 别的项目path
git submodule update --remote --merge
```
5. 使用docker/cmd/cmd.sh 就可以运行
