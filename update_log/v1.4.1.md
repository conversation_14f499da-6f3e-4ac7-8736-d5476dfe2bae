## 1.数据库变动

### 1.1. 添加以下数据库表

```mysql
CREATE TABLE `work_order_field`
(
    `id`          INT UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`        VARCHAR(20)      NOT NULL COMMENT '字段名称',
    `key`         VARCHAR(20)      NOT NULL COMMENT '字段键值',
    `type`        TINYINT UNSIGNED NOT NULL COMMENT '字段类型 1:单行文本 2:多行文本 3:下拉框 4:多选框 5:数字 6:日期 7:图片',
    `is_require`  TINYINT UNSIGNED NOT NULL COMMENT '是否必填 1:是 0:否',
    `options`     VARCHAR(200)     NOT NULL COMMENT '选项列表',
    `create_time` TIMESTAMP        NOT NULL COMMENT '创建时间',
    `update_time` TIMESTAMP        NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ="工单字段";

-- 业务表
CREATE TABLE `work_order_field_relation`
(
    `field_id`   INT UNSIGNED NOT NULL COMMENT '字段ID',
    `corp_id`    INT UNSIGNED NOT NULL COMMENT '运营商ID',
    `station_id` INT UNSIGNED NOT NULL COMMENT '场站ID',
    UNIQUE KEY `unique_index` (`corp_id`, `station_id`, `field_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ="工单字段关联表";


CREATE TABLE `work_order_template`
(
    `id`          INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`        VARCHAR(20)  NOT NULL COMMENT '模板名称',
    `describe`    VARCHAR(100) NOT NULL COMMENT '模板描述',
    `create_time` TIMESTAMP    NOT NULL COMMENT '创建时间',
    `update_time` TIMESTAMP    NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ="工单模板";

CREATE TABLE `work_order_template_field`
(
    `template_id` INT UNSIGNED     NOT NULL COMMENT '模板ID',
    `field_id`    INT UNSIGNED     NOT NULL COMMENT '字段ID',
    `sort`        TINYINT UNSIGNED NOT NULL COMMENT '序号',
    UNIQUE KEY `unique_index` (`template_id`, `field_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ="工单模板字段";

-- 业务表
CREATE TABLE `work_order_template_relation`
(
    `template_id`          INT UNSIGNED NOT NULL COMMENT '模板ID',
    `corp_id`              INT UNSIGNED NOT NULL COMMENT '运营商ID',
    `station_id`           INT UNSIGNED NOT NULL COMMENT '场站ID',
    `notice_admin_user_id` INT UNSIGNED NOT NULL COMMENT '默认通知的管理员',
    UNIQUE KEY `unique_index` (`corp_id`, `station_id`, `template_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ="工单模板关联表";


CREATE TABLE `work_order`
(
    `id`               CHAR(36)         NOT NULL COMMENT '主键',
    `title`            VARCHAR(50)      NOT NULL COMMENT '标题',
    `priority`         TINYINT UNSIGNED NOT NULL COMMENT '优先级 1:低 2:标准 3:高 4:紧急',
    `status`           TINYINT UNSIGNED NOT NULL COMMENT '状态 1:待指派 2:解决中 3:已解决 4:已关闭',
    `source`           TINYINT UNSIGNED NOT NULL COMMENT '来源 1:后台创建 2:告警生成 3:用户创建',
    `template_id`      INT UNSIGNED     NOT NULL COMMENT '模板ID',
    `create_user_type` TINYINT UNSIGNED NOT NULL COMMENT '创建者的用户类型 1:后台用户 2:普通用户 3:自动创建',
    `create_user_id`   INT UNSIGNED     NOT NULL COMMENT '创建者的用户ID',
    `create_time`      TIMESTAMP        NOT NULL COMMENT '创建时间',
    `update_time`      TIMESTAMP        NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ="工单";

CREATE TABLE `work_order_relation`
(
    `work_order_id` CHAR(36)         NOT NULL COMMENT '主键',
    `corp_id`       INT UNSIGNED     NOT NULL COMMENT '运营商ID',
    `station_id`    INT UNSIGNED     NOT NULL COMMENT '场站ID',
    `device_type`   TINYINT UNSIGNED NOT NULL COMMENT '设备类型 1:充电桩 2:充电枪 3:能源路由器 9:无',
    `device_id`     VARCHAR(32)      NOT NULL COMMENT '设备编号',
    UNIQUE KEY `unique_index` (`work_order_id`, `corp_id`, `station_id`, `device_type`, `device_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ="工单关系表";

CREATE TABLE `work_order_extra_data`
(
    `work_order_id` CHAR(36) NOT NULL COMMENT '工单ID',
    `extra_data`    TEXT     NOT NULL COMMENT '额外的数据',
    PRIMARY KEY (`work_order_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ="工单额外的数据";


CREATE TABLE `work_order_processing_records`
(
    `id`            INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `work_order_id` CHAR(36)     NOT NULL COMMENT '工单ID',
    `admin_user_id` INT UNSIGNED NOT NULL COMMENT '处理人ID',
    `message`       VARCHAR(200) NOT NULL COMMENT '处理的回复',
    `attachment`    VARCHAR(50)  NOT NULL COMMENT '附件路径',
    `create_time`   TIMESTAMP    NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ="工单处理记录";

ALTER TABLE `work_order_processing_records`
    ADD COLUMN `user_type` TINYINT UNSIGNED DEFAULT 1 NOT NULL COMMENT '用户类型 1:后台用户 2:普通用户 3:系统';
ALTER TABLE `work_order_processing_records`
    CHANGE COLUMN `admin_user_id` `user_id` INT UNSIGNED NOT NULL COMMENT '用户ID';


CREATE TABLE `work_order_responsible`
(
    `work_order_id` CHAR(36)         NOT NULL COMMENT '工单ID',
    `admin_user_id` INT UNSIGNED     NOT NULL COMMENT '负责人ID',
    `is_activate`   TINYINT UNSIGNED NOT NULL COMMENT '是否激活 1:是 2:否',
    UNIQUE KEY `unique_index` (`work_order_id`, `admin_user_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ="工单负责人";

-- is_activate 当记录中的 is_activate 处于激活状态时, 说明 admin_user_id 是这个工单当前的实际负责人; 
-- 当工单转交给其他人时, 这条记录将取消激活状态, 负责人将变为历史负责人, Ta 保留查询的权限, 不过不在具有操作权限.


CREATE TABLE `system_notice`
(
    `work_order_id`   CHAR(36)     NOT NULL COMMENT '工单ID',
    `processed_by_id` INT UNSIGNED NOT NULL COMMENT '处理人ID',
    UNIQUE KEY `unique_index` (`work_order_id`, `processed_by_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ="系统通知";

CREATE TABLE `admin_users_relation_applet`
(
    `admin_user_id` INT UNSIGNED NOT NULL COMMENT '后台用户ID',
    `open_id`       CHAR(28)     NOT NULL COMMENT 'OPEN ID',
    UNIQUE KEY `unique_index` (`admin_user_id`, `open_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ="后台用户关联的小程序信息";

-- 新建主动告警配置表
CREATE TABLE `active_alarm_config`
(
    `id`                            tinyint(3) unsigned NOT NULL COMMENT '主键',
    `sf_reason_for_stop_white_list` varchar(400)        NOT NULL COMMENT '充电枪故障告警 - 充电停止原因白名单(多个以#分隔)',
    `ccsr_cpu_alarm_value`          tinyint(3) unsigned NOT NULL COMMENT '能源路由器资源告警 - CPU使用率警报值(单位:百分比)',
    `ccsr_memory_alarm_value`       int(10) unsigned    NOT NULL COMMENT '能源路由器资源告警 - 剩余内存警报值(单位:MB)',
    `ccsr_disk_alarm_value`         int(10) unsigned    NOT NULL COMMENT '能源路由器资源告警 - 剩余磁盘警报值(单位:MB)',
    `update_time`                   timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='主动告警配置表';
-- 主动告警的默认配置
INSERT INTO `active_alarm_config` (`id`, `sf_reason_for_stop_white_list`,
                                   `ccsr_cpu_alarm_value`, `ccsr_memory_alarm_value`,
                                   `ccsr_disk_alarm_value`, `update_time`)
VALUES (1, '65#66#67#68#69#75#78#110#64', 90, 100, 100, '2024-02-22 16:03:18');



CREATE TABLE `stations_active_alarm_config`
(
    `station_id`                  INT UNSIGNED     NOT NULL COMMENT '充电场站ID',
    `corp_id`                     INT UNSIGNED     NOT NULL COMMENT '运营商ID',

    `enterprise_wechat_key`       varchar(36)      NOT NULL COMMENT '企业微信机器人密钥',

    `sf_is_generate_work_order`   TINYINT UNSIGNED NOT NULL COMMENT '充电枪故障告警 - 是否自动生成工单 1:是 2:否',
    `sf_auto_dispatch_user_id`    INT UNSIGNED     NOT NULL COMMENT '充电枪故障告警 - 自动派发给指定工作人员',

    `cso_is_generate_work_order`  TINYINT UNSIGNED NOT NULL COMMENT '充电服务离线告警 - 是否自动生成工单 1:是 2:否',
    `cso_auto_dispatch_user_id`   INT UNSIGNED     NOT NULL COMMENT '充电服务离线告警 - 自动派发给指定工作人员',

    `remp_is_generate_work_order` TINYINT UNSIGNED NOT NULL COMMENT '读取电表功率告警 - 是否自动生成工单 1:是 2:否',
    `remp_auto_dispatch_user_id`  INT UNSIGNED     NOT NULL COMMENT '读取电表功率告警 - 自动派发给指定工作人员',

    `po_is_generate_work_order`   TINYINT UNSIGNED NOT NULL COMMENT '充电桩离线告警 - 是否自动生成工单 1:是 2:否',
    `po_auto_dispatch_user_id`    INT UNSIGNED     NOT NULL COMMENT '充电桩离线告警 - 自动派发给指定工作人员',

    `ccsr_is_generate_work_order` TINYINT UNSIGNED NOT NULL COMMENT '能源路由器资源告警 - 是否自动生成工单 1:是 2:否',
    `ccsr_auto_dispatch_user_id`  INT UNSIGNED     NOT NULL COMMENT '能源路由器资源告警 - 自动派发给指定工作人员',

    `update_time`                 timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`station_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='运营商主动告警配置表';

ALTER TABLE `stations_active_alarm_config`
    ADD COLUMN `email` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '邮箱地址';

-- 将原先的告警记录表名称更改为 alarm_record_old
RENAME TABLE `alarm_record` TO `alarm_record_old`;

-- 创建新的告警记录表
CREATE TABLE `alarm_record`
(
    `id`            int(10) unsigned    NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `corp_id`       INT unsigned        NOT NULL COMMENT '运营商编号',
    `station_id`    INT unsigned        NOT NULL COMMENT '场站id',
    `device_type`   tinyint(3) unsigned NOT NULL COMMENT '设备类型 1:充电枪 2:充电桩 3:能源路由器',
    `device_id`     bigint(20) unsigned NOT NULL COMMENT '告警设备id，与类型对应：充电枪编号、充电桩编号',
    `type`          TINYINT UNSIGNED    NOT NULL COMMENT '告警类型 1:实时监测数据告警 2:交易记录告警 3:充电服务离线告警 4:读取电表功率告警 5:充电桩离线告警 6:能源路由器资源告警',
    `code`          varchar(6)          NOT NULL COMMENT '告警代码',
    `alarm_time`    INT UNSIGNED        NOT NULL COMMENT '告警的时间',
    `recovery_time` int(10) unsigned    NOT NULL DEFAULT 0 COMMENT '恢复的时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `alarm_index` (`device_type`, `device_id`, `type`, `code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='告警记录';

ALTER TABLE `alarm_record`
    CHANGE COLUMN `device_id` `device_id` VARCHAR(24) NOT NULL COMMENT '告警设备id，与类型对应：充电枪编号、充电桩编号、能源路由器ID';

ALTER TABLE `alarm_record`
    ADD COLUMN `is_recovery` TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否已恢复 1:未恢复 2:已恢复';

-- 增加索引
ALTER TABLE `alarm_record`
    ADD INDEX `common_index` (`is_recovery`, `device_type`, `device_id`, `type`);

-- 充电订单增加异常告警字段，用于备注异常来此哪个节点。
ALTER TABLE `order`
    ADD COLUMN `abnormal_alarm_node` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '异常告警节点 0:无 1:充电管理 2:调度算法 3:驱动服务 4:充电桩 5:中转服务 6:业务平台';

-- 修改备注
ALTER TABLE `order`
    CHANGE COLUMN `consumption_amount` `consumption_amount` int(8) NOT NULL DEFAULT '0' COMMENT '消费金额(包含服务费与充电费) - 保留4为小数';


-- 增加充电枪二维码表
CREATE TABLE `shots_qrcode`
(
    `id`     BIGINT unsigned NOT NULL AUTO_INCREMENT COMMENT '充电枪ID',
    `qrcode` VARCHAR(60)     NOT NULL COMMENT '枪二维码url',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='充电枪二维码';
INSERT INTO `shots_qrcode` (`id`, `qrcode`)
SELECT `id`, `qrcode`
FROM `shots`;
```


