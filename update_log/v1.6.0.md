# 版本号：v1.6.0

# 新增功能

- 优惠活动管理
  - 能够给指定场站设置某个时间范围内，充电服务费打折优惠。

# 数据库改动

```mysql
CREATE TABLE `activity`
(
    `id`             int(10) unsigned    NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `corp_id`        int(10) unsigned    NOT NULL COMMENT '运营商ID',
    `title`          varchar(20)         NOT NULL COMMENT "标题",
    `describe`       varchar(250)        NOT NULL COMMENT "描述",
    `discount`       tinyint(3) unsigned NOT NULL COMMENT "优惠折扣(精确到小数点后1位)",
    `status`         tinyint(3)          NOT NULL DEFAULT 1 COMMENT "活动开关 1:开启 0:关闭",
    `create_user_id` int(10)             NOT NULL COMMENT "创建者ID",
    `create_time`    timestamp           NOT NULL COMMENT "创建时间",
    `update_time`    timestamp           NOT NULL COMMENT "更新时间",
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='活动';

CREATE TABLE `activity_stations`
(
    `activity_id` int(10) unsigned NOT NULL COMMENT "活动ID",
    `station_id`  int(10) unsigned NOT NULL COMMENT '场站ID',
    `create_time` timestamp        NOT NULL COMMENT "创建时间",
    UNIQUE KEY (`activity_id`, `station_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='活动场站';



CREATE TABLE `activity_time`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT "主键",
    `activity_id` int(10) unsigned NOT NULL COMMENT "活动ID",
    `start_time`  timestamp        NOT NULL COMMENT "活动开始时间",
    `end_time`    timestamp        NOT NULL COMMENT "活动结束时间",
    `create_time` timestamp        NOT NULL COMMENT "创建时间",
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='活动时间';

-- 备份表
CREATE TABLE `order_2024_08_13` LIKE `order`;
INSERT INTO `order_2024_08_13`
SELECT *
FROM `order`;

ALTER TABLE `order`
    ADD COLUMN `discount` tinyint(3) unsigned NOT NULL DEFAULT 100 COMMENT "优惠折扣(精确到小数点后1位)";

-- 修改备注
ALTER TABLE `order`
    CHANGE COLUMN `money` `money` int(8) NOT NULL DEFAULT '0' COMMENT '订单原本应付金额(包含电费与服务费) - 单位分(小数点后2位)';
ALTER TABLE `order`
    CHANGE COLUMN `coupon_money` `coupon_money` int(8) NOT NULL DEFAULT '0' COMMENT '本次优惠金额(包含电费与服务费) - 单位分(小数点后2位)';
ALTER TABLE `order`
    CHANGE COLUMN `consumption_amount` `consumption_amount` int(8) NOT NULL DEFAULT '0' COMMENT '桩端上报的消费金额(包含服务费与充电费) - 保留4为小数';
ALTER TABLE `order`
    CHANGE COLUMN `ser_price` `ser_price` int(11) NOT NULL DEFAULT '0' COMMENT '本次服务费用(这是打折后的服务费用) - 保留4为小数';
ALTER TABLE `order`
    ADD COLUMN `billing_mode_id` int(11) NOT NULL DEFAULT 0 COMMENT "费率组ID";
```