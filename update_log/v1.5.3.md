# 改动说明

1. 增加获取PCX格式的小程序二维码接口；
2. 场站增加预约充电的开关，开启时表示场站支持预约充电，关闭时表示不支持；


## Nginx 配置改动

增加以下配置，解决PCX访问跨域问题:

```nginx
location ~ /static/shots/qrcode-pcx/.*\.(pcx)$
{
    expires      30d;
    error_log /dev/null;
    access_log /dev/null;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET';
}
```

## 数据库改动

```mysql
ALTER TABLE `stations_extra_info`
    ADD COLUMN `is_support_reservation` TINYINT NOT NULL DEFAULT 0 COMMENT '是否支持预约充电 0:不支持 1:支持';
```

