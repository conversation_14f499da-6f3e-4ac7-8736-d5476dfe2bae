## 1.数据库变动（订单清分）

### 1.1. 添加以下数据库表

```mysql
ALTER TABLE `order` ADD COLUMN `clearing_electricity_price` int(11) NOT NULL DEFAULT 0 COMMENT '应分充电费 - 保留4为小数' AFTER `freeze_status`;

ALTER TABLE `order` ADD COLUMN `clearing_ser_price` int(11) NOT NULL DEFAULT 0 COMMENT '应分服务费 - 保留4为小数' AFTER `clearing_electricity_price`;

ALTER TABLE `order` ADD COLUMN `clearing_status` tinyint(2) NOT NULL DEFAULT 1 COMMENT '清分状态，1待分成，2已计算，3已分成，20无需分成\n' AFTER `clearing_ser_price`;




```


```mysql
CREATE TABLE `order_clearing`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单ID',
  `station_id` bigint(24) NOT NULL COMMENT '场站id',
  `corp_id` bigint(24) NOT NULL COMMENT '运营商id',
  `clearing_electricity_price` int(11) NOT NULL DEFAULT 0 COMMENT '应分充电费 - 保留4为小数',
  `clearing_ser_price` int(11) NOT NULL DEFAULT 0 COMMENT '应分服务费 - 保留4为小数',
  `status` tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态，1待结算，2已结算\n',
  `clearing_time` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `order_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单时间(交易结束时间)',
  `bill_date` date NOT NULL COMMENT '账单日期',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `order_clearing_electricity_price` int(11) NOT NULL DEFAULT 0 COMMENT '订单应分充电费 - 保留4为小数',
  `order_clearing_ser_price` int(11) NOT NULL DEFAULT 0 COMMENT '订单应分服务费 - 保留4为小数',
  `ratio_electricity_price` int(11) NOT NULL DEFAULT 0 COMMENT '充电费分成比例',
  `ratio_ser_price` int(11) NOT NULL DEFAULT 0 COMMENT '服务费分成比例',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_station_corp_only`(`order_id`, `station_id`, `corp_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
```

```mysql
CREATE TABLE `stations_corp_clearing`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分成比例id',
  `station_id` bigint(24) NOT NULL COMMENT '场站id',
  `corp_id` bigint(24) NOT NULL COMMENT '运营商id',
  `ratio_electricity_price` int(11) NOT NULL DEFAULT 0 COMMENT '充电费分成比例',
  `ratio_ser_price` int(11) NOT NULL DEFAULT 0 COMMENT '服务费分成比例',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `sort_num` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `station_corp_only`(`station_id`, `corp_id`) USING BTREE,
  INDEX `sort_num`(`sort_num`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '场站运营商分成' ROW_FORMAT = Dynamic;
```
