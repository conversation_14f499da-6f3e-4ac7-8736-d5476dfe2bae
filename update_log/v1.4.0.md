# 支持多运营商

## 1.数据库变动

### 1.1. 添加新角色

```mysql
-- 添加角色
INSERT INTO `admin_auth_group` (`id`, `group_name`, `state`, `create_time`, `update_time`) VALUES (3, '运营商主账号', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO `admin_auth_group` (`id`, `group_name`, `state`, `create_time`, `update_time`) VALUES (6, '运营商子账号', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
-- 添加角色的功能权限
INSERT INTO `admin_auth_group_rules` (`group_id`, `rule_name`, `create_time`) SELECT 3 AS `group_id`, `rule_name`, CURRENT_TIMESTAMP AS `create_time` FROM `admin_auth_group_rules` WHERE `group_id` = 1;
INSERT INTO `admin_auth_group_rules` (`group_id`, `rule_name`, `create_time`) SELECT 6 AS `group_id`, `rule_name`, CURRENT_TIMESTAMP AS `create_time` FROM `admin_auth_group_rules` WHERE `group_id` = 1;
```
### 1.2. 用户表新增字段

```mysql
ALTER TABLE `admin_users` ADD COLUMN `corp_id` INT UNSIGNED DEFAULT 0 COMMENT '绑定的运营商ID(超级管理员及其子账号的运营商ID都为0)';
ALTER TABLE `admin_users` ADD COLUMN `pid` BIGINT UNSIGNED DEFAULT 0 COMMENT '父级ID';
```
### 1.3. 能源路由器表新增字段

```mysql
ALTER TABLE `centralized_controller` ADD COLUMN `corp_id` INT UNSIGNED COMMENT '运营商ID';
```

### 1.4. 运营商表增加字段
```mysql
ALTER TABLE `corp` ADD COLUMN `update_time` TIMESTAMP NOT NULL COMMENT '更新时间';
```

### 1.5. 新增充电场站数据权限表
```mysql
CREATE TABLE `station_data_authority`
(
    `admin_user_id` INT UNSIGNED NOT NULL COMMENT '管理员ID',
    `station_id`    INT UNSIGNED NOT NULL COMMENT '充电场站ID',
    UNIQUE KEY `unique_index`(`admin_user_id`, `station_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT="充电场站数据权限";
```

### 1.6. 新增运营商管理员分组表

```mysql
CREATE TABLE `corp_admin_group`
(
    `corp_id`    INT UNSIGNED NOT NULL COMMENT '运营商ID',
    `admin_group_id` INT UNSIGNED NOT NULL COMMENT '管理员分组ID',
    UNIQUE KEY `unique_index` (`corp_id`, `admin_group_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ="运营商管理员分组表数据权限";
```


## 2. 环境变量增加字段

- APP
  - NAME
  - VERSION
  - HOST
- TRANSFER_SERVICE
  - APP_ID
  - APP_SECRET

> 具体看 `.env.example`

## 3. 依赖更新

```bash
composer install
```

## 4. 进程重启

