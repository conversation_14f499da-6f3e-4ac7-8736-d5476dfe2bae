{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "https://www.thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repositories": [{"type": "vcs", "url": "*********************:6769455a35730943ed494704/liehuo_huyu/charge-transfer-service-protocol.git"}, {"type": "vcs", "url": "*********************:6769455a35730943ed494704/liehuo_huyu/tool.git"}, {"type": "vcs", "url": "*********************:6769455a35730943ed494704/php-redis-client.git"}], "require": {"php": "8.0.*|8.1.*", "topthink/framework": "^6.0.0", "topthink/think-orm": "v3.0.28", "topthink/think-multi-app": "^1.0", "topthink/think-template": "^2.0", "workerman/phpsocket.io": "^1.1", "ext-openssl": "*", "ext-json": "*", "topthink/think-view": "^1.0", "topthink/think-queue": "^3.0", "zjkal/time-helper": "^1.1", "yzh52521/easyhttp": "^1.0", "topthink/think-filesystem": "^2.0", "phpmailer/phpmailer": "^6.7", "ext-curl": "*", "predis/predis": "^2.1", "phpoffice/phpspreadsheet": "^1.28", "hg/apidoc": "^5.2", "ext-gd": "*", "endroid/qr-code": "4.8.*", "picqer/php-barcode-generator": "^2.2", "ext-libxml": "*", "ext-simplexml": "*", "workerman/gatewayclient": "^3.0", "workerman/validation": "^3.0", "wechatpay/wechatpay": "^1.4", "workerman/workerman": "^4.1", "workerman/http-client": "^2.0", "ramsey/uuid": "^4.7", "ext-sodium": "*", "ext-redis": "*", "liehuohuyu/charge-transfer-service-protocol": "v1.1.0.alpha.2", "chenbingji/tool": "@dev", "chenbingji/php-redis-client": "^1.10", "php-mqtt/client": "*"}, "require-dev": {"symfony/var-dumper": "^4.2", "phpunit/phpunit": "9.6"}, "autoload": {"psr-4": {"app\\": "app", "tests\\": "tests"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist"}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"], "生成模拟数据": ["@php vendor/phpunit/phpunit/phpunit --configuration phpunit.xml tests/api/TestGenerateData.php --testdox"]}}