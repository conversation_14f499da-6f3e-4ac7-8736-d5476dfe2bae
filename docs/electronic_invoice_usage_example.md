# 微信电子发票接口使用示例

本文档展示如何使用新增的两个微信电子发票接口：
1. 上传电子发票文件
2. 将电子发票插入微信用户卡包

## 1. 上传电子发票文件

### 基本用法

```php
use app\common\lib\wechat\ElectronicInvoiceClient;
use app\common\lib\wechat\entity\request\FapiaoFileMetaInfo;

// 创建客户端
$client = new ElectronicInvoiceClient();

// 文件路径
$filePath = '/path/to/invoice.pdf';

// 计算文件摘要（如果系统支持SM3）
try {
    $digest = FapiaoFileMetaInfo::calculateSM3Digest($filePath);
} catch (\RuntimeException $e) {
    // 如果不支持SM3，需要使用其他方式计算摘要
    $digest = hash_file('sha256', $filePath); // 临时使用SHA256
}

// 创建文件元信息
$metaInfo = FapiaoFileMetaInfo::createPdfMeta($digest);

// 上传文件
$response = $client->uploadFapiaoFile($filePath, $metaInfo);

if ($response->httpCode === 200) {
    $result = $response->analysisJsonBody();
    $fapiaoMediaId = $result['fapiao_media_id']; // 获取文件ID，用于后续插卡
    echo "文件上传成功，文件ID: " . $fapiaoMediaId;
} else {
    echo "文件上传失败: " . $response->body;
}
```

### 服务商模式

```php
// 服务商模式需要传入子商户号
$subMchid = '1234567890';
$metaInfo = FapiaoFileMetaInfo::createPdfMeta($digest, $subMchid);
```

## 2. 将电子发票插入微信用户卡包

### 基本用法

```php
use app\common\lib\wechat\ElectronicInvoiceClient;
use app\common\lib\wechat\entity\request\BuyerInformation;
use app\common\lib\wechat\entity\request\FapiaoCardInfo;
use app\common\lib\wechat\entity\request\SellerInformation;
use app\common\lib\wechat\entity\request\ExtraInformation;
use app\common\lib\wechat\entity\request\FapiaoItem;

// 创建客户端
$client = new ElectronicInvoiceClient();

// 1. 创建购买方信息（个人抬头）
$buyerInfo = BuyerInformation::createIndividual(
    '张三',
    '13800138000',
    '<EMAIL>'
);

// 或者创建单位抬头
$buyerInfo = BuyerInformation::createOrganization(
    '深圳市南山区测试企业',
    '202003261233701778',
    '深圳市南山区深南大道10000号',
    '************',
    '测试银行',
    '**************'
);

// 2. 创建销售方信息
$sellerInfo = new SellerInformation(
    '深圳市南山区测试公司',
    '202003261233701778'
);
$sellerInfo->setAddress('深圳市南山区深南大道10000号')
           ->setTelephone('************')
           ->setBankInfo('测试银行', '**************');

// 3. 创建额外信息
$extraInfo = ExtraInformation::create('张三', '李四', '王五');

// 4. 创建发票行项目
$item = FapiaoItem::createChargingService(
    1,          // 数量：1次
    ********,   // 单价：380.442元（分）
    ********,   // 金额：380.442元（分）
    4945800,    // 税额：49.458元（分）
    ********,   // 价税合计：429.90元（分）
    1300        // 税率：13%
);

// 5. 创建发票卡券信息
$cardInfo = new FapiaoCardInfo(
    $fapiaoMediaId,                    // 上传文件返回的文件ID
    '********',                        // 发票号码
    '************',                    // 发票代码
    FapiaoCardInfo::getCurrentFapiaoTime(), // 开票时间
    '69001808340631374774',            // 校验码
    ********,                          // 价税合计（分）
    4945800,                           // 税额（分）
    ********,                          // 金额（分）
    $sellerInfo,                       // 销售方信息
    [$item]                            // 发票行信息
);

// 设置可选信息
$cardInfo->setPassword('006>299-375/326>2+7/*0-+<351059<80<4*/5>+<11631+*3030/5*37+/-243159658+013>3409*044>4-/1+/9->*>69501*6++1997--21')
         ->setExtraInformation($extraInfo)
         ->setRemark('充电服务费');

// 6. 插入卡包
$fapiaoApplyId = '4200000444201910177461284488'; // 发票申请单号
$scene = ElectronicInvoiceClient::SceneWithWechatPay; // 微信支付场景

$response = $client->insertFapiaoCards(
    $fapiaoApplyId,
    $scene,
    $buyerInfo,
    [$cardInfo]
);

if ($response->httpCode === 202) {
    echo "发票插卡请求已受理";
} else {
    echo "发票插卡失败: " . $response->body;
}
```

### 批量插入多张发票

```php
// 可以一次插入最多5张发票
$cardInfoList = [];

for ($i = 0; $i < 3; $i++) {
    $cardInfo = new FapiaoCardInfo(
        $fapiaoMediaIds[$i],  // 不同的文件ID
        "1289779{$i}",        // 不同的发票号码
        '************',
        FapiaoCardInfo::getCurrentFapiaoTime(),
        "6900180834063137477{$i}",
        10000,  // 100元
        1300,   // 13元税额
        8700,   // 87元金额
        $sellerInfo,
        [$item]
    );
    
    $cardInfoList[] = $cardInfo;
}

$response = $client->insertFapiaoCards(
    $fapiaoApplyId,
    $scene,
    $buyerInfo,
    $cardInfoList
);
```

## 注意事项

1. **文件限制**：
   - 只支持PDF和OFD格式
   - 文件大小不能超过2MB
   - 上传的文件ID三天内有效

2. **摘要算法**：
   - 推荐使用SM3算法
   - 如果系统不支持SM3，需要安装相应扩展或使用其他算法

3. **插卡限制**：
   - 一个微信支付订单只能插卡一次
   - 最多对应五张电子发票
   - 多次调用会做幂等性检查

4. **场景选择**：
   - `WITH_WECHATPAY`：微信支付场景
   - `WITHOUT_WECHATPAY`：非微信支付场景

5. **错误处理**：
   - 上传文件接口返回200表示成功
   - 插卡接口返回202表示请求已受理
   - 具体结果需要通过回调或查询接口获取

## 完整流程示例

```php
try {
    // 1. 上传发票文件
    $digest = hash_file('sha256', $filePath); // 实际使用时应该用SM3
    $metaInfo = FapiaoFileMetaInfo::createPdfMeta($digest);
    $uploadResponse = $client->uploadFapiaoFile($filePath, $metaInfo);
    
    if ($uploadResponse->httpCode !== 200) {
        throw new \RuntimeException('文件上传失败');
    }
    
    $uploadResult = $uploadResponse->analysisJsonBody();
    $fapiaoMediaId = $uploadResult['fapiao_media_id'];
    
    // 2. 准备发票信息
    $buyerInfo = BuyerInformation::createIndividual('张三');
    $sellerInfo = new SellerInformation('测试公司', '123456789');
    $item = FapiaoItem::createChargingService(1, 10000, 10000, 1300, 11300);
    
    $cardInfo = new FapiaoCardInfo(
        $fapiaoMediaId,
        '12345678',
        '************',
        FapiaoCardInfo::getCurrentFapiaoTime(),
        '1234567890',
        11300,
        1300,
        10000,
        $sellerInfo,
        [$item]
    );
    
    // 3. 插入卡包
    $insertResponse = $client->insertFapiaoCards(
        'apply_id_123',
        ElectronicInvoiceClient::SceneWithWechatPay,
        $buyerInfo,
        [$cardInfo]
    );
    
    if ($insertResponse->httpCode === 202) {
        echo "发票处理成功，请等待回调通知";
    } else {
        throw new \RuntimeException('发票插卡失败');
    }
    
} catch (\Exception $e) {
    echo "处理失败: " . $e->getMessage();
}
```
