
## 1. 服务端向客户端发送的消息包


### 1.1. 登录信息

场景：在客户端刚建立连接后，服务端要求客户端使用这个 `client_id` 来进行登录操作。
接收方：发起登录的客户端
协议包格式：
```json
{
	"type": "login", // 类型 login:登录信息
	"client_id": xxx // 客户端ID
}
```


### 1.2. 登录成功

场景：在客户端登录成功后收到这个消息
接收方：单个客户端
协议包格式：
```json
{
	"type": "login_success_del" // 类型 login_success_del:登录成功
}
```

## 2. 客户端向服务端发送的消息

### 2.1. 登录

场景：客户端在收到登录信息后，发起的登录操作
接收方：服务端
协议包格式：
```json
{
	"type": "login_success" // 类型 login_success:登录
}
```

## 3. 客户端向客户端发送的消息

### 3.1. 发送到集中控制器成功

场景：
1. 在向 设备管理服务 发送：获取充电桩开启功率 0xDB 后，如果响应的状态码为 200，就发送这条消息；
接收方：单个客户端 - 发起充电的后台Socket客户端
协议包格式：
```json
{
	"type" => "运营平台远程控制启机-成功", // 类型
	"result" => "success", // 结果 success:成功 error:失败
	"msg" => "发送到集中控制器成功", // 消息描述
	"order_id" => "20230105000369012308301246440003", // 关联的交易流水号
}
```

### 3.2. 发送到集中控制器失败

场景：
1. 在向 设备管理服务 发送：获取充电桩开启功率 0xDB 后，如果响应的状态码不为 200，就发送这条消息；
接收方：单个客户端 - 发起充电的后台Socket客户端
协议包格式：
```json
{
	"type" => "运营平台远程控制启机-失败", // 类型
	"result" => "error", // 结果 success:成功 error:失败
	"msg" => "发送到集中控制器失败", // 消息描述
	"order_id" => "20230105000369012308301246440003", // 关联的交易流水号
}
```

### 3.3. 发起充电成功

> 注意：这里只是发起充电成功，还没有正式开始充电，而且也还可能会开启充电失败。

- 场景：在向 设备管理服务 发送请求：运营平台远程控制启机 0x34 后，如果响应的状态码为 200，就发送这条消息；
- 接收方：客户端 - 发起充电的后台Socket客户端
- 协议包格式：
```json
{
	"type" => "运营平台远程控制启机-成功", // 类型
	"result" => "success", // 结果 success:成功 error:失败
	"msg" => "发起充电成功", // 消息描述
	"order_id" => "20230105000369012308301246440003", // 关联的交易流水号
}
```

### 3.4. 发起充电失败

- 场景：在向 设备管理服务 发送请求：运营平台远程控制启机 0x34 后，如果响应的状态码不为 200，就发送这条消息；
- 接收方：客户端 - 发起充电的后台Socket客户端
- 协议包格式：
```json
{
	"type" => "运营平台远程控制启机-失败", // 类型
	"result" => "error", // 结果 success:成功 error:失败
	"msg" => "发起充电失败", // 消息描述
	"order_id" => "20230105000369012308301246440003", // 关联的交易流水号
}
```

### 3.5. 该桩不允许充电

- 场景：充电桩的输出功率在小于30 或 大于100 时，发送这个消息；
- 接收方：单个客户端 - 发起充电的后台Socket客户端
- 协议包格式：
```json
{
	"type" => "运营平台远程控制启机-失败", // 类型
	"result" => "error", // 结果 success:成功 error:失败
	"msg" => "该桩不允许充电", // 消息描述
	"order_id" => "20230105000369012308301246440003", // 关联的交易流水号
}
```

### 3.6. 收到开启功率

- 场景：充电桩的输出功率在不小于30 且 不大于100 时，发送这个消息；
- 接收方：单个客户端 - 发起充电的后台Socket客户端
- 协议包格式：
```json
{
	"type" => "运营平台远程控制启机-成功", // 类型
	"result" => "success", // 结果 success:成功 error:失败
	"msg" => "收到开启功率", // 消息描述
	"order_id" => "20230105000369012308301246440003", // 关联的交易流水号
}
```

### 3.7. 下发功率成功

- 场景：在向 设备管理服务 发送请求：充电桩工作参数设置 0x52 后，如果响应的状态码为 200 并且 request_id 不为空，就发送这条消息；
- 接收方：单个客户端 - 发起充电的后台Socket客户端
- 协议包格式：
```json
{
	"type" => "运营平台远程控制启机-成功", // 类型
	"result" => "success", // 结果 success:成功 error:失败
	"msg" => "下发功率成功", // 消息描述
	"order_id" => "20230105000369012308301246440003", // 关联的交易流水号
}
```

### 3.8. 下发功率失败

- 场景：在向 设备管理服务 发送请求：充电桩工作参数设置 0x52 后，如果响应的状态码不为 200 或 request_id 为空，就发送这条消息；
- 接收方：单个客户端 - 发起充电的后台Socket客户端
- 协议包格式：
```json
{
	"type" => "运营平台远程控制启机-失败", // 类型
	"result" => "error", // 结果 success:成功 error:失败
	"msg" => "下发功率失败", // 消息描述
	"order_id" => "20230105000369012308301246440003", // 关联的交易流水号
}
```

### 3.9. 设置功率失败

- 场景：如果 充电桩工作参数设置 失败，就发送这条消息；
- 接收方：单个客户端 - 发起充电的后台Socket客户端
- 协议包格式：
```json
{
	"type" => "运营平台远程控制启机-失败", // 类型
	"result" => "error", // 结果 success:成功 error:失败
	"msg" => "下发功率失败", // 消息描述
	"order_id" => "20230105000369012308301246440003", // 关联的交易流水号
}
```

### 3.10. 接收集中控制器-失败

- 场景：在发送 获取充电桩开启功率 0xDB 后，能源路由器并没有在指定时间内回复 充电桩开启功率响应 0xDA，就发送这条消息。
- 接收方：单个客户端 - 发起充电的后台Socket客户端
- 协议包格式：
```json
{
	"type" => "运营平台远程控制启机-失败", // 类型
	"result" => "error", // 结果 success:成功 error:失败
	"msg" => "接收集中控制器-失败", // 消息描述
	"order_id" => "20230105000369012308301246440003", // 关联的交易流水号
}
```

### 3.11. 接收下发功率-失败

- 场景：在发送 充电桩工作参数设置 0x52 后，能源路由器并没有在指定时间内回复 充电桩工作参数设置应答 0x51，就发送这条消息。
- 接收方：单个客户端 - 发起充电的后台Socket客户端
- 协议包格式：
```json
{
	"type" => "运营平台远程控制启机-失败", // 类型
	"result" => "error", // 结果 success:成功 error:失败
	"msg" => "接收下发功率-失败", // 消息描述
	"order_id" => "20230105000369012308301246440003", // 关联的交易流水号
}
```


### 3.12. 下发费率回复

- 场景：在向充电桩下发费率后，充电桩会回复设置费率的结果，这时会发送这个消息。
- 接收方：单个客户端 - 点击下发费率的后台用户
- 协议包格式：
```json
{
	"type" => "issue_tariff_group_response", // 类型
	"msg" => "下发费率回复", // 消息描述
	"data" => {
		"msg_type": "billing_model_response",
		"piles_id": 20230105000369, // 充电桩ID
		"setting_result": 1, // 设置费率是否成功 0:失败 1:成功
		"setting_result_text": "成功" // 设置费率是否成功的文字描述
	}
}
```

### 3.13. 远程启动充电命令回复

- 场景：在充电桩完成启动充电后，回复启动结果时，发送这个消息。
- 接收方：单个客户端 - 发起充电的后台Socket客户端
- 协议包格式：
```json
{
	"type" => "remote_start_charging_command_response", // 类型
	"msg" => "远程启动充电命令回复", // 消息描述
	"data" => {
		"msg_type": "remote_start_charging_command_response", // 消息类型
		"transaction_serial_number": "20230105000369012308301246440003", // 交易流水号
		"piles_id": 1, // 充电桩ID
		"shots_id": 1, // 充电枪ID
		"start_result": 1, // 启动结果 0:失败 1:成功
		"failure_reason": 0, // 失败原因 0:无 1:设备编号不匹配 2:枪已在充电 3:设备故障 4:设备离线 5:未插枪
		"failure_reason_text": "无" // 失败原因描述
	}
}
```

### 3.14. 远程停机命令回复

- 场景：在充电桩完成停止充电后，回复停止结果时，发送这个消息。
- 接收方：单个客户端 - 发起充电的后台Socket客户端
- 协议包格式：
```json
{
	"type" => "remote_shutdown_command_response", // 类型
	"msg" => "远程停机命令回复", // 消息描述
	"data" => {
		"msg_type": "remote_shutdown_command_response", // 类型 remote_shutdown_command_response:远程停机命令回复
		"piles_id": 1, // 充电桩ID
		"shots_id": 1, // 充电枪ID
		"stop_result": 1, // 停止结果 0:失败 1:成功
		"failure_reason": 0, // 失败原因
		"failure_reason_text": "无" // 失败原因描述
	}
}
```

### 3.15. 交易记录确认

- 场景：在结束充电后，即将进行结算充电订单时，发送这个消息。
- 接收方：单个客户端 - 发起充电的后台Socket客户端
- 协议包格式：
```json
{
	"type" => "transaction_record_confirmation", // 类型
	"msg" => "交易记录确认", // 消息描述
	"data" => {
		"msg_type": "transaction_record_confirmation", // 消息类型
		"transaction_serial_number": "", // 交易流水号
		"piles_id": 20230105000369, // 充电桩编码
		"shots_id": 2023010500036901, // 充电枪编码
		"start_time": "2020-03-16 17:14:47", // 开始时间
		"end_time": "2020-03-16 17:14:47", // 结束时间
		"sharp_price": 1.30000, // 尖单价
		"sharp_charge": 0, // 尖电量
		"loss_sharp_charge": 0, // 计损尖电量
		"sharp_amount": 0, // 尖金额
		"peak_price": 1.30000, // 峰单价
		"peak_charge": 0, // 峰电量
		"loss_peak_charge": 0, // 计损峰电量
		"peak_amount": 0, // 峰金额
		"flat_price":  1.30000, // 平单价
		"flat_charge": 0, // 平电量
		"loss_flat_charge": 0, // 计损平电量
		"flat_amount": 0, // 平金额
		"valley_price": 1.30000, // 谷单价
		"valley_charge": 0, // 谷电量
		"loss_valley_charge": 0, // 计损谷电量
		"valley_amount": 0, // 谷金额
		"electricity_meter_total_start": 0, // 电表总起值
		"electricity_meter_total_end": 0, // 电表总止值
		"total_charge": 0, // 总电量
		"loss_total_charge": 0, // 计损总电量
		"consumption_amount": 0, // 消费金额
		"unique_identifier_of_electric_vehicle": "", // 电动汽车唯一标识
		"transaction_identifier": 1, // 交易标识 1:app启动 2:卡启动 3:离线卡启动 4:vin码启动充电
		"transaction_identifier_text": "app 启动", // 交易标识文本
		"transaction_datetime": "2020-03-16 17:14:47", // 交易日期、时间
		"reason_for_stop": 0, // 停止原因
		"reason_for_stop_text": "无", // 停止原因文本
		"physical_card_number": "D14B0A54" // 物理卡号
	}
}
```

### 3.16. 上传实时监测数据

- 场景：在充电桩发送充电枪实时数据时，就发送这条消息。
- 接收方：单个客户端 - 发起充电的后台Socket客户端
- 协议包格式：
```json
{
	"type" => "upload_realtime_monitoring_data", // 类型
	"msg" => "上传实时监测数据", // 消息描述
	"data" => {
		"msg_type": "upload_realtime_monitoring_data",
		"transaction_serial_number": "20230105000369012308301246440003", // 交易流水号
		"piles_id": "20230105000369", // 充电桩ID
		"shots_id": 2023010500036901, // 充电枪ID
		"status": 2,  // 状态 0:离线 1:故障 2:空闲 3:充电
		"is_reset": 1, // 充电枪是否归位 0:否 1:是 2:未知(无法检测到枪是否插回枪座即未知)
		"is_plugged_in": 1, // 是否插枪 0:否 1:是
		"output_voltage": 0, // 输出电压 
		"output_current": 0, // 输出电流
		"gun_wire_temperature": 10, // 枪线温度
		"gun_wire_code": 0, // 枪线编码
		"soc": 0, // SOC
		"maximum_battery_temperature": 0, // 电池组最高温度
		"cumulative_charging_time": 0, // 累计充电时间
		"remaining_time": 0, // 剩余时间
		"charging_percentage": 0, // 充电度数
		"calculated_loss_charging_percentage": 0, // 计损充电度数
		"amount_charged": 0, // 已充金额
		"hardware_failure": 0, // 硬件故障 Bit位表示(0:否 1:是)低位到高位顺序 bit1:急停按钮动作故障 bit2:无可用整流模块 bit3:出风口温度过高 bit4:交流防雷故障 bit5:交直流模块DC20通信中断 bit6:绝缘检测模块FC08通信中断 bit7:电度表通信中断 bit8:读卡器通信中断 bit9:RC10通信中断 bit10:风扇调速板故障 bit11:直流熔断器故障 bit12:高压接触器故障 bit13:门打开
		"hardware_failure_arr": [] // 硬件故障描述
	},
	"is_low_current": true, // 是否是低电流 true:是 false:不是
	"is_low_voltage": true  // 是否是低电压 true:是 false:不是
}
```

