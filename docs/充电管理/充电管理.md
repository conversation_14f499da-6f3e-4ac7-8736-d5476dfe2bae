```json
{
	"index": "start_charge",
	"body": {
        "request_id": "1ee76c5f9cb76ba6a23f00163e066caa",	
        "piles_id": "**************",
        "shots_number": "1",
        "transaction_serial_number": "**************012312141626050022",
        "logical_card_number": "****************",
        "physical_card_number": "****************",
        "account_balance": 1000,
        "account_vip": 1
    }
}
```


---

## 网关层逻辑

### 服务启动

#### 入参

```
无
```

#### 运行过程

```
函数调用->初始化对象()

函数调用:监听事件(客户端消息读取 或 客户端连接建立)

循环(函数调用:等待事件触发())
    if 检测是否有连接建立() == 有
        加入到待登陆连接对象

    



```





---





---



