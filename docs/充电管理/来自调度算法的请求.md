# 来自调度算法的请求

## 版本: v1.0.1

## 考虑的点

- [x] 兼容云快充协议
- [ ] 接口之间并发的情况
- [ ] 充电桩出现故障导致停电后的情况
- [ ] 能源路由器重启或关机的情况

## 处理逻辑

### 调度算法 -> 充电管理: 上调功率

##### 入参

```
请求参数对象: 对象
    序列号: 数值
    充电桩ID: 数值
    充电枪编号: 数值
    分配功率: 数值
    最大可配置功率: 数值
桩端请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(运营平台远程停机|运营平台远程控制启机|上调功率|下调功率)
        发起操作的对象: 枚举(充电管理|中转服务器|调度算法)
        关联的序列号: 数值
```

#### 运行过程

```

var 序列号 = 生成序列号()
var 上下文对象 = {
    操作类型: 上调功率,
    发起操作的对象: 调度算法,
    关联的序列号: 请求参数对象.序列号
}
桩端请求上下文.{序列号} = 上下文对象


发送socket->驱动服务:充电桩工作参数设置(
    序列号: 序列号, 桩编号: 请求参数对象.充电桩ID, 是否允许工作: 0x00, 充电桩最大允许输出功率: 向下取整(分配功率/最大可配置功率)
)
```

#### 返回值

```
无
```

---

### 调度算法 -> 充电管理: 下调功率

##### 入参

```
请求参数对象: 对象
    序列号: 数值
    充电桩ID: 数值
    充电枪编号: 数值
    分配功率: 数值
    最大可配置功率: 数值
桩端请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(运营平台远程停机|运营平台远程控制启机|上调功率|下调功率)
        发起操作的对象: 枚举(充电管理|中转服务器|调度算法)
        关联的序列号: 数值
```

#### 运行过程

```
订单数据 = 数据库->查询订单->筛选条件(充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号)->查询数据()

if 订单数据.充电状态 == 启机中 || 订单数据.充电状态 == 充电中
    
    
    // 需要停机
    if 请求参数对象.分配功率 == 0
    
        var 序列号 = 生成序列号()
        var 上下文对象 = {
            操作类型: 下调功率,
            发起操作的对象: 调度算法,
            关联的序列号: 请求参数对象.序列号
        }
        桩端请求上下文.{序列号} = 上下文对象
    
         发送socket->驱动服务:运营平台远程停机(
            序列号: 序列号, 充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号
         )
        
    else
        充电桩数据 = 数据库->查询充电桩->筛选条件(充电桩ID: 请求参数对象.充电桩ID)->查询数据()
    
        // 不需要停机
        if 请求参数对象.分配功率 >= 充电桩数据.最低可配置功率
            var 序列号 = 生成序列号()
            var 上下文对象 = {
                操作类型: 下调功率,
                发起操作的对象: 调度算法,
                关联的序列号: 请求参数对象.序列号
            }
            桩端请求上下文.{序列号} = 上下文对象
        
            发送socket->驱动服务:充电桩工作参数设置(
                序列号: 序列号,
                充电桩ID: 请求参数对象.充电桩ID, 是否允许工作: 0x00, 
                充电桩最大允许输出功率: 向下取整(请求参数对象.分配功率/请求参数对象.最大可配置功率)
            )
        // 异常情况
        else
            记录异常日志()
// 异常情况
else
    记录异常日志()
```

#### 返回值

```
无
```

---

### 调度算法 -> 充电管理: 结束充电应答

##### 入参

```
请求参数对象: 对象
    序列号: 数值
    充电桩ID: 数值
    充电枪编号: 数值
    结束结果: 枚举(成功|失败)
调度算法请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(开始充电|结束充电|上调功率|下调功率)
        发起操作的对象: 枚举(中转服务器|调度算法|定时器)
        关联的序列号: 数值
```

#### 运行过程

```
订单数据 = 数据库->查询订单->筛选条件(充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号)->查询数据()

if 订单数据.订单状态 == 已结束
    发送socket->中转服务器: 交易记录(
        交易流水号: 订单数据.交易流水号, 桩编号: 订单数据.充电桩ID, 充电枪ID: 订单数据.充电枪ID,
        开始时间: 订单数据.开始时间, 结束时间: 订单数据.结束时间,
        尖单价: 订单数据.尖单价, 尖电量: 订单数据.尖电量, 计损尖电量: 订单数据.计损尖电量, 尖金额: 订单数据.尖金额,
        峰单价: 订单数据.峰单价, 峰电量: 订单数据.峰电量, 计损峰电量: 订单数据.计损峰电量, 峰金额: 订单数据.峰金额,
        平单价: 订单数据.平单价, 平电量: 订单数据.平电量, 计损平电量: 订单数据.计损平电量, 平金额: 订单数据.平金额,
        谷单价: 订单数据.谷单价, 谷电量: 订单数据.谷电量, 计损谷电量: 订单数据.计损谷电量, 谷金额: 订单数据.谷金额,
        电表总起值: 订单数据.电表总起值, 电表总止值: 订单数据.电表总止值, 总电量: 订单数据.总电量, 计损总电量: 订单数据.计损总电量,
        消费金额: 订单数据.消费金额, 电动汽车唯一标识: 订单数据.电动汽车唯一标识, 交易标识: 订单数据.交易标识,
        交易日期时间: 订单数据.交易日期时间, 停止原因: 订单数据.停止原因, 物理卡号: 订单数据.物理卡号
    )
else
    上下文对象 = 调度算法请求上下文.{请求参数对象.序列号}
    if 上下文对象.操作类型 == 结束充电 且 上下文对象.发起操作的对象 == 中转服务器
        发送socket->中转服务器:结束充电应答(
            序列号: 上下文对象.关联的序列号,  桩编号: 订单数据.充电桩ID, 充电枪ID: 订单数据.充电枪ID,
            停止结果: 成功, 失败原因: 无
        )
```

#### 返回值

```
无
```

---

### 调度算法 -> 充电管理: 开始充电应答

##### 入参

```
请求参数对象: 对象
    序列号: 数值
    充电桩ID: 数值
    充电枪编号: 数值
    开始结果: 枚举(成功|失败)
调度算法请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(开始充电|结束充电|上调功率|下调功率)
        发起操作的对象: 枚举(中转服务器|调度算法|定时器)
        关联的序列号: 数值
```

#### 运行过程

```
订单数据 = 数据库->查询订单->筛选条件(充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号)->查询数据()

var 上下文对象 = 调度算法请求上下文.{请求参数对象.序列号}

if 上下文对象.发起操作的对象 == 定时器
    发送socket->定时器服务:定时任务触发应答(序列号: 上下文对象.关联的序列号, 结果: 成功)

else if 上下文对象.发起操作的对象 == 中转服务器
    发送socket->中转服务器:开始充电应答(
        序列号: 上下文对象.关联的序列号, 交易流水号: 订单数据.交易流水号, 充电桩ID: 订单数据.充电桩ID,
        充电枪编号: 订单数据.充电枪编号, 启动结果: 成功, 失败原因: 无
    )
    
删除(调度算法请求上下文.{请求参数对象.序列号})
```

#### 返回值

```
无
```