# 来自中转服务器的请求

## 版本: v1.0.1

## 考虑的点
- [x] 兼容云快充协议
- [ ] 接口之间并发的情况
- [ ] 充电桩出现故障导致停电后的情况
- [ ] 能源路由器重启或关机的情况

## 处理逻辑

### 中转服务器 -> 充电管理: 开始订单

##### 入参

```
请求参数对象: 对象
    序列号: 数值
    类型: 枚举(非定时|定时)
    充电桩ID: 字符串
    充电枪编号: 字符串
    交易流水号: 字符串
    逻辑卡号: 字符串
    物理卡号: 字符串
    账户余额: 数值(精确到小数点后2位)
    账户VIP等级: 数值
    定时时间段: 数组[对象]
        起始时间: 日期时间
        结束时间: 日期时间
调度算法请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(开始充电|结束充电|上调功率|下调功率)
        发起操作的对象: 枚举(中转服务器|调度算法|定时器)
        关联的序列号: 数值
定时器服务请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(添加定时任务|定时任务触发)
        发起操作的对象: 枚举(中转服务器|调度算法|定时器)
        关联的序列号: 数值
```

#### 运行过程

```
if 请求参数对象.类型 == 定时
    数据库->新建订单(
        类型: 请求参数对象.类型, 充电桩ID:请求参数对象.充电桩ID, 充电枪编号:请求参数对象.充电枪编号, 交易流水号:请求参数对象.交易流水号, 
        逻辑卡号:请求参数对象.逻辑卡号, 物理卡号:请求参数对象.物理卡号, 账户余额:请求参数对象.账户余额,
        账户VIP等级:请求参数对象.账户VIP等级, 充电状态:进行中, 充电状态:空闲中, 开始时间:当前时间, 结束时间:null, 交易日期时间:当前时间, 
        尖电量:0, 计损尖电量:0, 尖金额:0, 峰电量:0, 计损峰电量:0, 峰金额:0,
        平电量:0, 计损平电量:0, 平金额:0, 谷电量:0, 计损谷电量:0, 谷金额:0,
        电表总起值:0, 电表总止值:0, 总电量:0, 计损总电量:0, 消费金额:0, 电动汽车唯一标识:"", 交易标识:null
    )

    var 序列号 = 生成序列号()
    var 上下文对象 = {
        操作类型: 添加定时任务,
        发起操作的对象: 中转服务器,
        关联的序列号: 请求参数对象.序列号
    }
    定时器服务请求上下文.{序列号} = 上下文对象

    发送socket->定时器服务:添加定时任务(
        序列号:序列号, 充电桩ID:请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号, 定时时间段:请求参数对象.定时时间段
    )
    
else if 请求参数对象.类型 == 非定时

    数据库->新建订单(
        类型: 请求参数对象.类型, 充电桩ID:请求参数对象.充电桩ID, 充电枪编号:请求参数对象.充电枪编号, 交易流水号:请求参数对象.交易流水号, 
        逻辑卡号:请求参数对象.逻辑卡号, 物理卡号:请求参数对象.物理卡号, 账户余额:请求参数对象.账户余额, 
        账户VIP等级:请求参数对象.账户VIP等级, 充电状态:进行中, 充电状态:空闲中, 开始时间:当前时间, 结束时间:null, 交易日期时间:当前时间, 
        尖电量:0, 计损尖电量:0, 尖金额:0, 峰电量:0, 计损峰电量:0, 峰金额:0,
        平电量:0, 计损平电量:0, 平金额:0, 谷电量:0, 计损谷电量:0, 谷金额:0,
        电表总起值:0, 电表总止值:0, 总电量:0, 计损总电量:0, 消费金额:0, 电动汽车唯一标识:"", 交易标识:null
    )
    
    var 序列号 = 生成序列号()
    var 上下文对象 = {
        操作类型: 开始充电,
        发起操作的对象: 中转服务器,
        关联的序列号: 请求参数对象.序列号
    }
    调度算法请求上下文.{序列号} = 上下文对象
    
    发送socket->调度算法:开始充电(序列号: 序列号, 充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号)
```

#### 返回值

```
无
```

---

### 中转服务器 -> 充电管理: 停止订单

##### 入参

```
请求参数对象: 对象
    序列号: 数值
    充电桩ID: 数值
    充电枪编号: 数值
桩端请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(运营平台远程停机|运营平台远程控制启机|上调功率|下调功率)
        发起操作的对象: 枚举(充电管理|中转服务器|调度算法)
        关联的序列号: 数值
```

#### 运行过程

```
订单数据 = 数据库->查询订单表->筛选条件(充电桩ID:请求参数对象.充电桩ID, 充电枪编号:请求参数对象.充电枪编号)->查询数据()

if 订单数据 == null
    发送Socket->中转服务器:停止充电应答(失败原因: 设备编号不匹配)
else if 订单数据.订单状态 == 结束中 || 订单数据.订单状态 == 已结束
    发送Socket->中转服务器:停止充电应答(失败原因: 已结束充电)
else 
    // 这时候还在充电中 
    if 订单数据.充电状态 == 启机中 || 订单数据.充电状态 == 充电中
    
        数据库->更新订单表->筛选条件(充电桩ID:订单数据.充电桩ID, 充电枪编号:订单数据.充电枪编号)->更新数据(
            订单状态:结束中, 停机状态:停机中, 触发停机类型:中转服务器
        )
        
        var 序列号 = 生成序列号()
        var 上下文对象 = {
            操作类型: 运营平台远程停机,
            发起操作的对象: 中转服务器,
            关联的序列号: 请求参数对象.序列号
        }
        桩端请求上下文.{序列号} = 上下文对象
        
        发送socket->驱动服务:运营平台远程停机(序列号: 序列号, 充电桩ID: 订单数据.充电桩ID, 充电枪编号: 订单数据.充电枪编号)
        
    // 这时候已经停止充电了
    else if 订单数据.充电状态 == 空闲中
        数据库->更新订单表->筛选条件(充电桩ID:订单数据.充电桩ID, 充电枪编号:订单数据.充电枪编号)->更新数据(订单状态:已结束, 触发停机类型:中转服务器)
        
        发送socket->中转服务器:停止充电应答(
            序列号: 请求参数对象.序列号,
            充电桩ID:订单数据.充电桩ID, 充电枪编号:订单数据.充电枪编号, 停止结果: 成功, 失败原因: 无
        )
        
        发送socket->中转服务器: 交易记录(
            交易流水号: 订单数据.交易流水号, 充电桩ID: 订单数据.充电桩ID, 充电枪编号: 订单数据.充电枪编号,
            开始时间: 订单数据.开始时间, 结束时间: 订单数据.结束时间, 
            尖单价: 请求参数对象.尖单价, 尖电量: 订单数据.尖电量, 计损尖电量: 订单数据.计损尖电量, 尖金额: 订单数据.尖金额,
            峰单价: 请求参数对象.峰单价, 峰电量: 订单数据.峰电量, 计损峰电量: 订单数据.计损峰电量, 峰金额: 订单数据.峰金额,
            平单价: 请求参数对象.平单价, 平电量: 订单数据.平电量, 计损平电量: 订单数据.计损平电量, 平金额: 订单数据.平金额,
            谷单价: 请求参数对象.谷单价, 谷电量: 订单数据.谷电量, 计损谷电量: 订单数据.计损谷电量, 谷金额: 订单数据.谷金额,
            电表总起值: 订单数据.电表总起值, 电表总止值: 订单数据.电表总止值, 总电量: 订单数据.总电量, 计损总电量: 订单数据.计损总电量,
            消费金额: 订单数据.消费金额, 电动汽车唯一标识: 订单数据.电动汽车唯一标识, 交易标识: 订单数据.交易标识, 
            交易日期时间: 订单数据.交易日期时间, 停止原因: 订单数据.停止原因, 物理卡号: 订单数据.物理卡号
        )
        
    // 这时候正在停止充电
    else if 订单数据.充电状态 == 停机中
        // TODO: 这里需要与驱动停止充电请求中增加一个互斥锁，避免出现逻辑混乱。
        数据库->更新订单表->筛选条件(充电桩ID: 订单数据.充电桩ID, 充电枪编号: 订单数据.充电枪编号)->更新数据(订单状态:结束中)
        
         发送socket->中转服务器:停止充电应答(
            序列号: 请求参数对象.序列号,
            充电桩ID:订单数据.充电桩ID, 充电枪编号:订单数据.充电枪编号, 停止结果: 成功, 失败原因: 无
        )
        
        // 这里这样就可以了，等停机应答发送来了之后，那边的逻辑自然会根据这个订单状态(结束中)，做后续操作。
```

#### 返回值

```
无
```

