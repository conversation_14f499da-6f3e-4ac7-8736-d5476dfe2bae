# 来自定时器服务的请求

## 版本: v1.0.1

## 考虑的点
- [x] 兼容云快充协议
- [ ] 接口之间并发的情况
- [ ] 充电桩出现故障导致停电后的情况
- [ ] 能源路由器重启或关机的情况


## 处理逻辑

### 中转服务器 -> 充电管理: 添加定时任务应答

##### 入参

```
请求参数对象: 对象
    序列号: 数值
    充电桩ID: 数值
    充电枪编号: 数值(1|2)
    添加结果: 枚举(成功)
定时器服务请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(添加定时任务|定时任务触发)
        发起操作的对象: 枚举(中转服务器|调度算法|定时器)
        关联的序列号: 数值
```

##### 运行过程

```
上下文对象 = 定时器服务请求上下文.{请求参数对象.序列号}

if 上下文对象.发起操作的对象 == 中转服务器
    订单数据 = 数据库->查询订单->筛选条件(充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号)->查询数据()
    发送socket->中转服务器:开始充电应答(
        序列号: 上下文对象.关联的序列号, 交易流水号: 订单数据.交易流水号, 充电桩ID: 订单数据.充电桩ID,
        充电枪编号: 订单数据.充电枪编号, 启动结果: 成功, 失败原因: 无
    )

删除(定时器服务请求上下文.{请求参数对象.序列号})

```

--- 


### 中转服务器 -> 充电管理: 定时任务触发

##### 入参

```
请求参数对象: 对象
    序列号: 数值
    充电桩ID: 数值
    充电枪编号: 数值(1|2)
调度算法请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(开始充电|结束充电|上调功率|下调功率)
        发起操作的对象: 枚举(中转服务器|调度算法|定时器)
        关联的序列号: 数值
```

##### 运行过程

```

var 序列号 = 生成序列号()
var 上下文对象 = {
    操作类型: 开始充电,
    发起操作的对象: 定时器,
    关联的序列号: 请求参数对象.序列号
}
调度算法请求上下文.{序列号} = 上下文对象

发送socket->调度算法: 开始充电(序列号:序列号, 充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号)
```
