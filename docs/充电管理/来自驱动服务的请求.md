# 来自驱动服务的请求

## 版本: v1.0.1

## 考虑的点
- [x] 兼容云快充协议
- [ ] 接口之间并发的情况
- [ ] 充电桩出现故障导致停电后的情况
- [ ] 能源路由器重启或关机的情况

## 处理逻辑

### 驱动服务 -> 充电管理: 远程启动充电命令回复(开始充电应答)

##### 入参
```
请求参数对象: 对象
    序列号: 数值
    交易流水号: 字符串
    充电桩ID: 字符串
    充电枪编号: 字符串
    启动结果: 枚举(失败|成功)
    失败原因: 枚举(无|设备编号不匹配|枪已在充电|设备故障|设备离线|未插枪)
桩端请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(运营平台远程停机|运营平台远程控制启机|上调功率|下调功率)
        发起操作的对象: 枚举(充电管理|中转服务器|调度算法)
        关联的序列号: 数值
```

#### 运行过程

```
if 请求参数对象.启动结果 == 成功
    数据库->更新订单->筛选条件(交易流水号 = 交易流水号)->更新数据(充电状态: 充电中)
    
    数据库->新建充电流水(
        充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号, 交易流水号: 请求参数对象.交易流水号, 
        状态: 记录中, 开始时间:当前时间, 结束时间:空, 
        尖电量: 0, 计损尖电量: 0, 尖金额: 0, 峰电量: 0, 计损峰电量: 0, 峰金额: 0,
        平电量: 0, 计损平电量: 0, 平金额: 0, 谷电量: 0, 计损谷电量: 0, 谷金额: 0,
        电表总起值: 0, 电表总止值: 0, 总电量: 0, 计损总电量: 0, 消费金额: 0
    )
    
    
else if 请求参数对象.启动结果 == 失败
    记录日志()
    
    数据库->更新订单->筛选条件(交易流水号: 请求参数对象.交易流水号)->更新数据(充电状态: 空闲中, 订单状态: 已结束)
    
    发送socket->调度算法: 结束充电(充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号)
    
删除(桩端请求上下文.{请求参数对象.序列号})
```

#### 返回值

```
无
```

---

### 驱动服务 -> 充电管理: 上传实时监测数据(充电过程中上报的充电数据)

##### 入参
```
请求参数对象: 对象
    交易流水号: 字符串
    充电桩ID: 字符串
    充电枪编号: 字符串
    状态: 枚举(离线|故障|空闲|充电)
    枪是否归位: 枚举(未归位|已归位|未知)
    是否插枪: 枚举(未插枪|已插枪)
    输出电压: 数值(精确到小数点后一位)
    输出电流: 数值(精确到小数点后一位)
    枪线温度: 数值(偏移量-50)
    枪线编码: 数值
    SOC: 数值
    电池组最高温度: 数值(偏移量-50)
    累计充电时间: 数值(单位:分钟)
    剩余时间: 数值(单位:分钟) 交流桩这个字段无效
    充电度数: 数值(精确到小数点后四位)
    计损充电度数: 数值(精确到小数点后四位)
    已充金额: 数值(精确到小数点后四位)
    硬件故障: 数组[枚举(急停按钮动作故障|无可用整流模块|...)]
```

#### 运行过程

```
数据库->更新充电流水->筛选条件(交易流水号: 请求参数对象.交易流水号, 状态: 记录中)->更新数据(
    计损总电量: 请求参数对象.计损充电度数, 总电量: 请求参数对象.充电度数, 消费金额: 请求参数对象.已充金额
)

充电流水数据 = 数据库->查询充电流水->筛选条件(交易流水号: 请求参数对象.交易流水号, 状态: 记录中)->查询数据()
订单数据 = 数据库->查询订单->筛选条件(交易流水号: 请求参数对象.交易流水号)->查询数据()

// 补充历史充电的电量与费用
请求参数对象.充电度数 = 订单数据.总电量 + 充电流水数据.总电量
请求参数对象.计损充电度数 += 订单数据.计损总电量 + 充电流水数据.计损总电量
请求参数对象.已充金额 += 订单数据.消费金额 + 充电流水数据.消费金额

发送socket->中转服务器:上传实时监测数据(请求参数对象)
```

#### 返回值

```
无
```

---

### 驱动服务 -> 充电管理: 远程停机命令回复

##### 入参
```
请求参数对象: 对象
    充电桩ID: 数值
    充电枪编号: 数值
    停止结果: 枚举(失败|成功)
    失败原因: 枚举(无|设备编号不匹配|枪未处于充电状态|其他)
桩端请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(运营平台远程停机|运营平台远程控制启机|上调功率|下调功率)
        发起操作的对象: 枚举(充电管理|中转服务器|调度算法)
        关联的序列号: 数值
调度算法请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(开始充电|结束充电|上调功率|下调功率)
        发起操作的对象: 枚举(中转服务器|调度算法|定时器)
        关联的序列号: 数值
```

#### 运行过程

```

订单数据 = 数据库->查询订单->筛选条件(充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号)->查询数据()

if 订单数据 == 空
    // 记录日志
else 
    
    上下文对象 = 桩端请求上下文.{请求参数对象.序列号}
    
    if 请求参数对象.停止结果 == 成功
        数据库->更新订单->筛选条件(充电桩ID:请求参数对象.充电桩ID, 充电枪编号:请求参数对象.充电枪编号)->更新数据(充电状态: 空闲中)

        if 上下文对象.操作类型 == 下调功率
            发送socket->调度算法:下调功率应答(
                序列号: 上下文对象.关联的序列号, 充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号, 结果: 成功
            )
        else if 上下文对象.操作类型 == 运营平台远程停机
        
            var 序列号 = 生成序列号()
            var 新上下文对象 = {
                操作类型: 结束充电,
                发起操作的对象: 中转服务器,
                关联的序列号: 请求参数对象.序列号
            }
            调度算法请求上下文.{序列号} = 上下文对象
            
            发送socket->调度算法:下调功率应答(
                序列号: 新上下文对象.关联的序列号, 充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号, 结果: 成功
            )
            
    else if 请求参数对象.停止结果 == 失败
    
        记录异常日志()
        
        充电流水数据 = 数据库->充电流水记录->筛选条件(充电桩ID:请求参数对象.充电桩ID, 充电枪编号:请求参数对象.充电枪编号)->查询数据()
        
        数据库->更新订单->筛选条件(充电桩ID:请求参数对象.充电桩ID, 充电枪编号:请求参数对象.充电枪编号)->更新数据(
            充电状态: 空闲中, 订单状态: 已结束,
            尖电量+: 充电流水数据.尖电量, 计损尖电量+: 充电流水数据.计损尖电量, 尖金额+: 充电流水数据.尖金额,
            峰电量+: 充电流水数据.峰电量, 计损峰电量+: 充电流水数据.计损峰电量, 峰金额+: 充电流水数据.峰金额,
            平电量+: 充电流水数据.平电量, 计损平电量+: 充电流水数据.计损平电量, 平金额+: 充电流水数据.平金额,
            谷电量+: 充电流水数据.谷电量, 计损谷电量+: 充电流水数据.计损谷电量, 谷金额+: 充电流水数据.谷金额,
            电表总止值: 充电流水数据.电表总止值, 总电量+: 充电流水数据.总电量, 计损总电量+: 充电流水数据.计损总电量,
            消费金额+: 充电流水数据.消费金额, 结束时间: 当前日期时间, 交易日期时间: 当前日期时间, 
        )
        
        发送socket->调度算法:下调功率应答(
            序列号: 上下文对象.关联的序列号, 充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号, 结果: 失败
        )
        
        发送socket->调度算法:结束充电(充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号)
    
    if 订单数据.触发停机类型 == 中转服务器
        发送socket->中转服务请求:结束充电应答(
            充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号, 
            停止结果: 请求参数对象.停止结果, 失败原因: 请求参数对象.失败原因
        )
```

#### 返回值

```
无
```

--- 

### 驱动服务 -> 充电管理: 交易记录

##### 入参

```
请求参数对象: 对象
    交易流水号: 字符串
    充电桩ID: 数值
    充电枪编号: 数值
    开始时间: 日期时间
    结束时间: 日期时间
    尖单价: 数值(精确到小数点后五位) 尖电费+尖服务费
    尖电量: 数值(精确到小数点后四位)
    计损尖电量: 数值(精确到小数点后四位)
    尖金额: 数值(精确到小数点后四位)
    峰单价: 数值(精确到小数点后五位) 峰电费+峰服务费
    峰电量: 数值(精确到小数点后四位)
    计损峰电量: 数值(精确到小数点后四位)
    峰金额: 数值(精确到小数点后四位)
    平单价: 数值(精确到小数点后五位) 平电费+平服务费
    平电量: 数值(精确到小数点后四位)
    计损平电量: 数值(精确到小数点后四位)
    平金额: 数值(精确到小数点后四位)
    谷单价: 数值(精确到小数点后五位) 谷电费+谷服务费
    谷电量: 数值(精确到小数点后四位)
    计损谷电量: 数值(精确到小数点后四位)
    谷金额: 数值(精确到小数点后四位)
    电表总起值: 数值(精确到小数点后四位)
    电表总止值: 数值(精确到小数点后四位)
    总电量: 数值(精确到小数点后四位)
    计损总电量: 数值(精确到小数点后四位)
    消费金额: 数值(精确到小数点后四位) 电费+服务费
    电动汽车唯一标识: 字符串
    交易标识: 枚举(app启动|卡启动|离线卡启动|vin码启动充电)
    交易日期时间: 日期时间
    停止原因: 数值
    物理卡号: 字符串
```

#### 运行过程

```
订单数据 = 数据库->查询订单->筛选条件(交易流水号 = 交易流水号)->查询() 

if 订单数据 == 空
    // 在日志上打个标记: 方便排查
else 
    // 如果满足以下条件，说明是用户停机的充电。
    if 订单数据.订单状态 == 结束中
    
        // 完成这一阶段的充电流水
        数据库->更新充电流水->筛选条件(交易流水号 = 交易流水号 且 状态 = 记录中)->更新数据(
            尖电量: 请求参数对象.尖电量, 计损尖电量: 请求参数对象.计损尖电量, 尖金额: 请求参数对象.尖金额,
            峰电量: 请求参数对象.峰电量, 计损峰电量: 请求参数对象.计损峰电量, 峰金额: 请求参数对象.峰金额,
            平电量: 请求参数对象.平电量, 计损平电量: 请求参数对象.计损平电量, 平金额: 请求参数对象.平金额,
            谷电量: 请求参数对象.谷电量, 计损谷电量: 请求参数对象.计损谷电量, 谷金额: 请求参数对象.谷金额,
            电表总止值: 请求参数对象.电表总止值, 总电量: 请求参数对象.总电量, 计损总电量: 请求参数对象.计损总电量,
            消费金额: 请求参数对象.消费金额, 状态: 记录完成, 结束时间: 请求参数对象.结束时间
        )
        
        // 更新订单状态: 已结束, 累计电量与费用("+:" 表示累加)
        数据库->更新订单->筛选条件(交易流水号: 请求参数对象.交易流水号)->更新数据(
            尖电量+: 请求参数对象.尖电量, 计损尖电量+: 请求参数对象.计损尖电量, 尖金额+: 请求参数对象.尖金额,
            峰电量+: 请求参数对象.峰电量, 计损峰电量+: 请求参数对象.计损峰电量, 峰金额+: 请求参数对象.峰金额,
            平电量+: 请求参数对象.平电量, 计损平电量+: 请求参数对象.计损平电量, 平金额+: 请求参数对象.平金额,
            谷电量+: 请求参数对象.谷电量, 计损谷电量+: 请求参数对象.计损谷电量, 谷金额+: 请求参数对象.谷金额,
            电表总止值: 请求参数对象.电表总止值, 总电量+: 请求参数对象.总电量, 计损总电量+: 请求参数对象.计损总电量,
            消费金额+: 请求参数对象.消费金额, 订单状态: 已结束, 结束时间: 请求参数对象.结束时间, 交易日期时间: 请求参数对象.交易日期时间, 
            电动汽车唯一标识: 请求参数对象.电动汽车唯一标识, 交易标识: 请求参数对象.交易标识
        )
        
        // 查询累计后的订单数据
        订单数据 = 数据库->查询订单->筛选条件(交易流水号: 请求参数对象.交易流水号)->查询数据()

        // 交易记录应答
        发送socket->驱动服务:交易记录应答(交易流水号: 请求参数对象.交易标识, 确认结果: 上传成功)

        // 发送交易记录给业务层
        发送socket->中转服务器: 交易记录(
            交易流水号: 订单数据.交易流水号, 充电桩ID: 订单数据.充电桩ID, 充电枪编号: 订单数据.充电枪编号,
            开始时间: 订单数据.开始时间, 结束时间: 订单数据.结束时间, 
            尖单价: 请求参数对象.尖单价, 尖电量: 订单数据.尖电量, 计损尖电量: 订单数据.计损尖电量, 尖金额: 订单数据.尖金额,
            峰单价: 请求参数对象.峰单价, 峰电量: 订单数据.峰电量, 计损峰电量: 订单数据.计损峰电量, 峰金额: 订单数据.峰金额,
            平单价: 请求参数对象.平单价, 平电量: 订单数据.平电量, 计损平电量: 订单数据.计损平电量, 平金额: 订单数据.平金额,
            谷单价: 请求参数对象.谷单价, 谷电量: 订单数据.谷电量, 计损谷电量: 订单数据.计损谷电量, 谷金额: 订单数据.谷金额,
            电表总起值: 订单数据.电表总起值, 电表总止值: 订单数据.电表总止值, 总电量: 订单数据.总电量, 计损总电量: 订单数据.计损总电量,
            消费金额: 订单数据.消费金额, 电动汽车唯一标识: 订单数据.电动汽车唯一标识, 交易标识: 订单数据.交易标识, 
            交易日期时间: 订单数据.交易日期时间, 停止原因: 订单数据.停止原因, 物理卡号: 订单数据.物理卡号
        )
        
        
    // 这种情况是调度算法下调功率后，将充电桩暂时停止充电了。
    else if 订单数据.订单状态 == 进行中
        数据库->更新充电流水->筛选条件(交易流水号 = 交易流水号 且 状态 = 记录中)->更新数据(
            尖电量: 请求参数对象.尖电量, 计损尖电量: 请求参数对象.计损尖电量, 尖金额: 请求参数对象.尖金额,
            峰电量: 请求参数对象.峰电量, 计损峰电量: 请求参数对象.计损峰电量, 峰金额: 请求参数对象.峰金额,
            平电量: 请求参数对象.平电量, 计损平电量: 请求参数对象.计损平电量, 平金额: 请求参数对象.平金额,
            谷电量: 请求参数对象.谷电量, 计损谷电量: 请求参数对象.计损谷电量, 谷金额: 请求参数对象.谷金额,
            电表总止值: 请求参数对象.电表总止值, 总电量: 请求参数对象.总电量, 计损总电量: 请求参数对象.计损总电量,
            消费金额: 请求参数对象.消费金额, 状态: 记录完成, 结束时间: 请求参数对象.结束时间
        )
        
        // +: 表示累加
        数据库->更新订单->筛选条件(交易流水号: 请求参数对象.交易流水号)->更新数据(
            尖电量+: 请求参数对象.尖电量, 计损尖电量+: 请求参数对象.计损尖电量, 尖金额+: 请求参数对象.尖金额,
            峰电量+: 请求参数对象.峰电量, 计损峰电量+: 请求参数对象.计损峰电量, 峰金额+: 请求参数对象.峰金额,
            平电量+: 请求参数对象.平电量, 计损平电量+: 请求参数对象.计损平电量, 平金额+: 请求参数对象.平金额,
            谷电量+: 请求参数对象.谷电量, 计损谷电量+: 请求参数对象.计损谷电量, 谷金额+: 请求参数对象.谷金额,
            电表总止值: 请求参数对象.电表总止值, 总电量+: 请求参数对象.总电量, 计损总电量+: 请求参数对象.计损总电量,
            消费金额+: 请求参数对象.消费金额, 订单状态: 已结束, 结束时间: 请求参数对象.结束时间, 交易日期时间: 请求参数对象.交易日期时间, 
            电动汽车唯一标识: 请求参数对象.电动汽车唯一标识, 交易标识: 请求参数对象.交易标识
        )
        
        发送socket->驱动服务:交易记录应答(交易流水号: 请求参数对象.交易标识, 确认结果: 上传成功)
```

#### 返回值

```
无
```

---

### 驱动服务 -> 充电管理: 充电桩工作参数设置应答

##### 入参

```
请求参数对象: 对象
    序列号: 数值
    充电桩ID: 数值
    充电枪编号: 数值
    设置结果: 枚举(失败|成功)
桩端请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(运营平台远程停机|运营平台远程控制启机|上调功率|下调功率)
        发起操作的对象: 枚举(充电管理|中转服务器|调度算法)
        关联的序列号: 数值
```

#### 运行过程

```

订单数据 = 数据库->查询订单->筛选条件(充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号)->查询数据()

上下文对象 = 桩端请求上下文.{请求参数对象.序列号}

if 上下文对象.操作类型 == 上调功率
    发送socket->调度算法: 上调功率应答(
        序列号: 上下文对象.关联的序列号,
        充电桩ID: 请求参数对象.充电桩ID, 
        充电枪编号: 请求参数对象.充电枪编号, 
        设置结果: 请求参数对象.设置结果
    )
    
    if 订单数据.充电状态 == 空闲中 || 订单数据.充电状态 == 停机中
        
        var 序列号 = 生成序列号()
        var 新上下文对象 = {
            操作类型: 运营平台远程控制启机,
            发起操作的对象: 充电管理,
            关联的序列号: null
        }
        桩端请求上下文.{序列号} = 新上下文对象
    
        数据库->更新订单->筛选条件(充电桩ID: 请求参数对象.充电桩ID, 充电枪编号: 请求参数对象.充电枪编号)->更新数据(
            充电状态: 启机中, 
        )
        
        发送socket->驱动服务: 运营平台远程控制启机(
            序列号: 新上下文对象.序列号,
            交易流水号: 订单数据.交易流水号, 桩编号: 订单数据.充电桩ID, 枪号: 订单数据.充电枪编号,
            逻辑卡号: 订单数据.逻辑卡号, 物理卡号: 订单数据.物理卡号, 账户余额: 订单数据.账户余额,
        )
        
else if 上下文对象.操作类型 == 下调功率

    发送socket->调度算法: 下调功率应答(
        序列号: 上下文对象.关联的序列号,
        充电桩ID: 请求参数对象.充电桩ID, 
        充电枪编号: 请求参数对象.充电枪编号, 
        设置结果: 请求参数对象.设置结果
    )
```