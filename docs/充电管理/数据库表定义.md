# 数据库定义

## 版本: v1.0.1

## 数据库表

```
桩端请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(运营平台远程停机|运营平台远程控制启机|上调功率|下调功率)
        发起操作的对象: 枚举(充电管理|中转服务器|调度算法)
        关联的序列号: 数值
        
        
调度算法请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(开始充电|结束充电|上调功率|下调功率)
        发起操作的对象: 枚举(中转服务器|调度算法|定时器)
        关联的序列号: 数值

定时器服务请求上下文: 对象{key: 序列号, value: 上下文对象}
    序列号: 数值
    上下文对象: 对象
        操作类型: 枚举(添加定时任务|定时任务触发)
        发起操作的对象: 枚举(中转服务器|调度算法|定时器)
        关联的序列号: 数值
```


### 充电桩表

```
充电桩ID: 数值
充电枪编号: 数值(1|2)
最低可配置功率: 数值
最高可配置功率: 数值
可配置功率步长: 数值
所属相线: 枚举(a|b|c)
一级电缆ID: 数值
二级电缆ID: 数值
三级电缆ID: 数值
```

---

### 充电记录表

```
充电桩ID: 数值
充电枪编号: 数值(1|2)
实时功率: 数值(单位:瓦)
计划功率: 数值(单位:瓦)
```

---

### 订单表
```
类型: 枚举(非定时|定时)
充电桩ID: 数值
充电枪编号: 数值(1|2)
交易流水号: 字符串
逻辑卡号: 字符串
物理卡号: 字符串
账户余额: 数值(精确到小数点后2位)
账户VIP等级: 数值
充电状态: 枚举(空闲中|启机中|充电中|停机中)
订单状态: 枚举(进行中|结束中|已结束)
触发停机类型: 枚举(中转服务器|调度算法)
触发启机类型: 枚举(中转服务器|调度算法)
功率分配类型: 枚举(上调功率|下调功率)
触发调度算法开始调度的因素: 枚举(定时器服务|中转服务器)
开始时间: 日期时间
结束时间: 日期时间
交易日期时间: 日期时间
尖电量: 数值(精确到小数点后四位)
计损尖电量: 数值(精确到小数点后四位)
尖金额: 数值(精确到小数点后四位)
峰电量: 数值(精确到小数点后四位)
计损峰电量: 数值(精确到小数点后四位)
峰金额: 数值(精确到小数点后四位)
平电量: 数值(精确到小数点后四位)
计损平电量: 数值(精确到小数点后四位)
平金额: 数值(精确到小数点后四位)
谷电量: 数值(精确到小数点后四位)
计损谷电量: 数值(精确到小数点后四位)
谷金额: 数值(精确到小数点后四位)
电表总起值: 数值(精确到小数点后四位)
电表总止值: 数值(精确到小数点后四位)
总电量: 数值(精确到小数点后四位)
计损总电量: 数值(精确到小数点后四位)
消费金额: 数值(精确到小数点后四位) 包含电费 + 服务费
电动汽车唯一标识: 字符串
交易标识: 枚举(app启动|卡启动|离线卡启动|vin码启动充电)
```

---

### 充电流水表

```
主键ID: 数值(自增)
充电桩ID: 数值
充电枪编号: 数值(1|2)
交易流水号: 字符串
状态: 枚举(记录中|记录完成)
开始时间: 日期时间
结束时间: 日期时间
尖电量: 数值(精确到小数点后四位)
计损尖电量: 数值(精确到小数点后四位)
尖金额: 数值(精确到小数点后四位)
峰电量: 数值(精确到小数点后四位)
计损峰电量: 数值(精确到小数点后四位)
峰金额: 数值(精确到小数点后四位)
平电量: 数值(精确到小数点后四位)
计损平电量: 数值(精确到小数点后四位)
平金额: 数值(精确到小数点后四位)
谷电量: 数值(精确到小数点后四位)
计损谷电量: 数值(精确到小数点后四位)
谷金额: 数值(精确到小数点后四位)
电表总起值: 数值(精确到小数点后四位)
电表总止值: 数值(精确到小数点后四位)
总电量: 数值(精确到小数点后四位)
计损总电量: 数值(精确到小数点后四位)
消费金额: 数值(精确到小数点后四位) 包含电费 + 服务费
```