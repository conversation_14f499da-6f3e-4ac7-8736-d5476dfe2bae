
```mermaid
sequenceDiagram
	autonumber
	客户端->>服务端: WebSocket 建立连接
	服务端->>服务端: 创建定时器：30秒后如果还没有登录，断开连接。
	服务端->>客户端: WebSocket 登录邀请
	客户端->>设备管理服务: HTTP login_notice_web_socket
	设备管理服务->>客户端: WebSocket 登录信息
	客户端->>服务端: WebSocket 登录
	服务端->>服务端: 登录成功 删除定时器
	服务端->>客户端: 登录成功
```

## 1. 服务端向客户端发送的消息包


### 1.1. 登录邀请

场景：在客户端刚建立连接后，服务端要求客户端使用这个 `client_id` 来进行登录操作。
接收方：发起登录的客户端
协议包格式：
```json
{
	"type": "login", // 类型 login:登录信息
	"client_id": xxx // 客户端ID
}
```


### 1.2. 登录成功

场景：在客户端登录成功后收到这个消息
接收方：单个客户端
协议包格式：
```json
{
	"type": "login_success_del" // 类型 login_success_del:登录成功
}
```

## 2. 客户端向服务端发送的消息

### 2.1. 登录

场景：客户端在收到登录信息后，发起的登录操作
接收方：服务端
协议包格式：
```json
{
	"type": "login_success", // 类型 login_success:登录
	"login_id": xxx // 登录ID
}
```

## 3. 客户端向客户端发送的消息

### 3.1. 登录信息

场景：客户端在收到登录信息后，发起的登录操作
接收方：服务端
协议包格式：
```json
{
	"type": "login_success", // 类型 login_success:登录
	"ws_group": xxx,         // 分组
	"token": xxx,            // 登录令牌
	"login_id": xxx          // 登录ID
}
```

### 3.2. 上传实时监测数据通知

- 场景：在充电桩发送充电枪实时数据时，就发送这条消息。
- 接收方：给 "后台" 分组的所有客户端发送
- 协议包格式：
```json
{
	"type": "upload_realtime_monitoring_data_notice",  
	"msg": "上传实时监测数据通知",  
	"data" => {
		"msg_type": "upload_realtime_monitoring_data",
		"transaction_serial_number": "20230105000369012308301246440003", // 交易流水号
		"piles_id": 20230105000369, // 充电桩ID
		"shots_id": 2023010500036901, // 充电枪ID
		"status": 2,  // 状态 0:离线 1:故障 2:空闲 3:充电
		"is_reset": 1, // 充电枪是否归位 0:否 1:是 2:未知(无法检测到枪是否插回枪座即未知)
		"is_plugged_in": 1, // 是否插枪 0:否 1:是
		"output_voltage": 0, // 输出电压 
		"output_current": 0, // 输出电流
		"gun_wire_temperature": 10, // 枪线温度
		"gun_wire_code": 0, // 枪线编码
		"soc": 0, // SOC
		"maximum_battery_temperature": 0, // 电池组最高温度
		"cumulative_charging_time": 0, // 累计充电时间
		"remaining_time": 0, // 剩余时间
		"charging_percentage": 0, // 充电度数
		"calculated_loss_charging_percentage": 0, // 计损充电度数
		"amount_charged": 0, // 已充金额
		"hardware_failure": 0, // 硬件故障 Bit位表示(0:否 1:是)低位到高位顺序 bit1:急停按钮动作故障 bit2:无可用整流模块 bit3:出风口温度过高 bit4:交流防雷故障 bit5:交直流模块DC20通信中断 bit6:绝缘检测模块FC08通信中断 bit7:电度表通信中断 bit8:读卡器通信中断 bit9:RC10通信中断 bit10:风扇调速板故障 bit11:直流熔断器故障 bit12:高压接触器故障 bit13:门打开
		"hardware_failure_arr": [] // 硬件故障描述
	}
}
```