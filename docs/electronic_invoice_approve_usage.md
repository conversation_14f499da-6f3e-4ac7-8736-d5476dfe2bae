# 电子发票审批接口使用说明

本文档说明如何使用新增的电子发票审批功能。

## 功能概述

电子发票审批功能允许后台管理员对用户提交的发票申请进行审批，审批通过后自动上传PDF文件到微信并插入用户卡包。

## 数据库变更

首先需要执行数据库迁移文件：

```sql
-- 执行以下SQL文件
database/change_log/2024-12-26/electronic_invoice_approve.sql
```

## 接口说明

### 1. 获取待审批发票列表

**接口地址：** `POST /admin/electronic_invoice/pending_approval`

**请求参数：**
```json
{
    "id": "",                    // 可选，发票申请单号
    "nickname": "",              // 可选，用户昵称
    "title_type": 0,             // 可选，抬头类型 0:个人 1:单位
    "title_name": "",            // 可选，抬头名称
    "taxpayer_id": "",           // 可选，纳税人识别号
    "start_time": "",            // 可选，起始时间
    "end_time": "",              // 可选，结束时间
    "page": 1,                   // 可选，页码
    "limit": 10                  // 可选，每页显示条数
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "成功",
    "data": {
        "total": 10,
        "per_page": 10,
        "current_page": 1,
        "last_page": 1,
        "data": [
            {
                "id": "abc123",
                "user_id": 1001,
                "nickname": "张三",
                "total_amount": 10000,
                "title_type": 0,
                "title_name": "张三",
                "taxpayer_id": "",
                "stage": 1,
                "approve_status": 0,
                "approve_remark": "",
                "approve_admin_id": 0,
                "approve_time": 0,
                "file_path": "",
                "create_time": 1703577600,
                "update_time": 1703577600
            }
        ]
    }
}
```

### 2. 电子发票审批

**接口地址：** `POST /admin/electronic_invoice/approve`

**请求参数：**
```json
{
    "fapiao_apply_id": "abc123",     // 必填，发票申请单号
    "file_path": "/path/to/file.pdf", // 必填，PDF文件地址
    "approve_status": 1,             // 必填，审批状态 1:通过 2:拒绝
    "remark": "审批通过"              // 可选，审批备注
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "审批处理成功",
    "data": {
        "message": "发票审批通过，已成功上传并插入用户卡包"
    }
}
```

## 使用流程

### 1. 完整的审批流程

```php
// 1. 获取待审批列表
$pendingList = $client->post('/admin/electronic_invoice/pending_approval', [
    'page' => 1,
    'limit' => 10
]);

// 2. 选择要审批的申请
$fapiaoApplyId = 'abc123';
$filePath = '/uploads/invoices/invoice_20241226.pdf';

// 3. 审批通过
$approveResult = $client->post('/admin/electronic_invoice/approve', [
    'fapiao_apply_id' => $fapiaoApplyId,
    'file_path' => $filePath,
    'approve_status' => 1, // 1:通过
    'remark' => '发票信息核实无误，审批通过'
]);

// 4. 审批拒绝
$rejectResult = $client->post('/admin/electronic_invoice/approve', [
    'fapiao_apply_id' => $fapiaoApplyId,
    'file_path' => $filePath,
    'approve_status' => 2, // 2:拒绝
    'remark' => '发票信息有误，请重新提交'
]);
```

### 2. 前端集成示例

```javascript
// 获取待审批列表
async function getPendingApprovals(page = 1) {
    const response = await fetch('/admin/electronic_invoice/pending_approval', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        },
        body: JSON.stringify({
            page: page,
            limit: 10
        })
    });
    
    return await response.json();
}

// 审批发票
async function approveInvoice(fapiaoApplyId, filePath, approveStatus, remark = '') {
    const response = await fetch('/admin/electronic_invoice/approve', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        },
        body: JSON.stringify({
            fapiao_apply_id: fapiaoApplyId,
            file_path: filePath,
            approve_status: approveStatus,
            remark: remark
        })
    });
    
    return await response.json();
}

// 使用示例
getPendingApprovals().then(data => {
    console.log('待审批列表:', data);
    
    if (data.data.data.length > 0) {
        const firstApply = data.data.data[0];
        
        // 审批通过
        approveInvoice(
            firstApply.id,
            '/uploads/invoices/invoice.pdf',
            1,
            '审批通过'
        ).then(result => {
            console.log('审批结果:', result);
        });
    }
});
```

## 状态说明

### 审批状态 (approve_status)
- `0`: 待审批
- `1`: 审批通过
- `2`: 审批拒绝

### 申请阶段 (stage)
- `0`: 用户发起申请
- `1`: 用户填写抬头完成（可以进行审批）
- `2`: 开具发票完成

### 抬头类型 (title_type)
- `0`: 个人
- `1`: 单位

## 注意事项

1. **文件要求**：
   - 只支持PDF格式
   - 文件大小不能超过2MB
   - 文件路径必须是服务器可访问的绝对路径

2. **审批权限**：
   - 只有登录的管理员才能进行审批操作
   - 系统会记录审批人ID和审批时间

3. **状态限制**：
   - 只能审批状态为"用户填写抬头完成"的申请
   - 已审批的申请不能重复审批

4. **微信接口**：
   - 审批通过后会自动调用微信接口上传文件
   - 文件上传成功后会自动插入用户卡包
   - 如果微信接口调用失败，会抛出异常

5. **日志记录**：
   - 所有审批操作都会记录到系统日志
   - 包括审批人、审批时间、审批结果等信息

## 错误处理

常见错误及解决方案：

1. **发票申请记录不存在**
   - 检查 `fapiao_apply_id` 是否正确
   - 确认申请记录是否已被删除

2. **PDF文件不存在**
   - 检查文件路径是否正确
   - 确认文件是否已上传到服务器

3. **文件格式不支持**
   - 只支持PDF格式，检查文件扩展名
   - 确认文件没有损坏

4. **微信接口调用失败**
   - 检查微信配置是否正确
   - 确认网络连接正常
   - 查看详细错误日志

5. **权限不足**
   - 确认管理员已登录
   - 检查管理员权限配置

## 扩展功能

如需扩展功能，可以考虑：

1. **批量审批**：支持一次审批多个申请
2. **审批流程**：增加多级审批流程
3. **自动审批**：基于规则的自动审批
4. **审批统计**：审批数据统计和报表
5. **消息通知**：审批结果的消息推送
