## 2025-4-15

修改订单表discount字段类型，提高精度
```sql
ALTER TABLE `order` 
MODIFY COLUMN `discount` decimal(11, 2) UNSIGNED NOT NULL DEFAULT 100 COMMENT '优惠折扣(整数部分精确到小数点后1位)29.4表示2.94折' AFTER `reservation_time`
```

添加附加费字段
```sql
ALTER TABLE `order` 
ADD COLUMN `surcharge` int(11) NOT NULL DEFAULT 0 COMMENT '附加费(精确小数点后5位,不参与折扣)' AFTER `ser_price`;

ALTER TABLE `tariff_group`
    ADD COLUMN `surcharge` int(11) NULL DEFAULT 0 COMMENT '附加费(精确到小数点后5位,不参与折扣)' AFTER `valley_ser_fee`;

ALTER TABLE `order`
    MODIFY COLUMN `ser_price` int(11) NOT NULL DEFAULT 0 COMMENT '本次服务费用(这是打折后的服务费用)+附加费 - 保留4为小数' AFTER `electricity_price`
```

## 2025-3-21

修改订单表的discount字段，将其数据类型修改为int(11),提高上限
```sql
ALTER TABLE `order` 
MODIFY COLUMN `discount` int(11) UNSIGNED NOT NULL DEFAULT 100 COMMENT '优惠折扣(精确到小数点后1位)' AFTER `reservation_time`
```