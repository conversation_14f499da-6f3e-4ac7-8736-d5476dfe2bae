services:
  nginx: &nginx
    extends:
      file: ../docker-compose/common.yml
      service: common
    image: ${DOCKER_BASE_IMAGE_PREFIX}nginx:${NGINX_VERSION}-${CI_COMMIT_REF_NAME}
    container_name: ${DOCKER_CONTAINER_PREFIX}${CI_SOURCE_NAME}-nginx
    environment:
      - NGINX_ENVSUBST_TEMPLATE_SUFFIX=.tpl
    mem_limit: 50M
    healthcheck:
      # test: [ "CMD-SHELL", "curl -I http://localhost:80/ || exit 1" ]
      test: [ "CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:80/ || exit 1" ]
      start_period: 5s
  nginx-build:
    <<: *nginx
    build:
      context: .
      args:
        - NGINX_SELF_BASE_IMAGE=${DOCKER_BASE_IMAGE_PREFIX}nginx:${NGINX_VERSION}-${CI_COMMIT_REF_NAME}
    image: ${DOCKER_IMAGE_PREFIX}${CI_SOURCE_NAME}/nginx:${NGINX_VERSION}-${CI_COMMIT_REF_NAME}
