server {
    listen       80;
    listen  [::]:80;
    server_name  localhost;

    index       index.php index.html index.htm default.php default.htm default.html;
    root        /www;

    location / {
        root   /www;
        index  index.html index.htm;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # 禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md) {
        return 404;
    }

    # 一键申请SSL证书验证目录相关设置
    location ~ \.well-known {
        allow all;
    }

    # 禁止在证书验证目录放入敏感文件
    if ($uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$") {
        return 403;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$ {
        expires     30d;
        error_log   /dev/null;
        access_log  /dev/null;
    }

    location ~ .*\.(js|css)?$ {
        expires     12h;
        error_log   /dev/null;
        access_log  /dev/null;
    }
}
