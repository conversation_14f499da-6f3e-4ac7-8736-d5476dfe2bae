# 允许所有来源
add_header 'Access-Control-Allow-Origin' '*';

# 允许的 HTTP 方法
add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';

# 允许的请求头
add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';

# 允许携带凭证（如 cookies）
add_header 'Access-Control-Allow-Credentials' 'true';

# 处理预检请求（OPTIONS 方法）
if ($request_method = 'OPTIONS') {
    add_header 'Access-Control-Max-Age' 1728000;  # 20 days
    add_header 'Content-Type' 'text/plain charset=UTF-8';
    add_header 'Content-Length' 0;
    return 204;
}
