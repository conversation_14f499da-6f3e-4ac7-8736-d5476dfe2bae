
user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

worker_rlimit_nofile 51200;

events {
    use                 epoll;
    # worker_connections  51200;
    worker_connections  2048;
    multi_accept        on;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent $request_time $upstream_response_time $upstream_addr '
                      '"$http_referer" "$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    server_names_hash_bucket_size   512;
    client_header_buffer_size       32k;
    large_client_header_buffers     4 32k;
    client_max_body_size            50m;

    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;

    keepalive_timeout  60;

    fastcgi_connect_timeout         300;
    fastcgi_send_timeout            300;
    fastcgi_read_timeout            300;
    fastcgi_buffer_size             64k;
    fastcgi_buffers                 4 64k;
    fastcgi_busy_buffers_size       128k;
    fastcgi_temp_file_write_size    256k;
    fastcgi_intercept_errors        on;

    gzip                on;
    gzip_min_length     1k;
    gzip_buffers        4 16k;
    gzip_http_version   1.1;
    gzip_comp_level     2;
    gzip_types          text/plain application/javascript application/x-javascript text/javascript text/css application/xml application/json image/jpeg image/gif image/png font/ttf font/otf image/svg+xml application/xml+rss text/x-js;
    gzip_vary           on;
    gzip_proxied        expired no-cache no-store private auth;
    gzip_disable        "MSIE [1-6]\.";

    limit_conn_zone     $binary_remote_addr zone=perip:10m;
    limit_conn_zone     $server_name zone=perserver:10m;

    server_tokens off;

    include /etc/nginx/conf.d/default.conf;
}
