## 数据库改动
```mysql
CREATE TABLE `charging_qrcode`
(
    `id`          INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '充电二维码ID',
    `url`         VARCHAR(255) NOT NULL COMMENT '二维码内容中的URL',
    `path`        VARCHAR(255) NOT NULL COMMENT '二维码保存的路径',
    `create_time` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  AUTO_INCREMENT = 10000000
  ROW_FORMAT = DYNAMIC COMMENT ='充电二维码';

CREATE TABLE `charging_qrcode_to_shots`
(
    `id`          INT UNSIGNED    NOT NULL COMMENT '充电二维码ID',
    `shots_id`    BIGINT UNSIGNED NOT NULL COMMENT '充电枪ID',
    `create_time` TIMESTAMP       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='充电二维码与充电枪的绑定关系';
```

## nginx 配置

```nginx
# 增加以下配置，用于解决二维码图片跨域问题。
location /charging_qrcode/ {
		add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type text/plain;
        add_header Content-Length 0;
        return 204;
    }
}
```

## composer 改动

```bash
composer require endroid/qr-code
```