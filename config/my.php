<?php
/** @noinspection HttpUrlsUsage */
/** @noinspection SpellCheckingInspection */

// +----------------------------------------------------------------------
// | 自定义配置
// +----------------------------------------------------------------------
return [
    'ws_port' => env('my.WS_PORT', 6872), // 服务端端口号
    'api_host' => env('my.API_HOST', ''), // http api监听地址
    'charge_service_webman_api' => env('my.CHARGE_SERVICE_WEBMAN_API', ''),
    'charge_service_webman_notice' => env('my.CHARGE_SERVICE_WEBMAN_NOTICE', ''),
    'charge_service_webman_centralized_api' => env('my.CHARGE_SERVICE_WEBMAN_CENTRALIZED_API', ''),
    'charge_service_webman_token' => env('my.CHARGE_SERVICE_WEBMAN_TOKEN', ''),
    'host' => env('my.HOST', ''),
    'send_handle_timer_timeout' => env('my.SEND_HANDLE_TIMER_TIMEOUT', ''),
    'qq_map_key' => env('my.QQ_MAP_KEY', ''),

    'SendHandleTimerApi' => env('my.SEND_HANDLE_TIMER_API', ''),
    'TimeoutHandleJobApi' => env('my.TIMEOUT_HANDLE_JOB_API', ''),

    'SendHandleTimerHost' => env('my.SEND_HANDLE_TIMER_HOST', ''),
    'TimeoutHandleJobHost' => env('my.TIMEOUT_HANDLE_JOB_HOST', ''),

    'StartChargingTimerApi' => env('my.START_CHARGING_TIMER_API', ''),
    'StartChargingTimerHost' => env('my.START_CHARGING_TIMER_HOST', ''),

    'StartChargingJobApi' => env('my.START_CHARGING_JOB_API', ''),
    'StartChargingJobHost' => env('my.START_CHARGING_JOB_HOST', ''),

    // 消息中心Socket
    'ChargeNoticeSocketRemoteAddress' => env('my.CHARGE_NOTICE_SOCKET_REMOTE_ADDRESS', ''),

    'redis' => [
        'scheme' => env('REDIS_SCHEME', 'tcp'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'port' => env('REDIS_PORT', 6379),
        'password' => env('REDIS_PASSWORD', ''),
        'database' => env('REDIS_DATABASE', 4),
        'timeout' => env('REDIS_TIMEOUT', 0),
        'persistent' => env('REDIS_PERSISTENT', true), // 是否是长连接
    ],

    'redis2' => [
        'scheme' => env('REDIS_SCHEME', 'tcp'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'port' => env('REDIS_PORT', 6379),
        'password' => env('REDIS_PASSWORD', ''),
        'database' => 2,
        'timeout' => env('REDIS_TIMEOUT', 0),
        'persistent' => env('REDIS_PERSISTENT', true), // 是否是长连接
    ],

    'redis5' => [
        'scheme' => env('REDIS_SCHEME', 'tcp'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'port' => env('REDIS_PORT', 6379),
        'password' => env('REDIS_PASSWORD', ''),
        'database' => 5,
        'timeout' => env('REDIS_TIMEOUT', 0),
        'persistent' => env('REDIS_PERSISTENT', true), // 是否是长连接
    ],

    'charge_manager_service' => [
        'host' => env('CHARGE_MANAGER_SERVICE.HOST', 'tcp://127.0.0.1:7273'),
        'connection_timeout' => env('CHARGE_MANAGER_SERVICE.CONNECTION_TIMEOUT', 1),
    ],
    'transfer_service' => [
        'host' => env('TRANSFER_SERVICE.HOST', 'tcp://127.0.0.1:7273'),
        'domain' => env('TRANSFER_SERVICE.DOMAIN', ''),
        'token' => env('TRANSFER_SERVICE.TOKEN', ''),
        'app_id' => env('TRANSFER_SERVICE.APP_ID', ''),
        'app_secret' => env('TRANSFER_SERVICE.APP_SECRET', '')
    ],

    'period' => [
        ['id' => 1, 'start' => '00:00', 'end' => '00:30'],
        ['id' => 2, 'start' => '00:30', 'end' => '01:00'],
        ['id' => 3, 'start' => '01:00', 'end' => '01:30'],
        ['id' => 4, 'start' => '01:30', 'end' => '02:00'],
        ['id' => 5, 'start' => '02:00', 'end' => '02:30'],
        ['id' => 6, 'start' => '02:30', 'end' => '03:00'],
        ['id' => 7, 'start' => '03:00', 'end' => '03:30'],
        ['id' => 8, 'start' => '03:30', 'end' => '04:00'],
        ['id' => 9, 'start' => '04:00', 'end' => '04:30'],
        ['id' => 10, 'start' => '04:30', 'end' => '05:00'],
        ['id' => 11, 'start' => '05:00', 'end' => '05:30'],
        ['id' => 12, 'start' => '05:30', 'end' => '06:00'],
        ['id' => 13, 'start' => '06:00', 'end' => '06:30'],
        ['id' => 14, 'start' => '06:30', 'end' => '07:00'],
        ['id' => 15, 'start' => '07:00', 'end' => '07:30'],
        ['id' => 16, 'start' => '07:30', 'end' => '08:00'],
        ['id' => 17, 'start' => '08:00', 'end' => '08:30'],
        ['id' => 18, 'start' => '08:30', 'end' => '09:00'],
        ['id' => 19, 'start' => '09:00', 'end' => '09:30'],
        ['id' => 20, 'start' => '09:30', 'end' => '10:00'],
        ['id' => 21, 'start' => '10:00', 'end' => '10:30'],
        ['id' => 22, 'start' => '10:30', 'end' => '11:00'],
        ['id' => 23, 'start' => '11:00', 'end' => '11:30'],
        ['id' => 24, 'start' => '11:30', 'end' => '12:00'],
        ['id' => 25, 'start' => '12:00', 'end' => '12:30'],
        ['id' => 26, 'start' => '12:30', 'end' => '13:00'],
        ['id' => 27, 'start' => '13:00', 'end' => '13:30'],
        ['id' => 28, 'start' => '13:30', 'end' => '14:00'],
        ['id' => 29, 'start' => '14:00', 'end' => '14:30'],
        ['id' => 30, 'start' => '14:30', 'end' => '15:00'],
        ['id' => 31, 'start' => '15:00', 'end' => '15:30'],
        ['id' => 32, 'start' => '15:30', 'end' => '16:00'],
        ['id' => 33, 'start' => '16:00', 'end' => '16:30'],
        ['id' => 34, 'start' => '16:30', 'end' => '17:00'],
        ['id' => 35, 'start' => '17:00', 'end' => '17:30'],
        ['id' => 36, 'start' => '17:30', 'end' => '18:00'],
        ['id' => 37, 'start' => '18:00', 'end' => '18:30'],
        ['id' => 38, 'start' => '18:30', 'end' => '19:00'],
        ['id' => 39, 'start' => '19:00', 'end' => '19:30'],
        ['id' => 40, 'start' => '19:30', 'end' => '20:00'],
        ['id' => 41, 'start' => '20:00', 'end' => '20:30'],
        ['id' => 42, 'start' => '20:30', 'end' => '21:00'],
        ['id' => 43, 'start' => '21:00', 'end' => '21:30'],
        ['id' => 44, 'start' => '21:30', 'end' => '22:00'],
        ['id' => 45, 'start' => '22:00', 'end' => '22:30'],
        ['id' => 46, 'start' => '22:30', 'end' => '23:00'],
        ['id' => 47, 'start' => '23:00', 'end' => '23:30'],
        ['id' => 48, 'start' => '23:30', 'end' => '24:00']
    ],


];
