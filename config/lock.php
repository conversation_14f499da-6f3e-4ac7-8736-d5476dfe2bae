<?php

return [
    'redis' => [
        'scheme' => env('REDIS_SCHEME', 'tcp'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'port' => env('REDIS_PORT', 6379),
        'password' => env('REDIS_PASSWORD', ''),
        'database' => env('REDIS_DATABASE', 4),
        'timeout' => env('REDIS_TIMEOUT', 0),
        'persistent' => env('REDIS_PERSISTENT', true), // 是否是长连接
    ],
    // 锁的有效时长(单位:秒)
    'expire' => 10,
    // 获取锁最大连续失败次数
    'lock_fail_count' => 100,

];