<?php
// +----------------------------------------------------------------------
// | RabbitMQ配置
// +----------------------------------------------------------------------

return [
    // RabbitMQ连接配置 - 从.env文件读取
    'connection' => [
        'host' => env('RABBITMQ_HOST', '127.0.0.1'),
        'port' => (int)env('RABBITMQ_PORT', 5672),
        'user' => env('RABBITMQ_USER', 'guest'),
        'password' => env('RABBITMQ_PASSWORD', 'guest'),
        'vhost' => env('RABBITMQ_VHOST', '/'),
        'connection_timeout' => (float)env('RABBITMQ_CONNECTION_TIMEOUT', 3.0),
        'read_write_timeout' => (float)env('RABBITMQ_READ_WRITE_TIMEOUT', 3.0),
        'heartbeat' => (int)env('RABBITMQ_HEARTBEAT', 0),
        'keepalive' => (bool)env('RABBITMQ_KEEPALIVE', false),
    ],
];
