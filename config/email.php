<?php
return [
    // 调试级别
    // 0:无输出的调试级别。
    // 1:显示客户端->服务器消息的调试级别。
    // 2:调试级别显示客户端->服务器和服务器->客户端消息。
    // 3:调试级别显示连接状态、客户端->服务器和服务器->客户端消息。
    // 4:显示所有消息的调试级别。
    'debug' => env('EMAIL.DEBUG', 0),
    // 设置要通过的SMTP服务器
    'smtp_host' => env('EMAIL.SMTP_HOST', true),
    // SMTP的端口
    'smtp_port' => env('EMAIL.SMTP_PORT', 465),
    // 启用SMTP身份验证
    'smtp_auth' => env('EMAIL.SMTP_AUTH', true),
    // SMTP用户名
    'username' => env('EMAIL.USERNAME', ''),
    // SMTP密码
    'password' => env('EMAIL.PASSWORD', ''),
    // 启用隐式TLS加密
    'smtp_secure' => env('EMAIL.SMTP_SECURE', ''),
    // 发件人邮箱
    'from_address' => env('EMAIL.FROM_ADDRESS', ''),
    // 发件人名称
    'from_name' => env('EMAIL.FROM_NAME', '')
];