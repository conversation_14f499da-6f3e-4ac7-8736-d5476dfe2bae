<?php

return [
    'temporary' => [
        // 企业微信机器人密钥
        'enterprise_wechat_key' => env('TEMPORARY_ACTIVE_ALARM_CONFIG.ENTERPRISE_WECHAT_KEY', ''),
        // 充电枪故障告警 - 是否自动生成工单 1:是 2:否
        'sf_is_generate_work_order' => env('TEMPORARY_ACTIVE_ALARM_CONFIG.SF_IS_GENERATE_WORK_ORDER', 2),
        // 充电枪故障告警 - 自动派发给指定工作人员
        'sf_auto_dispatch_user_id' => env('TEMPORARY_ACTIVE_ALARM_CONFIG.SF_AUTO_DISPATCH_USER_ID', 0),
        // 充电服务离线告警 - 是否自动生成工单 1:是 2:否
        'cso_is_generate_work_order' => env('TEMPORARY_ACTIVE_ALARM_CONFIG.CSO_IS_GENERATE_WORK_ORDER', 2),
        // 充电服务离线告警 - 自动派发给指定工作人员
        'cso_auto_dispatch_user_id' => env('TEMPORARY_ACTIVE_ALARM_CONFIG.CSO_AUTO_DISPATCH_USER_ID', 0),
        //  读取电表功率告警 - 是否自动生成工单 1:是 2:否
        'remp_is_generate_work_order' => env('TEMPORARY_ACTIVE_ALARM_CONFIG.REMP_IS_GENERATE_WORK_ORDER', 2),
        // 读取电表功率告警 - 自动派发给指定工作人员
        'remp_auto_dispatch_user_id' => env('TEMPORARY_ACTIVE_ALARM_CONFIG.REMP_AUTO_DISPATCH_USER_ID', 0),
        // 充电桩离线告警 - 是否自动生成工单 1:是 2:否
        'po_is_generate_work_order' => env('TEMPORARY_ACTIVE_ALARM_CONFIG.PO_IS_GENERATE_WORK_ORDER', 2),
        // 充电桩离线告警 - 自动派发给指定工作人员
        'po_auto_dispatch_user_id' => env('TEMPORARY_ACTIVE_ALARM_CONFIG.PO_AUTO_DISPATCH_USER_ID', 0),
        // 能源路由器资源告警 - 是否自动生成工单 1:是 2:否
        'ccsr_is_generate_work_order' => env('TEMPORARY_ACTIVE_ALARM_CONFIG.CCSR_IS_GENERATE_WORK_ORDER', 2),
        // 能源路由器资源告警 - 自动派发给指定工作人员
        'ccsr_auto_dispatch_user_id' => env('TEMPORARY_ACTIVE_ALARM_CONFIG.CCSR_AUTO_DISPATCH_USER_ID', 0),
        // 邮件
        'email' => env('TEMPORARY_ACTIVE_ALARM_CONFIG.EMAIL', ''),
    ]
];