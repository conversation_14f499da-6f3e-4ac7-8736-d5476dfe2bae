<?php

// +----------------------------------------------------------------------
// | 自定义配置
// +----------------------------------------------------------------------
return [
    'appid' => env('WECHAT.APPID'),
    'secret' => env('WECHAT.SECRET'),
    'api_key_v3' => env('WECHAT.API_KEY_V3'),
    'api_key_v2' => env('WECHAT.API_KEY_V2'),
    'mch_id' => env('WECHAT.MCH_ID'),
    'weixin_callback_url' => env('WECHAT.WEIXIN_CALLBACK_URL'),
    'weixin_pingtai_sn' => env('WECHAT.WEIXIN_PINGTAI_SN'),
    'weixin_pingtai_key' => env('WECHAT.WEIXIN_PINGTAI_KEY'),
    'apiclient_key_sn' => env('WECHAT.API_CLIENT_KEY_SN'),
    'api_client_public_key' => implode("\n", env('WECHAT.API_CLIENT_PUBLIC_KEY', [])),
    'apiclient_key' => implode("\n", env('WECHAT.API_CLIENT_KEY', [])),
    'apiclient_cert' => implode("\n", env('WECHAT.API_CLIENT_CERT', [])),
    // 内部API令牌(防止被外人调用)
    'internal_api_token' => env('WECHAT.INTERNAL_API_TOKEN'),
    // 企业微信API密钥
    'qy_api_key' => env('WECHAT.QY_API_KEY'),
    'weixin_refund_callback_url' => env('WECHAT.WEIXIN_REFUND_CALLBACK_URL'),
];
