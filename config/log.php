<?php

// +----------------------------------------------------------------------
// | 日志设置
// +----------------------------------------------------------------------
return [
    // 默认日志记录通道
    'default'      => env('log.channel', 'file'),
    // 日志记录级别
    'level'        => [
        'error',
        'warning',
        'sql',
        'aliApi',
        'info',
        '成功',
        '错误',
        '警告',
        '信息',
        '充电桩Socket',
        '下发指令',
        '开始充电任务',
        '下发指令定时器',
        '超时处理任务',
        '开始充电定时器',
        'wechat_callback',
        'request',
        'response',
        'debug'
    ],
    // 日志类型记录的通道 ['error'=>'email',...]
    'type_channel' => [],
    // 关闭全局日志写入
    'close'        => false,
    // 全局日志处理 支持闭包
    'processor'    => null,

    // 日志通道列表
    'channels'     => [
        'file' => [
            // 日志记录方式
            'type'           => 'File',
            // 日志保存目录
            'path'           => '',
            // 单文件日志写入
            'single'         => false,
            // 独立日志级别
            'apart_level'    => [
                'aliApi',
                '成功',
                '错误',
                '警告',
                '信息',
                '充电桩Socket',
                '下发指令',
                '开始充电任务',
                '下发指令定时器',
                '超时处理任务',
                '开始充电定时器',
            ],
            // 最大日志文件数量
            'max_files'      => 0,
            // 使用JSON格式记录
            'json'           => false,
            // 日志处理
            'processor'      => null,
            // 关闭通道日志写入
            'close'          => false,
            // 日志输出格式化
            'format'         => '%s[%s]%s',
            'time_format'    => '',
            // 是否实时写入
            'realtime_write' => true,
            // 日志文件大小限制（超出会生成多个文件）
            'file_size'      => 30*1024*1024
        ],
        'cli_log' => [
            // 日志记录方式
            'type'           => 'File',
            // 日志保存目录
            'path'           => 'runtime/cli_log',
            // 单文件日志写入
            'single'         => false,
            // 独立日志级别
            'apart_level'    => [],
            // 最大日志文件数量
            'max_files'      => 0,
            // 使用JSON格式记录
            'json'           => false,
            // 日志处理
            'processor'      => null,
            // 关闭通道日志写入
            'close'          => false,
            // 日志输出格式化
            'format'         => '%s[%s]%s',
            'time_format'    => '',
            // 是否实时写入
            'realtime_write' => true,
            // 日志文件大小限制（超出会生成多个文件）
            'file_size'      => 30*1024*1024
        ],
        'charge_client_read' => [
            // 日志记录方式
            'type'           => 'File',
            // 日志保存目录
            'path'           => 'runtime/charge_client_read',
            // 单文件日志写入
            'single'         => false,
            // 独立日志级别
            'apart_level'    => [],
            // 最大日志文件数量
            'max_files'      => 0,
            // 使用JSON格式记录
            'json'           => false,
            // 日志处理
            'processor'      => null,
            // 关闭通道日志写入
            'close'          => false,
            // 日志输出格式化
            'format'         => '%s[%s]%s',
            'time_format'    => '',
            // 是否实时写入
            'realtime_write' => true,
            // 日志文件大小限制（超出会生成多个文件）
            'file_size'      => 30*1024*1024
        ],

        'charge_client_write' => [
            // 日志记录方式
            'type'           => 'File',
            // 日志保存目录
            'path'           => 'runtime/charge_client_write',
            // 单文件日志写入
            'single'         => false,
            // 独立日志级别
            'apart_level'    => [],
            // 最大日志文件数量
            'max_files'      => 0,
            // 使用JSON格式记录
            'json'           => false,
            // 日志处理
            'processor'      => null,
            // 关闭通道日志写入
            'close'          => false,
            // 日志输出格式化
            'format'         => '%s[%s]%s',
            'time_format'    => '',
            // 是否实时写入
            'realtime_write' => true,
            // 日志文件大小限制（超出会生成多个文件）
            'file_size'      => 30*1024*1024
        ],
        // 其它日志通道配置
    ],

];
