services:
  loki: &loki
    extends:
      file: ../docker-compose/common.yml
      service: common
    image: ${DOCKER_BASE_IMAGE_PREFIX}loki:${LOKI_VERSION}-${CI_COMMIT_REF_NAME}
    container_name: ${DOCKER_CONTAINER_PREFIX}${CI_SOURCE_NAME}-loki
    ports:
      - $LOKI_PORT:3100
    mem_limit: 300M
    healthcheck:
      test: [ "CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3100/ready || exit 1" ]
  loki-build:
    <<: *loki
    build:
      context: .
      args:
        - LOKI_SELF_BASE_IMAGE=${DOCKER_BASE_IMAGE_PREFIX}loki:${LOKI_VERSION}-${CI_COMMIT_REF_NAME}
    image: ${DOCKER_IMAGE_PREFIX}${CI_SOURCE_NAME}/loki:${LOKI_VERSION}-${CI_COMMIT_REF_NAME}
