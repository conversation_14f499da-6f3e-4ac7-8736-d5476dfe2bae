<?php

namespace org;

use <PERSON><PERSON>Mailer\PHPMailer\PHPMailer;
use think\facade\Config;

class SendMessage
{
    /**
     * 发送邮件
     * @param array $email 邮箱地址
     * @param string $title 邮件标题
     * @param string $body 邮件内容
     * @param array $attachs 附件
     * @return mixed
     */
    public static function sendMail($email, $title, $body, $attachs = [])
    {

        $mail   = new PHPMailer(true);
        $config = Config::get('message.mail');
        try {
            $mail->Mailer     = $config['driver'];// 使用SMTP服务
            $mail->CharSet    = "utf8";// 编码格式为utf8，不设置编码的话，中文会出现乱码
            $mail->Host       = $config['host'];// 发送方的SMTP服务器地址
            $mail->SMTPAuth   = true;// 是否使用身份验证
            $mail->Username   = $config['username'];// 发送方的163邮箱用户名，就是你申请163的SMTP服务使用的163邮箱</span><span style="color:#333333;">
            $mail->Password   = $config['password'];// 发送方的邮箱密码，注意用163邮箱这里填写的是“客户端授权密码”而不是邮箱的登录密码！</span><span style="color:#333333;">
            $mail->SMTPSecure = "ssl";// 使用ssl协议方式</span><span style="color:#333333;">
            $mail->Port       = 465;// 163邮箱的ssl协议方式端口号是465/994
            $mail->isHTML();
            $mail->setFrom($config['form_address']);// 设置发件人信息，如邮件格式说明中的发件人，这里会显示为Mailer(<EMAIL>），Mailer是当做名字显示
            $mail->addReplyTo($config['form_address']);// 设置回复人信息，指的是收件人收到邮件后，如果要回复，回复邮件将发送到的邮箱地址
            foreach ($email as $v) {
                $mail->addAddress($v);
            }

            $mail->Subject = $title;// 邮件标题
            $mail->Body    = $body;// 邮件正文
            if ($attachs) {
                foreach ($attachs as $v) {
                    $mail->addAttachment($v);
                }
            };// 添加附件
            $mail->send();
            return 1;
        } catch (\Exception $e) {
            return "发送邮件错误: " . $mail->ErrorInfo;// 输出错误信息
        }
    }



}