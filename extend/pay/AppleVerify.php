<?php

namespace pay;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class AppleVerify
{

    //订单验证
    public function verifyReceipt($receipt)
    {
        //判断订单是否存在 检查状态
        //写入日志  查看票据格式  记录日志的方法 这个方法不贴出来了
        $data   = [
            'receipt-data' => $receipt,
            'password'     => config('apiconfig.apple_secret')
        ];
        $result = $this->post($data);
        trace('apple支付验证=》返回信息2：' . json_encode_cn($result), '信息');
        if($result){
            return $result;
        }else{
            return false;
        }

        /*if (!$result || $result['status'] !== 0) {
            trace('apple支付验证错误：'.json_encode_cn($result), '错误');
            return false;
        }*/
        //$receiptitem        = $result['receipt']['in_app'];
        //$item               = $this->arraySort($receiptitem, 'purchase_date_ms', SORT_DESC);
        //$item['is_sandbox'] = $result['environment'] === 'Sandbox' ? 1 : 0;
        //return $item;
    }

    //查找最新数据的方法
    public function post($data, $is_sandbox = false)
    {
        $url = 'https://buy.itunes.apple.com/verifyReceipt';      //正式
        if ($is_sandbox) {
            $url = 'https://sandbox.itunes.apple.com/verifyReceipt';  //测试
        }
        $client = new Client();
        try {
            $response = $client->request('post', $url, ['json' => $data, 'verify' => false]);
            trace('apple支付验证=》返回信息：' . json_encode_cn($response), '信息');
            if ($response->getStatusCode() != '200') {
                trace('apple支付验证错误：' . json_encode_cn($data) . "错误信息：" . (string)$response->getBody(), '错误');
                return false;
            }
            $result = json_decode((string)$response->getBody(), true);
            if ($result['status'] == 21007 && !$is_sandbox) {
                return $this->post($data, true);
            }
            return $result;
        } catch (GuzzleException $e) {
            trace('apple支付验证错误：' . json_encode_cn($data) . "错误信息：" . (string)$e->getMessage(), '错误');
            return false;
        }

    }

    //数组排序
    public static function arraySort($arr, $key, $type = SORT_ASC)
    {
        $sort_arr = array_column($arr, $key); // 初始化存放数组将要排序的字段值
        array_multisort($sort_arr, $type, $arr);
        return $arr[0]; // 数据返回
    }
}