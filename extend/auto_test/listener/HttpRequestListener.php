<?php
declare (strict_types=1);

namespace extend\auto_test\listener;

use auto_test\AutoTest;
use think\Request;

class HttpRequestListener
{
    public function handle(): bool
    {
        $request = app(Request::class);

        AutoTest::createInstance()->setCache(
            $request->action(),
            AutoTest::StartChargeStageApplet,
            'request',
            $request->header('authorization'),
            $request->all()
        );

        return true;
    }

}
