<?php
declare (strict_types=1);

namespace extend\auto_test\listener;

use auto_test\AutoTest;
use think\Request;
use think\Response;

class HttpResponseListener
{
    public function handle(Response $response): bool
    {
        $request = app(Request::class);

        AutoTest::createInstance()->setCache(
            $request->action(),
            AutoTest::StartChargeStageApplet,
            'run_result',
            $request->header('authorization'),
            $response->getContent()
        );


        return true;
    }

}
