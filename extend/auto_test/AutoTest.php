<?php

namespace auto_test;

use Redis;

class AutoTest
{
    protected bool $status = true;

    protected function __construct()
    {
    }

    public static function createInstance(): static
    {
        return new self();
    }

    public function getRedisClient(): Redis
    {
        $redis = new Redis();
        $redis->connect('127.0.0.1', 6379, 0);
//        $redis->auth('');
        return $redis;
    }

    public const TypeStartCharge = 'start_charge';

    public const StartChargeStageApplet = 1; // 运营后台 - 小程序
    public const StartChargeStageStartChargingJob = 2; // 运营后台 - 开始充电进程
    public const StartChargeStageTransferService = 3; // 中转服务
    public const StartChargeStageChargeServiceWebman = 4; // 设备管理中心

    public function setCache(string $type, int $stage, string $step, string $identifying, string|array $checkData)
    {
        if ($this->status === false) return;
        if (is_array($checkData)) $checkData = json_encode($checkData);
        $key = sprintf('auto_test:%s:%s', $type, $identifying);
        $this->getRedisClient()->hMSet($key, [
            'stage' => $stage,
            'step' => $step,
            'checkData' => $checkData
        ]);
    }

    public function getCache(string $type, int $stage, string $identifying)
    {
        if ($this->status === false) return;
        $key = sprintf('auto_test:%s:%s', $type, $identifying);
        return $this->getRedisClient()->hGetAll($key);
    }
}