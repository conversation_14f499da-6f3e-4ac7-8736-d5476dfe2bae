<?php

namespace wechat;

use GuzzleHttp\Client;

class WeChatPay2
{
    public function unifiedOrder($args)
    {
        $args['nonce_str'] = md5(uniqid());
        $args['sign_type'] = 'MD5';
        $args['spbill_create_ip'] = '127.0.0.1';
        $args['sign'] = $this->makeSign($args);
        $xml = DataTransform::arrayToXml($args);
        $api = "https://api.mch.weixin.qq.com/pay/unifiedorder";
        $client = new Client(['base_uri' => $api]);
        $res   = $client->request('POST',$xml, [
            'stream'         => true,
            'stream_context' => [
                'ssl' => [
                    'allow_self_signed' => true
                ],
            ]
        ]);
        if (!$res) return false;
        return DataTransform::xmlToArray($res);
    }
    //签名
    public function makeSign($params){
        //签名步骤一：按字典序排序数组参数
        ksort($params);
        $string = $this->ToUrlParams($params);  //参数进行拼接key=value&k=v
        //签名步骤二：在string后加入KEY
        $string = $string . "&key=key";
        //签名步骤三：MD5加密
        $string = md5($string);
        //签名步骤四：所有字符转为大写
        $result = strtoupper($string);
        return $result;
    }
    /**
     * 格式化参数格式化成url参数
     */
    public function ToUrlParams($params)
    {
        $buff = "";
        foreach ($params as $k => $v)
        {
            if($k != "sign" && $v != "" && !is_array($v)){
                $buff .= $k . "=" . $v . "&";
            }
        }

        $buff = trim($buff, "&");
        return $buff;
    }
}