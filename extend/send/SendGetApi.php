<?php

namespace send;

use app\common\context\Order as OrderContext;
use app\common\lib\VerifyData;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\response\Json;
use Throwable;

class SendGetApi
{
    /**
     * 计费模型设置
     * lwj 2023.8.15 新增
     * lwj 2023.8.21 修改
     * @param array $data
     * @param array $user_data
     * @return Json
     */
    public static function billing_model_setting(array $data,array $user_data):Json
    {
        try {
            $verify_data = v::input($data, VerifyData::chargingPile([
                'piles_id',
                'billing_mode_id',
                'sharp_fee',
                'sharp_ser_fee',
                'peak_fee',
                'peak_ser_fee',
                'flat_fee',
                'flat_ser_fee',
                'valley_fee',
                'valley_ser_fee',
                'loss_rate',
                'period_codes',
            ]));

            return send_http_post_charge('billing_model_setting',$verify_data,$user_data);

        } catch (ValidationException $e) {
            return res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return res_error([], '处理异常：'.$e->getMessage());
        }
    }

    /**
     * 读取实时监测数据
     * lwj 2023.8.15 新增
     * lwj 2023.8.21 修改
     * @param array $data
     * @param array $user_data
     * @return Json
     */
    public static function read_real_time_monitoring_data(array $data,array $user_data): Json
    {
        try {
            $verify_data = v::input($data, VerifyData::chargingPile([
                'piles_id',
                'shots_id',
            ]));

            return send_http_post_charge('read_real_time_monitoring_data',$verify_data,$user_data);

        } catch (ValidationException $e) {
            return res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return res_error([], '处理异常：'.$e->getMessage());
        }
    }

    /**
     * 运营平台远程控制启机
     * lwj 2023.8.15 新增
     * lwj 2023.8.21 修改
     * @param array $data
     * @param array $user_data
     * @return Json
     */
    public static function remote_control_startup_of_operating_platform(array $data,array $user_data): Json
    {
        try {
            $verify_data = v::input($data, VerifyData::chargingPile([
                'transaction_serial_number',
                'piles_id',
                'shots_id',
                'logical_card_number',
                'physical_card_number',
                'account_balance',
            ]));

//            cache('充电桩服务订单_' . $verify_data['transaction_serial_number'], $user_data, 864000); //暂时保存10天
            app(OrderContext::class)->saveOrderIdToContextMap($verify_data['transaction_serial_number'], $user_data);

            return send_http_post_charge('remote_control_startup_of_operating_platform',$verify_data,$user_data);

        } catch (ValidationException $e) {
            return res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return res_error([], '处理异常：'.$e->getMessage());
        }
    }

    /**
     * 运营平台远程停机
     * lwj 2023.8.15 新增
     * lwj 2023.8.21 修改
     * @param array $data
     * @param array $user_data
     * @return Json
     */
    public static function remote_shutdown_of_operating_platform(array $data,array $user_data): Json
    {
        try {
            $verify_data = v::input($data, VerifyData::chargingPile([
                'piles_id',
                'shots_id',
            ]));

            return send_http_post_charge('remote_shutdown_of_operating_platform',$verify_data,$user_data);

        } catch (ValidationException $e) {
            return res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return res_error([], '处理异常：'.$e->getMessage());
        }
    }

    /**
     * 远程账户余额更新
     * lwj 2023.8.15 新增
     * lwj 2023.8.21 修改
     * @param array $data
     * @param array $user_data
     * @return Json
     */
    public static function remote_account_balance_update(array $data,array $user_data): Json
    {
        try {
            $verify_data = v::input($data, VerifyData::chargingPile([
                'piles_id',
                'shots_id',
                'physical_card_number',
                'account_balance',
            ]));

            return send_http_post_charge('remote_account_balance_update',$verify_data,$user_data);

        } catch (ValidationException $e) {
            return res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return res_error([], '处理异常：'.$e->getMessage());
        }
    }

    /**
     * 充电桩工作参数设置
     * lwj 2023.8.15 新增
     * lwj 2023.8.21 修改
     * @param array $data
     * @param array $user_data
     * @return Json
     */
    public static function charging_station_parameter_setting(array $data,array $user_data): Json
    {
        try {
            $verify_data = v::input($data, VerifyData::chargingPile([
                'piles_id',
                'allow_work',
                'maximum_allowed_output_power',
            ]));

            return send_http_post_charge('charging_station_parameter_setting',$verify_data,$user_data);

        } catch (ValidationException $e) {
            return res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return res_error([], '处理异常：'.$e->getMessage());
        }
    }



    /**
     * 远程重启
     * lwj 2023.8.15 新增
     * lwj 2023.8.21 修改
     * @param array $data
     * @param array $user_data
     * @return Json
     */
    public static function remote_restart(array $data,array $user_data): Json
    {
        try {
            $verify_data = v::input($data, VerifyData::chargingPile([
                'piles_id',
                'execute_control',
            ]));

            return send_http_post_charge('remote_restart',$verify_data,$user_data);

        } catch (ValidationException $e) {
            return res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return res_error([], '处理异常：'.$e->getMessage());
        }
    }

    /**
     * 远程更新
     * lwj 2023.8.15 新增
     * lwj 2023.8.21 修改
     * @param array $data
     * @param array $user_data
     * @return Json
     */
    public static function remote_update(array $data,array $user_data): Json
    {
        try {
            $verify_data = v::input($data, VerifyData::chargingPile([
                'piles_id',
                'piles_model',
                'piles_power',
                'upgrade_server_address',
                'upgrade_server_port',
                'username',
                'password',
                'file_path',
                'execute_control',
                'download_timeout',
            ]));

            return send_http_post_charge('remote_update',$verify_data,$user_data);

        } catch (ValidationException $e) {
            return res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return res_error([], '处理异常：'.$e->getMessage());
        }
    }



}